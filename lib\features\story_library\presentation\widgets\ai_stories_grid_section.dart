import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

/// A widget representing the "AI Stories" section on the Home Screen grid.
///
/// This section is for stories generated by an AI, offering a unique
/// content category to the user.
class AIStoriesGridSection extends StatelessWidget {
  const AIStoriesGridSection({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final screenSize = MediaQuery.of(context).size;

    // Responsive icon size
    final iconSize = screenSize.width < 600 ? 40.0 : 48.0;

    return GestureDetector(
      onTap: () {
        // Navigate to the AI story generation screen
        context.go('/ai_stories');
      },
      child: Card(
        elevation: 4.0,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16.0),
        ),
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(16.0),
            gradient: LinearGradient(
              colors: [
                theme.colorScheme.tertiary.withValues(alpha: 0.7),
                theme.colorScheme.tertiary,
              ],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
          ),
          child: Padding(
            padding: EdgeInsets.symmetric(
              horizontal: screenSize.width * 0.02,
              vertical: 8.0,
            ),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.smart_toy_outlined,
                  size: iconSize,
                  color: theme.colorScheme.onTertiary,
                ),
                const SizedBox(height: 8.0),
                Text(
                  'AI Stories',
                  style: theme.textTheme.titleMedium?.copyWith(
                    color: theme.colorScheme.onTertiary,
                    fontWeight: FontWeight.bold,
                  ),
                  textAlign: TextAlign.center,
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
