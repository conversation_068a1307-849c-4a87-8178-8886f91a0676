import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:choice_once_upon_a_time/app/providers/service_providers.dart';

/// Popup to confirm if user wants to exit the current story
class ExitStoryConfirmationPopup extends ConsumerStatefulWidget {
  final VoidCallback? onConfirmExit;
  final VoidCallback? onCancel;

  const ExitStoryConfirmationPopup({
    super.key,
    this.onConfirmExit,
    this.onCancel,
  });

  @override
  ConsumerState<ExitStoryConfirmationPopup> createState() => _ExitStoryConfirmationPopupState();
}

class _ExitStoryConfirmationPopupState extends ConsumerState<ExitStoryConfirmationPopup>
    with TickerProviderStateMixin {
  late AnimationController _scaleController;
  late Animation<double> _scaleAnimation;
  bool _isPlayingPrompt = false;

  @override
  void initState() {
    super.initState();
    
    _scaleController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    
    _scaleAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _scaleController,
      curve: Curves.elasticOut,
    ));
    
    _scaleController.forward();
    
    // Auto-play the narrator prompt
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _playNarratorPrompt();
    });
  }

  @override
  void dispose() {
    _scaleController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Material(
      color: Colors.black.withOpacity(0.7),
      child: Center(
        child: ScaleTransition(
          scale: _scaleAnimation,
          child: Container(
            margin: const EdgeInsets.all(24),
            padding: const EdgeInsets.all(24),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(20),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.3),
                  blurRadius: 20,
                  offset: const Offset(0, 10),
                ),
              ],
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Character icon
                Container(
                  width: 80,
                  height: 80,
                  decoration: BoxDecoration(
                    color: Colors.orange[100],
                    shape: BoxShape.circle,
                  ),
                  child: Icon(
                    Icons.sentiment_neutral,
                    size: 40,
                    color: Colors.orange[600],
                  ),
                ),
                
                const SizedBox(height: 20),
                
                // Title
                Text(
                  'Leave Our Story?',
                  style: theme.textTheme.headlineSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: Colors.orange[700],
                  ),
                  textAlign: TextAlign.center,
                ),
                
                const SizedBox(height: 16),
                
                // Message
                Text(
                  'Are you sure you want to leave our story adventure? We can always continue later!',
                  style: theme.textTheme.bodyLarge?.copyWith(
                    color: Colors.grey[600],
                  ),
                  textAlign: TextAlign.center,
                ),
                
                const SizedBox(height: 24),
                
                // Audio control
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    OutlinedButton.icon(
                      onPressed: _isPlayingPrompt ? _stopPrompt : _playNarratorPrompt,
                      icon: Icon(_isPlayingPrompt ? Icons.stop : Icons.volume_up),
                      label: Text(_isPlayingPrompt ? 'Stop' : 'Hear Message'),
                      style: OutlinedButton.styleFrom(
                        foregroundColor: Colors.orange[600],
                        side: BorderSide(color: Colors.orange[300]!),
                      ),
                    ),
                  ],
                ),
                
                const SizedBox(height: 32),
                
                // Action buttons
                Row(
                  children: [
                    Expanded(
                      child: OutlinedButton(
                        onPressed: () {
                          _stopPrompt();
                          Navigator.of(context).pop();
                          widget.onCancel?.call();
                        },
                        style: OutlinedButton.styleFrom(
                          foregroundColor: Colors.green[600],
                          side: BorderSide(color: Colors.green[300]!),
                          padding: const EdgeInsets.symmetric(vertical: 16),
                        ),
                        child: const Text(
                          'Stay in Story',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                    ),
                    
                    const SizedBox(width: 16),
                    
                    Expanded(
                      child: ElevatedButton(
                        onPressed: () {
                          _stopPrompt();
                          Navigator.of(context).pop();
                          widget.onConfirmExit?.call();
                        },
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.orange[600],
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(vertical: 16),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                        ),
                        child: const Text(
                          'Leave Story',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Future<void> _playNarratorPrompt() async {
    final ttsService = ref.read(ttsServiceProvider);
    
    setState(() {
      _isPlayingPrompt = true;
    });

    try {
      await ttsService.speakText(
        'Oh, are you thinking of leaving our story adventure? That\'s okay! We can always continue later when you\'re ready. What would you like to do?',
        emotionCue: 'gentle',
      );
    } catch (e) {
      debugPrint('Error playing narrator prompt: $e');
    } finally {
      if (mounted) {
        setState(() {
          _isPlayingPrompt = false;
        });
      }
    }
  }

  void _stopPrompt() {
    final ttsService = ref.read(ttsServiceProvider);
    ttsService.stop();
    setState(() {
      _isPlayingPrompt = false;
    });
  }

  /// Show the exit story confirmation popup
  static Future<bool?> show(BuildContext context, {
    VoidCallback? onConfirmExit,
    VoidCallback? onCancel,
  }) {
    return showDialog<bool>(
      context: context,
      barrierDismissible: false,
      builder: (context) => ExitStoryConfirmationPopup(
        onConfirmExit: onConfirmExit,
        onCancel: onCancel,
      ),
    );
  }
}
