import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:choice_once_upon_a_time/features/parent_zone/presentation/widgets/settings_list_item_widget.dart';
import 'package:choice_once_upon_a_time/core/auth/session_manager.dart';
import 'package:choice_once_upon_a_time/core/utils/app_logger.dart';
// import 'package:choice_once_upon_a_time/core/mixins/screen_narrator_mixin.dart';

/// Parent Zone Dashboard screen
class ParentZoneDashboardScreen extends ConsumerStatefulWidget {
  const ParentZoneDashboardScreen({super.key});

  @override
  ConsumerState<ParentZoneDashboardScreen> createState() => _ParentZoneDashboardScreenState();
}

class _ParentZoneDashboardScreenState extends ConsumerState<ParentZoneDashboardScreen> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _checkAuthentication();
      // playScreenIntroduction(ref, 'screen_parent_zone_intro');
    });
  }

  /// Check if user is authenticated, redirect to parent gate if not
  Future<void> _checkAuthentication() async {
    try {
      final sessionManager = ref.read(sessionManagerProvider);
      final hasValidSession = await sessionManager.hasValidSession();

      if (!hasValidSession) {
        AppLogger.warning('ParentZone: No valid session found, redirecting to parent gate');
        if (mounted) {
          context.go('/parent_gate_entry');
        }
      } else {
        AppLogger.info('ParentZone: Valid session found, allowing access');
      }
    } catch (e, stackTrace) {
      AppLogger.error('ParentZone: Error checking authentication', e, stackTrace);
      if (mounted) {
        context.go('/parent_gate_entry');
      }
    }
  }

  @override
  void dispose() {
    // disposeScreenNarrator(ref);
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Scaffold(
      appBar: AppBar(
        title: const Text('Parent Zone'),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => context.pop(),
        ),
      ),
      body: SafeArea(
        child: ListView(
          padding: const EdgeInsets.all(16),
          children: [
          // Welcome section
          Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: theme.colorScheme.primary.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Text(
                  'Welcome to Parent Zone',
                  style: theme.textTheme.headlineSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: theme.colorScheme.primary,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 8),
                Text(
                  'Manage your child\'s story experience and app settings.',
                  style: theme.textTheme.bodyMedium?.copyWith(
                    color: Colors.grey[700],
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
          
          const SizedBox(height: 24),
          
          // Settings sections
          Center(
            child: Text(
              'Settings',
              style: theme.textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          
          const SizedBox(height: 16),
          
          // Sound Settings
          SettingsListItemWidget(
            title: 'Sound Settings',
            subtitle: 'Adjust volume and audio preferences',
            icon: Icons.volume_up,
            onTap: () => _navigateToSoundSettings(context),
          ),
          
          const SizedBox(height: 8),
          
          // User Profiles
          SettingsListItemWidget(
            title: 'User Profiles',
            subtitle: 'Manage family reading profiles',
            icon: Icons.people,
            onTap: () => _navigateToUserProfiles(context),
          ),

          const SizedBox(height: 8),

          // Progress Tracking
          SettingsListItemWidget(
            title: 'Progress Tracking',
            subtitle: 'View reading analytics and achievements',
            icon: Icons.analytics,
            onTap: () => _navigateToProgressTracking(context),
          ),

          const SizedBox(height: 8),

          // Subscription
          SettingsListItemWidget(
            title: 'Subscription',
            subtitle: 'Manage your premium subscription',
            icon: Icons.star,
            onTap: () => _navigateToSubscription(context),
          ),

          const SizedBox(height: 8),

          // Downloaded Stories
          SettingsListItemWidget(
            title: 'Downloaded Stories',
            subtitle: 'Manage offline story downloads',
            icon: Icons.download,
            onTap: () => _navigateToDownloads(context),
          ),
          
          const SizedBox(height: 8),
          
          // Language Settings
          SettingsListItemWidget(
            title: 'Language',
            subtitle: 'Change narration language',
            icon: Icons.language,
            onTap: () => _navigateToLanguage(context),
          ),

          const SizedBox(height: 8),

          // Narrator Voice Settings
          SettingsListItemWidget(
            title: 'Narrator Voice',
            subtitle: 'Create and select custom narrator voices',
            icon: Icons.record_voice_over,
            onTap: () => _navigateToNarratorCreation(context),
          ),
          
          const SizedBox(height: 24),
          
          // About section
          Center(
            child: Text(
              'About',
              style: theme.textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          
          const SizedBox(height: 16),
          
          // Privacy Policy
          SettingsListItemWidget(
            title: 'Privacy Policy',
            subtitle: 'Read our privacy policy',
            icon: Icons.privacy_tip,
            onTap: () => _navigateToPrivacyPolicy(context),
          ),
          
          const SizedBox(height: 8),
          
          // Terms of Service
          SettingsListItemWidget(
            title: 'Terms of Service',
            subtitle: 'Read our terms of service',
            icon: Icons.description,
            onTap: () => _navigateToTerms(context),
          ),
          
          const SizedBox(height: 8),
          
          // About App
          SettingsListItemWidget(
            title: 'About App',
            subtitle: 'App version and information',
            icon: Icons.info,
            onTap: () => _navigateToAbout(context),
          ),
          
          const SizedBox(height: 32),
          
          // Sign Out button (placeholder)
          Center(
            child: TextButton(
              onPressed: () => _signOut(context),
              child: Text(
                'Sign Out',
                style: TextStyle(
                  color: Colors.red[600],
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ),
        ],
        ),
      ),
    );
  }

  void _navigateToSoundSettings(BuildContext context) {
    context.go('/parent_zone/sound_settings');
  }

  void _navigateToUserProfiles(BuildContext context) {
    context.go('/parent_zone/user_profiles');
  }

  void _navigateToProgressTracking(BuildContext context) {
    context.go('/parent_zone/progress_tracking');
  }

  void _navigateToSubscription(BuildContext context) {
    context.go('/parent_zone/subscription');
  }

  void _navigateToDownloads(BuildContext context) {
    context.go('/parent_zone/manage_downloads');
  }

  void _navigateToLanguage(BuildContext context) {
    context.go('/parent_zone/language_settings');
  }

  void _navigateToNarratorCreation(BuildContext context) {
    context.go('/parent_zone/narrator_selection');
  }

  void _navigateToPrivacyPolicy(BuildContext context) {
    context.go('/parent_zone/help_support');
  }

  void _navigateToTerms(BuildContext context) {
    context.go('/parent_zone/help_support');
  }

  void _navigateToAbout(BuildContext context) {
    context.go('/parent_zone/about_stories');
  }

  void _signOut(BuildContext context) {
    // For Sprint 5, just show a confirmation dialog
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Sign Out'),
          content: const Text('Are you sure you want to sign out?'),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Cancel'),
            ),
            ElevatedButton(
              onPressed: () async {
                Navigator.of(context).pop();

                // Clear the session
                try {
                  final sessionManager = ref.read(sessionManagerProvider);
                  await sessionManager.clearSession();
                  AppLogger.info('ParentZone: Session cleared successfully');
                } catch (e, stackTrace) {
                  AppLogger.error('ParentZone: Error clearing session', e, stackTrace);
                }

                if (mounted) {
                  context.go('/home');
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(content: Text('Signed out successfully')),
                  );
                }
              },
              child: const Text('Sign Out'),
            ),
          ],
        );
      },
    );
  }
}
