import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:choice_once_upon_a_time/app/providers/service_providers.dart';
import 'package:choice_once_upon_a_time/core/utils/app_logger.dart';

/// Calm Exit Screen (Screen 20) - Final bedtime transition screen
class CalmExitScreen extends ConsumerStatefulWidget {
  const CalmExitScreen({super.key});

  @override
  ConsumerState<CalmExitScreen> createState() => _CalmExitScreenState();
}

class _CalmExitScreenState extends ConsumerState<CalmExitScreen>
    with TickerProviderStateMixin {
  late AnimationController _fadeController;
  late AnimationController _starController;
  late Animation<double> _fadeAnimation;
  late Animation<double> _starAnimation;
  
  bool _hasSpokenGoodnight = false;

  @override
  void initState() {
    super.initState();
    
    // Initialize animations
    _fadeController = AnimationController(
      duration: const Duration(seconds: 3),
      vsync: this,
    );
    
    _starController = AnimationController(
      duration: const Duration(seconds: 8),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _fadeController,
      curve: Curves.easeInOut,
    ));

    _starAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _starController,
      curve: Curves.easeInOut,
    ));

    // Start animations and speak goodnight message
    _startExitSequence();
  }

  @override
  void dispose() {
    _fadeController.dispose();
    _starController.dispose();
    super.dispose();
  }

  Future<void> _startExitSequence() async {
    // Start fade in animation
    _fadeController.forward();
    
    // Wait a moment, then start star animation
    await Future.delayed(const Duration(milliseconds: 500));
    _starController.repeat(reverse: true);
    
    // Speak the final goodnight message
    await _speakGoodnightMessage();
    
    // Auto-close app after delay (or indicate to parent to use OS controls)
    await Future.delayed(const Duration(seconds: 15));
    
    if (mounted) {
      // In a real implementation, you might want to minimize the app
      // or show instructions for the parent to close it
      _showCloseInstructions();
    }
  }

  Future<void> _speakGoodnightMessage() async {
    if (_hasSpokenGoodnight) return;
    
    try {
      final ttsService = ref.read(ttsServiceProvider);
      
      const goodnightMessages = [
        "The stars are twinkling just for you... Sweet dreams, little one.",
        "Close your eyes and let the gentle night carry you to dreamland.",
        "Sleep tight, brave adventurer. Tomorrow brings new stories to explore.",
        "The moon is watching over you tonight. Rest well, dear child.",
      ];
      
      // Pick a random goodnight message
      final message = goodnightMessages[
        DateTime.now().millisecond % goodnightMessages.length
      ];
      
      await ttsService.speakText(
        message,
        emotionCue: 'very_soft, whisper, gentle, sleepy',
      );
      
      _hasSpokenGoodnight = true;
    } catch (e) {
      AppLogger.error('Error speaking goodnight message', e);
      _hasSpokenGoodnight = true;
    }
  }

  void _showCloseInstructions() {
    // Show a subtle instruction for parents
    if (mounted) {
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => AlertDialog(
          backgroundColor: Colors.black87,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Icon(
                Icons.bedtime,
                color: Colors.white70,
                size: 48,
              ),
              const SizedBox(height: 16),
              const Text(
                'Sweet Dreams',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 20,
                  fontWeight: FontWeight.w300,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 8),
              const Text(
                'You can now close the app using your device\'s home button.',
                style: TextStyle(
                  color: Colors.white70,
                  fontSize: 14,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),
              TextButton(
                onPressed: () {
                  Navigator.of(context).pop();
                  // In a real app, you might navigate back to home or minimize
                },
                child: const Text(
                  'OK',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 16,
                  ),
                ),
              ),
            ],
          ),
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      onPopInvokedWithResult: (bool didPop, Object? result) async {
        if (didPop) return;

        // Show options to continue story or go home
        await _showExitOptionsDialog(context);
      },
      child: Scaffold(
        backgroundColor: const Color(0xFF0D1B2A), // Deep night blue
        body: AnimatedBuilder(
          animation: Listenable.merge([_fadeAnimation, _starAnimation]),
          builder: (context, child) {
          return Container(
            width: double.infinity,
            height: double.infinity,
            decoration: const BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [
                  Color(0xFF0D1B2A), // Deep night blue
                  Color(0xFF1B263B), // Slightly lighter blue
                  Color(0xFF0D1B2A), // Back to deep blue
                ],
              ),
            ),
            child: Stack(
              children: [
                // Animated stars
                ...List.generate(20, (index) => _buildStar(index)),
                
                // Moon
                Positioned(
                  top: 80,
                  right: 60,
                  child: FadeTransition(
                    opacity: _fadeAnimation,
                    child: Container(
                      width: 80,
                      height: 80,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        color: Colors.yellow[100],
                        boxShadow: [
                          BoxShadow(
                            color: Colors.yellow.withOpacity(0.3),
                            blurRadius: 20,
                            spreadRadius: 5,
                          ),
                        ],
                      ),
                      child: Icon(
                        Icons.bedtime,
                        color: Colors.yellow[800],
                        size: 40,
                      ),
                    ),
                  ),
                ),
                
                // Central content
                Center(
                  child: FadeTransition(
                    opacity: _fadeAnimation,
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        // Sleeping character or peaceful icon
                        Container(
                          width: 120,
                          height: 120,
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            color: Colors.white.withOpacity(0.1),
                          ),
                          child: const Icon(
                            Icons.nights_stay,
                            color: Colors.white70,
                            size: 60,
                          ),
                        ),
                        
                        const SizedBox(height: 40),
                        
                        // Sweet Dreams text
                        const Text(
                          'Sweet Dreams...',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 32,
                            fontWeight: FontWeight.w300,
                            letterSpacing: 2,
                          ),
                          textAlign: TextAlign.center,
                        ),
                        
                        const SizedBox(height: 16),
                        
                        // Subtitle
                        const Text(
                          'Until our next adventure',
                          style: TextStyle(
                            color: Colors.white60,
                            fontSize: 16,
                            fontWeight: FontWeight.w300,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ],
                    ),
                  ),
                ),
                
                // Gentle breathing indicator
                Positioned(
                  bottom: 100,
                  left: 0,
                  right: 0,
                  child: FadeTransition(
                    opacity: _fadeAnimation,
                    child: ScaleTransition(
                      scale: Tween<double>(
                        begin: 0.8,
                        end: 1.0,
                      ).animate(_starAnimation),
                      child: Center(
                        child: Container(
                          width: 60,
                          height: 60,
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            color: Colors.white.withOpacity(0.1),
                            border: Border.all(
                              color: Colors.white.withOpacity(0.3),
                              width: 1,
                            ),
                          ),
                          child: const Icon(
                            Icons.favorite,
                            color: Colors.white30,
                            size: 24,
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          );
        },
      ),
      ),
    );
  }

  /// Show exit options dialog for the calm exit screen
  Future<void> _showExitOptionsDialog(BuildContext context) async {
    return showDialog<void>(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          backgroundColor: Colors.black87,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          title: const Text(
            'What would you like to do?',
            style: TextStyle(color: Colors.white),
          ),
          content: const Text(
            'You can continue your story or return to the home screen.',
            style: TextStyle(color: Colors.white70),
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                // Return to the previous screen (story player)
                Navigator.of(context).pop();
              },
              child: const Text(
                'Continue Story',
                style: TextStyle(color: Colors.blue),
              ),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
                // Navigate to home screen
                context.go('/home');
              },
              child: const Text('Go Home'),
            ),
          ],
        );
      },
    );
  }

  Widget _buildStar(int index) {
    // Create random positions for stars
    final random = index * 17; // Simple pseudo-random based on index
    final left = (random % 300).toDouble() + 50;
    final top = ((random * 3) % 400).toDouble() + 100;
    final size = ((random % 3) + 2).toDouble();
    
    return Positioned(
      left: left,
      top: top,
      child: FadeTransition(
        opacity: _fadeAnimation,
        child: ScaleTransition(
          scale: Tween<double>(
            begin: 0.5,
            end: 1.0,
          ).animate(CurvedAnimation(
            parent: _starController,
            curve: Interval(
              (index % 10) / 10, // Stagger the animation
              1.0,
              curve: Curves.easeInOut,
            ),
          )),
          child: Container(
            width: size,
            height: size,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: Colors.white.withOpacity(0.8),
              boxShadow: [
                BoxShadow(
                  color: Colors.white.withOpacity(0.5),
                  blurRadius: 4,
                  spreadRadius: 1,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
