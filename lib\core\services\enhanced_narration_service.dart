import 'dart:async';
import 'package:flutter_tts/flutter_tts.dart';
import 'package:just_audio/just_audio.dart';
import 'package:logger/logger.dart';
import 'package:choice_once_upon_a_time/models/enhanced_story_model.dart';

/// Enhanced service for story narration with character voices and background music
class EnhancedNarrationService {
  static final Logger _logger = Logger();
  
  final FlutterTts _flutterTts = FlutterTts();
  final AudioPlayer _backgroundMusicPlayer = AudioPlayer();
  
  // Narration state
  bool _isInitialized = false;
  bool _isNarrating = false;
  bool _isPaused = false;
  bool _backgroundMusicEnabled = true;
  
  // Current narration
  EnhancedSceneModel? _currentScene;
  int _currentSentenceIndex = 0;
  List<String> _currentSentences = [];
  
  // Voice settings
  NarratorProfileModel? _narratorProfile;
  CharacterModel? _currentSpeaker;
  double _backgroundMusicVolume = 0.3;
  
  // Stream controllers for UI updates
  final StreamController<String> _currentTextController = StreamController<String>.broadcast();
  final StreamController<EnhancedNarrationProgress> _progressController = StreamController<EnhancedNarrationProgress>.broadcast();
  final StreamController<bool> _narrationStateController = StreamController<bool>.broadcast();
  final StreamController<String> _currentSpeakerController = StreamController<String>.broadcast();
  
  // Getters for streams
  Stream<String> get currentTextStream => _currentTextController.stream;
  Stream<EnhancedNarrationProgress> get progressStream => _progressController.stream;
  Stream<bool> get narrationStateStream => _narrationStateController.stream;
  Stream<String> get currentSpeakerStream => _currentSpeakerController.stream;
  
  // Getters for state
  bool get isNarrating => _isNarrating;
  bool get isPaused => _isPaused;
  bool get backgroundMusicEnabled => _backgroundMusicEnabled;
  double get backgroundMusicVolume => _backgroundMusicVolume;

  /// Initialize the enhanced narration service
  Future<void> initialize() async {
    if (_isInitialized) return;
    
    try {
      _logger.i('[EnhancedNarrationService] Initializing enhanced narration service');
      
      // Set up TTS callbacks
      _flutterTts.setStartHandler(() {
        _logger.d('[EnhancedNarrationService] TTS started');
        _isNarrating = true;
        _narrationStateController.add(true);
      });
      
      _flutterTts.setCompletionHandler(() {
        _logger.d('[EnhancedNarrationService] TTS completed');
        _onSentenceCompleted();
      });
      
      _flutterTts.setErrorHandler((msg) {
        _logger.e('[EnhancedNarrationService] TTS error: $msg');
        _isNarrating = false;
        _narrationStateController.add(false);
      });
      
      // Initialize background music player
      _backgroundMusicPlayer.setVolume(_backgroundMusicVolume);
      _backgroundMusicPlayer.setLoopMode(LoopMode.one);
      
      _isInitialized = true;
      _logger.i('[EnhancedNarrationService] Enhanced narration service initialized successfully');
      
    } catch (e) {
      _logger.e('[EnhancedNarrationService] Failed to initialize enhanced narration: $e');
      throw Exception('Failed to initialize enhanced narration service: $e');
    }
  }

  /// Start narrating an enhanced scene
  Future<void> narrateEnhancedScene(
    EnhancedSceneModel scene,
    EnhancedStoryModel story,
  ) async {
    if (!_isInitialized) {
      await initialize();
    }
    
    try {
      _logger.i('[EnhancedNarrationService] Starting enhanced narration for scene: ${scene.id}');
      
      // Stop any current narration
      await stop();
      
      _currentScene = scene;
      _narratorProfile = story.narratorProfile;
      
      // Find the speaker character
      _currentSpeaker = story.getCharacterByName(scene.speaker);
      _currentSpeakerController.add(scene.speaker);
      
      // Split text into sentences
      _currentSentences = _splitIntoSentences(scene.text);
      _currentSentenceIndex = 0;
      
      // Start background music if available and enabled
      if (_backgroundMusicEnabled && story.backgroundMusicPath != null) {
        await _startBackgroundMusic(story.backgroundMusicPath!);
      }
      
      // Apply voice settings for the speaker
      await _applyVoiceSettings();
      
      _updateProgress();
      
      // Start narrating the first sentence
      await _narrateCurrentSentence();
      
    } catch (e) {
      _logger.e('[EnhancedNarrationService] Failed to start enhanced scene narration: $e');
      _isNarrating = false;
      _narrationStateController.add(false);
    }
  }

  /// Apply voice settings based on current speaker
  Future<void> _applyVoiceSettings() async {
    VoiceModel voiceSettings;
    
    if (_currentSpeaker != null) {
      // Use character-specific voice settings
      voiceSettings = _currentSpeaker!.voice;
      _logger.d('[EnhancedNarrationService] Applying voice settings for character: ${_currentSpeaker!.name}');
    } else if (_narratorProfile != null) {
      // Use narrator voice settings
      voiceSettings = _narratorProfile!.voice;
      _logger.d('[EnhancedNarrationService] Applying narrator voice settings');
    } else {
      // Use default settings
      voiceSettings = const VoiceModel(
        pitch: 1.0,
        rate: 0.5,
        volume: 1.0,
      );
      _logger.d('[EnhancedNarrationService] Using default voice settings');
    }
    
    await _flutterTts.setSpeechRate(voiceSettings.rate);
    await _flutterTts.setVolume(voiceSettings.volume);
    await _flutterTts.setPitch(voiceSettings.pitch);
    await _flutterTts.setLanguage('en-US');
    
    // Set voice name if specified
    if (voiceSettings.name != null) {
      await _flutterTts.setVoice({
        'name': voiceSettings.name!,
        'locale': 'en-US',
      });
    }
  }

  /// Start background music
  Future<void> _startBackgroundMusic(String musicPath) async {
    try {
      _logger.d('[EnhancedNarrationService] Starting background music: $musicPath');
      await _backgroundMusicPlayer.setAsset(musicPath);
      await _backgroundMusicPlayer.play();
    } catch (e) {
      _logger.w('[EnhancedNarrationService] Failed to start background music: $e');
    }
  }

  /// Split text into sentences for narration
  List<String> _splitIntoSentences(String text) {
    // Remove emotion cues in brackets
    final cleanText = text.replaceAll(RegExp(r'\[.*?\]'), '');
    
    // Split by sentence endings
    final sentences = cleanText
        .split(RegExp(r'[.!?]+'))
        .map((s) => s.trim())
        .where((s) => s.isNotEmpty)
        .toList();
    
    return sentences;
  }

  /// Narrate the current sentence
  Future<void> _narrateCurrentSentence() async {
    if (_currentSentenceIndex >= _currentSentences.length) {
      _onNarrationCompleted();
      return;
    }
    
    final sentence = _currentSentences[_currentSentenceIndex];
    _logger.d('[EnhancedNarrationService] Narrating sentence ${_currentSentenceIndex + 1}/${_currentSentences.length}: $sentence');
    
    // Update current text for subtitle display
    _currentTextController.add(sentence);
    
    // Speak the sentence
    await _flutterTts.speak(sentence);
  }

  /// Handle sentence completion
  void _onSentenceCompleted() {
    _currentSentenceIndex++;
    _updateProgress();
    
    if (_currentSentenceIndex < _currentSentences.length) {
      // Continue with next sentence after a brief pause
      final pauseDuration = _currentScene?.pauseDuration ?? 500;
      Future.delayed(Duration(milliseconds: pauseDuration), () {
        if (_isNarrating && !_isPaused) {
          _narrateCurrentSentence();
        }
      });
    } else {
      _onNarrationCompleted();
    }
  }

  /// Handle narration completion
  void _onNarrationCompleted() {
    _logger.i('[EnhancedNarrationService] Enhanced scene narration completed');
    _isNarrating = false;
    _narrationStateController.add(false);
    _currentTextController.add(''); // Clear subtitle
  }

  /// Update progress information
  void _updateProgress() {
    final progress = EnhancedNarrationProgress(
      currentSentence: _currentSentenceIndex + 1,
      totalSentences: _currentSentences.length,
      progress: _currentSentences.isNotEmpty ? (_currentSentenceIndex + 1) / _currentSentences.length : 0.0,
      currentSpeaker: _currentScene?.speaker ?? 'Narrator',
      emotion: _currentScene?.emotion ?? 'neutral',
    );
    _progressController.add(progress);
  }

  /// Narrate reflection text
  Future<void> narrateReflection(ReflectionModel reflection) async {
    try {
      _logger.i('[EnhancedNarrationService] Starting reflection narration');
      
      // Apply narrator voice for reflection
      if (_narratorProfile != null) {
        await _applyVoiceSettings();
      }
      
      _currentTextController.add(reflection.text);
      await _flutterTts.speak(reflection.text);
      
    } catch (e) {
      _logger.e('[EnhancedNarrationService] Failed to narrate reflection: $e');
    }
  }

  /// Set background music enabled/disabled
  Future<void> setBackgroundMusicEnabled(bool enabled) async {
    _backgroundMusicEnabled = enabled;
    
    if (!enabled && _backgroundMusicPlayer.playing) {
      await _backgroundMusicPlayer.pause();
    } else if (enabled && !_backgroundMusicPlayer.playing) {
      await _backgroundMusicPlayer.play();
    }
    
    _logger.d('[EnhancedNarrationService] Background music enabled: $enabled');
  }

  /// Set background music volume
  Future<void> setBackgroundMusicVolume(double volume) async {
    _backgroundMusicVolume = volume.clamp(0.0, 1.0);
    await _backgroundMusicPlayer.setVolume(_backgroundMusicVolume);
    _logger.d('[EnhancedNarrationService] Background music volume: $_backgroundMusicVolume');
  }

  /// Pause narration
  Future<void> pause() async {
    if (_isNarrating && !_isPaused) {
      await _flutterTts.pause();
      await _backgroundMusicPlayer.pause();
      _isPaused = true;
      _logger.d('[EnhancedNarrationService] Enhanced narration paused');
    }
  }

  /// Resume narration
  Future<void> resume() async {
    if (_isPaused) {
      await _flutterTts.speak(_currentSentences[_currentSentenceIndex]);
      if (_backgroundMusicEnabled) {
        await _backgroundMusicPlayer.play();
      }
      _isPaused = false;
      _logger.d('[EnhancedNarrationService] Enhanced narration resumed');
    }
  }

  /// Stop narration
  Future<void> stop() async {
    if (_isNarrating || _isPaused) {
      await _flutterTts.stop();
      await _backgroundMusicPlayer.stop();
      _isNarrating = false;
      _isPaused = false;
      _narrationStateController.add(false);
      _currentTextController.add(''); // Clear subtitle
      _logger.d('[EnhancedNarrationService] Enhanced narration stopped');
    }
  }

  /// Dispose of resources
  void dispose() {
    _flutterTts.stop();
    _backgroundMusicPlayer.dispose();
    _currentTextController.close();
    _progressController.close();
    _narrationStateController.close();
    _currentSpeakerController.close();
    _logger.i('[EnhancedNarrationService] Enhanced narration service disposed');
  }
}

/// Enhanced progress information for narration
class EnhancedNarrationProgress {
  final int currentSentence;
  final int totalSentences;
  final double progress;
  final String currentSpeaker;
  final String emotion;

  EnhancedNarrationProgress({
    required this.currentSentence,
    required this.totalSentences,
    required this.progress,
    required this.currentSpeaker,
    required this.emotion,
  });
}
