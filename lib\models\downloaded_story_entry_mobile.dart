import 'package:isar/isar.dart';

part 'downloaded_story_entry_mobile.g.dart';

/// Isar collection for tracking downloaded stories for offline access
@collection
class DownloadedStoryEntry {
  /// Auto-increment ID for Isar (web-compatible)
  Id id = 0;

  /// Unique story identifier
  @Index(unique: true, replace: true)
  String storyId;

  /// Story version
  String version;

  /// When the story was downloaded
  DateTime downloadedAt;

  /// Path to the stored full story JSON file
  String localStoryJsonPath;

  /// List of asset filenames/IDs stored locally
  List<String> downloadedAssetIds;

  /// Whether the story is fully downloaded and ready for offline use
  bool isFullyDownloaded;

  /// Estimated total size in MB
  int totalSizeMb;

  /// Last time this entry was accessed
  DateTime lastAccessedAt;

  DownloadedStoryEntry({
    required this.storyId,
    required this.version,
    required this.downloadedAt,
    required this.localStoryJsonPath,
    required this.downloadedAssetIds,
    this.isFullyDownloaded = false,
    this.totalSizeMb = 0,
    required this.lastAccessedAt,
  });

  /// Creates a copy of this entry with updated fields
  DownloadedStoryEntry copyWith({
    String? storyId,
    String? version,
    DateTime? downloadedAt,
    String? localStoryJsonPath,
    List<String>? downloadedAssetIds,
    bool? isFullyDownloaded,
    int? totalSizeMb,
    DateTime? lastAccessedAt,
  }) {
    return DownloadedStoryEntry(
      storyId: storyId ?? this.storyId,
      version: version ?? this.version,
      downloadedAt: downloadedAt ?? this.downloadedAt,
      localStoryJsonPath: localStoryJsonPath ?? this.localStoryJsonPath,
      downloadedAssetIds: downloadedAssetIds ?? this.downloadedAssetIds,
      isFullyDownloaded: isFullyDownloaded ?? this.isFullyDownloaded,
      totalSizeMb: totalSizeMb ?? this.totalSizeMb,
      lastAccessedAt: lastAccessedAt ?? this.lastAccessedAt,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is DownloadedStoryEntry && 
           other.storyId == storyId && 
           other.version == version;
  }

  @override
  int get hashCode => Object.hash(storyId, version);

  @override
  String toString() {
    return 'DownloadedStoryEntry(storyId: $storyId, version: $version, fullyDownloaded: $isFullyDownloaded)';
  }
}
