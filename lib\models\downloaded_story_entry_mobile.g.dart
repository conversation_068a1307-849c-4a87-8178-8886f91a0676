// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'downloaded_story_entry_mobile.dart';

// **************************************************************************
// IsarCollectionGenerator
// **************************************************************************

// coverage:ignore-file
// ignore_for_file: duplicate_ignore, non_constant_identifier_names, constant_identifier_names, invalid_use_of_protected_member, unnecessary_cast, prefer_const_constructors, lines_longer_than_80_chars, require_trailing_commas, inference_failure_on_function_invocation, unnecessary_parenthesis, unnecessary_raw_strings, unnecessary_null_checks, join_return_with_assignment, prefer_final_locals, avoid_js_rounded_ints, avoid_positional_boolean_parameters, always_specify_types

extension GetDownloadedStoryEntryCollection on Isar {
  IsarCollection<DownloadedStoryEntry> get downloadedStoryEntrys =>
      this.collection();
}

const DownloadedStoryEntrySchema = CollectionSchema(
  name: r'DownloadedStoryEntry',
  id: -987661745305330891,
  properties: {
    r'downloadedAssetIds': PropertySchema(
      id: 0,
      name: r'downloadedAssetIds',
      type: IsarType.stringList,
    ),
    r'downloadedAt': PropertySchema(
      id: 1,
      name: r'downloadedAt',
      type: IsarType.dateTime,
    ),
    r'hashCode': PropertySchema(
      id: 2,
      name: r'hashCode',
      type: IsarType.long,
    ),
    r'isFullyDownloaded': PropertySchema(
      id: 3,
      name: r'isFullyDownloaded',
      type: IsarType.bool,
    ),
    r'lastAccessedAt': PropertySchema(
      id: 4,
      name: r'lastAccessedAt',
      type: IsarType.dateTime,
    ),
    r'localStoryJsonPath': PropertySchema(
      id: 5,
      name: r'localStoryJsonPath',
      type: IsarType.string,
    ),
    r'storyId': PropertySchema(
      id: 6,
      name: r'storyId',
      type: IsarType.string,
    ),
    r'totalSizeMb': PropertySchema(
      id: 7,
      name: r'totalSizeMb',
      type: IsarType.long,
    ),
    r'version': PropertySchema(
      id: 8,
      name: r'version',
      type: IsarType.string,
    )
  },
  estimateSize: _downloadedStoryEntryEstimateSize,
  serialize: _downloadedStoryEntrySerialize,
  deserialize: _downloadedStoryEntryDeserialize,
  deserializeProp: _downloadedStoryEntryDeserializeProp,
  idName: r'id',
  indexes: {
    r'storyId': IndexSchema(
      id: -7904996416186759579,
      name: r'storyId',
      unique: true,
      replace: true,
      properties: [
        IndexPropertySchema(
          name: r'storyId',
          type: IndexType.hash,
          caseSensitive: true,
        )
      ],
    )
  },
  links: {},
  embeddedSchemas: {},
  getId: _downloadedStoryEntryGetId,
  getLinks: _downloadedStoryEntryGetLinks,
  attach: _downloadedStoryEntryAttach,
  version: '3.1.0+1',
);

int _downloadedStoryEntryEstimateSize(
  DownloadedStoryEntry object,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  var bytesCount = offsets.last;
  bytesCount += 3 + object.downloadedAssetIds.length * 3;
  {
    for (var i = 0; i < object.downloadedAssetIds.length; i++) {
      final value = object.downloadedAssetIds[i];
      bytesCount += value.length * 3;
    }
  }
  bytesCount += 3 + object.localStoryJsonPath.length * 3;
  bytesCount += 3 + object.storyId.length * 3;
  bytesCount += 3 + object.version.length * 3;
  return bytesCount;
}

void _downloadedStoryEntrySerialize(
  DownloadedStoryEntry object,
  IsarWriter writer,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  writer.writeStringList(offsets[0], object.downloadedAssetIds);
  writer.writeDateTime(offsets[1], object.downloadedAt);
  writer.writeLong(offsets[2], object.hashCode);
  writer.writeBool(offsets[3], object.isFullyDownloaded);
  writer.writeDateTime(offsets[4], object.lastAccessedAt);
  writer.writeString(offsets[5], object.localStoryJsonPath);
  writer.writeString(offsets[6], object.storyId);
  writer.writeLong(offsets[7], object.totalSizeMb);
  writer.writeString(offsets[8], object.version);
}

DownloadedStoryEntry _downloadedStoryEntryDeserialize(
  Id id,
  IsarReader reader,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  final object = DownloadedStoryEntry(
    downloadedAssetIds: reader.readStringList(offsets[0]) ?? [],
    downloadedAt: reader.readDateTime(offsets[1]),
    isFullyDownloaded: reader.readBoolOrNull(offsets[3]) ?? false,
    lastAccessedAt: reader.readDateTime(offsets[4]),
    localStoryJsonPath: reader.readString(offsets[5]),
    storyId: reader.readString(offsets[6]),
    totalSizeMb: reader.readLongOrNull(offsets[7]) ?? 0,
    version: reader.readString(offsets[8]),
  );
  object.id = id;
  return object;
}

P _downloadedStoryEntryDeserializeProp<P>(
  IsarReader reader,
  int propertyId,
  int offset,
  Map<Type, List<int>> allOffsets,
) {
  switch (propertyId) {
    case 0:
      return (reader.readStringList(offset) ?? []) as P;
    case 1:
      return (reader.readDateTime(offset)) as P;
    case 2:
      return (reader.readLong(offset)) as P;
    case 3:
      return (reader.readBoolOrNull(offset) ?? false) as P;
    case 4:
      return (reader.readDateTime(offset)) as P;
    case 5:
      return (reader.readString(offset)) as P;
    case 6:
      return (reader.readString(offset)) as P;
    case 7:
      return (reader.readLongOrNull(offset) ?? 0) as P;
    case 8:
      return (reader.readString(offset)) as P;
    default:
      throw IsarError('Unknown property with id $propertyId');
  }
}

Id _downloadedStoryEntryGetId(DownloadedStoryEntry object) {
  return object.id;
}

List<IsarLinkBase<dynamic>> _downloadedStoryEntryGetLinks(
    DownloadedStoryEntry object) {
  return [];
}

void _downloadedStoryEntryAttach(
    IsarCollection<dynamic> col, Id id, DownloadedStoryEntry object) {
  object.id = id;
}

extension DownloadedStoryEntryByIndex on IsarCollection<DownloadedStoryEntry> {
  Future<DownloadedStoryEntry?> getByStoryId(String storyId) {
    return getByIndex(r'storyId', [storyId]);
  }

  DownloadedStoryEntry? getByStoryIdSync(String storyId) {
    return getByIndexSync(r'storyId', [storyId]);
  }

  Future<bool> deleteByStoryId(String storyId) {
    return deleteByIndex(r'storyId', [storyId]);
  }

  bool deleteByStoryIdSync(String storyId) {
    return deleteByIndexSync(r'storyId', [storyId]);
  }

  Future<List<DownloadedStoryEntry?>> getAllByStoryId(
      List<String> storyIdValues) {
    final values = storyIdValues.map((e) => [e]).toList();
    return getAllByIndex(r'storyId', values);
  }

  List<DownloadedStoryEntry?> getAllByStoryIdSync(List<String> storyIdValues) {
    final values = storyIdValues.map((e) => [e]).toList();
    return getAllByIndexSync(r'storyId', values);
  }

  Future<int> deleteAllByStoryId(List<String> storyIdValues) {
    final values = storyIdValues.map((e) => [e]).toList();
    return deleteAllByIndex(r'storyId', values);
  }

  int deleteAllByStoryIdSync(List<String> storyIdValues) {
    final values = storyIdValues.map((e) => [e]).toList();
    return deleteAllByIndexSync(r'storyId', values);
  }

  Future<Id> putByStoryId(DownloadedStoryEntry object) {
    return putByIndex(r'storyId', object);
  }

  Id putByStoryIdSync(DownloadedStoryEntry object, {bool saveLinks = true}) {
    return putByIndexSync(r'storyId', object, saveLinks: saveLinks);
  }

  Future<List<Id>> putAllByStoryId(List<DownloadedStoryEntry> objects) {
    return putAllByIndex(r'storyId', objects);
  }

  List<Id> putAllByStoryIdSync(List<DownloadedStoryEntry> objects,
      {bool saveLinks = true}) {
    return putAllByIndexSync(r'storyId', objects, saveLinks: saveLinks);
  }
}

extension DownloadedStoryEntryQueryWhereSort
    on QueryBuilder<DownloadedStoryEntry, DownloadedStoryEntry, QWhere> {
  QueryBuilder<DownloadedStoryEntry, DownloadedStoryEntry, QAfterWhere>
      anyId() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(const IdWhereClause.any());
    });
  }
}

extension DownloadedStoryEntryQueryWhere
    on QueryBuilder<DownloadedStoryEntry, DownloadedStoryEntry, QWhereClause> {
  QueryBuilder<DownloadedStoryEntry, DownloadedStoryEntry, QAfterWhereClause>
      idEqualTo(Id id) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IdWhereClause.between(
        lower: id,
        upper: id,
      ));
    });
  }

  QueryBuilder<DownloadedStoryEntry, DownloadedStoryEntry, QAfterWhereClause>
      idNotEqualTo(Id id) {
    return QueryBuilder.apply(this, (query) {
      if (query.whereSort == Sort.asc) {
        return query
            .addWhereClause(
              IdWhereClause.lessThan(upper: id, includeUpper: false),
            )
            .addWhereClause(
              IdWhereClause.greaterThan(lower: id, includeLower: false),
            );
      } else {
        return query
            .addWhereClause(
              IdWhereClause.greaterThan(lower: id, includeLower: false),
            )
            .addWhereClause(
              IdWhereClause.lessThan(upper: id, includeUpper: false),
            );
      }
    });
  }

  QueryBuilder<DownloadedStoryEntry, DownloadedStoryEntry, QAfterWhereClause>
      idGreaterThan(Id id, {bool include = false}) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(
        IdWhereClause.greaterThan(lower: id, includeLower: include),
      );
    });
  }

  QueryBuilder<DownloadedStoryEntry, DownloadedStoryEntry, QAfterWhereClause>
      idLessThan(Id id, {bool include = false}) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(
        IdWhereClause.lessThan(upper: id, includeUpper: include),
      );
    });
  }

  QueryBuilder<DownloadedStoryEntry, DownloadedStoryEntry, QAfterWhereClause>
      idBetween(
    Id lowerId,
    Id upperId, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IdWhereClause.between(
        lower: lowerId,
        includeLower: includeLower,
        upper: upperId,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<DownloadedStoryEntry, DownloadedStoryEntry, QAfterWhereClause>
      storyIdEqualTo(String storyId) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.equalTo(
        indexName: r'storyId',
        value: [storyId],
      ));
    });
  }

  QueryBuilder<DownloadedStoryEntry, DownloadedStoryEntry, QAfterWhereClause>
      storyIdNotEqualTo(String storyId) {
    return QueryBuilder.apply(this, (query) {
      if (query.whereSort == Sort.asc) {
        return query
            .addWhereClause(IndexWhereClause.between(
              indexName: r'storyId',
              lower: [],
              upper: [storyId],
              includeUpper: false,
            ))
            .addWhereClause(IndexWhereClause.between(
              indexName: r'storyId',
              lower: [storyId],
              includeLower: false,
              upper: [],
            ));
      } else {
        return query
            .addWhereClause(IndexWhereClause.between(
              indexName: r'storyId',
              lower: [storyId],
              includeLower: false,
              upper: [],
            ))
            .addWhereClause(IndexWhereClause.between(
              indexName: r'storyId',
              lower: [],
              upper: [storyId],
              includeUpper: false,
            ));
      }
    });
  }
}

extension DownloadedStoryEntryQueryFilter on QueryBuilder<DownloadedStoryEntry,
    DownloadedStoryEntry, QFilterCondition> {
  QueryBuilder<DownloadedStoryEntry, DownloadedStoryEntry,
      QAfterFilterCondition> downloadedAssetIdsElementEqualTo(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'downloadedAssetIds',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DownloadedStoryEntry, DownloadedStoryEntry,
      QAfterFilterCondition> downloadedAssetIdsElementGreaterThan(
    String value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'downloadedAssetIds',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DownloadedStoryEntry, DownloadedStoryEntry,
      QAfterFilterCondition> downloadedAssetIdsElementLessThan(
    String value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'downloadedAssetIds',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DownloadedStoryEntry, DownloadedStoryEntry,
      QAfterFilterCondition> downloadedAssetIdsElementBetween(
    String lower,
    String upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'downloadedAssetIds',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DownloadedStoryEntry, DownloadedStoryEntry,
      QAfterFilterCondition> downloadedAssetIdsElementStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'downloadedAssetIds',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DownloadedStoryEntry, DownloadedStoryEntry,
      QAfterFilterCondition> downloadedAssetIdsElementEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'downloadedAssetIds',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DownloadedStoryEntry, DownloadedStoryEntry,
          QAfterFilterCondition>
      downloadedAssetIdsElementContains(String value,
          {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'downloadedAssetIds',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DownloadedStoryEntry, DownloadedStoryEntry,
          QAfterFilterCondition>
      downloadedAssetIdsElementMatches(String pattern,
          {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'downloadedAssetIds',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DownloadedStoryEntry, DownloadedStoryEntry,
      QAfterFilterCondition> downloadedAssetIdsElementIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'downloadedAssetIds',
        value: '',
      ));
    });
  }

  QueryBuilder<DownloadedStoryEntry, DownloadedStoryEntry,
      QAfterFilterCondition> downloadedAssetIdsElementIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'downloadedAssetIds',
        value: '',
      ));
    });
  }

  QueryBuilder<DownloadedStoryEntry, DownloadedStoryEntry,
      QAfterFilterCondition> downloadedAssetIdsLengthEqualTo(int length) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'downloadedAssetIds',
        length,
        true,
        length,
        true,
      );
    });
  }

  QueryBuilder<DownloadedStoryEntry, DownloadedStoryEntry,
      QAfterFilterCondition> downloadedAssetIdsIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'downloadedAssetIds',
        0,
        true,
        0,
        true,
      );
    });
  }

  QueryBuilder<DownloadedStoryEntry, DownloadedStoryEntry,
      QAfterFilterCondition> downloadedAssetIdsIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'downloadedAssetIds',
        0,
        false,
        999999,
        true,
      );
    });
  }

  QueryBuilder<DownloadedStoryEntry, DownloadedStoryEntry,
      QAfterFilterCondition> downloadedAssetIdsLengthLessThan(
    int length, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'downloadedAssetIds',
        0,
        true,
        length,
        include,
      );
    });
  }

  QueryBuilder<DownloadedStoryEntry, DownloadedStoryEntry,
      QAfterFilterCondition> downloadedAssetIdsLengthGreaterThan(
    int length, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'downloadedAssetIds',
        length,
        include,
        999999,
        true,
      );
    });
  }

  QueryBuilder<DownloadedStoryEntry, DownloadedStoryEntry,
      QAfterFilterCondition> downloadedAssetIdsLengthBetween(
    int lower,
    int upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'downloadedAssetIds',
        lower,
        includeLower,
        upper,
        includeUpper,
      );
    });
  }

  QueryBuilder<DownloadedStoryEntry, DownloadedStoryEntry,
      QAfterFilterCondition> downloadedAtEqualTo(DateTime value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'downloadedAt',
        value: value,
      ));
    });
  }

  QueryBuilder<DownloadedStoryEntry, DownloadedStoryEntry,
      QAfterFilterCondition> downloadedAtGreaterThan(
    DateTime value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'downloadedAt',
        value: value,
      ));
    });
  }

  QueryBuilder<DownloadedStoryEntry, DownloadedStoryEntry,
      QAfterFilterCondition> downloadedAtLessThan(
    DateTime value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'downloadedAt',
        value: value,
      ));
    });
  }

  QueryBuilder<DownloadedStoryEntry, DownloadedStoryEntry,
      QAfterFilterCondition> downloadedAtBetween(
    DateTime lower,
    DateTime upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'downloadedAt',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<DownloadedStoryEntry, DownloadedStoryEntry,
      QAfterFilterCondition> hashCodeEqualTo(int value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'hashCode',
        value: value,
      ));
    });
  }

  QueryBuilder<DownloadedStoryEntry, DownloadedStoryEntry,
      QAfterFilterCondition> hashCodeGreaterThan(
    int value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'hashCode',
        value: value,
      ));
    });
  }

  QueryBuilder<DownloadedStoryEntry, DownloadedStoryEntry,
      QAfterFilterCondition> hashCodeLessThan(
    int value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'hashCode',
        value: value,
      ));
    });
  }

  QueryBuilder<DownloadedStoryEntry, DownloadedStoryEntry,
      QAfterFilterCondition> hashCodeBetween(
    int lower,
    int upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'hashCode',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<DownloadedStoryEntry, DownloadedStoryEntry,
      QAfterFilterCondition> idEqualTo(Id value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'id',
        value: value,
      ));
    });
  }

  QueryBuilder<DownloadedStoryEntry, DownloadedStoryEntry,
      QAfterFilterCondition> idGreaterThan(
    Id value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'id',
        value: value,
      ));
    });
  }

  QueryBuilder<DownloadedStoryEntry, DownloadedStoryEntry,
      QAfterFilterCondition> idLessThan(
    Id value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'id',
        value: value,
      ));
    });
  }

  QueryBuilder<DownloadedStoryEntry, DownloadedStoryEntry,
      QAfterFilterCondition> idBetween(
    Id lower,
    Id upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'id',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<DownloadedStoryEntry, DownloadedStoryEntry,
      QAfterFilterCondition> isFullyDownloadedEqualTo(bool value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'isFullyDownloaded',
        value: value,
      ));
    });
  }

  QueryBuilder<DownloadedStoryEntry, DownloadedStoryEntry,
      QAfterFilterCondition> lastAccessedAtEqualTo(DateTime value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'lastAccessedAt',
        value: value,
      ));
    });
  }

  QueryBuilder<DownloadedStoryEntry, DownloadedStoryEntry,
      QAfterFilterCondition> lastAccessedAtGreaterThan(
    DateTime value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'lastAccessedAt',
        value: value,
      ));
    });
  }

  QueryBuilder<DownloadedStoryEntry, DownloadedStoryEntry,
      QAfterFilterCondition> lastAccessedAtLessThan(
    DateTime value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'lastAccessedAt',
        value: value,
      ));
    });
  }

  QueryBuilder<DownloadedStoryEntry, DownloadedStoryEntry,
      QAfterFilterCondition> lastAccessedAtBetween(
    DateTime lower,
    DateTime upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'lastAccessedAt',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<DownloadedStoryEntry, DownloadedStoryEntry,
      QAfterFilterCondition> localStoryJsonPathEqualTo(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'localStoryJsonPath',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DownloadedStoryEntry, DownloadedStoryEntry,
      QAfterFilterCondition> localStoryJsonPathGreaterThan(
    String value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'localStoryJsonPath',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DownloadedStoryEntry, DownloadedStoryEntry,
      QAfterFilterCondition> localStoryJsonPathLessThan(
    String value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'localStoryJsonPath',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DownloadedStoryEntry, DownloadedStoryEntry,
      QAfterFilterCondition> localStoryJsonPathBetween(
    String lower,
    String upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'localStoryJsonPath',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DownloadedStoryEntry, DownloadedStoryEntry,
      QAfterFilterCondition> localStoryJsonPathStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'localStoryJsonPath',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DownloadedStoryEntry, DownloadedStoryEntry,
      QAfterFilterCondition> localStoryJsonPathEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'localStoryJsonPath',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DownloadedStoryEntry, DownloadedStoryEntry,
          QAfterFilterCondition>
      localStoryJsonPathContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'localStoryJsonPath',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DownloadedStoryEntry, DownloadedStoryEntry,
          QAfterFilterCondition>
      localStoryJsonPathMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'localStoryJsonPath',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DownloadedStoryEntry, DownloadedStoryEntry,
      QAfterFilterCondition> localStoryJsonPathIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'localStoryJsonPath',
        value: '',
      ));
    });
  }

  QueryBuilder<DownloadedStoryEntry, DownloadedStoryEntry,
      QAfterFilterCondition> localStoryJsonPathIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'localStoryJsonPath',
        value: '',
      ));
    });
  }

  QueryBuilder<DownloadedStoryEntry, DownloadedStoryEntry,
      QAfterFilterCondition> storyIdEqualTo(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'storyId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DownloadedStoryEntry, DownloadedStoryEntry,
      QAfterFilterCondition> storyIdGreaterThan(
    String value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'storyId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DownloadedStoryEntry, DownloadedStoryEntry,
      QAfterFilterCondition> storyIdLessThan(
    String value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'storyId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DownloadedStoryEntry, DownloadedStoryEntry,
      QAfterFilterCondition> storyIdBetween(
    String lower,
    String upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'storyId',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DownloadedStoryEntry, DownloadedStoryEntry,
      QAfterFilterCondition> storyIdStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'storyId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DownloadedStoryEntry, DownloadedStoryEntry,
      QAfterFilterCondition> storyIdEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'storyId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DownloadedStoryEntry, DownloadedStoryEntry,
          QAfterFilterCondition>
      storyIdContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'storyId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DownloadedStoryEntry, DownloadedStoryEntry,
          QAfterFilterCondition>
      storyIdMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'storyId',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DownloadedStoryEntry, DownloadedStoryEntry,
      QAfterFilterCondition> storyIdIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'storyId',
        value: '',
      ));
    });
  }

  QueryBuilder<DownloadedStoryEntry, DownloadedStoryEntry,
      QAfterFilterCondition> storyIdIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'storyId',
        value: '',
      ));
    });
  }

  QueryBuilder<DownloadedStoryEntry, DownloadedStoryEntry,
      QAfterFilterCondition> totalSizeMbEqualTo(int value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'totalSizeMb',
        value: value,
      ));
    });
  }

  QueryBuilder<DownloadedStoryEntry, DownloadedStoryEntry,
      QAfterFilterCondition> totalSizeMbGreaterThan(
    int value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'totalSizeMb',
        value: value,
      ));
    });
  }

  QueryBuilder<DownloadedStoryEntry, DownloadedStoryEntry,
      QAfterFilterCondition> totalSizeMbLessThan(
    int value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'totalSizeMb',
        value: value,
      ));
    });
  }

  QueryBuilder<DownloadedStoryEntry, DownloadedStoryEntry,
      QAfterFilterCondition> totalSizeMbBetween(
    int lower,
    int upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'totalSizeMb',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<DownloadedStoryEntry, DownloadedStoryEntry,
      QAfterFilterCondition> versionEqualTo(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'version',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DownloadedStoryEntry, DownloadedStoryEntry,
      QAfterFilterCondition> versionGreaterThan(
    String value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'version',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DownloadedStoryEntry, DownloadedStoryEntry,
      QAfterFilterCondition> versionLessThan(
    String value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'version',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DownloadedStoryEntry, DownloadedStoryEntry,
      QAfterFilterCondition> versionBetween(
    String lower,
    String upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'version',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DownloadedStoryEntry, DownloadedStoryEntry,
      QAfterFilterCondition> versionStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'version',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DownloadedStoryEntry, DownloadedStoryEntry,
      QAfterFilterCondition> versionEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'version',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DownloadedStoryEntry, DownloadedStoryEntry,
          QAfterFilterCondition>
      versionContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'version',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DownloadedStoryEntry, DownloadedStoryEntry,
          QAfterFilterCondition>
      versionMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'version',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<DownloadedStoryEntry, DownloadedStoryEntry,
      QAfterFilterCondition> versionIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'version',
        value: '',
      ));
    });
  }

  QueryBuilder<DownloadedStoryEntry, DownloadedStoryEntry,
      QAfterFilterCondition> versionIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'version',
        value: '',
      ));
    });
  }
}

extension DownloadedStoryEntryQueryObject on QueryBuilder<DownloadedStoryEntry,
    DownloadedStoryEntry, QFilterCondition> {}

extension DownloadedStoryEntryQueryLinks on QueryBuilder<DownloadedStoryEntry,
    DownloadedStoryEntry, QFilterCondition> {}

extension DownloadedStoryEntryQuerySortBy
    on QueryBuilder<DownloadedStoryEntry, DownloadedStoryEntry, QSortBy> {
  QueryBuilder<DownloadedStoryEntry, DownloadedStoryEntry, QAfterSortBy>
      sortByDownloadedAt() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'downloadedAt', Sort.asc);
    });
  }

  QueryBuilder<DownloadedStoryEntry, DownloadedStoryEntry, QAfterSortBy>
      sortByDownloadedAtDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'downloadedAt', Sort.desc);
    });
  }

  QueryBuilder<DownloadedStoryEntry, DownloadedStoryEntry, QAfterSortBy>
      sortByHashCode() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'hashCode', Sort.asc);
    });
  }

  QueryBuilder<DownloadedStoryEntry, DownloadedStoryEntry, QAfterSortBy>
      sortByHashCodeDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'hashCode', Sort.desc);
    });
  }

  QueryBuilder<DownloadedStoryEntry, DownloadedStoryEntry, QAfterSortBy>
      sortByIsFullyDownloaded() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'isFullyDownloaded', Sort.asc);
    });
  }

  QueryBuilder<DownloadedStoryEntry, DownloadedStoryEntry, QAfterSortBy>
      sortByIsFullyDownloadedDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'isFullyDownloaded', Sort.desc);
    });
  }

  QueryBuilder<DownloadedStoryEntry, DownloadedStoryEntry, QAfterSortBy>
      sortByLastAccessedAt() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'lastAccessedAt', Sort.asc);
    });
  }

  QueryBuilder<DownloadedStoryEntry, DownloadedStoryEntry, QAfterSortBy>
      sortByLastAccessedAtDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'lastAccessedAt', Sort.desc);
    });
  }

  QueryBuilder<DownloadedStoryEntry, DownloadedStoryEntry, QAfterSortBy>
      sortByLocalStoryJsonPath() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'localStoryJsonPath', Sort.asc);
    });
  }

  QueryBuilder<DownloadedStoryEntry, DownloadedStoryEntry, QAfterSortBy>
      sortByLocalStoryJsonPathDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'localStoryJsonPath', Sort.desc);
    });
  }

  QueryBuilder<DownloadedStoryEntry, DownloadedStoryEntry, QAfterSortBy>
      sortByStoryId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'storyId', Sort.asc);
    });
  }

  QueryBuilder<DownloadedStoryEntry, DownloadedStoryEntry, QAfterSortBy>
      sortByStoryIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'storyId', Sort.desc);
    });
  }

  QueryBuilder<DownloadedStoryEntry, DownloadedStoryEntry, QAfterSortBy>
      sortByTotalSizeMb() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'totalSizeMb', Sort.asc);
    });
  }

  QueryBuilder<DownloadedStoryEntry, DownloadedStoryEntry, QAfterSortBy>
      sortByTotalSizeMbDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'totalSizeMb', Sort.desc);
    });
  }

  QueryBuilder<DownloadedStoryEntry, DownloadedStoryEntry, QAfterSortBy>
      sortByVersion() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'version', Sort.asc);
    });
  }

  QueryBuilder<DownloadedStoryEntry, DownloadedStoryEntry, QAfterSortBy>
      sortByVersionDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'version', Sort.desc);
    });
  }
}

extension DownloadedStoryEntryQuerySortThenBy
    on QueryBuilder<DownloadedStoryEntry, DownloadedStoryEntry, QSortThenBy> {
  QueryBuilder<DownloadedStoryEntry, DownloadedStoryEntry, QAfterSortBy>
      thenByDownloadedAt() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'downloadedAt', Sort.asc);
    });
  }

  QueryBuilder<DownloadedStoryEntry, DownloadedStoryEntry, QAfterSortBy>
      thenByDownloadedAtDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'downloadedAt', Sort.desc);
    });
  }

  QueryBuilder<DownloadedStoryEntry, DownloadedStoryEntry, QAfterSortBy>
      thenByHashCode() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'hashCode', Sort.asc);
    });
  }

  QueryBuilder<DownloadedStoryEntry, DownloadedStoryEntry, QAfterSortBy>
      thenByHashCodeDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'hashCode', Sort.desc);
    });
  }

  QueryBuilder<DownloadedStoryEntry, DownloadedStoryEntry, QAfterSortBy>
      thenById() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'id', Sort.asc);
    });
  }

  QueryBuilder<DownloadedStoryEntry, DownloadedStoryEntry, QAfterSortBy>
      thenByIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'id', Sort.desc);
    });
  }

  QueryBuilder<DownloadedStoryEntry, DownloadedStoryEntry, QAfterSortBy>
      thenByIsFullyDownloaded() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'isFullyDownloaded', Sort.asc);
    });
  }

  QueryBuilder<DownloadedStoryEntry, DownloadedStoryEntry, QAfterSortBy>
      thenByIsFullyDownloadedDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'isFullyDownloaded', Sort.desc);
    });
  }

  QueryBuilder<DownloadedStoryEntry, DownloadedStoryEntry, QAfterSortBy>
      thenByLastAccessedAt() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'lastAccessedAt', Sort.asc);
    });
  }

  QueryBuilder<DownloadedStoryEntry, DownloadedStoryEntry, QAfterSortBy>
      thenByLastAccessedAtDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'lastAccessedAt', Sort.desc);
    });
  }

  QueryBuilder<DownloadedStoryEntry, DownloadedStoryEntry, QAfterSortBy>
      thenByLocalStoryJsonPath() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'localStoryJsonPath', Sort.asc);
    });
  }

  QueryBuilder<DownloadedStoryEntry, DownloadedStoryEntry, QAfterSortBy>
      thenByLocalStoryJsonPathDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'localStoryJsonPath', Sort.desc);
    });
  }

  QueryBuilder<DownloadedStoryEntry, DownloadedStoryEntry, QAfterSortBy>
      thenByStoryId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'storyId', Sort.asc);
    });
  }

  QueryBuilder<DownloadedStoryEntry, DownloadedStoryEntry, QAfterSortBy>
      thenByStoryIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'storyId', Sort.desc);
    });
  }

  QueryBuilder<DownloadedStoryEntry, DownloadedStoryEntry, QAfterSortBy>
      thenByTotalSizeMb() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'totalSizeMb', Sort.asc);
    });
  }

  QueryBuilder<DownloadedStoryEntry, DownloadedStoryEntry, QAfterSortBy>
      thenByTotalSizeMbDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'totalSizeMb', Sort.desc);
    });
  }

  QueryBuilder<DownloadedStoryEntry, DownloadedStoryEntry, QAfterSortBy>
      thenByVersion() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'version', Sort.asc);
    });
  }

  QueryBuilder<DownloadedStoryEntry, DownloadedStoryEntry, QAfterSortBy>
      thenByVersionDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'version', Sort.desc);
    });
  }
}

extension DownloadedStoryEntryQueryWhereDistinct
    on QueryBuilder<DownloadedStoryEntry, DownloadedStoryEntry, QDistinct> {
  QueryBuilder<DownloadedStoryEntry, DownloadedStoryEntry, QDistinct>
      distinctByDownloadedAssetIds() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'downloadedAssetIds');
    });
  }

  QueryBuilder<DownloadedStoryEntry, DownloadedStoryEntry, QDistinct>
      distinctByDownloadedAt() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'downloadedAt');
    });
  }

  QueryBuilder<DownloadedStoryEntry, DownloadedStoryEntry, QDistinct>
      distinctByHashCode() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'hashCode');
    });
  }

  QueryBuilder<DownloadedStoryEntry, DownloadedStoryEntry, QDistinct>
      distinctByIsFullyDownloaded() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'isFullyDownloaded');
    });
  }

  QueryBuilder<DownloadedStoryEntry, DownloadedStoryEntry, QDistinct>
      distinctByLastAccessedAt() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'lastAccessedAt');
    });
  }

  QueryBuilder<DownloadedStoryEntry, DownloadedStoryEntry, QDistinct>
      distinctByLocalStoryJsonPath({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'localStoryJsonPath',
          caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<DownloadedStoryEntry, DownloadedStoryEntry, QDistinct>
      distinctByStoryId({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'storyId', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<DownloadedStoryEntry, DownloadedStoryEntry, QDistinct>
      distinctByTotalSizeMb() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'totalSizeMb');
    });
  }

  QueryBuilder<DownloadedStoryEntry, DownloadedStoryEntry, QDistinct>
      distinctByVersion({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'version', caseSensitive: caseSensitive);
    });
  }
}

extension DownloadedStoryEntryQueryProperty on QueryBuilder<
    DownloadedStoryEntry, DownloadedStoryEntry, QQueryProperty> {
  QueryBuilder<DownloadedStoryEntry, int, QQueryOperations> idProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'id');
    });
  }

  QueryBuilder<DownloadedStoryEntry, List<String>, QQueryOperations>
      downloadedAssetIdsProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'downloadedAssetIds');
    });
  }

  QueryBuilder<DownloadedStoryEntry, DateTime, QQueryOperations>
      downloadedAtProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'downloadedAt');
    });
  }

  QueryBuilder<DownloadedStoryEntry, int, QQueryOperations> hashCodeProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'hashCode');
    });
  }

  QueryBuilder<DownloadedStoryEntry, bool, QQueryOperations>
      isFullyDownloadedProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'isFullyDownloaded');
    });
  }

  QueryBuilder<DownloadedStoryEntry, DateTime, QQueryOperations>
      lastAccessedAtProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'lastAccessedAt');
    });
  }

  QueryBuilder<DownloadedStoryEntry, String, QQueryOperations>
      localStoryJsonPathProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'localStoryJsonPath');
    });
  }

  QueryBuilder<DownloadedStoryEntry, String, QQueryOperations>
      storyIdProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'storyId');
    });
  }

  QueryBuilder<DownloadedStoryEntry, int, QQueryOperations>
      totalSizeMbProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'totalSizeMb');
    });
  }

  QueryBuilder<DownloadedStoryEntry, String, QQueryOperations>
      versionProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'version');
    });
  }
}
