import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:choice_once_upon_a_time/features/auth/data/auth_service.dart';

// Generate mocks
@GenerateMocks([FirebaseAuth, User, UserCredential])
import 'auth_service_test.mocks.dart';

void main() {
  group('AuthService', () {
    late AuthService authService;
    late MockFirebaseAuth mockFirebaseAuth;
    late MockUser mockUser;
    late MockUserCredential mockUserCredential;

    setUp(() {
      mockFirebaseAuth = MockFirebaseAuth();
      mockUser = MockUser();
      mockUserCredential = MockUserCredential();
      authService = AuthService(firebaseAuth: mockFirebaseAuth);
    });

    group('currentUser', () {
      test('returns current user when authenticated', () {
        // Arrange
        when(mockFirebaseAuth.currentUser).thenReturn(mockUser);

        // Act
        final result = authService.currentUser;

        // Assert
        expect(result, equals(mockUser));
      });

      test('returns null when not authenticated', () {
        // Arrange
        when(mockFirebaseAuth.currentUser).thenReturn(null);

        // Act
        final result = authService.currentUser;

        // Assert
        expect(result, isNull);
      });
    });

    group('isAuthenticated', () {
      test('returns true when user is authenticated', () {
        // Arrange
        when(mockFirebaseAuth.currentUser).thenReturn(mockUser);

        // Act
        final result = authService.isAuthenticated;

        // Assert
        expect(result, isTrue);
      });

      test('returns false when user is not authenticated', () {
        // Arrange
        when(mockFirebaseAuth.currentUser).thenReturn(null);

        // Act
        final result = authService.isAuthenticated;

        // Assert
        expect(result, isFalse);
      });
    });

    group('signUpWithEmailAndPassword', () {
      test('returns UserCredential on successful sign up', () async {
        // Arrange
        const email = '<EMAIL>';
        const password = 'password123';
        when(mockFirebaseAuth.createUserWithEmailAndPassword(
          email: email,
          password: password,
        )).thenAnswer((_) async => mockUserCredential);

        // Act
        final result = await authService.signUpWithEmailAndPassword(
          email: email,
          password: password,
        );

        // Assert
        expect(result, equals(mockUserCredential));
        verify(mockFirebaseAuth.createUserWithEmailAndPassword(
          email: email,
          password: password,
        )).called(1);
      });

      test('throws AuthException on FirebaseAuthException', () async {
        // Arrange
        const email = '<EMAIL>';
        const password = 'weak';
        when(mockFirebaseAuth.createUserWithEmailAndPassword(
          email: email,
          password: password,
        )).thenThrow(FirebaseAuthException(code: 'weak-password'));

        // Act & Assert
        expect(
          () => authService.signUpWithEmailAndPassword(
            email: email,
            password: password,
          ),
          throwsA(isA<AuthException>()),
        );
      });
    });

    group('signInWithEmailAndPassword', () {
      test('returns UserCredential on successful sign in', () async {
        // Arrange
        const email = '<EMAIL>';
        const password = 'password123';
        when(mockFirebaseAuth.signInWithEmailAndPassword(
          email: email,
          password: password,
        )).thenAnswer((_) async => mockUserCredential);

        // Act
        final result = await authService.signInWithEmailAndPassword(
          email: email,
          password: password,
        );

        // Assert
        expect(result, equals(mockUserCredential));
        verify(mockFirebaseAuth.signInWithEmailAndPassword(
          email: email,
          password: password,
        )).called(1);
      });

      test('throws AuthException on wrong password', () async {
        // Arrange
        const email = '<EMAIL>';
        const password = 'wrongpassword';
        when(mockFirebaseAuth.signInWithEmailAndPassword(
          email: email,
          password: password,
        )).thenThrow(FirebaseAuthException(code: 'wrong-password'));

        // Act & Assert
        expect(
          () => authService.signInWithEmailAndPassword(
            email: email,
            password: password,
          ),
          throwsA(isA<AuthException>()),
        );
      });
    });

    group('signOut', () {
      test('calls FirebaseAuth signOut', () async {
        // Arrange
        when(mockFirebaseAuth.signOut()).thenAnswer((_) async {});

        // Act
        await authService.signOut();

        // Assert
        verify(mockFirebaseAuth.signOut()).called(1);
      });

      test('throws AuthException on error', () async {
        // Arrange
        when(mockFirebaseAuth.signOut()).thenThrow(Exception('Sign out failed'));

        // Act & Assert
        expect(
          () => authService.signOut(),
          throwsA(isA<AuthException>()),
        );
      });
    });

    group('sendPasswordResetEmail', () {
      test('calls FirebaseAuth sendPasswordResetEmail', () async {
        // Arrange
        const email = '<EMAIL>';
        when(mockFirebaseAuth.sendPasswordResetEmail(email: email))
            .thenAnswer((_) async {});

        // Act
        await authService.sendPasswordResetEmail(email);

        // Assert
        verify(mockFirebaseAuth.sendPasswordResetEmail(email: email)).called(1);
      });

      test('throws AuthException on FirebaseAuthException', () async {
        // Arrange
        const email = '<EMAIL>';
        when(mockFirebaseAuth.sendPasswordResetEmail(email: email))
            .thenThrow(FirebaseAuthException(code: 'user-not-found'));

        // Act & Assert
        expect(
          () => authService.sendPasswordResetEmail(email),
          throwsA(isA<AuthException>()),
        );
      });
    });
  });

  group('AuthException', () {
    test('creates exception with custom message', () {
      // Arrange
      const message = 'Custom error message';

      // Act
      final exception = AuthException(message);

      // Assert
      expect(exception.message, equals(message));
      expect(exception.code, isNull);
    });

    test('creates exception from FirebaseAuthException', () {
      // Arrange
      final firebaseException = FirebaseAuthException(code: 'weak-password');

      // Act
      final exception = AuthException.fromFirebaseAuthException(firebaseException);

      // Assert
      expect(exception.message, equals('The password provided is too weak.'));
      expect(exception.code, equals('weak-password'));
    });

    test('handles unknown error codes', () {
      // Arrange
      final firebaseException = FirebaseAuthException(code: 'unknown-error');

      // Act
      final exception = AuthException.fromFirebaseAuthException(firebaseException);

      // Assert
      expect(exception.message, equals('An authentication error occurred.'));
      expect(exception.code, equals('unknown-error'));
    });
  });
}
