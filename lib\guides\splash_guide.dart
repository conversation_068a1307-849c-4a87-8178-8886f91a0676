class SplashGuide {
  static String getWelcomeMessage() {
    final hour = DateTime.now().hour; // Get current hour (0-23)

    if (hour >= 5 && hour < 12) {
      return "Good morning! Welcome to Dream Land. We're excited to have you here.";
    } else if (hour >= 12 && hour < 17) {
      return "Good afternoon! Welcome to Dream Land. We hope you enjoy your journey.";
    } else {
      return "Good evening! Welcome to Dream Land. Let's define your path.";
    }
  }
}