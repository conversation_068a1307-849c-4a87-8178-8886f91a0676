// Mocks generated by Mocki<PERSON> 5.4.4 from annotations
// in choice_once_upon_a_time/test/features/story_library/data/enhanced_story_repository_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i4;
import 'dart:typed_data' as _i12;

import 'package:choice_once_upon_a_time/core/services/asset_story_service.dart'
    as _i5;
import 'package:choice_once_upon_a_time/core/services/firebase_storage_service.dart'
    as _i7;
import 'package:choice_once_upon_a_time/core/services/zip_extraction_service.dart'
    as _i9;
import 'package:choice_once_upon_a_time/core/storage/offline_storage_service_mobile.dart'
    as _i10;
import 'package:choice_once_upon_a_time/models/story_metadata_model.dart'
    as _i11;
import 'package:choice_once_upon_a_time/models/story_model.dart' as _i6;
import 'package:cloud_firestore/cloud_firestore.dart' as _i3;
import 'package:firebase_core/firebase_core.dart' as _i2;
import 'package:mockito/mockito.dart' as _i1;
import 'package:mockito/src/dummies.dart' as _i8;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeFirebaseApp_0 extends _i1.SmartFake implements _i2.FirebaseApp {
  _FakeFirebaseApp_0(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeSettings_1 extends _i1.SmartFake implements _i3.Settings {
  _FakeSettings_1(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeCollectionReference_2<T extends Object?> extends _i1.SmartFake
    implements _i3.CollectionReference<T> {
  _FakeCollectionReference_2(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeWriteBatch_3 extends _i1.SmartFake implements _i3.WriteBatch {
  _FakeWriteBatch_3(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeLoadBundleTask_4 extends _i1.SmartFake
    implements _i3.LoadBundleTask {
  _FakeLoadBundleTask_4(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeQuerySnapshot_5<T1 extends Object?> extends _i1.SmartFake
    implements _i3.QuerySnapshot<T1> {
  _FakeQuerySnapshot_5(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeQuery_6<T extends Object?> extends _i1.SmartFake
    implements _i3.Query<T> {
  _FakeQuery_6(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeDocumentReference_7<T extends Object?> extends _i1.SmartFake
    implements _i3.DocumentReference<T> {
  _FakeDocumentReference_7(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeFuture_8<T1> extends _i1.SmartFake implements _i4.Future<T1> {
  _FakeFuture_8(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

/// A class which mocks [AssetStoryService].
///
/// See the documentation for Mockito's code generation for more information.
class MockAssetStoryService extends _i1.Mock implements _i5.AssetStoryService {
  MockAssetStoryService() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i4.Future<List<String>> getAvailableStoryIds() => (super.noSuchMethod(
        Invocation.method(
          #getAvailableStoryIds,
          [],
        ),
        returnValue: _i4.Future<List<String>>.value(<String>[]),
      ) as _i4.Future<List<String>>);

  @override
  _i4.Future<bool> storyExistsInAssets(String? storyId) => (super.noSuchMethod(
        Invocation.method(
          #storyExistsInAssets,
          [storyId],
        ),
        returnValue: _i4.Future<bool>.value(false),
      ) as _i4.Future<bool>);

  @override
  _i4.Future<_i6.StoryModel?> loadStoryFromAssets(String? storyId) =>
      (super.noSuchMethod(
        Invocation.method(
          #loadStoryFromAssets,
          [storyId],
        ),
        returnValue: _i4.Future<_i6.StoryModel?>.value(),
      ) as _i4.Future<_i6.StoryModel?>);

  @override
  _i4.Future<Map<String, dynamic>> validateStoryAssets(String? storyId) =>
      (super.noSuchMethod(
        Invocation.method(
          #validateStoryAssets,
          [storyId],
        ),
        returnValue:
            _i4.Future<Map<String, dynamic>>.value(<String, dynamic>{}),
      ) as _i4.Future<Map<String, dynamic>>);

  @override
  void clearCache() => super.noSuchMethod(
        Invocation.method(
          #clearCache,
          [],
        ),
        returnValueForMissingStub: null,
      );

  @override
  Map<String, dynamic> getCacheStats() => (super.noSuchMethod(
        Invocation.method(
          #getCacheStats,
          [],
        ),
        returnValue: <String, dynamic>{},
      ) as Map<String, dynamic>);
}

/// A class which mocks [FirebaseStorageService].
///
/// See the documentation for Mockito's code generation for more information.
class MockFirebaseStorageService extends _i1.Mock
    implements _i7.FirebaseStorageService {
  MockFirebaseStorageService() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i4.Future<String> downloadStoryZip(
    String? storyId, {
    dynamic Function(double)? onProgress,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #downloadStoryZip,
          [storyId],
          {#onProgress: onProgress},
        ),
        returnValue: _i4.Future<String>.value(_i8.dummyValue<String>(
          this,
          Invocation.method(
            #downloadStoryZip,
            [storyId],
            {#onProgress: onProgress},
          ),
        )),
      ) as _i4.Future<String>);

  @override
  _i4.Future<Map<String, dynamic>?> downloadStoryMetadata(String? storyId) =>
      (super.noSuchMethod(
        Invocation.method(
          #downloadStoryMetadata,
          [storyId],
        ),
        returnValue: _i4.Future<Map<String, dynamic>?>.value(),
      ) as _i4.Future<Map<String, dynamic>?>);

  @override
  _i4.Future<bool> storyExists(String? storyId) => (super.noSuchMethod(
        Invocation.method(
          #storyExists,
          [storyId],
        ),
        returnValue: _i4.Future<bool>.value(false),
      ) as _i4.Future<bool>);

  @override
  _i4.Future<String?> getAssetDownloadUrl(
    String? storyId,
    String? assetPath,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #getAssetDownloadUrl,
          [
            storyId,
            assetPath,
          ],
        ),
        returnValue: _i4.Future<String?>.value(),
      ) as _i4.Future<String?>);

  @override
  _i4.Future<List<String>> listAvailableStories() => (super.noSuchMethod(
        Invocation.method(
          #listAvailableStories,
          [],
        ),
        returnValue: _i4.Future<List<String>>.value(<String>[]),
      ) as _i4.Future<List<String>>);
}

/// A class which mocks [ZipExtractionService].
///
/// See the documentation for Mockito's code generation for more information.
class MockZipExtractionService extends _i1.Mock
    implements _i9.ZipExtractionService {
  MockZipExtractionService() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i4.Future<String> extractStoryZip(
    String? zipFilePath,
    String? storyId, {
    dynamic Function(double)? onProgress,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #extractStoryZip,
          [
            zipFilePath,
            storyId,
          ],
          {#onProgress: onProgress},
        ),
        returnValue: _i4.Future<String>.value(_i8.dummyValue<String>(
          this,
          Invocation.method(
            #extractStoryZip,
            [
              zipFilePath,
              storyId,
            ],
            {#onProgress: onProgress},
          ),
        )),
      ) as _i4.Future<String>);

  @override
  _i4.Future<bool> validateStoryStructure(String? storyPath) =>
      (super.noSuchMethod(
        Invocation.method(
          #validateStoryStructure,
          [storyPath],
        ),
        returnValue: _i4.Future<bool>.value(false),
      ) as _i4.Future<bool>);

  @override
  _i4.Future<String> getStoryPath(String? storyId) => (super.noSuchMethod(
        Invocation.method(
          #getStoryPath,
          [storyId],
        ),
        returnValue: _i4.Future<String>.value(_i8.dummyValue<String>(
          this,
          Invocation.method(
            #getStoryPath,
            [storyId],
          ),
        )),
      ) as _i4.Future<String>);

  @override
  _i4.Future<bool> isStoryDownloaded(String? storyId) => (super.noSuchMethod(
        Invocation.method(
          #isStoryDownloaded,
          [storyId],
        ),
        returnValue: _i4.Future<bool>.value(false),
      ) as _i4.Future<bool>);

  @override
  _i4.Future<bool> deleteLocalStory(String? storyId) => (super.noSuchMethod(
        Invocation.method(
          #deleteLocalStory,
          [storyId],
        ),
        returnValue: _i4.Future<bool>.value(false),
      ) as _i4.Future<bool>);

  @override
  _i4.Future<int> getLocalStorySize(String? storyId) => (super.noSuchMethod(
        Invocation.method(
          #getLocalStorySize,
          [storyId],
        ),
        returnValue: _i4.Future<int>.value(0),
      ) as _i4.Future<int>);

  @override
  _i4.Future<List<String>> listLocalStories() => (super.noSuchMethod(
        Invocation.method(
          #listLocalStories,
          [],
        ),
        returnValue: _i4.Future<List<String>>.value(<String>[]),
      ) as _i4.Future<List<String>>);
}

/// A class which mocks [OfflineStorageService].
///
/// See the documentation for Mockito's code generation for more information.
class MockOfflineStorageService extends _i1.Mock
    implements _i10.OfflineStorageService {
  MockOfflineStorageService() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i4.Future<void> initDatabase() => (super.noSuchMethod(
        Invocation.method(
          #initDatabase,
          [],
        ),
        returnValue: _i4.Future<void>.value(),
        returnValueForMissingStub: _i4.Future<void>.value(),
      ) as _i4.Future<void>);

  @override
  _i4.Future<bool> isStoryDownloaded(
    String? storyId,
    String? version,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #isStoryDownloaded,
          [
            storyId,
            version,
          ],
        ),
        returnValue: _i4.Future<bool>.value(false),
      ) as _i4.Future<bool>);

  @override
  _i4.Future<_i6.StoryModel?> getLocalStory(
    String? storyId, {
    String? version = r'1.0.0',
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #getLocalStory,
          [storyId],
          {#version: version},
        ),
        returnValue: _i4.Future<_i6.StoryModel?>.value(),
      ) as _i4.Future<_i6.StoryModel?>);

  @override
  _i4.Future<bool> hasEnoughStorageSpace(int? sizeInBytes) =>
      (super.noSuchMethod(
        Invocation.method(
          #hasEnoughStorageSpace,
          [sizeInBytes],
        ),
        returnValue: _i4.Future<bool>.value(false),
      ) as _i4.Future<bool>);

  @override
  _i4.Future<bool> downloadStory(
    _i11.StoryMetadataModel? storyMeta, {
    dynamic Function(double)? onProgress,
    dynamic Function(String)? onStatusUpdate,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #downloadStory,
          [storyMeta],
          {
            #onProgress: onProgress,
            #onStatusUpdate: onStatusUpdate,
          },
        ),
        returnValue: _i4.Future<bool>.value(false),
      ) as _i4.Future<bool>);

  @override
  void cancelDownload() => super.noSuchMethod(
        Invocation.method(
          #cancelDownload,
          [],
        ),
        returnValueForMissingStub: null,
      );

  @override
  _i4.Future<void> close() => (super.noSuchMethod(
        Invocation.method(
          #close,
          [],
        ),
        returnValue: _i4.Future<void>.value(),
        returnValueForMissingStub: _i4.Future<void>.value(),
      ) as _i4.Future<void>);
}

/// A class which mocks [FirebaseFirestore].
///
/// See the documentation for Mockito's code generation for more information.
class MockFirebaseFirestore extends _i1.Mock implements _i3.FirebaseFirestore {
  MockFirebaseFirestore() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i2.FirebaseApp get app => (super.noSuchMethod(
        Invocation.getter(#app),
        returnValue: _FakeFirebaseApp_0(
          this,
          Invocation.getter(#app),
        ),
      ) as _i2.FirebaseApp);

  @override
  set app(_i2.FirebaseApp? _app) => super.noSuchMethod(
        Invocation.setter(
          #app,
          _app,
        ),
        returnValueForMissingStub: null,
      );

  @override
  String get databaseURL => (super.noSuchMethod(
        Invocation.getter(#databaseURL),
        returnValue: _i8.dummyValue<String>(
          this,
          Invocation.getter(#databaseURL),
        ),
      ) as String);

  @override
  set databaseURL(String? _databaseURL) => super.noSuchMethod(
        Invocation.setter(
          #databaseURL,
          _databaseURL,
        ),
        returnValueForMissingStub: null,
      );

  @override
  String get databaseId => (super.noSuchMethod(
        Invocation.getter(#databaseId),
        returnValue: _i8.dummyValue<String>(
          this,
          Invocation.getter(#databaseId),
        ),
      ) as String);

  @override
  set databaseId(String? _databaseId) => super.noSuchMethod(
        Invocation.setter(
          #databaseId,
          _databaseId,
        ),
        returnValueForMissingStub: null,
      );

  @override
  set settings(_i3.Settings? settings) => super.noSuchMethod(
        Invocation.setter(
          #settings,
          settings,
        ),
        returnValueForMissingStub: null,
      );

  @override
  _i3.Settings get settings => (super.noSuchMethod(
        Invocation.getter(#settings),
        returnValue: _FakeSettings_1(
          this,
          Invocation.getter(#settings),
        ),
      ) as _i3.Settings);

  @override
  Map<dynamic, dynamic> get pluginConstants => (super.noSuchMethod(
        Invocation.getter(#pluginConstants),
        returnValue: <dynamic, dynamic>{},
      ) as Map<dynamic, dynamic>);

  @override
  _i3.CollectionReference<Map<String, dynamic>> collection(
          String? collectionPath) =>
      (super.noSuchMethod(
        Invocation.method(
          #collection,
          [collectionPath],
        ),
        returnValue: _FakeCollectionReference_2<Map<String, dynamic>>(
          this,
          Invocation.method(
            #collection,
            [collectionPath],
          ),
        ),
      ) as _i3.CollectionReference<Map<String, dynamic>>);

  @override
  _i3.WriteBatch batch() => (super.noSuchMethod(
        Invocation.method(
          #batch,
          [],
        ),
        returnValue: _FakeWriteBatch_3(
          this,
          Invocation.method(
            #batch,
            [],
          ),
        ),
      ) as _i3.WriteBatch);

  @override
  _i4.Future<void> clearPersistence() => (super.noSuchMethod(
        Invocation.method(
          #clearPersistence,
          [],
        ),
        returnValue: _i4.Future<void>.value(),
        returnValueForMissingStub: _i4.Future<void>.value(),
      ) as _i4.Future<void>);

  @override
  _i4.Future<void> enablePersistence(
          [_i3.PersistenceSettings? persistenceSettings]) =>
      (super.noSuchMethod(
        Invocation.method(
          #enablePersistence,
          [persistenceSettings],
        ),
        returnValue: _i4.Future<void>.value(),
        returnValueForMissingStub: _i4.Future<void>.value(),
      ) as _i4.Future<void>);

  @override
  _i3.LoadBundleTask loadBundle(_i12.Uint8List? bundle) => (super.noSuchMethod(
        Invocation.method(
          #loadBundle,
          [bundle],
        ),
        returnValue: _FakeLoadBundleTask_4(
          this,
          Invocation.method(
            #loadBundle,
            [bundle],
          ),
        ),
      ) as _i3.LoadBundleTask);

  @override
  void useFirestoreEmulator(
    String? host,
    int? port, {
    bool? sslEnabled = false,
    bool? automaticHostMapping = true,
  }) =>
      super.noSuchMethod(
        Invocation.method(
          #useFirestoreEmulator,
          [
            host,
            port,
          ],
          {
            #sslEnabled: sslEnabled,
            #automaticHostMapping: automaticHostMapping,
          },
        ),
        returnValueForMissingStub: null,
      );

  @override
  _i4.Future<_i3.QuerySnapshot<T>> namedQueryWithConverterGet<T>(
    String? name, {
    _i3.GetOptions? options = const _i3.GetOptions(),
    required _i3.FromFirestore<T>? fromFirestore,
    required _i3.ToFirestore<T>? toFirestore,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #namedQueryWithConverterGet,
          [name],
          {
            #options: options,
            #fromFirestore: fromFirestore,
            #toFirestore: toFirestore,
          },
        ),
        returnValue:
            _i4.Future<_i3.QuerySnapshot<T>>.value(_FakeQuerySnapshot_5<T>(
          this,
          Invocation.method(
            #namedQueryWithConverterGet,
            [name],
            {
              #options: options,
              #fromFirestore: fromFirestore,
              #toFirestore: toFirestore,
            },
          ),
        )),
      ) as _i4.Future<_i3.QuerySnapshot<T>>);

  @override
  _i4.Future<_i3.QuerySnapshot<Map<String, dynamic>>> namedQueryGet(
    String? name, {
    _i3.GetOptions? options = const _i3.GetOptions(),
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #namedQueryGet,
          [name],
          {#options: options},
        ),
        returnValue: _i4.Future<_i3.QuerySnapshot<Map<String, dynamic>>>.value(
            _FakeQuerySnapshot_5<Map<String, dynamic>>(
          this,
          Invocation.method(
            #namedQueryGet,
            [name],
            {#options: options},
          ),
        )),
      ) as _i4.Future<_i3.QuerySnapshot<Map<String, dynamic>>>);

  @override
  _i3.Query<Map<String, dynamic>> collectionGroup(String? collectionPath) =>
      (super.noSuchMethod(
        Invocation.method(
          #collectionGroup,
          [collectionPath],
        ),
        returnValue: _FakeQuery_6<Map<String, dynamic>>(
          this,
          Invocation.method(
            #collectionGroup,
            [collectionPath],
          ),
        ),
      ) as _i3.Query<Map<String, dynamic>>);

  @override
  _i4.Future<void> disableNetwork() => (super.noSuchMethod(
        Invocation.method(
          #disableNetwork,
          [],
        ),
        returnValue: _i4.Future<void>.value(),
        returnValueForMissingStub: _i4.Future<void>.value(),
      ) as _i4.Future<void>);

  @override
  _i3.DocumentReference<Map<String, dynamic>> doc(String? documentPath) =>
      (super.noSuchMethod(
        Invocation.method(
          #doc,
          [documentPath],
        ),
        returnValue: _FakeDocumentReference_7<Map<String, dynamic>>(
          this,
          Invocation.method(
            #doc,
            [documentPath],
          ),
        ),
      ) as _i3.DocumentReference<Map<String, dynamic>>);

  @override
  _i4.Future<void> enableNetwork() => (super.noSuchMethod(
        Invocation.method(
          #enableNetwork,
          [],
        ),
        returnValue: _i4.Future<void>.value(),
        returnValueForMissingStub: _i4.Future<void>.value(),
      ) as _i4.Future<void>);

  @override
  _i4.Stream<void> snapshotsInSync() => (super.noSuchMethod(
        Invocation.method(
          #snapshotsInSync,
          [],
        ),
        returnValue: _i4.Stream<void>.empty(),
      ) as _i4.Stream<void>);

  @override
  _i4.Future<T> runTransaction<T>(
    _i3.TransactionHandler<T>? transactionHandler, {
    Duration? timeout = const Duration(seconds: 30),
    int? maxAttempts = 5,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #runTransaction,
          [transactionHandler],
          {
            #timeout: timeout,
            #maxAttempts: maxAttempts,
          },
        ),
        returnValue: _i8.ifNotNull(
              _i8.dummyValueOrNull<T>(
                this,
                Invocation.method(
                  #runTransaction,
                  [transactionHandler],
                  {
                    #timeout: timeout,
                    #maxAttempts: maxAttempts,
                  },
                ),
              ),
              (T v) => _i4.Future<T>.value(v),
            ) ??
            _FakeFuture_8<T>(
              this,
              Invocation.method(
                #runTransaction,
                [transactionHandler],
                {
                  #timeout: timeout,
                  #maxAttempts: maxAttempts,
                },
              ),
            ),
      ) as _i4.Future<T>);

  @override
  _i4.Future<void> terminate() => (super.noSuchMethod(
        Invocation.method(
          #terminate,
          [],
        ),
        returnValue: _i4.Future<void>.value(),
        returnValueForMissingStub: _i4.Future<void>.value(),
      ) as _i4.Future<void>);

  @override
  _i4.Future<void> waitForPendingWrites() => (super.noSuchMethod(
        Invocation.method(
          #waitForPendingWrites,
          [],
        ),
        returnValue: _i4.Future<void>.value(),
        returnValueForMissingStub: _i4.Future<void>.value(),
      ) as _i4.Future<void>);

  @override
  _i4.Future<void> setIndexConfiguration({
    required List<_i3.Index>? indexes,
    List<_i3.FieldOverrides>? fieldOverrides,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #setIndexConfiguration,
          [],
          {
            #indexes: indexes,
            #fieldOverrides: fieldOverrides,
          },
        ),
        returnValue: _i4.Future<void>.value(),
        returnValueForMissingStub: _i4.Future<void>.value(),
      ) as _i4.Future<void>);

  @override
  _i4.Future<void> setIndexConfigurationFromJSON(String? json) =>
      (super.noSuchMethod(
        Invocation.method(
          #setIndexConfigurationFromJSON,
          [json],
        ),
        returnValue: _i4.Future<void>.value(),
        returnValueForMissingStub: _i4.Future<void>.value(),
      ) as _i4.Future<void>);
}
