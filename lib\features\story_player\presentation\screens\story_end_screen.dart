import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:choice_once_upon_a_time/models/story_model.dart';
import 'package:choice_once_upon_a_time/app/providers/service_providers.dart';
import 'package:choice_once_upon_a_time/shared/widgets/primary_button_widget.dart';

/// Screen displayed when a story is completed
class StoryEndScreen extends ConsumerStatefulWidget {
  final StoryModel story;

  const StoryEndScreen({
    super.key,
    required this.story,
  });

  @override
  ConsumerState<StoryEndScreen> createState() => _StoryEndScreenState();
}

class _StoryEndScreenState extends ConsumerState<StoryEndScreen> {
  bool _isPlayingDiscussion = false;

  @override
  void initState() {
    super.initState();
    // Auto-play the end of story discussion if available
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _playEndOfStoryDiscussion();
    });
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Scaffold(
      appBar: AppBar(
        title: const Text('Story Complete'),
        automaticallyImplyLeading: false,
        actions: [
          IconButton(
            icon: const Icon(Icons.home),
            onPressed: () => context.go('/home'),
            tooltip: 'Return to Library',
          ),
        ],
      ),
      body: Padding(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // Celebration section
            Expanded(
              flex: 2,
              child: Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(16),
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      Colors.green[100]!,
                      Colors.blue[100]!,
                    ],
                  ),
                ),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.celebration,
                      size: 80,
                      color: Colors.green[600],
                    ),
                    const SizedBox(height: 16),
                    Text(
                      'Story Complete!',
                      style: theme.textTheme.headlineMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: Colors.green[700],
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'You finished "${widget.story.title}"',
                      style: theme.textTheme.bodyLarge?.copyWith(
                        color: Colors.grey[600],
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
            ),
            
            const SizedBox(height: 24),
            
            // Discussion section
            Expanded(
              flex: 1,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Let\'s Talk About It',
                    style: theme.textTheme.headlineSmall?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  
                  const SizedBox(height: 16),
                  
                  Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: Colors.blue[50],
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(color: Colors.blue[200]!),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Icon(
                              Icons.psychology,
                              color: Colors.blue[600],
                              size: 20,
                            ),
                            const SizedBox(width: 8),
                            Text(
                              'Moral Value: ${widget.story.targetMoralValue}',
                              style: TextStyle(
                                color: Colors.blue[700],
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 8),
                        Text(
                          _getDiscussionPrompt(),
                          style: TextStyle(
                            color: Colors.blue[700],
                          ),
                        ),
                      ],
                    ),
                  ),
                  
                  const SizedBox(height: 16),
                  
                  // Audio controls for discussion
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      ElevatedButton.icon(
                        onPressed: _isPlayingDiscussion ? _stopDiscussion : _playEndOfStoryDiscussion,
                        icon: Icon(_isPlayingDiscussion ? Icons.stop : Icons.play_arrow),
                        label: Text(_isPlayingDiscussion ? 'Stop Discussion' : 'Play Discussion'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.blue[100],
                          foregroundColor: Colors.blue[700],
                        ),
                      ),
                    ],
                  ),
                  
                  const Spacer(),
                  
                  // Action buttons
                  Row(
                    children: [
                      Expanded(
                        child: OutlinedButton.icon(
                          onPressed: () => _replayStory(context),
                          icon: const Icon(Icons.replay),
                          label: const Text('Play Again'),
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: PrimaryButtonWidget(
                          text: 'More Stories',
                          onPressed: () => context.go('/home'),
                          icon: const Icon(Icons.library_books),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  String _getDiscussionPrompt() {
    switch (widget.story.targetMoralValue.toLowerCase()) {
      case 'honesty':
        return 'What did you learn about telling the truth? Can you think of a time when being honest was important?';
      case 'kindness':
        return 'How did the characters show kindness? How can you be kind to others?';
      case 'courage':
        return 'What made the character brave? When might you need to be brave?';
      case 'friendship':
        return 'What makes a good friend? How did the characters help each other?';
      case 'responsibility':
        return 'How did the character take responsibility? What responsibilities do you have?';
      default:
        return 'What did you learn from this story? How can you use this lesson in your life?';
    }
  }

  Future<void> _playEndOfStoryDiscussion() async {
    final ttsService = ref.read(ttsServiceProvider);
    
    setState(() {
      _isPlayingDiscussion = true;
    });

    try {
      // Play the discussion prompt with a warm, encouraging tone
      await ttsService.speakText(
        _getDiscussionPrompt(),
        emotionCue: 'encouraging',
      );
    } catch (e) {
      debugPrint('Error playing end of story discussion: $e');
    } finally {
      if (mounted) {
        setState(() {
          _isPlayingDiscussion = false;
        });
      }
    }
  }

  void _stopDiscussion() {
    final ttsService = ref.read(ttsServiceProvider);
    ttsService.stop();
    setState(() {
      _isPlayingDiscussion = false;
    });
  }

  void _replayStory(BuildContext context) {
    // Navigate back to story intro to replay
    context.go('/story/${widget.story.id}');
  }
}
