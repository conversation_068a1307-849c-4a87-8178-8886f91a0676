import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:choice_once_upon_a_time/features/parent_zone/presentation/providers/narrator_creation_provider.dart';
import 'package:choice_once_upon_a_time/models/narrator_profile_model.dart';
import 'package:choice_once_upon_a_time/models/enhanced_story_model.dart' show VoiceModel;

void main() {
  group('NarratorCreationProvider', () {
    late ProviderContainer container;

    setUp(() async {
      SharedPreferences.setMockInitialValues({});
      container = ProviderContainer();
    });

    tearDown(() {
      container.dispose();
    });

    test('initial state has empty narrators list', () {
      final state = container.read(narratorCreationProvider);
      
      expect(state.narrators, isEmpty);
      expect(state.isLoading, isFalse);
      expect(state.error, isNull);
      expect(state.selectedNarrator, isNotNull); // Should have default narrator
    });

    test('save<PERSON><PERSON><PERSON> adds new narrator to list', () async {
      final notifier = container.read(narratorCreationProvider.notifier);
      
      final narrator = NarratorProfileModel(
        id: 'test-1',
        name: 'Test Narrator',
        description: 'A test narrator',
        voice: const VoiceModel(
          name: 'Test Voice',
          pitch: 1.0,
          rate: 1.0,
          volume: 1.0,
        ),
        category: NarratorCategory.custom,
        gender: NarratorGender.neutral,
        ageRange: NarratorAgeRange.adult,
        personalities: [NarratorPersonality.gentle],
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      await notifier.saveNarrator(narrator);

      final state = container.read(narratorCreationProvider);
      expect(state.narrators, hasLength(1));
      expect(state.narrators.first.name, equals('Test Narrator'));
    });

    test('saveNarrator updates existing narrator', () async {
      final notifier = container.read(narratorCreationProvider.notifier);
      
      final narrator = NarratorProfileModel(
        id: 'test-1',
        name: 'Test Narrator',
        description: 'A test narrator',
        voice: const VoiceModel(
          name: 'Test Voice',
          pitch: 1.0,
          rate: 1.0,
          volume: 1.0,
        ),
        category: NarratorCategory.custom,
        gender: NarratorGender.neutral,
        ageRange: NarratorAgeRange.adult,
        personalities: [NarratorPersonality.gentle],
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      // Save initial narrator
      await notifier.saveNarrator(narrator);

      // Update narrator
      final updatedNarrator = narrator.copyWith(
        name: 'Updated Narrator',
        description: 'An updated narrator',
      );
      await notifier.saveNarrator(updatedNarrator);

      final state = container.read(narratorCreationProvider);
      expect(state.narrators, hasLength(1));
      expect(state.narrators.first.name, equals('Updated Narrator'));
      expect(state.narrators.first.description, equals('An updated narrator'));
    });

    test('deleteNarrator removes narrator from list', () async {
      final notifier = container.read(narratorCreationProvider.notifier);
      
      final narrator = NarratorProfileModel(
        id: 'test-1',
        name: 'Test Narrator',
        description: 'A test narrator',
        voice: const VoiceModel(
          name: 'Test Voice',
          pitch: 1.0,
          rate: 1.0,
          volume: 1.0,
        ),
        category: NarratorCategory.custom,
        gender: NarratorGender.neutral,
        ageRange: NarratorAgeRange.adult,
        personalities: [NarratorPersonality.gentle],
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      // Save narrator
      await notifier.saveNarrator(narrator);
      expect(container.read(narratorCreationProvider).narrators, hasLength(1));

      // Delete narrator
      await notifier.deleteNarrator('test-1');
      
      final state = container.read(narratorCreationProvider);
      expect(state.narrators, isEmpty);
    });

    test('setSelectedNarrator updates selected narrator', () async {
      final notifier = container.read(narratorCreationProvider.notifier);
      
      final narrator = NarratorProfileModel(
        id: 'test-1',
        name: 'Test Narrator',
        description: 'A test narrator',
        voice: const VoiceModel(
          name: 'Test Voice',
          pitch: 1.0,
          rate: 1.0,
          volume: 1.0,
        ),
        category: NarratorCategory.custom,
        gender: NarratorGender.neutral,
        ageRange: NarratorAgeRange.adult,
        personalities: [NarratorPersonality.gentle],
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      // Save narrator
      await notifier.saveNarrator(narrator);

      // Select narrator
      await notifier.setSelectedNarrator('test-1');
      
      final state = container.read(narratorCreationProvider);
      expect(state.selectedNarrator?.id, equals('test-1'));
      expect(state.selectedNarrator?.name, equals('Test Narrator'));
    });

    test('loadNarrator returns correct narrator by ID', () async {
      final notifier = container.read(narratorCreationProvider.notifier);
      
      final narrator = NarratorProfileModel(
        id: 'test-1',
        name: 'Test Narrator',
        description: 'A test narrator',
        voice: const VoiceModel(
          name: 'Test Voice',
          pitch: 1.0,
          rate: 1.0,
          volume: 1.0,
        ),
        category: NarratorCategory.custom,
        gender: NarratorGender.neutral,
        ageRange: NarratorAgeRange.adult,
        personalities: [NarratorPersonality.gentle],
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      // Save narrator
      await notifier.saveNarrator(narrator);

      // Load narrator
      final loadedNarrator = await notifier.loadNarrator('test-1');
      
      expect(loadedNarrator, isNotNull);
      expect(loadedNarrator!.id, equals('test-1'));
      expect(loadedNarrator.name, equals('Test Narrator'));
    });

    test('loadNarrator returns null for non-existent ID', () async {
      final notifier = container.read(narratorCreationProvider.notifier);
      
      final loadedNarrator = await notifier.loadNarrator('non-existent');
      
      expect(loadedNarrator, isNull);
    });

    test('createNewNarrator returns narrator with default values', () {
      final notifier = container.read(narratorCreationProvider.notifier);
      
      final newNarrator = notifier.createNewNarrator();
      
      expect(newNarrator.id, isNotEmpty);
      expect(newNarrator.name, isEmpty);
      expect(newNarrator.description, isEmpty);
      expect(newNarrator.category, equals(NarratorCategory.custom));
      expect(newNarrator.gender, equals(NarratorGender.neutral));
      expect(newNarrator.ageRange, equals(NarratorAgeRange.adult));
      expect(newNarrator.personalities, isEmpty);
      expect(newNarrator.voice.pitch, equals(1.0));
      expect(newNarrator.voice.rate, equals(1.0));
      expect(newNarrator.voice.volume, equals(1.0));
    });

    test('selectedNarrator getter returns default when no selection', () {
      final notifier = container.read(narratorCreationProvider.notifier);
      
      final selectedNarrator = notifier.selectedNarrator;
      
      expect(selectedNarrator.id, equals('default'));
      expect(selectedNarrator.name, equals('Default Narrator'));
      expect(selectedNarrator.isDefault, isTrue);
    });

    test('availableCategories returns all narrator categories', () {
      final notifier = container.read(narratorCreationProvider.notifier);
      
      final categories = notifier.availableCategories;
      
      expect(categories, equals(NarratorCategory.values));
    });

    test('availableGenders returns all narrator genders', () {
      final notifier = container.read(narratorCreationProvider.notifier);
      
      final genders = notifier.availableGenders;
      
      expect(genders, equals(NarratorGender.values));
    });

    test('availableAgeRanges returns all age ranges', () {
      final notifier = container.read(narratorCreationProvider.notifier);
      
      final ageRanges = notifier.availableAgeRanges;
      
      expect(ageRanges, equals(NarratorAgeRange.values));
    });

    test('availablePersonalities returns all personalities', () {
      final notifier = container.read(narratorCreationProvider.notifier);
      
      final personalities = notifier.availablePersonalities;
      
      expect(personalities, equals(NarratorPersonality.values));
    });

    test('state updates correctly during save operation', () async {
      final notifier = container.read(narratorCreationProvider.notifier);
      
      final narrator = NarratorProfileModel(
        id: 'test-1',
        name: 'Test Narrator',
        description: 'A test narrator',
        voice: const VoiceModel(
          name: 'Test Voice',
          pitch: 1.0,
          rate: 1.0,
          volume: 1.0,
        ),
        category: NarratorCategory.custom,
        gender: NarratorGender.neutral,
        ageRange: NarratorAgeRange.adult,
        personalities: [NarratorPersonality.gentle],
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      // Start save operation
      final saveFuture = notifier.saveNarrator(narrator);
      
      // Check loading state
      expect(container.read(narratorCreationProvider).isLoading, isTrue);
      
      // Wait for completion
      await saveFuture;
      
      // Check final state
      final finalState = container.read(narratorCreationProvider);
      expect(finalState.isLoading, isFalse);
      expect(finalState.error, isNull);
      expect(finalState.narrators, hasLength(1));
    });

    test('deleting selected narrator switches to default', () async {
      final notifier = container.read(narratorCreationProvider.notifier);
      
      final narrator = NarratorProfileModel(
        id: 'test-1',
        name: 'Test Narrator',
        description: 'A test narrator',
        voice: const VoiceModel(
          name: 'Test Voice',
          pitch: 1.0,
          rate: 1.0,
          volume: 1.0,
        ),
        category: NarratorCategory.custom,
        gender: NarratorGender.neutral,
        ageRange: NarratorAgeRange.adult,
        personalities: [NarratorPersonality.gentle],
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      // Save and select narrator
      await notifier.saveNarrator(narrator);
      await notifier.setSelectedNarrator('test-1');
      
      expect(container.read(narratorCreationProvider).selectedNarrator?.id, equals('test-1'));

      // Delete selected narrator
      await notifier.deleteNarrator('test-1');
      
      final state = container.read(narratorCreationProvider);
      expect(state.selectedNarrator?.id, equals('default'));
      expect(state.selectedNarrator?.isDefault, isTrue);
    });
  });
}
