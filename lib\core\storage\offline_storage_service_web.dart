import 'package:choice_once_upon_a_time/models/downloaded_story_entry_web.dart';
import 'package:choice_once_upon_a_time/models/story_model.dart';

/// Web implementation of OfflineStorageService
/// Uses in-memory storage since Isar doesn't support web
class OfflineStorageService {
  static final Map<String, DownloadedStoryEntry> _inMemoryStorage = {};
  
  /// Initialize the storage service (no-op for web)
  Future<void> initDatabase() async {
    // No initialization needed for web
  }

  /// Check if a story is downloaded and available offline
  Future<bool> isStoryDownloaded(String storyId, String version) async {
    final entry = _inMemoryStorage[storyId];
    return entry != null && 
           entry.version == version && 
           entry.isFullyDownloaded;
  }

  /// Get a locally stored story (always returns null on web)
  Future<StoryModel?> getLocalStory(String storyId) async {
    // Web doesn't support offline storage, always return null
    return null;
  }

  /// Save a downloaded story entry (in-memory for web)
  Future<void> saveDownloadedStory(DownloadedStoryEntry entry) async {
    _inMemoryStorage[entry.storyId] = entry;
  }

  /// Update the last accessed time for a story
  Future<void> updateLastAccessed(String storyId) async {
    final entry = _inMemoryStorage[storyId];
    if (entry != null) {
      final updated = entry.copyWith(lastAccessedAt: DateTime.now());
      _inMemoryStorage[storyId] = updated;
    }
  }

  /// Get all downloaded stories
  Future<List<DownloadedStoryEntry>> getAllDownloadedStories() async {
    return _inMemoryStorage.values.toList();
  }

  /// Remove a downloaded story
  Future<void> removeDownloadedStory(String storyId) async {
    _inMemoryStorage.remove(storyId);
  }

  /// Get total storage used by downloaded stories in MB
  Future<int> getTotalStorageUsedMb() async {
    final entries = _inMemoryStorage.values.toList();
    return entries.fold<int>(0, (sum, entry) => sum + entry.totalSizeMb);
  }

  /// Clean up old downloaded stories
  Future<void> cleanupOldStories() async {
    final thirtyDaysAgo = DateTime.now().subtract(const Duration(days: 30));
    
    final keysToRemove = <String>[];
    for (final entry in _inMemoryStorage.entries) {
      if (entry.value.lastAccessedAt.isBefore(thirtyDaysAgo)) {
        keysToRemove.add(entry.key);
      }
    }
    
    for (final key in keysToRemove) {
      _inMemoryStorage.remove(key);
    }
  }

  /// Close the storage (no-op for web)
  Future<void> close() async {
    // No cleanup needed for web
  }
}
