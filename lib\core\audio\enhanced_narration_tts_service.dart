import 'dart:async';
import 'dart:math';
import 'package:flutter_tts/flutter_tts.dart';
import 'package:choice_once_upon_a_time/core/audio/narration_tts_service_interface.dart';
import 'package:choice_once_upon_a_time/models/tts_models.dart';
import 'package:choice_once_upon_a_time/core/utils/app_logger.dart';

/// Enhanced TTS service implementation with advanced narration features
class EnhancedNarrationTTSService implements IEnhancedNarrationTTSService {
  static final EnhancedNarrationTTSService _instance = EnhancedNarrationTTSService._internal();
  factory EnhancedNarrationTTSService() => _instance;
  EnhancedNarrationTTSService._internal();

  FlutterTts? _flutterTts;
  TTSState _currentState = TTSState.initial;
  TTSServiceConfig _config = const TTSServiceConfig();
  
  // Stream controllers
  final StreamController<TTSEvent> _eventController = StreamController<TTSEvent>.broadcast();
  final StreamController<TTSState> _stateController = StreamController<TTSState>.broadcast();
  final StreamController<TTSEvent> _wordBoundaryController = StreamController<TTSEvent>.broadcast();
  final StreamController<TTSEvent> _sentenceBoundaryController = StreamController<TTSEvent>.broadcast();
  
  // Character voice configurations
  final Map<String, TTSParameters> _characterVoices = {};
  
  // Preloaded text for faster speech initiation
  String? _preloadedText;
  
  // Current speech tracking
  String? _currentText;
  int _currentWordIndex = 0;
  Timer? _wordTrackingTimer;
  List<String> _words = [];

  @override
  Stream<TTSEvent> get eventStream => _eventController.stream;

  @override
  Stream<TTSState> get stateStream => _stateController.stream;

  @override
  Stream<TTSEvent> get wordBoundaryStream => _wordBoundaryController.stream;

  @override
  Stream<TTSEvent> get sentenceBoundaryStream => _sentenceBoundaryController.stream;

  @override
  TTSState get currentState => _currentState;

  @override
  bool get isAvailable => _currentState.isAvailable;

  @override
  bool get isSpeaking => _currentState.status == TTSStatus.speaking;

  @override
  bool get isPaused => _currentState.status == TTSStatus.paused;

  /// Initialize the TTS service with configuration
  Future<bool> initializeWithConfig(TTSServiceConfig config) async {
    _config = config;
    return await initialize();
  }

  @override
  Future<bool> initialize() async {
    try {
      _updateState(_currentState.copyWith(status: TTSStatus.initializing));
      
      _flutterTts = FlutterTts();
      
      // Set up event handlers
      await _setupEventHandlers();
      
      // Set default parameters
      await setParameters(_config.defaultParameters);
      
      // Set default language
      await setLanguage(_config.defaultLanguage);
      
      // Get available voices
      final voices = await getAvailableVoices();
      
      _updateState(_currentState.copyWith(
        status: TTSStatus.ready,
        isAvailable: true,
        availableVoices: voices,
      ));
      
      AppLogger.info('[NarrationTTS] Service initialized successfully');
      return true;
      
    } catch (e) {
      final errorMsg = 'Failed to initialize TTS service: $e';
      AppLogger.error('[NarrationTTS] $errorMsg', e);
      _updateState(TTSState.createError(errorMsg));
      return false;
    }
  }

  /// Set up event handlers for flutter_tts
  Future<void> _setupEventHandlers() async {
    if (_flutterTts == null) return;

    _flutterTts!.setStartHandler(() {
      AppLogger.debug('[NarrationTTS] Speech started');
      _updateState(_currentState.copyWith(status: TTSStatus.speaking));
      _eventController.add(TTSEvent.started(_currentText ?? ''));
      
      // Start word tracking if enabled
      if (_config.enableWordTracking && _currentText != null) {
        _startWordTracking();
      }
    });

    _flutterTts!.setCompletionHandler(() {
      AppLogger.debug('[NarrationTTS] Speech completed');
      _updateState(_currentState.copyWith(status: TTSStatus.ready));
      _eventController.add(TTSEvent.completed());
      _stopWordTracking();
      _currentText = null;
      _currentWordIndex = 0;
    });

    _flutterTts!.setErrorHandler((msg) {
      final errorMsg = 'TTS Error: $msg';
      AppLogger.error('[NarrationTTS] $errorMsg');
      _updateState(_currentState.copyWith(
        status: TTSStatus.error,
        error: errorMsg,
      ));
      _eventController.add(TTSEvent.error(errorMsg));
      _stopWordTracking();
    });

    _flutterTts!.setPauseHandler(() {
      AppLogger.debug('[NarrationTTS] Speech paused');
      _updateState(_currentState.copyWith(status: TTSStatus.paused));
      _eventController.add(TTSEvent.paused());
      _stopWordTracking();
    });

    _flutterTts!.setContinueHandler(() {
      AppLogger.debug('[NarrationTTS] Speech resumed');
      _updateState(_currentState.copyWith(status: TTSStatus.speaking));
      _eventController.add(TTSEvent.resumed());
      
      // Resume word tracking if enabled
      if (_config.enableWordTracking && _currentText != null) {
        _startWordTracking();
      }
    });
  }

  @override
  Future<bool> speak(String text, {TTSParameters? parameters}) async {
    if (_flutterTts == null || !isAvailable) {
      AppLogger.warning('[NarrationTTS] Service not available for speech');
      return false;
    }

    try {
      // Set parameters if provided
      if (parameters != null) {
        await setParameters(parameters);
      }

      _currentText = text;
      _words = _splitIntoWords(text);
      _currentWordIndex = 0;

      await _flutterTts!.speak(text);
      AppLogger.debug('[NarrationTTS] Started speaking: ${text.substring(0, min(50, text.length))}...');
      return true;
      
    } catch (e) {
      AppLogger.error('[NarrationTTS] Error speaking text', e);
      return false;
    }
  }

  @override
  Future<bool> speakWithWordTracking(String text, {TTSParameters? parameters}) async {
    // Word tracking is enabled by default in this implementation
    return await speak(text, parameters: parameters);
  }

  @override
  Future<bool> speakWithSentenceTracking(String text, {TTSParameters? parameters}) async {
    // Enable sentence tracking for this speech
    final originalConfig = _config;
    _config = _config.copyWith(enableSentenceTracking: true);
    
    final result = await speak(text, parameters: parameters);
    
    // Restore original config
    _config = originalConfig;
    return result;
  }

  @override
  Future<bool> speakWithFullTracking(String text, {TTSParameters? parameters}) async {
    // Enable both word and sentence tracking
    final originalConfig = _config;
    _config = _config.copyWith(
      enableWordTracking: true,
      enableSentenceTracking: true,
    );
    
    final result = await speak(text, parameters: parameters);
    
    // Restore original config
    _config = originalConfig;
    return result;
  }

  @override
  Future<bool> speakWithEmotion(String text, String emotionCue) async {
    final emotionParams = getEmotionParameters(emotionCue);
    return await speak(text, parameters: emotionParams);
  }

  @override
  Future<bool> speakAsCharacter(String text, String characterId) async {
    final characterParams = _characterVoices[characterId];
    if (characterParams == null) {
      AppLogger.warning('[NarrationTTS] No voice configured for character: $characterId');
      return await speak(text);
    }
    
    return await speak(text, parameters: characterParams);
  }

  @override
  Future<bool> pause() async {
    if (_flutterTts == null || !isSpeaking) return false;
    
    try {
      await _flutterTts!.pause();
      return true;
    } catch (e) {
      AppLogger.error('[NarrationTTS] Error pausing speech', e);
      return false;
    }
  }

  @override
  Future<bool> resume() async {
    if (_flutterTts == null || !isPaused) return false;
    
    try {
      // Note: flutter_tts doesn't have a direct resume method
      // We need to continue from where we left off
      await _flutterTts!.speak('');
      return true;
    } catch (e) {
      AppLogger.error('[NarrationTTS] Error resuming speech', e);
      return false;
    }
  }

  @override
  Future<bool> stop() async {
    if (_flutterTts == null) return false;
    
    try {
      await _flutterTts!.stop();
      _stopWordTracking();
      _currentText = null;
      _currentWordIndex = 0;
      return true;
    } catch (e) {
      AppLogger.error('[NarrationTTS] Error stopping speech', e);
      return false;
    }
  }

  @override
  Future<void> setParameters(TTSParameters parameters) async {
    if (_flutterTts == null) return;

    try {
      await _flutterTts!.setSpeechRate(parameters.rate);
      await _flutterTts!.setPitch(parameters.pitch);
      await _flutterTts!.setVolume(parameters.volume);
      
      _updateState(_currentState.copyWith(parameters: parameters));
      AppLogger.debug('[NarrationTTS] Parameters updated: rate=${parameters.rate}, pitch=${parameters.pitch}, volume=${parameters.volume}');
    } catch (e) {
      AppLogger.error('[NarrationTTS] Error setting parameters', e);
    }
  }

  @override
  Future<List<TTSVoice>> getAvailableVoices() async {
    if (_flutterTts == null) return [];

    try {
      final voices = await _flutterTts!.getVoices;
      return voices.map<TTSVoice>((voice) {
        return TTSVoice(
          id: voice['name'] ?? '',
          name: voice['name'] ?? '',
          language: voice['locale'] ?? '',
          gender: voice['gender'],
        );
      }).toList();
    } catch (e) {
      AppLogger.error('[NarrationTTS] Error getting voices', e);
      return [];
    }
  }

  @override
  Future<bool> setVoice(TTSVoice voice) async {
    if (_flutterTts == null) return false;

    try {
      await _flutterTts!.setVoice({'name': voice.name, 'locale': voice.language});
      
      // Update available voices to mark this one as selected
      final updatedVoices = _currentState.availableVoices.map((v) {
        return v.copyWith(isSelected: v.id == voice.id);
      }).toList();
      
      _updateState(_currentState.copyWith(
        selectedVoice: voice,
        availableVoices: updatedVoices,
      ));
      
      AppLogger.debug('[NarrationTTS] Voice set to: ${voice.name}');
      return true;
    } catch (e) {
      AppLogger.error('[NarrationTTS] Error setting voice', e);
      return false;
    }
  }

  @override
  Future<bool> setLanguage(String languageCode) async {
    if (_flutterTts == null) return false;

    try {
      await _flutterTts!.setLanguage(languageCode);

      final updatedParams = _currentState.parameters.copyWith(language: languageCode);
      _updateState(_currentState.copyWith(parameters: updatedParams));

      AppLogger.debug('[NarrationTTS] Language set to: $languageCode');
      return true;
    } catch (e) {
      AppLogger.error('[NarrationTTS] Error setting language', e);
      return false;
    }
  }

  @override
  Future<bool> isLanguageSupported(String languageCode) async {
    final voices = await getAvailableVoices();
    return voices.any((voice) => voice.language.startsWith(languageCode));
  }

  @override
  Future<int?> estimateSpeechDuration(String text) async {
    // Rough estimation: average speaking rate is about 150 words per minute
    // This can be refined based on current speech rate settings
    final wordCount = _splitIntoWords(text).length;
    const baseWordsPerMinute = 150;
    final adjustedWordsPerMinute = baseWordsPerMinute * _currentState.parameters.rate;
    final durationMinutes = wordCount / adjustedWordsPerMinute;
    return (durationMinutes * 60 * 1000).round(); // Convert to milliseconds
  }

  @override
  Future<void> setEmotionParameters(String emotionCue) async {
    final emotionParams = getEmotionParameters(emotionCue);
    await setParameters(emotionParams);
  }

  @override
  TTSParameters getEmotionParameters(String emotionCue) {
    return EmotionVoiceMapper.getParametersForEmotion(emotionCue);
  }

  @override
  Future<bool> preloadText(String text) async {
    _preloadedText = text;
    AppLogger.debug('[NarrationTTS] Text preloaded for faster speech initiation');
    return true;
  }

  @override
  Future<void> clearPreloadedText() async {
    _preloadedText = null;
    AppLogger.debug('[NarrationTTS] Preloaded text cleared');
  }

  @override
  Future<void> setCharacterVoice(String characterId, TTSParameters parameters) async {
    _characterVoices[characterId] = parameters;
    AppLogger.debug('[NarrationTTS] Voice configured for character: $characterId');
  }

  @override
  Future<void> setSSMLEnabled(bool enabled) async {
    // flutter_tts has limited SSML support, this is a placeholder for future enhancement
    AppLogger.debug('[NarrationTTS] SSML enabled: $enabled');
  }

  @override
  Future<bool> speakSSML(String ssmlText) async {
    // For now, strip SSML tags and speak as plain text
    final plainText = _stripSSMLTags(ssmlText);
    return await speak(plainText);
  }

  @override
  Future<void> dispose() async {
    _stopWordTracking();
    await _flutterTts?.stop();
    _flutterTts = null;

    await _eventController.close();
    await _stateController.close();
    await _wordBoundaryController.close();
    await _sentenceBoundaryController.close();

    AppLogger.debug('[NarrationTTS] Service disposed');
  }

  /// Update the current state and notify listeners
  void _updateState(TTSState newState) {
    _currentState = newState;
    _stateController.add(newState);
  }

  /// Start word tracking for real-time highlighting
  void _startWordTracking() async {
    if (_currentText == null || _words.isEmpty) return;

    _stopWordTracking(); // Stop any existing tracking

    // Estimate timing for each word based on speech rate
    final estimatedDuration = await estimateSpeechDuration(_currentText!);
    if (estimatedDuration == null) return;

    final wordDuration = estimatedDuration ~/ _words.length;

    _wordTrackingTimer = Timer.periodic(Duration(milliseconds: wordDuration), (timer) {
      if (_currentWordIndex < _words.length) {
        final word = _words[_currentWordIndex];
        final charIndex = _getCharIndexForWord(_currentWordIndex);

        final event = TTSEvent.wordBoundary(
          wordIndex: _currentWordIndex,
          charIndex: charIndex,
          length: word.length,
        );

        _wordBoundaryController.add(event);
        _eventController.add(event);

        AppLogger.debug('[NarrationTTS] Word boundary: $_currentWordIndex - $word');
        _currentWordIndex++;
      } else {
        _stopWordTracking();
      }
    });
  }

  /// Stop word tracking
  void _stopWordTracking() {
    _wordTrackingTimer?.cancel();
    _wordTrackingTimer = null;
  }

  /// Split text into words for tracking
  List<String> _splitIntoWords(String text) {
    return text.split(RegExp(r'\s+'))
        .where((word) => word.isNotEmpty)
        .toList();
  }

  /// Get character index for a specific word
  int _getCharIndexForWord(int wordIndex) {
    if (_currentText == null || wordIndex >= _words.length) return 0;

    int charIndex = 0;
    for (int i = 0; i < wordIndex; i++) {
      charIndex += _words[i].length + 1; // +1 for space
    }
    return charIndex;
  }

  /// Strip SSML tags from text
  String _stripSSMLTags(String ssmlText) {
    return ssmlText.replaceAll(RegExp(r'<[^>]*>'), '');
  }
}

/// Extension to add copyWith method to TTSServiceConfig
extension TTSServiceConfigExtension on TTSServiceConfig {
  TTSServiceConfig copyWith({
    bool? enableWordTracking,
    bool? enableSentenceTracking,
    bool? enableEmotionMapping,
    bool? enableSSML,
    String? defaultLanguage,
    TTSParameters? defaultParameters,
    int? maxRetryAttempts,
    int? operationTimeoutMs,
  }) {
    return TTSServiceConfig(
      enableWordTracking: enableWordTracking ?? this.enableWordTracking,
      enableSentenceTracking: enableSentenceTracking ?? this.enableSentenceTracking,
      enableEmotionMapping: enableEmotionMapping ?? this.enableEmotionMapping,
      enableSSML: enableSSML ?? this.enableSSML,
      defaultLanguage: defaultLanguage ?? this.defaultLanguage,
      defaultParameters: defaultParameters ?? this.defaultParameters,
      maxRetryAttempts: maxRetryAttempts ?? this.maxRetryAttempts,
      operationTimeoutMs: operationTimeoutMs ?? this.operationTimeoutMs,
    );
  }
}
