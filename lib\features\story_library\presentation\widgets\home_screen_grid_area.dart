import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

// Import all the grid section widgets that will be part of this area.
// These will be created in the next steps.
import 'package:choice_once_upon_a_time/features/story_library/presentation/widgets/story_library_grid_section.dart';
import 'package:choice_once_upon_a_time/features/story_library/presentation/widgets/continue_reading_grid_section.dart';
import 'package:choice_once_upon_a_time/features/story_library/presentation/widgets/featured_stories_grid_section.dart';
import 'package:choice_once_upon_a_time/features/story_library/presentation/widgets/ai_stories_grid_section.dart';
import 'package:choice_once_upon_a_time/features/parent_zone/presentation/widgets/parent_zone_grid_section.dart';
import 'package:choice_once_upon_a_time/features/rewards/presentation/widgets/rewards_grid_section.dart';

/// A widget that displays a scrollable grid of different content sections on the home screen.
///
/// This widget is responsible for arranging the various interactive sections
/// in a responsive grid layout.
class HomeScreenGridArea extends ConsumerWidget {
  const HomeScreenGridArea({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // For now, we use a placeholder list. In a real implementation, this list
    // would be built dynamically based on user state (e.g., show "Continue Reading"
    // only if there's a story in progress).
    final List<Widget> gridSections = [
      const StoryLibraryGridSection(),
      const ContinueReadingGridSection(), // This would be conditionally included
      const FeaturedStoriesGridSection(),
      const RewardsGridSection(),
      const AIStoriesGridSection(),
      const ParentZoneGridSection(),
    ];

    // Use LayoutBuilder for responsive design
    return LayoutBuilder(
      builder: (context, constraints) {
        // Determine number of columns based on screen width
        int crossAxisCount;
        double childAspectRatio;

        if (constraints.maxWidth > 1200) {
          crossAxisCount = 4;
          childAspectRatio = 1.0;
        } else if (constraints.maxWidth > 800) {
          crossAxisCount = 3;
          childAspectRatio = 1.0;
        } else if (constraints.maxWidth > 500) {
          crossAxisCount = 2;
          childAspectRatio = 1.0;
        } else {
          crossAxisCount = 1;
          childAspectRatio = 1.2;
        }

        return GridView.builder(
          padding: EdgeInsets.symmetric(
            horizontal: constraints.maxWidth * 0.04, // 4% of screen width
            vertical: 16.0,
          ),
          gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: crossAxisCount,
            crossAxisSpacing: constraints.maxWidth * 0.04, // Responsive spacing
            mainAxisSpacing: 16.0,
            childAspectRatio: childAspectRatio,
          ),
          itemCount: gridSections.length,
          itemBuilder: (context, index) {
            return gridSections[index];
          },
          shrinkWrap: true, // Important when nesting a scrollable view inside another.
          physics: const NeverScrollableScrollPhysics(), // The parent will handle scrolling.
        );
      },
    );
  }
}
