import 'package:flutter/material.dart';
import 'package:choice_once_upon_a_time/core/services/story_settings_service.dart';

/// Widget for controlling scene progression in story narration
class SceneProgressionControls extends StatefulWidget {
  final VoidCallback? onContinue;
  final VoidCallback? onReplay;
  final bool isNarrationComplete;
  final bool isAutoMode;
  final StorySettingsService settingsService;

  const SceneProgressionControls({
    super.key,
    this.onContinue,
    this.onReplay,
    required this.isNarrationComplete,
    required this.isAutoMode,
    required this.settingsService,
  });

  @override
  State<SceneProgressionControls> createState() => _SceneProgressionControlsState();
}

class _SceneProgressionControlsState extends State<SceneProgressionControls>
    with TickerProviderStateMixin {
  late final AnimationController _fadeController;
  late final AnimationController _pulseController;
  late final Animation<double> _fadeAnimation;
  late final Animation<double> _pulseAnimation;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
  }

  void _initializeAnimations() {
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );

    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _fadeController,
      curve: Curves.easeInOut,
    ));

    _pulseAnimation = Tween<double>(
      begin: 1.0,
      end: 1.1,
    ).animate(CurvedAnimation(
      parent: _pulseController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void didUpdateWidget(SceneProgressionControls oldWidget) {
    super.didUpdateWidget(oldWidget);
    
    if (widget.isNarrationComplete && !oldWidget.isNarrationComplete) {
      _showControls();
    } else if (!widget.isNarrationComplete && oldWidget.isNarrationComplete) {
      _hideControls();
    }
  }

  void _showControls() {
    _fadeController.forward();
    if (!widget.isAutoMode) {
      _pulseController.repeat(reverse: true);
    }
  }

  void _hideControls() {
    _fadeController.reverse();
    _pulseController.stop();
    _pulseController.reset();
  }

  @override
  void dispose() {
    _fadeController.dispose();
    _pulseController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final screenSize = MediaQuery.of(context).size;
    final isSmallScreen = screenSize.width < 600;

    if (!widget.isNarrationComplete) {
      return const SizedBox.shrink();
    }

    return AnimatedBuilder(
      animation: _fadeAnimation,
      builder: (context, child) {
        return Opacity(
          opacity: _fadeAnimation.value,
          child: Container(
            margin: EdgeInsets.symmetric(
              horizontal: isSmallScreen ? 16.0 : 24.0,
              vertical: 16.0,
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Auto mode indicator
                if (widget.isAutoMode) ...[
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 16.0,
                      vertical: 8.0,
                    ),
                    decoration: BoxDecoration(
                      color: theme.colorScheme.primaryContainer,
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          Icons.auto_mode,
                          size: 16,
                          color: theme.colorScheme.onPrimaryContainer,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          'Auto Mode - Next scene in 3 seconds',
                          style: theme.textTheme.bodySmall?.copyWith(
                            color: theme.colorScheme.onPrimaryContainer,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(height: 16),
                ],

                // Manual mode controls
                if (!widget.isAutoMode) ...[
                  AnimatedBuilder(
                    animation: _pulseAnimation,
                    builder: (context, child) {
                      return Transform.scale(
                        scale: _pulseAnimation.value,
                        child: ElevatedButton.icon(
                          onPressed: widget.onContinue,
                          icon: const Icon(Icons.arrow_forward),
                          label: const Text('Continue'),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: theme.colorScheme.primary,
                            foregroundColor: theme.colorScheme.onPrimary,
                            padding: EdgeInsets.symmetric(
                              horizontal: isSmallScreen ? 24.0 : 32.0,
                              vertical: isSmallScreen ? 12.0 : 16.0,
                            ),
                            textStyle: TextStyle(
                              fontSize: isSmallScreen ? 16.0 : 18.0,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ),
                      );
                    },
                  ),
                  const SizedBox(height: 12),
                ],

                // Replay button (available in both modes)
                TextButton.icon(
                  onPressed: widget.onReplay,
                  icon: const Icon(Icons.replay),
                  label: const Text('Replay Scene'),
                  style: TextButton.styleFrom(
                    foregroundColor: theme.colorScheme.onSurfaceVariant,
                    padding: EdgeInsets.symmetric(
                      horizontal: isSmallScreen ? 16.0 : 20.0,
                      vertical: isSmallScreen ? 8.0 : 12.0,
                    ),
                  ),
                ),

                // Mode toggle button
                const SizedBox(height: 8),
                TextButton.icon(
                  onPressed: () => _toggleProgressionMode(),
                  icon: Icon(widget.isAutoMode ? Icons.touch_app : Icons.auto_mode),
                  label: Text(
                    widget.isAutoMode ? 'Switch to Manual' : 'Switch to Auto',
                  ),
                  style: TextButton.styleFrom(
                    foregroundColor: theme.colorScheme.secondary,
                    textStyle: const TextStyle(fontSize: 14),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  void _toggleProgressionMode() {
    final newMode = !widget.isAutoMode;
    widget.settingsService.setAutoSceneProgression(newMode);
    
    if (newMode) {
      _pulseController.stop();
      _pulseController.reset();
    } else {
      _pulseController.repeat(reverse: true);
    }
  }
}
