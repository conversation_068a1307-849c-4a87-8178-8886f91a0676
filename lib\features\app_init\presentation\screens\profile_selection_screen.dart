import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:choice_once_upon_a_time/features/parent_zone/data/user_profile_service.dart';
import 'package:choice_once_upon_a_time/features/parent_zone/presentation/widgets/create_profile_dialog.dart';
import 'package:choice_once_upon_a_time/shared_widgets/loading_indicator_widget.dart';
import 'package:choice_once_upon_a_time/core/utils/app_logger.dart';

/// Screen for selecting or creating child profiles after login
class ProfileSelectionScreen extends ConsumerStatefulWidget {
  const ProfileSelectionScreen({super.key});

  @override
  ConsumerState<ProfileSelectionScreen> createState() => _ProfileSelectionScreenState();
}

class _ProfileSelectionScreenState extends ConsumerState<ProfileSelectionScreen> {
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    AppLogger.debug('[COMPONENT_LOAD] Full path: lib/features/app_init/presentation/screens/profile_selection_screen.dart - ProfileSelectionScreen');

    // Check for auto-selection after the widget is built
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _checkAutoSelection();
    });
  }

  /// Check if we should auto-select a profile
  void _checkAutoSelection() {
    final profilesAsync = ref.read(userProfilesProvider);
    profilesAsync.whenData((profiles) {
      if (profiles.length == 1 && !_isLoading) {
        // Auto-select the only profile
        AppLogger.info('[PROFILE_SELECTION] Auto-selecting single profile: ${profiles.first.name}');
        _selectProfile(profiles.first);
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final screenSize = MediaQuery.of(context).size;
    final profilesAsync = ref.watch(userProfilesProvider);

    return Scaffold(
      body: SafeArea(
        child: Container(
          width: double.infinity,
          height: double.infinity,
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [
                theme.colorScheme.primary.withValues(alpha: 0.1),
                theme.colorScheme.surface,
              ],
            ),
          ),
          child: profilesAsync.when(
            data: (profiles) => _buildContent(context, theme, screenSize, profiles),
            loading: () => const Center(child: LoadingIndicatorWidget()),
            error: (error, stack) => _buildErrorState(theme, error),
          ),
        ),
      ),
    );
  }

  Widget _buildContent(BuildContext context, ThemeData theme, Size screenSize, List<UserProfile> profiles) {
    if (profiles.isEmpty) {
      return _buildCreateFirstProfile(theme, screenSize);
    } else {
      return _buildSelectProfile(theme, screenSize, profiles);
    }
  }

  Widget _buildCreateFirstProfile(ThemeData theme, Size screenSize) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(24),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          SizedBox(height: screenSize.height * 0.1),
          
          // Welcome illustration
          Container(
            width: 200,
            height: 200,
            decoration: BoxDecoration(
              color: theme.colorScheme.primary.withValues(alpha: 0.1),
              shape: BoxShape.circle,
            ),
            child: Icon(
              Icons.child_care,
              size: 100,
              color: theme.colorScheme.primary,
            ),
          ),
          
          const SizedBox(height: 32),
          
          // Welcome text
          Text(
            'Welcome to Choice!',
            style: theme.textTheme.headlineMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: theme.colorScheme.primary,
            ),
            textAlign: TextAlign.center,
          ),
          
          const SizedBox(height: 16),
          
          Text(
            'Let\'s create your child\'s first profile to get started with personalized storytelling.',
            style: theme.textTheme.bodyLarge?.copyWith(
              color: theme.colorScheme.onSurface.withValues(alpha: 0.8),
            ),
            textAlign: TextAlign.center,
          ),
          
          const SizedBox(height: 48),
          
          // Create profile button
          SizedBox(
            width: double.infinity,
            child: ElevatedButton.icon(
              onPressed: _isLoading ? null : () => _showCreateProfileDialog(),
              icon: const Icon(Icons.add_circle),
              label: const Text('Create Child Profile'),
              style: ElevatedButton.styleFrom(
                backgroundColor: theme.colorScheme.primary,
                foregroundColor: theme.colorScheme.onPrimary,
                padding: const EdgeInsets.symmetric(vertical: 16),
                textStyle: const TextStyle(fontSize: 18, fontWeight: FontWeight.w600),
              ),
            ),
          ),
          
          const SizedBox(height: 24),
          
          // Skip for now button
          TextButton(
            onPressed: _isLoading ? null : () => _continueWithoutProfile(),
            child: Text(
              'Continue without profile',
              style: TextStyle(
                color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSelectProfile(ThemeData theme, Size screenSize, List<UserProfile> profiles) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(24),
      child: Column(
        children: [
          SizedBox(height: screenSize.height * 0.05),
          
          // Header
          Text(
            'Choose Your Profile',
            style: theme.textTheme.headlineMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: theme.colorScheme.primary,
            ),
            textAlign: TextAlign.center,
          ),
          
          const SizedBox(height: 8),
          
          Text(
            'Select which child will be reading today',
            style: theme.textTheme.bodyLarge?.copyWith(
              color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
            ),
            textAlign: TextAlign.center,
          ),
          
          const SizedBox(height: 32),
          
          // Profile grid
          GridView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: screenSize.width > 600 ? 3 : 2,
              crossAxisSpacing: 16,
              mainAxisSpacing: 16,
              childAspectRatio: 0.8,
            ),
            itemCount: profiles.length,
            itemBuilder: (context, index) {
              final profile = profiles[index];
              return _buildProfileCard(theme, profile);
            },
          ),
          
          const SizedBox(height: 32),
          
          // Add new profile button
          SizedBox(
            width: double.infinity,
            child: OutlinedButton.icon(
              onPressed: _isLoading ? null : () => _showCreateProfileDialog(),
              icon: const Icon(Icons.add),
              label: const Text('Add New Child'),
              style: OutlinedButton.styleFrom(
                padding: const EdgeInsets.symmetric(vertical: 16),
                side: BorderSide(color: theme.colorScheme.primary),
              ),
            ),
          ),
          
          const SizedBox(height: 16),
          
          // Continue without profile
          TextButton(
            onPressed: _isLoading ? null : () => _continueWithoutProfile(),
            child: Text(
              'Continue without selecting profile',
              style: TextStyle(
                color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildProfileCard(ThemeData theme, UserProfile profile) {
    return Card(
      elevation: 2,
      child: InkWell(
        onTap: _isLoading ? null : () => _selectProfile(profile),
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // Avatar
              Container(
                width: 60,
                height: 60,
                decoration: BoxDecoration(
                  color: profile.avatarColor,
                  shape: BoxShape.circle,
                ),
                child: const Icon(
                  Icons.child_care,
                  size: 30,
                  color: Colors.white,
                ),
              ),
              
              const SizedBox(height: 12),
              
              // Name
              Text(
                profile.name,
                style: theme.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
              
              const SizedBox(height: 4),
              
              // Age
              Text(
                '${profile.age} years old',
                style: theme.textTheme.bodySmall?.copyWith(
                  color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
                ),
              ),
              
              const SizedBox(height: 8),
              
              // Stats
              if (profile.storiesCompleted > 0)
                Text(
                  '${profile.storiesCompleted} stories',
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: theme.colorScheme.primary,
                    fontWeight: FontWeight.w500,
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildErrorState(ThemeData theme, Object error) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 64,
            color: theme.colorScheme.error,
          ),
          const SizedBox(height: 16),
          Text(
            'Error loading profiles',
            style: theme.textTheme.titleLarge,
          ),
          const SizedBox(height: 8),
          Text(
            error.toString(),
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          ElevatedButton(
            onPressed: () => ref.refresh(userProfilesProvider),
            child: const Text('Retry'),
          ),
        ],
      ),
    );
  }

  void _showCreateProfileDialog() {
    showDialog(
      context: context,
      builder: (context) => CreateProfileDialog(
        onProfileCreated: (profile) async {
          setState(() => _isLoading = true);
          
          final service = ref.read(userProfileServiceProvider);
          final success = await service.createProfile(profile);
          
          if (success) {
            ref.refresh(userProfilesProvider);
            AppLogger.info('[PROFILE_SELECTION] Created new profile for ${profile.name}');
            
            // Auto-select the newly created profile
            ref.read(selectedProfileProvider.notifier).state = profile;
            _navigateToHome();
          } else {
            if (mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('Failed to create profile. Name may already exist.')),
              );
            }
          }
          
          if (mounted) {
            setState(() => _isLoading = false);
          }
        },
      ),
    );
  }

  void _selectProfile(UserProfile profile) {
    setState(() => _isLoading = true);
    
    // Update selected profile
    ref.read(selectedProfileProvider.notifier).state = profile;
    
    // Update last active time
    final service = ref.read(userProfileServiceProvider);
    service.updateProfile(profile.copyWith(lastActiveAt: DateTime.now()));
    
    AppLogger.info('[PROFILE_SELECTION] Selected profile: ${profile.name}');
    
    _navigateToHome();
  }

  void _continueWithoutProfile() {
    AppLogger.info('[PROFILE_SELECTION] Continuing without profile selection');
    _navigateToHome();
  }

  void _navigateToHome() {
    context.go('/home');
  }
}
