import 'package:flutter/material.dart';
import 'package:choice_once_upon_a_time/core/utils/app_logger.dart';

/// Widget that displays a loading animation during scene transitions
class SceneTransitionLoadingWidget extends StatefulWidget {
  final String? currentSceneId;
  final String? nextSceneId;
  final String loadingText;
  final Duration duration;
  final VoidCallback? onComplete;

  const SceneTransitionLoadingWidget({
    super.key,
    this.currentSceneId,
    this.nextSceneId,
    this.loadingText = 'Loading next scene...',
    this.duration = const Duration(milliseconds: 1500),
    this.onComplete,
  });

  @override
  State<SceneTransitionLoadingWidget> createState() => _SceneTransitionLoadingWidgetState();
}

class _SceneTransitionLoadingWidgetState extends State<SceneTransitionLoadingWidget>
    with TickerProviderStateMixin {
  late final AnimationController _fadeController;
  late final AnimationController _scaleController;
  late final AnimationController _rotationController;
  late final AnimationController _progressController;
  
  late final Animation<double> _fadeAnimation;
  late final Animation<double> _scaleAnimation;
  late final Animation<double> _rotationAnimation;
  late final Animation<double> _progressAnimation;

  @override
  void initState() {
    super.initState();
    AppLogger.debug('[COMPONENT_LOAD] Full path: lib/features/story_player/presentation/widgets/scene_transition_loading_widget.dart - SceneTransitionLoadingWidget');
    
    _initializeAnimations();
    _startLoadingSequence();
  }

  void _initializeAnimations() {
    // Fade animation for the entire widget
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _fadeController,
      curve: Curves.easeInOut,
    ));

    // Scale animation for the loading indicator
    _scaleController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    _scaleAnimation = Tween<double>(
      begin: 0.8,
      end: 1.2,
    ).animate(CurvedAnimation(
      parent: _scaleController,
      curve: Curves.elasticOut,
    ));

    // Rotation animation for the loading spinner
    _rotationController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );
    _rotationAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _rotationController,
      curve: Curves.linear,
    ));

    // Progress animation
    _progressController = AnimationController(
      duration: widget.duration,
      vsync: this,
    );
    _progressAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _progressController,
      curve: Curves.easeInOut,
    ));
  }

  Future<void> _startLoadingSequence() async {
    AppLogger.debug('[SCENE_TRANSITION] Starting loading sequence for transition: ${widget.currentSceneId} → ${widget.nextSceneId}');
    
    // Start all animations
    _fadeController.forward();
    _scaleController.repeat(reverse: true);
    _rotationController.repeat();
    _progressController.forward();

    // Wait for the specified duration
    await Future.delayed(widget.duration);

    // Complete the loading
    await _completeLoading();
  }

  Future<void> _completeLoading() async {
    AppLogger.debug('[SCENE_TRANSITION] Completing loading sequence');
    
    // Stop repeating animations
    _scaleController.stop();
    _rotationController.stop();
    
    // Fade out
    await _fadeController.reverse();
    
    // Notify completion
    widget.onComplete?.call();
  }

  @override
  void dispose() {
    _fadeController.dispose();
    _scaleController.dispose();
    _rotationController.dispose();
    _progressController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final screenSize = MediaQuery.of(context).size;
    final isSmallScreen = screenSize.width < 600;

    return FadeTransition(
      opacity: _fadeAnimation,
      child: Container(
        color: Colors.black.withValues(alpha: 0.8),
        child: Center(
          child: ScaleTransition(
            scale: _scaleAnimation,
            child: Container(
              padding: EdgeInsets.all(isSmallScreen ? 24 : 32),
              margin: EdgeInsets.symmetric(
                horizontal: screenSize.width * 0.1,
                vertical: screenSize.height * 0.1,
              ),
              decoration: BoxDecoration(
                color: theme.colorScheme.surface,
                borderRadius: BorderRadius.circular(16),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.3),
                    blurRadius: 20,
                    offset: const Offset(0, 10),
                  ),
                ],
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // Loading spinner
                  RotationTransition(
                    turns: _rotationAnimation,
                    child: Container(
                      width: isSmallScreen ? 60 : 80,
                      height: isSmallScreen ? 60 : 80,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        gradient: LinearGradient(
                          colors: [
                            theme.colorScheme.primary,
                            theme.colorScheme.secondary,
                          ],
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                        ),
                      ),
                      child: Icon(
                        Icons.auto_stories,
                        color: Colors.white,
                        size: isSmallScreen ? 30 : 40,
                      ),
                    ),
                  ),
                  
                  SizedBox(height: isSmallScreen ? 16 : 24),
                  
                  // Loading text
                  Text(
                    widget.loadingText,
                    style: theme.textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                      fontSize: isSmallScreen ? 16 : 18,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  
                  SizedBox(height: isSmallScreen ? 12 : 16),
                  
                  // Progress bar
                  AnimatedBuilder(
                    animation: _progressAnimation,
                    builder: (context, child) {
                      return Container(
                        width: double.infinity,
                        height: 4,
                        decoration: BoxDecoration(
                          color: theme.colorScheme.surfaceContainerHighest,
                          borderRadius: BorderRadius.circular(2),
                        ),
                        child: FractionallySizedBox(
                          alignment: Alignment.centerLeft,
                          widthFactor: _progressAnimation.value,
                          child: Container(
                            decoration: BoxDecoration(
                              color: theme.colorScheme.primary,
                              borderRadius: BorderRadius.circular(2),
                            ),
                          ),
                        ),
                      );
                    },
                  ),
                  
                  SizedBox(height: isSmallScreen ? 8 : 12),
                  
                  // Scene transition info
                  if (widget.currentSceneId != null && widget.nextSceneId != null)
                    Text(
                      'Transitioning from ${widget.currentSceneId} to ${widget.nextSceneId}',
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: theme.colorScheme.onSurfaceVariant,
                        fontSize: isSmallScreen ? 12 : 14,
                      ),
                      textAlign: TextAlign.center,
                    ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
