import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:choice_once_upon_a_time/core/models/story_status.dart';
import 'package:choice_once_upon_a_time/features/story_library/presentation/providers/enhanced_story_library_provider.dart';
import 'package:choice_once_upon_a_time/core/utils/app_logger.dart';

/// Enhanced story card widget with download status and progress
class EnhancedStoryCardWidget extends ConsumerWidget {
  final StoryMetadata story;
  final bool isSmallScreen;

  const EnhancedStoryCardWidget({
    super.key,
    required this.story,
    this.isSmallScreen = false,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);
    final screenSize = MediaQuery.of(context).size;
    final cardWidth = isSmallScreen ? screenSize.width * 0.42 : 200.0;
    final cardHeight = isSmallScreen ? cardWidth * 1.4 : 280.0;

    return Container(
      width: cardWidth,
      height: cardHeight,
      margin: const EdgeInsets.all(8),
      child: Card(
        elevation: 4,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        child: InkWell(
          onTap: () => _handleCardTap(context, ref),
          borderRadius: BorderRadius.circular(12),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              // Cover image with status overlay
              Expanded(
                flex: 3,
                child: _buildCoverImage(theme),
              ),
              
              // Story info section
              Expanded(
                flex: 2,
                child: Padding(
                  padding: const EdgeInsets.all(12),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Title
                      Text(
                        story.title,
                        style: theme.textTheme.titleSmall?.copyWith(
                          fontWeight: FontWeight.bold,
                          fontSize: isSmallScreen ? 12 : 14,
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                      
                      const SizedBox(height: 4),
                      
                      // Age range and duration
                      Row(
                        children: [
                          Icon(
                            Icons.child_care,
                            size: isSmallScreen ? 12 : 14,
                            color: theme.colorScheme.primary,
                          ),
                          const SizedBox(width: 4),
                          Text(
                            '${story.ageRange}+',
                            style: theme.textTheme.bodySmall?.copyWith(
                              fontSize: isSmallScreen ? 10 : 11,
                            ),
                          ),
                          const SizedBox(width: 8),
                          Icon(
                            Icons.access_time,
                            size: isSmallScreen ? 12 : 14,
                            color: theme.colorScheme.primary,
                          ),
                          const SizedBox(width: 4),
                          Text(
                            '${story.estimatedDuration}min',
                            style: theme.textTheme.bodySmall?.copyWith(
                              fontSize: isSmallScreen ? 10 : 11,
                            ),
                          ),
                        ],
                      ),
                      
                      const Spacer(),
                      
                      // Action button based on status
                      _buildActionButton(context, ref, theme),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// Build cover image with status overlay
  Widget _buildCoverImage(ThemeData theme) {
    return Stack(
      children: [
        // Cover image
        ClipRRect(
          borderRadius: const BorderRadius.vertical(top: Radius.circular(12)),
          child: Container(
            width: double.infinity,
            height: double.infinity,
            decoration: BoxDecoration(
              color: theme.colorScheme.surfaceVariant,
            ),
            child: story.hasCoverImage
                ? _buildImage()
                : _buildPlaceholder(theme),
          ),
        ),
        
        // Status indicator
        Positioned(
          top: 8,
          right: 8,
          child: _buildStatusIndicator(theme),
        ),
        
        // Premium badge
        if (story.isPremium)
          Positioned(
            top: 8,
            left: 8,
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
              decoration: BoxDecoration(
                color: Colors.amber,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Text(
                'PREMIUM',
                style: TextStyle(
                  color: Colors.black,
                  fontSize: isSmallScreen ? 8 : 10,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
      ],
    );
  }

  /// Build image widget
  Widget _buildImage() {
    if (story.coverImagePath != null && story.coverImagePath!.startsWith('assets/')) {
      return Image.asset(
        story.coverImagePath!,
        fit: BoxFit.cover,
        errorBuilder: (context, error, stackTrace) {
          return Image.asset(
            'assets/default/default_image.png',
            fit: BoxFit.cover,
          );
        },
      );
    } else if (story.coverImageUrl != null) {
      return Image.network(
        story.coverImageUrl!,
        fit: BoxFit.cover,
        errorBuilder: (context, error, stackTrace) {
          return Image.asset(
            'assets/default/default_image.png',
            fit: BoxFit.cover,
          );
        },
      );
    } else {
      return Image.asset(
        'assets/default/default_image.png',
        fit: BoxFit.cover,
      );
    }
  }

  /// Build placeholder for missing cover image
  Widget _buildPlaceholder(ThemeData theme) {
    return Container(
      color: theme.colorScheme.surfaceVariant,
      child: Icon(
        Icons.auto_stories,
        size: isSmallScreen ? 40 : 60,
        color: theme.colorScheme.onSurfaceVariant,
      ),
    );
  }

  /// Build status indicator
  Widget _buildStatusIndicator(ThemeData theme) {
    IconData icon;
    Color color;
    
    switch (story.status) {
      case StoryStatus.offline:
        icon = Icons.offline_pin;
        color = Colors.green;
        break;
      case StoryStatus.downloaded:
        icon = Icons.download_done;
        color = Colors.blue;
        break;
      case StoryStatus.downloadable:
        icon = Icons.cloud_download;
        color = Colors.orange;
        break;
      case StoryStatus.downloading:
        icon = Icons.downloading;
        color = Colors.blue;
        break;
      case StoryStatus.downloadFailed:
        icon = Icons.error;
        color = Colors.red;
        break;
      case StoryStatus.locked:
        icon = Icons.lock;
        color = Colors.grey;
        break;
      default:
        icon = Icons.help;
        color = Colors.grey;
    }

    return Container(
      padding: const EdgeInsets.all(4),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.9),
        shape: BoxShape.circle,
      ),
      child: Icon(
        icon,
        size: isSmallScreen ? 14 : 16,
        color: Colors.white,
      ),
    );
  }

  /// Build action button based on story status
  Widget _buildActionButton(BuildContext context, WidgetRef ref, ThemeData theme) {
    switch (story.status) {
      case StoryStatus.offline:
      case StoryStatus.downloaded:
        return SizedBox(
          width: double.infinity,
          height: isSmallScreen ? 28 : 32,
          child: ElevatedButton(
            onPressed: () => _playStory(context),
            style: ElevatedButton.styleFrom(
              backgroundColor: theme.colorScheme.primary,
              foregroundColor: theme.colorScheme.onPrimary,
              padding: const EdgeInsets.symmetric(vertical: 4),
            ),
            child: Text(
              'PLAY',
              style: TextStyle(
                fontSize: isSmallScreen ? 10 : 12,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        );
        
      case StoryStatus.downloadable:
      case StoryStatus.downloadFailed:
        return SizedBox(
          width: double.infinity,
          height: isSmallScreen ? 28 : 32,
          child: ElevatedButton(
            onPressed: () => _downloadStory(ref),
            style: ElevatedButton.styleFrom(
              backgroundColor: theme.colorScheme.secondary,
              foregroundColor: theme.colorScheme.onSecondary,
              padding: const EdgeInsets.symmetric(vertical: 4),
            ),
            child: Text(
              story.status == StoryStatus.downloadFailed ? 'RETRY' : 'DOWNLOAD',
              style: TextStyle(
                fontSize: isSmallScreen ? 10 : 12,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        );
        
      case StoryStatus.downloading:
        return Column(
          children: [
            LinearProgressIndicator(
              value: story.downloadProgress,
              backgroundColor: theme.colorScheme.surfaceVariant,
              valueColor: AlwaysStoppedAnimation<Color>(theme.colorScheme.primary),
            ),
            const SizedBox(height: 4),
            Text(
              '${(story.downloadProgress * 100).toInt()}%',
              style: TextStyle(
                fontSize: isSmallScreen ? 9 : 10,
                color: theme.colorScheme.onSurfaceVariant,
              ),
            ),
          ],
        );
        
      case StoryStatus.locked:
        return SizedBox(
          width: double.infinity,
          height: isSmallScreen ? 28 : 32,
          child: ElevatedButton(
            onPressed: null, // Disabled for now
            style: ElevatedButton.styleFrom(
              backgroundColor: theme.colorScheme.surfaceVariant,
              foregroundColor: theme.colorScheme.onSurfaceVariant,
              padding: const EdgeInsets.symmetric(vertical: 4),
            ),
            child: Text(
              'LOCKED',
              style: TextStyle(
                fontSize: isSmallScreen ? 10 : 12,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        );
        
      default:
        return const SizedBox.shrink();
    }
  }

  /// Handle card tap
  void _handleCardTap(BuildContext context, WidgetRef ref) {
    if (story.status.canPlay) {
      _playStory(context);
    } else if (story.status.canDownload) {
      _downloadStory(ref);
    }
  }

  /// Play the story
  void _playStory(BuildContext context) {
    AppLogger.info('[ENHANCED_STORY_CARD] Playing story: ${story.id}');
    context.go('/enhanced_story_player/${story.id}');
  }

  /// Download the story
  void _downloadStory(WidgetRef ref) {
    AppLogger.info('[ENHANCED_STORY_CARD] Downloading story: ${story.id}');
    ref.read(enhancedStoryLibraryProvider.notifier).downloadStory(story.id);
  }
}
