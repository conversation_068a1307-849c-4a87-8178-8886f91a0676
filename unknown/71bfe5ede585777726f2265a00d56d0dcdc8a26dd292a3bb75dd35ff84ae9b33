import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:choice_once_upon_a_time/l10n/app_localizations.dart';
import 'package:choice_once_upon_a_time/core/audio/tts_service_interface.dart';
import 'package:choice_once_upon_a_time/core/utils/app_logger.dart';

/// Enhanced voice guidance manager with per-screen session tracking
class EnhancedVoiceGuidanceManager {
  final TTSServiceInterface _ttsService;
  
  // Track which screens have played their voice guide this session
  final Set<String> _playedScreens = <String>{};
  
  // Track if manual replay is in progress
  bool _isManualReplay = false;

  EnhancedVoiceGuidanceManager(this._ttsService);

  /// Play voice guide for a screen with automatic session tracking
  /// [screenId] - Unique identifier for the screen
  /// [textProvider] - Function that returns localized text
  /// [emotionCue] - Emotion cue for TTS
  /// [forcePlay] - Force play even if already played this session
  Future<void> playScreenGuide({
    required String screenId,
    required BuildContext context,
    required String Function(AppLocalizations) textProvider,
    String emotionCue = 'friendly',
    bool forcePlay = false,
  }) async {
    try {
      final l10n = AppLocalizations.of(context);
      if (l10n == null) {
        AppLogger.warning('EnhancedVoiceGuide: AppLocalizations is null for screen $screenId');
        return;
      }

      // Check if this screen has already played this session
      if (!forcePlay && _playedScreens.contains(screenId)) {
        AppLogger.debug('EnhancedVoiceGuide: Screen $screenId already played this session, skipping');
        return;
      }

      final textToSpeak = textProvider(l10n);
      if (textToSpeak.isNotEmpty) {
        await _ttsService.speakText(textToSpeak, emotionCue: emotionCue);
        
        // Mark this screen as played
        _playedScreens.add(screenId);
        
        AppLogger.info('EnhancedVoiceGuide: Played guide for screen $screenId');
      }
    } catch (e, stackTrace) {
      AppLogger.error('EnhancedVoiceGuide: Error playing guide for screen $screenId', e, stackTrace);
    }
  }

  /// Manually replay the voice guide for a screen
  Future<void> replayScreenGuide({
    required String screenId,
    required BuildContext context,
    required String Function(AppLocalizations) textProvider,
    String emotionCue = 'friendly',
  }) async {
    _isManualReplay = true;
    
    try {
      await playScreenGuide(
        screenId: screenId,
        context: context,
        textProvider: textProvider,
        emotionCue: emotionCue,
        forcePlay: true, // Always force play for manual replay
      );
      
      AppLogger.info('EnhancedVoiceGuide: Manual replay completed for screen $screenId');
    } finally {
      _isManualReplay = false;
    }
  }

  /// Play voice guide with direct text (for backward compatibility)
  Future<void> playGuide(
    BuildContext context,
    String Function(AppLocalizations) textProvider, {
    String emotionCue = 'friendly',
  }) async {
    try {
      final l10n = AppLocalizations.of(context);
      if (l10n == null) {
        AppLogger.warning('EnhancedVoiceGuide: AppLocalizations is null for direct guide');
        return;
      }

      final textToSpeak = textProvider(l10n);
      if (textToSpeak.isNotEmpty) {
        await _ttsService.speakText(textToSpeak, emotionCue: emotionCue);
        AppLogger.debug('EnhancedVoiceGuide: Played direct guide');
      }
    } catch (e, stackTrace) {
      AppLogger.error('EnhancedVoiceGuide: Error playing direct guide', e, stackTrace);
    }
  }

  /// Play voice guide with direct text string
  Future<void> playDirectText(String text, {String emotionCue = 'friendly'}) async {
    try {
      if (text.isNotEmpty) {
        await _ttsService.speakText(text, emotionCue: emotionCue);
        AppLogger.debug('EnhancedVoiceGuide: Played direct text');
      }
    } catch (e, stackTrace) {
      AppLogger.error('EnhancedVoiceGuide: Error playing direct text', e, stackTrace);
    }
  }

  /// Stop any currently playing voice guidance
  Future<void> stopGuide() async {
    try {
      await _ttsService.stop();
      AppLogger.debug('EnhancedVoiceGuide: Stopped playback');
    } catch (e, stackTrace) {
      AppLogger.error('EnhancedVoiceGuide: Error stopping guide', e, stackTrace);
    }
  }

  /// Check if a screen has been played this session
  bool hasScreenBeenPlayed(String screenId) {
    return _playedScreens.contains(screenId);
  }

  /// Check if manual replay is in progress
  bool get isManualReplay => _isManualReplay;

  /// Reset session tracking (call when app restarts or user logs out)
  void resetSession() {
    _playedScreens.clear();
    AppLogger.info('EnhancedVoiceGuide: Session reset, cleared played screens');
  }

  /// Get list of screens played this session (for debugging)
  List<String> get playedScreens => _playedScreens.toList();

  /// Get count of screens played this session
  int get playedScreenCount => _playedScreens.length;
}

/// Provider for the enhanced voice guidance manager
final enhancedVoiceGuidanceManagerProvider = Provider<EnhancedVoiceGuidanceManager>((ref) {
  final ttsService = ref.watch(ttsServiceProvider);
  return EnhancedVoiceGuidanceManager(ttsService);
});

/// Import this provider in service_providers.dart
final ttsServiceProvider = Provider<TTSServiceInterface>((ref) {
  throw UnimplementedError('ttsServiceProvider must be overridden');
});
