import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:choice_once_upon_a_time/core/services/story_rewards_service.dart';

/// Grid section widget for displaying rewards summary on home screen
class RewardsGridSection extends StatefulWidget {
  const RewardsGridSection({super.key});

  @override
  State<RewardsGridSection> createState() => _RewardsGridSectionState();
}

class _RewardsGridSectionState extends State<RewardsGridSection>
    with SingleTickerProviderStateMixin {
  late final AnimationController _animationController;
  late final Animation<double> _scaleAnimation;
  late final Animation<double> _glowAnimation;
  
  late final StoryRewardsService _rewardsService;
  int _totalRewards = 0;
  int _completedStories = 0;

  @override
  void initState() {
    super.initState();
    _rewardsService = StoryRewardsService.instance;
    
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 2000),
      vsync: this,
    );

    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 1.05,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _glowAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _loadRewardsData();
    _animationController.repeat(reverse: true);
  }

  Future<void> _loadRewardsData() async {
    try {
      await _rewardsService.initialize();
      
      if (mounted) {
        setState(() {
          _totalRewards = _rewardsService.earnedRewards.values
              .fold(0, (sum, rewards) => sum + rewards.length);
          _completedStories = _rewardsService.completedStories.length;
        });
      }
    } catch (e) {
      // Handle error silently for grid section
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final screenSize = MediaQuery.of(context).size;
    final isSmallScreen = screenSize.width < 600;

    return AnimatedBuilder(
      animation: _scaleAnimation,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: Card(
            elevation: 6,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(16),
            ),
            child: Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(16),
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    Colors.amber.withOpacity(0.1),
                    theme.colorScheme.surface,
                    Colors.orange.withOpacity(0.05),
                  ],
                ),
                boxShadow: [
                  BoxShadow(
                    color: Colors.amber.withOpacity(0.2 + (_glowAnimation.value * 0.1)),
                    blurRadius: 8 + (_glowAnimation.value * 4),
                    spreadRadius: 1,
                  ),
                ],
              ),
              child: InkWell(
                onTap: () => context.go('/rewards'),
                borderRadius: BorderRadius.circular(16),
                child: Padding(
                  padding: EdgeInsets.all(isSmallScreen ? 16.0 : 20.0),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      // Rewards icon with glow effect
                      AnimatedBuilder(
                        animation: _glowAnimation,
                        builder: (context, child) {
                          return Container(
                            width: isSmallScreen ? 48 : 56,
                            height: isSmallScreen ? 48 : 56,
                            decoration: BoxDecoration(
                              shape: BoxShape.circle,
                              color: Colors.amber,
                              boxShadow: [
                                BoxShadow(
                                  color: Colors.amber.withOpacity(
                                    0.4 + (_glowAnimation.value * 0.3),
                                  ),
                                  blurRadius: 12 + (_glowAnimation.value * 8),
                                  spreadRadius: 2,
                                ),
                              ],
                            ),
                            child: Icon(
                              Icons.emoji_events,
                              color: Colors.white,
                              size: isSmallScreen ? 24 : 28,
                            ),
                          );
                        },
                      ),

                      SizedBox(height: isSmallScreen ? 12 : 16),

                      // Title
                      Text(
                        'My Rewards',
                        style: theme.textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: theme.colorScheme.primary,
                          fontSize: isSmallScreen ? 18 : 20,
                        ),
                        textAlign: TextAlign.center,
                      ),

                      SizedBox(height: isSmallScreen ? 8 : 12),

                      // Rewards summary
                      if (_totalRewards > 0) ...[
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                          children: [
                            _buildStatChip(
                              icon: Icons.star,
                              value: _totalRewards.toString(),
                              label: 'Rewards',
                              color: Colors.amber,
                              theme: theme,
                              isSmall: isSmallScreen,
                            ),
                            _buildStatChip(
                              icon: Icons.book,
                              value: _completedStories.toString(),
                              label: 'Stories',
                              color: theme.colorScheme.primary,
                              theme: theme,
                              isSmall: isSmallScreen,
                            ),
                          ],
                        ),
                      ] else ...[
                        Text(
                          'Complete stories to\nearn rewards!',
                          style: theme.textTheme.bodyMedium?.copyWith(
                            color: theme.colorScheme.onSurfaceVariant,
                            fontSize: isSmallScreen ? 12 : 14,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ],

                      SizedBox(height: isSmallScreen ? 8 : 12),

                      // Action hint
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 12,
                          vertical: 6,
                        ),
                        decoration: BoxDecoration(
                          color: Colors.amber.withOpacity(0.2),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Text(
                          'Tap to view all',
                          style: theme.textTheme.labelSmall?.copyWith(
                            color: Colors.amber.shade700,
                            fontWeight: FontWeight.w600,
                            fontSize: isSmallScreen ? 10 : 12,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildStatChip({
    required IconData icon,
    required String value,
    required String label,
    required Color color,
    required ThemeData theme,
    required bool isSmall,
  }) {
    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: isSmall ? 8 : 10,
        vertical: isSmall ? 4 : 6,
      ),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: color.withOpacity(0.3),
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            icon,
            color: color,
            size: isSmall ? 14 : 16,
          ),
          const SizedBox(height: 2),
          Text(
            value,
            style: theme.textTheme.labelLarge?.copyWith(
              color: color,
              fontWeight: FontWeight.bold,
              fontSize: isSmall ? 12 : 14,
            ),
          ),
          Text(
            label,
            style: theme.textTheme.labelSmall?.copyWith(
              color: theme.colorScheme.onSurfaceVariant,
              fontSize: isSmall ? 8 : 10,
            ),
          ),
        ],
      ),
    );
  }
}
