import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:integration_test/integration_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:choice_once_upon_a_time/main.dart' as app;

void main() {
  IntegrationTestWidgetsFlutterBinding.ensureInitialized();

  group('User Journey Integration Tests', () {
    testWidgets('Complete story discovery and download flow', (WidgetTester tester) async {
      // Launch the app
      app.main();
      await tester.pumpAndSettle(const Duration(seconds: 3));

      // Test 1: Navigate from home to story library
      await tester.tap(find.text('Story Library'));
      await tester.pumpAndSettle();

      // Verify we're on the story library screen
      expect(find.text('Full Story Library'), findsOneWidget);

      // Test 2: Search for a story
      await tester.enterText(find.byType(TextField), 'test');
      await tester.pumpAndSettle();

      // Test 3: Select a story (if available)
      final storyCards = find.byType(Card);
      if (storyCards.evaluate().isNotEmpty) {
        await tester.tap(storyCards.first);
        await tester.pumpAndSettle();
      }

      // Test 4: Navigate back to home
      await tester.tap(find.byIcon(Icons.arrow_back));
      await tester.pumpAndSettle();

      // Verify we're back on home screen
      expect(find.text('Choice'), findsOneWidget);
    });

    testWidgets('Continue reading flow', (WidgetTester tester) async {
      // Launch the app
      app.main();
      await tester.pumpAndSettle(const Duration(seconds: 3));

      // Test 1: Navigate to continue reading
      final continueReadingFinder = find.text('Continue Reading');
      if (continueReadingFinder.evaluate().isNotEmpty) {
        await tester.tap(continueReadingFinder);
        await tester.pumpAndSettle();

        // Verify we're on the continue reading screen
        expect(find.text('Continue Reading'), findsOneWidget);

        // Navigate back
        await tester.tap(find.byIcon(Icons.arrow_back));
        await tester.pumpAndSettle();
      }
    });

    testWidgets('Global narrator functionality', (WidgetTester tester) async {
      // Launch the app
      app.main();
      await tester.pumpAndSettle(const Duration(seconds: 3));

      // Test global narrator controls (if visible)
      final playButton = find.byIcon(Icons.play_arrow);
      if (playButton.evaluate().isNotEmpty) {
        await tester.tap(playButton);
        await tester.pumpAndSettle();

        // Check if pause button appears
        final pauseButton = find.byIcon(Icons.pause);
        if (pauseButton.evaluate().isNotEmpty) {
          await tester.tap(pauseButton);
          await tester.pumpAndSettle();
        }

        // Check for stop button
        final stopButton = find.byIcon(Icons.stop);
        if (stopButton.evaluate().isNotEmpty) {
          await tester.tap(stopButton);
          await tester.pumpAndSettle();
        }
      }
    });

    testWidgets('Responsive design across screen sizes', (WidgetTester tester) async {
      // Test different screen sizes
      final testSizes = [
        const Size(375, 667),  // iPhone SE
        const Size(414, 896),  // iPhone 11 Pro Max
        const Size(768, 1024), // iPad
        const Size(1024, 768), // iPad landscape
      ];

      for (final size in testSizes) {
        await tester.binding.setSurfaceSize(size);
        
        // Launch the app
        app.main();
        await tester.pumpAndSettle(const Duration(seconds: 2));

        // Verify the app loads without errors
        expect(find.byType(MaterialApp), findsOneWidget);
        
        // Test navigation
        final storyLibraryButton = find.text('Story Library');
        if (storyLibraryButton.evaluate().isNotEmpty) {
          await tester.tap(storyLibraryButton);
          await tester.pumpAndSettle();
          
          // Navigate back
          final backButton = find.byIcon(Icons.arrow_back);
          if (backButton.evaluate().isNotEmpty) {
            await tester.tap(backButton);
            await tester.pumpAndSettle();
          }
        }
      }
    });

    testWidgets('Parent zone navigation', (WidgetTester tester) async {
      // Launch the app
      app.main();
      await tester.pumpAndSettle(const Duration(seconds: 3));

      // Test parent zone access
      final parentZoneButton = find.text('Parent Zone');
      if (parentZoneButton.evaluate().isNotEmpty) {
        await tester.tap(parentZoneButton);
        await tester.pumpAndSettle();

        // This might require authentication, so we'll just verify the navigation attempt
        // The actual authentication flow would be tested separately
      }
    });

    testWidgets('Settings and preferences', (WidgetTester tester) async {
      // Launch the app
      app.main();
      await tester.pumpAndSettle(const Duration(seconds: 3));

      // Look for settings access
      final settingsButton = find.byIcon(Icons.settings);
      if (settingsButton.evaluate().isNotEmpty) {
        await tester.tap(settingsButton);
        await tester.pumpAndSettle();

        // Navigate back if we successfully entered settings
        final backButton = find.byIcon(Icons.arrow_back);
        if (backButton.evaluate().isNotEmpty) {
          await tester.tap(backButton);
          await tester.pumpAndSettle();
        }
      }
    });

    testWidgets('Offline functionality', (WidgetTester tester) async {
      // Launch the app
      app.main();
      await tester.pumpAndSettle(const Duration(seconds: 3));

      // Navigate to story library
      final storyLibraryButton = find.text('Story Library');
      if (storyLibraryButton.evaluate().isNotEmpty) {
        await tester.tap(storyLibraryButton);
        await tester.pumpAndSettle();

        // Look for download buttons
        final downloadButtons = find.text('Download');
        if (downloadButtons.evaluate().isNotEmpty) {
          // Test download functionality (this would be mocked in a real test)
          await tester.tap(downloadButtons.first);
          await tester.pumpAndSettle();
        }

        // Navigate back
        final backButton = find.byIcon(Icons.arrow_back);
        if (backButton.evaluate().isNotEmpty) {
          await tester.tap(backButton);
          await tester.pumpAndSettle();
        }
      }
    });

    testWidgets('Theme consistency across screens', (WidgetTester tester) async {
      // Launch the app
      app.main();
      await tester.pumpAndSettle(const Duration(seconds: 3));

      // Navigate through different screens to verify theme consistency
      final screens = [
        'Story Library',
        'Continue Reading',
        'Parent Zone',
      ];

      for (final screenName in screens) {
        final screenButton = find.text(screenName);
        if (screenButton.evaluate().isNotEmpty) {
          await tester.tap(screenButton);
          await tester.pumpAndSettle();

          // Verify theme elements are present
          expect(find.byType(MaterialApp), findsOneWidget);
          
          // Navigate back
          final backButton = find.byIcon(Icons.arrow_back);
          if (backButton.evaluate().isNotEmpty) {
            await tester.tap(backButton);
            await tester.pumpAndSettle();
          }
        }
      }
    });

    testWidgets('Error handling and recovery', (WidgetTester tester) async {
      // Launch the app
      app.main();
      await tester.pumpAndSettle(const Duration(seconds: 3));

      // Test navigation to potentially problematic screens
      final storyLibraryButton = find.text('Story Library');
      if (storyLibraryButton.evaluate().isNotEmpty) {
        await tester.tap(storyLibraryButton);
        await tester.pumpAndSettle();

        // Verify error states are handled gracefully
        // Look for error messages or loading indicators
        final errorMessages = find.textContaining('Error');
        final loadingIndicators = find.byType(CircularProgressIndicator);
        
        // The app should either show content, loading, or a graceful error state
        expect(
          errorMessages.evaluate().isNotEmpty || 
          loadingIndicators.evaluate().isNotEmpty ||
          find.byType(Card).evaluate().isNotEmpty,
          isTrue,
        );

        // Navigate back
        final backButton = find.byIcon(Icons.arrow_back);
        if (backButton.evaluate().isNotEmpty) {
          await tester.tap(backButton);
          await tester.pumpAndSettle();
        }
      }
    });

    testWidgets('Performance under load', (WidgetTester tester) async {
      // Launch the app
      app.main();
      await tester.pumpAndSettle(const Duration(seconds: 3));

      // Perform rapid navigation to test performance
      for (int i = 0; i < 5; i++) {
        final storyLibraryButton = find.text('Story Library');
        if (storyLibraryButton.evaluate().isNotEmpty) {
          await tester.tap(storyLibraryButton);
          await tester.pumpAndSettle();

          final backButton = find.byIcon(Icons.arrow_back);
          if (backButton.evaluate().isNotEmpty) {
            await tester.tap(backButton);
            await tester.pumpAndSettle();
          }
        }
      }

      // Verify the app is still responsive
      expect(find.byType(MaterialApp), findsOneWidget);
    });
  });
}
