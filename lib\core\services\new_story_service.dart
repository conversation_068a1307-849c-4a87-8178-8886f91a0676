import 'dart:convert';
import 'package:flutter/services.dart';
import 'package:logger/logger.dart';
import 'package:choice_once_upon_a_time/models/enhanced_story_model.dart';
import 'package:choice_once_upon_a_time/models/story_metadata_model.dart';
import 'package:choice_once_upon_a_time/core/utils/app_logger.dart';

/// New story service that scans assets/stories folder structure
/// Each folder represents a story with storyid as folder name
/// Each story folder contains: story.json, images/, assets/ subfolders
class NewStoryService {
  static final Logger _logger = Logger();

  // Cache for loaded stories and metadata
  final Map<String, EnhancedStoryModel> _storyCache = {};
  final Map<String, StoryMetadataModel> _metadataCache = {};
  List<StoryMetadataModel>? _allStoriesCache;

  /// Scans assets/stories folder and returns all available stories
  Future<List<StoryMetadataModel>> getAllStoryMetadata() async {
    if (_allStoriesCache != null) {
      return _allStoriesCache!;
    }

    try {
      AppLogger.debug('[NEW_STORY_SERVICE] Scanning assets/stories folder for story directories');
      
      // Get the asset manifest to find story folders
      final manifestContent = await rootBundle.loadString('AssetManifest.json');
      final Map<String, dynamic> manifest = jsonDecode(manifestContent);

      final storyMetadataList = <StoryMetadataModel>[];
      final storyFolders = <String>{};

      // Find all story.json files to identify story folders
      for (final assetPath in manifest.keys) {
        if (assetPath.startsWith('assets/stories/') && assetPath.endsWith('/story.json')) {
          // Extract folder name (story ID) from path
          final pathParts = assetPath.split('/');
          if (pathParts.length >= 4) {
            final storyId = pathParts[2]; // assets/stories/[storyId]/story.json
            storyFolders.add(storyId);
          }
        }
      }

      AppLogger.debug('[NEW_STORY_SERVICE] Found ${storyFolders.length} story folders: ${storyFolders.toList()}');

      // Load metadata for each story
      for (final storyId in storyFolders) {
        try {
          final metadata = await _loadStoryMetadata(storyId);
          if (metadata != null) {
            storyMetadataList.add(metadata);
            _metadataCache[storyId] = metadata;
          }
        } catch (e) {
          _logger.e('[NEW_STORY_SERVICE] Failed to load metadata for story $storyId: $e');
        }
      }

      _allStoriesCache = storyMetadataList;
      AppLogger.debug('[NEW_STORY_SERVICE] Successfully loaded ${storyMetadataList.length} story metadata entries');
      return storyMetadataList;

    } catch (e) {
      _logger.e('[NEW_STORY_SERVICE] Failed to scan stories folder: $e');
      return [];
    }
  }

  /// Loads metadata for a specific story from its story.json file
  Future<StoryMetadataModel?> _loadStoryMetadata(String storyId) async {
    try {
      final storyPath = 'assets/stories/$storyId/story.json';
      AppLogger.debug('[NEW_STORY_SERVICE] Loading metadata from: $storyPath');
      
      final jsonString = await rootBundle.loadString(storyPath);
      final Map<String, dynamic> storyJson = jsonDecode(jsonString);

      // Extract metadata from the new story.json structure
      final title = storyJson['title'] as String;
      final moral = storyJson['moral'] as String;
      final ageGroup = storyJson['age_group'] as String? ?? '3-5';
      final coverImage = storyJson['cover_image'] as String? ?? 'story_cover.jpg';
      
      // Calculate estimated duration from setup if available
      final setup = storyJson['setup'] as Map<String, dynamic>?;
      final estimatedTime = setup?['estimated_time'] as String?;
      int durationMinutes = 5; // default
      if (estimatedTime != null) {
        final match = RegExp(r'(\d+)').firstMatch(estimatedTime);
        if (match != null) {
          durationMinutes = int.tryParse(match.group(1)!) ?? 5;
        }
      }

      // Build cover image path
      final coverImagePath = 'assets/stories/$storyId/images/$coverImage';

      return StoryMetadataModel(
        id: storyId,
        title: {'en-US': title},
        coverImageUrl: coverImagePath,
        loglineShort: {'en-US': 'A story about $moral'},
        targetMoralValue: moral,
        targetAgeSubSegment: ageGroup,
        estimatedDurationMinutes: durationMinutes,
        isFree: true,
        isLocked: false,
        published: true,
        dataSource: 'new_asset',
        version: '2.0.0',
        supportedLanguages: ['en-US'],
        defaultLanguage: 'en-US',
        initialSceneId: 'scene_1', // Default from new structure
      );

    } catch (e) {
      _logger.e('[NEW_STORY_SERVICE] Failed to load metadata for $storyId: $e');
      return null;
    }
  }

  /// Loads a complete story by ID
  Future<EnhancedStoryModel?> loadStory(String storyId) async {
    if (_storyCache.containsKey(storyId)) {
      return _storyCache[storyId];
    }

    try {
      AppLogger.debug('[NEW_STORY_SERVICE] Loading complete story: $storyId');
      
      final storyPath = 'assets/stories/$storyId/story.json';
      final jsonString = await rootBundle.loadString(storyPath);
      final Map<String, dynamic> storyJson = jsonDecode(jsonString);

      // Create enhanced story model from new JSON structure
      final story = EnhancedStoryModel.fromJson(storyJson);
      _storyCache[storyId] = story;

      AppLogger.debug('[NEW_STORY_SERVICE] Successfully loaded story: $storyId');
      return story;

    } catch (e) {
      _logger.e('[NEW_STORY_SERVICE] Failed to load story $storyId: $e');
      return null;
    }
  }

  /// Checks if a story is available locally (always true for asset stories)
  Future<bool> isStoryAvailableLocally(String storyId) async {
    try {
      final storyPath = 'assets/stories/$storyId/story.json';
      await rootBundle.loadString(storyPath);
      return true;
    } catch (e) {
      return false;
    }
  }

  /// Gets the download/play status for a story
  Future<String> getStoryStatus(String storyId) async {
    final isAvailable = await isStoryAvailableLocally(storyId);
    return isAvailable ? 'play' : 'download';
  }

  /// Gets scene image with fallback
  Future<String> getSceneImageWithFallback(String storyId, String? imageName) async {
    return 'assets/stories/$storyId/images/${imageName ?? 'default_scene.jpg'}';
  }

  /// Gets character image with fallback
  Future<String> getCharacterImageWithFallback(String storyId, String? imageName) async {
    return 'assets/stories/$storyId/images/${imageName ?? 'default_character.jpg'}';
  }

  /// Gets background music with fallback
  Future<String> getBackgroundMusicWithFallback(String storyId, String? musicName) async {
    return 'assets/stories/$storyId/assets/${musicName ?? 'default_music.mp3'}';
  }

  /// Gets sound effect with fallback
  Future<String> getSoundEffectWithFallback(String storyId, String? soundName) async {
    return 'assets/stories/$storyId/assets/${soundName ?? 'default_sound.mp3'}';
  }

  /// Gets vocabulary image with fallback
  Future<String> getVocabularyImageWithFallback(String storyId, String? imageName) async {
    return 'assets/stories/$storyId/images/${imageName ?? 'default_vocab.jpg'}';
  }

  /// Validates that fallback assets are available
  Future<bool> validateFallbackAssets() async {
    return true; // Simplified for now
  }

  /// Clears all caches
  void clearCache() {
    _storyCache.clear();
    _metadataCache.clear();
    _allStoriesCache = null;
    AppLogger.debug('[NEW_STORY_SERVICE] Cache cleared');
  }
}
