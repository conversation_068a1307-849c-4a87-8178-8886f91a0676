import 'package:json_annotation/json_annotation.dart';

part 'rewards_model.g.dart';

/// Model for story rewards system
@JsonSerializable()
class RewardsModel {
  /// Reward given for completing the entire story
  final String? completion;

  /// List of rewards for making moral choices throughout the story
  final List<String> moralChoices;

  const RewardsModel({
    this.completion,
    this.moralChoices = const [],
  });

  /// Creates a RewardsModel from JSON
  factory RewardsModel.fromJson(Map<String, dynamic> json) =>
      _$RewardsModelFromJson(json);

  /// Converts this model to JSON
  Map<String, dynamic> toJson() => _$RewardsModelToJson(this);

  /// Creates a copy of this model with updated fields
  RewardsModel copyWith({
    String? completion,
    List<String>? moralChoices,
  }) {
    return RewardsModel(
      completion: completion ?? this.completion,
      moralChoices: moralChoices ?? this.moralChoices,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is RewardsModel &&
        other.completion == completion &&
        _listEquals(other.moralChoices, moralChoices);
  }

  @override
  int get hashCode => completion.hashCode ^ moralChoices.hashCode;

  @override
  String toString() {
    return 'RewardsModel(completion: $completion, moralChoices: $moralChoices)';
  }
}

/// Model for tracking earned rewards
@JsonSerializable()
class EarnedReward {
  /// The reward identifier/name
  final String rewardId;

  /// Type of reward: 'completion' or 'moral_choice'
  final String rewardType;

  /// When the reward was earned
  final DateTime earnedAt;

  /// The story ID where this reward was earned
  final String storyId;

  /// Optional scene ID for moral choice rewards
  final String? sceneId;

  const EarnedReward({
    required this.rewardId,
    required this.rewardType,
    required this.earnedAt,
    required this.storyId,
    this.sceneId,
  });

  /// Creates an EarnedReward from JSON
  factory EarnedReward.fromJson(Map<String, dynamic> json) =>
      _$EarnedRewardFromJson(json);

  /// Converts this model to JSON
  Map<String, dynamic> toJson() => _$EarnedRewardToJson(this);

  /// Creates a copy of this model with updated fields
  EarnedReward copyWith({
    String? rewardId,
    String? rewardType,
    DateTime? earnedAt,
    String? storyId,
    String? sceneId,
  }) {
    return EarnedReward(
      rewardId: rewardId ?? this.rewardId,
      rewardType: rewardType ?? this.rewardType,
      earnedAt: earnedAt ?? this.earnedAt,
      storyId: storyId ?? this.storyId,
      sceneId: sceneId ?? this.sceneId,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is EarnedReward &&
        other.rewardId == rewardId &&
        other.rewardType == rewardType &&
        other.earnedAt == earnedAt &&
        other.storyId == storyId &&
        other.sceneId == sceneId;
  }

  @override
  int get hashCode {
    return rewardId.hashCode ^
        rewardType.hashCode ^
        earnedAt.hashCode ^
        storyId.hashCode ^
        sceneId.hashCode;
  }

  @override
  String toString() {
    return 'EarnedReward(rewardId: $rewardId, rewardType: $rewardType, earnedAt: $earnedAt, storyId: $storyId, sceneId: $sceneId)';
  }
}

/// Helper function to compare lists
bool _listEquals<T>(List<T>? a, List<T>? b) {
  if (a == null) return b == null;
  if (b == null || a.length != b.length) return false;
  for (int index = 0; index < a.length; index += 1) {
    if (a[index] != b[index]) return false;
  }
  return true;
}
