import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

// Import the new widgets we've created
import 'package:choice_once_upon_a_time/features/story_library/presentation/widgets/animated_scene_widget.dart';
import 'package:choice_once_upon_a_time/features/story_library/presentation/widgets/home_screen_grid_area.dart';
import 'package:choice_once_upon_a_time/features/parent_zone/data/user_profile_service.dart';
import 'package:choice_once_upon_a_time/core/auth/session_manager.dart';
import 'package:choice_once_upon_a_time/core/utils/app_logger.dart';
import 'package:choice_once_upon_a_time/l10n/app_localizations.dart';

/// The main home screen of the application.
///
/// This screen serves as the central hub, featuring an animated scene
/// and a scrollable grid of content sections like the story library,
/// featured stories, and the parent zone.
class HomeScreen extends ConsumerWidget {
  const HomeScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    AppLogger.debug('[COMPONENT_LOAD] Full path: lib/features/story_library/presentation/screens/home_screen.dart - HomeScreen');
    final theme = Theme.of(context);
    final screenSize = MediaQuery.of(context).size;
    final selectedProfile = ref.watch(selectedProfileProvider);
    final currentSessionAsync = ref.watch(currentSessionProvider);
    final l10n = AppLocalizations.of(context);

    // Responsive expanded height based on screen size
    final expandedHeight = screenSize.height * 0.3; // 30% of screen height

    return PopScope(
      canPop: false,
      onPopInvokedWithResult: (bool didPop, Object? result) async {
        if (didPop) return;

        // Show exit confirmation dialog for root screen
        final shouldExit = await _showExitConfirmationDialog(context);
        if (shouldExit == true) {
          // Exit the app
          SystemNavigator.pop();
        }
      },
      child: Scaffold(
        body: CustomScrollView(
          slivers: [
            SliverAppBar(
              expandedHeight: expandedHeight.clamp(150.0, 300.0), // Min 150, max 300
              floating: false,
              pinned: true,
              actions: [
                // Profile switcher button
                if (selectedProfile != null)
                  Padding(
                    padding: const EdgeInsets.only(right: 8),
                    child: IconButton(
                      onPressed: () => context.go('/profile_selection'),
                      tooltip: 'Switch Profile',
                      icon: Container(
                        width: 32,
                        height: 32,
                        decoration: BoxDecoration(
                          color: selectedProfile.avatarColor,
                          shape: BoxShape.circle,
                          border: Border.all(color: Colors.white, width: 2),
                        ),
                        child: const Icon(
                          Icons.child_care,
                          size: 18,
                          color: Colors.white,
                        ),
                      ),
                    ),
                  )
                else
                  Padding(
                    padding: const EdgeInsets.only(right: 8),
                    child: IconButton(
                      onPressed: () => context.go('/profile_selection'),
                      tooltip: 'Select Profile',
                      icon: const Icon(Icons.person_add),
                    ),
                  ),
              ],
              flexibleSpace: FlexibleSpaceBar(
                title: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Choice: Once Upon A Time',
                      style: theme.textTheme.titleLarge?.copyWith(
                        color: theme.colorScheme.onPrimary,
                        fontWeight: FontWeight.bold,
                        shadows: [
                          Shadow(
                            blurRadius: 4.0,
                            color: Colors.black.withValues(alpha: 0.5),
                            offset: const Offset(1.0, 1.0),
                          ),
                        ],
                      ),
                    ),
                    // Show personalized greeting based on authentication and profile state
                    currentSessionAsync.when(
                      data: (session) {
                        if (session != null && selectedProfile != null) {
                          // User is logged in and has selected a child profile
                          return Text(
                            '${l10n?.welcomeBackUser(session.displayName) ?? 'Welcome back!'} ${l10n?.childProfileGreeting(selectedProfile.name) ?? 'Ready to play!'}',
                            style: theme.textTheme.bodyMedium?.copyWith(
                              color: theme.colorScheme.onPrimary.withValues(alpha: 0.9),
                              shadows: [
                                Shadow(
                                  blurRadius: 2.0,
                                  color: Colors.black.withValues(alpha: 0.5),
                                  offset: const Offset(0.5, 0.5),
                                ),
                              ],
                            ),
                          );
                        } else if (session != null) {
                          // User is logged in but no child profile selected
                          return Text(
                            '${l10n?.welcomeBackUser(session.displayName) ?? 'Welcome back!'} Please select a child profile to continue.',
                            style: theme.textTheme.bodyMedium?.copyWith(
                              color: theme.colorScheme.onPrimary.withValues(alpha: 0.9),
                              shadows: [
                                Shadow(
                                  blurRadius: 2.0,
                                  color: Colors.black.withValues(alpha: 0.5),
                                  offset: const Offset(0.5, 0.5),
                                ),
                              ],
                            ),
                          );
                        } else if (selectedProfile != null) {
                          // No user session but child profile selected (guest mode)
                          return Text(
                            l10n?.welcomeChild(selectedProfile.name) ?? 'Welcome back, ${selectedProfile.name}!',
                            style: theme.textTheme.bodyMedium?.copyWith(
                              color: theme.colorScheme.onPrimary.withValues(alpha: 0.9),
                              shadows: [
                                Shadow(
                                  blurRadius: 2.0,
                                  color: Colors.black.withValues(alpha: 0.5),
                                  offset: const Offset(0.5, 0.5),
                                ),
                              ],
                            ),
                          );
                        } else {
                          // No session and no profile
                          return Text(
                            'Welcome! Please select a child profile to get started.',
                            style: theme.textTheme.bodyMedium?.copyWith(
                              color: theme.colorScheme.onPrimary.withValues(alpha: 0.9),
                              shadows: [
                                Shadow(
                                  blurRadius: 2.0,
                                  color: Colors.black.withValues(alpha: 0.5),
                                  offset: const Offset(0.5, 0.5),
                                ),
                              ],
                            ),
                          );
                        }
                      },
                      loading: () => const SizedBox.shrink(),
                      error: (_, __) => selectedProfile != null
                          ? Text(
                              'Welcome back, ${selectedProfile.name}!',
                              style: theme.textTheme.bodyMedium?.copyWith(
                                color: theme.colorScheme.onPrimary.withValues(alpha: 0.9),
                                shadows: [
                                  Shadow(
                                    blurRadius: 2.0,
                                    color: Colors.black.withValues(alpha: 0.5),
                                    offset: const Offset(0.5, 0.5),
                                  ),
                                ],
                              ),
                            )
                          : const SizedBox.shrink(),
                    ),
                  ],
                ),
                background: const AnimatedSceneWidget(), // Our animated scene goes here
              ),
            ),
            const SliverToBoxAdapter(
              child: HomeScreenGridArea(), // The grid area with all our sections
            ),
          ],
        ),
      ),
    );
  }

  /// Show exit confirmation dialog for the home screen
  Future<bool?> _showExitConfirmationDialog(BuildContext context) {
    return showDialog<bool>(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Exit App'),
          content: const Text('Are you sure you want to exit the app?'),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: const Text('Cancel'),
            ),
            ElevatedButton(
              onPressed: () => Navigator.of(context).pop(true),
              child: const Text('Exit'),
            ),
          ],
        );
      },
    );
  }
}
