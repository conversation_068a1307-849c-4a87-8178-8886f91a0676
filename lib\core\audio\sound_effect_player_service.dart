import 'package:just_audio/just_audio.dart';

/// Types of UI sounds
enum UISoundType {
  buttonTap,
  choiceSelect,
  pageTransition,
  success,
  error,
  discovery,
}

/// Service for playing sound effects and UI sounds
class SoundEffectPlayerService {
  final Map<String, AudioPlayer> _players = {};
  final Map<UISoundType, String> _uiSoundPaths = {
    UISoundType.buttonTap: 'assets/audio/ui/button_tap.mp3',
    UISoundType.choiceSelect: 'assets/audio/ui/choice_select.mp3',
    UISoundType.pageTransition: 'assets/audio/ui/page_transition.mp3',
    UISoundType.success: 'assets/audio/ui/success.mp3',
    UISoundType.error: 'assets/audio/ui/error.mp3',
    UISoundType.discovery: 'assets/audio/ui/discovery.mp3',
  };

  double _volume = 1.0;
  bool _isEnabled = true;

  /// Play a sound effect from an asset path
  Future<void> playSound(String assetPath) async {
    if (!_isEnabled) return;

    try {
      // Get or create an audio player for this sound
      AudioPlayer player = _players[assetPath] ??= AudioPlayer();

      // Load and play the sound
      await player.setAsset(assetPath);
      await player.setVolume(_volume);
      await player.play();
    } catch (e) {
      print('Error playing sound $assetPath: $e');
    }
  }

  /// Play a UI sound by type
  Future<void> playUISound(UISoundType type) async {
    if (!_isEnabled) return;

    final assetPath = _uiSoundPaths[type];
    if (assetPath != null) {
      await playSound(assetPath);
    }
  }

  /// Play a sound from a URL (for story sound effects)
  Future<void> playSoundFromUrl(String url, {double? volume}) async {
    if (!_isEnabled) return;

    try {
      // Create a new player for URL-based sounds
      final player = AudioPlayer();
      await player.setUrl(url);
      await player.setVolume(volume ?? _volume);
      await player.play();

      // Clean up after playing
      player.playerStateStream.listen((state) {
        if (state.processingState == ProcessingState.completed) {
          player.dispose();
        }
      });
    } catch (e) {
      print('Error playing sound from URL $url: $e');
    }
  }

  /// Set the volume for sound effects (0.0 to 1.0)
  void setVolume(double volume) {
    _volume = volume.clamp(0.0, 1.0);
  }

  /// Enable or disable sound effects
  void setEnabled(bool enabled) {
    _isEnabled = enabled;
    if (!enabled) {
      stopAllSounds();
    }
  }

  /// Stop all currently playing sounds
  void stopAllSounds() {
    for (final player in _players.values) {
      try {
        player.stop();
      } catch (e) {
        print('Error stopping sound: $e');
      }
    }
  }

  /// Preload UI sounds for better performance
  Future<void> preloadUISounds() async {
    for (final entry in _uiSoundPaths.entries) {
      try {
        final player = AudioPlayer();
        await player.setAsset(entry.value);
        _players[entry.value] = player;
      } catch (e) {
        print('Error preloading UI sound ${entry.value}: $e');
      }
    }
  }

  /// Dispose of all audio players
  void dispose() {
    for (final player in _players.values) {
      try {
        player.dispose();
      } catch (e) {
        print('Error disposing audio player: $e');
      }
    }
    _players.clear();
  }

  /// Get current volume
  double get volume => _volume;

  /// Check if sound effects are enabled
  bool get isEnabled => _isEnabled;
}
