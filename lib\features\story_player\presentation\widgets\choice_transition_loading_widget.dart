import 'package:flutter/material.dart';
import 'package:choice_once_upon_a_time/core/utils/app_logger.dart';

/// Widget that displays a loading animation when processing choice selections
class ChoiceTransitionLoadingWidget extends StatefulWidget {
  final String selectedChoice;
  final String nextSceneId;
  final Duration duration;
  final VoidCallback? onComplete;

  const ChoiceTransitionLoadingWidget({
    super.key,
    required this.selectedChoice,
    required this.nextSceneId,
    this.duration = const Duration(milliseconds: 1200),
    this.onComplete,
  });

  @override
  State<ChoiceTransitionLoadingWidget> createState() => _ChoiceTransitionLoadingWidgetState();
}

class _ChoiceTransitionLoadingWidgetState extends State<ChoiceTransitionLoadingWidget>
    with TickerProviderStateMixin {
  late final AnimationController _fadeController;
  late final AnimationController _slideController;
  late final AnimationController _pulseController;
  
  late final Animation<double> _fadeAnimation;
  late final Animation<Offset> _slideAnimation;
  late final Animation<double> _pulseAnimation;

  @override
  void initState() {
    super.initState();
    AppLogger.debug('[COMPONENT_LOAD] Full path: lib/features/story_player/presentation/widgets/choice_transition_loading_widget.dart - ChoiceTransitionLoadingWidget');
    
    _initializeAnimations();
    _startChoiceSequence();
  }

  void _initializeAnimations() {
    // Fade animation for the entire widget
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 400),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _fadeController,
      curve: Curves.easeInOut,
    ));

    // Slide animation for the choice confirmation
    _slideController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );
    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 1),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _slideController,
      curve: Curves.elasticOut,
    ));

    // Pulse animation for the loading indicator
    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    _pulseAnimation = Tween<double>(
      begin: 0.8,
      end: 1.2,
    ).animate(CurvedAnimation(
      parent: _pulseController,
      curve: Curves.easeInOut,
    ));
  }

  Future<void> _startChoiceSequence() async {
    AppLogger.debug('[CHOICE_TRANSITION] Starting choice processing: ${widget.selectedChoice} → ${widget.nextSceneId}');
    
    // Start animations
    _fadeController.forward();
    await Future.delayed(const Duration(milliseconds: 200));
    
    _slideController.forward();
    _pulseController.repeat(reverse: true);

    // Wait for the specified duration
    await Future.delayed(widget.duration);

    // Complete the loading
    await _completeChoiceProcessing();
  }

  Future<void> _completeChoiceProcessing() async {
    AppLogger.debug('[CHOICE_TRANSITION] Completing choice processing');
    
    // Stop pulse animation
    _pulseController.stop();
    
    // Slide out and fade out
    await Future.wait([
      _slideController.reverse(),
      _fadeController.reverse(),
    ]);
    
    // Notify completion
    widget.onComplete?.call();
  }

  @override
  void dispose() {
    _fadeController.dispose();
    _slideController.dispose();
    _pulseController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final screenSize = MediaQuery.of(context).size;
    final isSmallScreen = screenSize.width < 600;

    return FadeTransition(
      opacity: _fadeAnimation,
      child: Container(
        color: Colors.black.withValues(alpha: 0.7),
        child: Center(
          child: SlideTransition(
            position: _slideAnimation,
            child: Container(
              padding: EdgeInsets.all(isSmallScreen ? 20 : 28),
              margin: EdgeInsets.symmetric(
                horizontal: screenSize.width * 0.1,
              ),
              decoration: BoxDecoration(
                color: theme.colorScheme.surface,
                borderRadius: BorderRadius.circular(16),
                border: Border.all(
                  color: theme.colorScheme.primary.withValues(alpha: 0.3),
                  width: 2,
                ),
                boxShadow: [
                  BoxShadow(
                    color: theme.colorScheme.primary.withValues(alpha: 0.2),
                    blurRadius: 20,
                    offset: const Offset(0, 8),
                  ),
                ],
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // Choice confirmation icon
                  ScaleTransition(
                    scale: _pulseAnimation,
                    child: Container(
                      width: isSmallScreen ? 50 : 60,
                      height: isSmallScreen ? 50 : 60,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        color: theme.colorScheme.primary,
                        boxShadow: [
                          BoxShadow(
                            color: theme.colorScheme.primary.withValues(alpha: 0.3),
                            blurRadius: 10,
                            spreadRadius: 2,
                          ),
                        ],
                      ),
                      child: Icon(
                        Icons.check,
                        color: theme.colorScheme.onPrimary,
                        size: isSmallScreen ? 25 : 30,
                      ),
                    ),
                  ),
                  
                  SizedBox(height: isSmallScreen ? 16 : 20),
                  
                  // Choice confirmation text
                  Text(
                    'You chose:',
                    style: theme.textTheme.bodyMedium?.copyWith(
                      color: theme.colorScheme.onSurfaceVariant,
                      fontSize: isSmallScreen ? 14 : 16,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  
                  SizedBox(height: isSmallScreen ? 8 : 12),
                  
                  // Selected choice
                  Container(
                    padding: EdgeInsets.symmetric(
                      horizontal: isSmallScreen ? 12 : 16,
                      vertical: isSmallScreen ? 8 : 10,
                    ),
                    decoration: BoxDecoration(
                      color: theme.colorScheme.primaryContainer,
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Text(
                      widget.selectedChoice,
                      style: theme.textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                        color: theme.colorScheme.onPrimaryContainer,
                        fontSize: isSmallScreen ? 16 : 18,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
                  
                  SizedBox(height: isSmallScreen ? 16 : 20),
                  
                  // Loading indicator
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      SizedBox(
                        width: isSmallScreen ? 16 : 20,
                        height: isSmallScreen ? 16 : 20,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: AlwaysStoppedAnimation<Color>(
                            theme.colorScheme.primary,
                          ),
                        ),
                      ),
                      SizedBox(width: isSmallScreen ? 8 : 12),
                      Text(
                        'Loading next scene...',
                        style: theme.textTheme.bodyMedium?.copyWith(
                          color: theme.colorScheme.onSurfaceVariant,
                          fontSize: isSmallScreen ? 14 : 16,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
