import 'dart:convert';
import 'dart:io';
import 'package:path_provider/path_provider.dart';
import 'package:choice_once_upon_a_time/models/narrator_profile_model.dart';
import 'package:choice_once_upon_a_time/models/enhanced_story_model.dart' show VoiceModel;
import 'package:choice_once_upon_a_time/core/utils/app_logger.dart';

/// Service for managing narrator profile storage and retrieval
class NarratorStorageService {
  static const String _narratorsFileName = 'narrator_profiles.json';
  Directory? _storageDirectory;
  List<NarratorProfileModel> _cachedNarrators = [];
  bool _isInitialized = false;

  NarratorStorageService() {
    AppLogger.debug('[COMPONENT_LOAD] Full path: lib/core/services/narrator_storage_service.dart - NarratorStorageService');
  }

  /// Initialize the storage service
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      AppLogger.debug('[NARRATOR_STORAGE] Initializing narrator storage service');
      _storageDirectory = await getApplicationSupportDirectory();
      
      final narratorsDir = Directory('${_storageDirectory!.path}/narrators');
      if (!await narratorsDir.exists()) {
        await narratorsDir.create(recursive: true);
        AppLogger.debug('[NARRATOR_STORAGE] Created narrators directory: ${narratorsDir.path}');
      }

      await _loadNarrators();
      await _ensureDefaultNarrators();
      
      _isInitialized = true;
      AppLogger.debug('[NARRATOR_STORAGE] Narrator storage service initialized successfully');
    } catch (e) {
      AppLogger.error('[NARRATOR_STORAGE] Failed to initialize narrator storage service', e);
      throw Exception('Failed to initialize narrator storage: $e');
    }
  }

  /// Get all narrator profiles
  Future<List<NarratorProfileModel>> getAllNarrators() async {
    await _ensureInitialized();
    AppLogger.debug('[NARRATOR_STORAGE] Retrieved ${_cachedNarrators.length} narrator profiles');
    return List.from(_cachedNarrators);
  }

  /// Get narrator by ID
  Future<NarratorProfileModel?> getNarratorById(String id) async {
    await _ensureInitialized();
    try {
      final narrator = _cachedNarrators.firstWhere((n) => n.id == id);
      AppLogger.debug('[NARRATOR_STORAGE] Found narrator with ID: $id');
      return narrator;
    } catch (e) {
      AppLogger.debug('[NARRATOR_STORAGE] Narrator not found with ID: $id');
      return null;
    }
  }

  /// Save a narrator profile
  Future<void> saveNarrator(NarratorProfileModel narrator) async {
    await _ensureInitialized();
    
    try {
      AppLogger.debug('[NARRATOR_STORAGE] Saving narrator: ${narrator.name} (ID: ${narrator.id})');
      
      // Validate narrator before saving
      if (!narrator.isValid) {
        throw Exception('Invalid narrator profile: ${narrator.name}');
      }

      // Update or add narrator
      final existingIndex = _cachedNarrators.indexWhere((n) => n.id == narrator.id);
      if (existingIndex >= 0) {
        _cachedNarrators[existingIndex] = narrator.copyWith(updatedAt: DateTime.now());
        AppLogger.debug('[NARRATOR_STORAGE] Updated existing narrator: ${narrator.name}');
      } else {
        _cachedNarrators.add(narrator);
        AppLogger.debug('[NARRATOR_STORAGE] Added new narrator: ${narrator.name}');
      }

      await _saveNarratorsToFile();
      AppLogger.debug('[NARRATOR_STORAGE] Successfully saved narrator: ${narrator.name}');
    } catch (e) {
      AppLogger.error('[NARRATOR_STORAGE] Failed to save narrator: ${narrator.name}', e);
      throw Exception('Failed to save narrator: $e');
    }
  }

  /// Delete a narrator profile
  Future<void> deleteNarrator(String id) async {
    await _ensureInitialized();
    
    try {
      AppLogger.debug('[NARRATOR_STORAGE] Deleting narrator with ID: $id');
      
      final narrator = await getNarratorById(id);
      if (narrator == null) {
        throw Exception('Narrator not found with ID: $id');
      }

      if (narrator.isDefault) {
        throw Exception('Cannot delete default narrator');
      }

      _cachedNarrators.removeWhere((n) => n.id == id);
      await _saveNarratorsToFile();
      
      AppLogger.debug('[NARRATOR_STORAGE] Successfully deleted narrator: ${narrator.name}');
    } catch (e) {
      AppLogger.error('[NARRATOR_STORAGE] Failed to delete narrator with ID: $id', e);
      throw Exception('Failed to delete narrator: $e');
    }
  }

  /// Get narrators by category
  Future<List<NarratorProfileModel>> getNarratorsByCategory(NarratorCategory category) async {
    await _ensureInitialized();
    final narrators = _cachedNarrators.where((n) => n.category == category).toList();
    AppLogger.debug('[NARRATOR_STORAGE] Found ${narrators.length} narrators in category: ${category.displayName}');
    return narrators;
  }

  /// Get default narrator
  Future<NarratorProfileModel?> getDefaultNarrator() async {
    await _ensureInitialized();
    try {
      final defaultNarrator = _cachedNarrators.firstWhere((n) => n.isDefault);
      AppLogger.debug('[NARRATOR_STORAGE] Found default narrator: ${defaultNarrator.name}');
      return defaultNarrator;
    } catch (e) {
      AppLogger.debug('[NARRATOR_STORAGE] No default narrator found');
      return null;
    }
  }

  /// Set default narrator
  Future<void> setDefaultNarrator(String id) async {
    await _ensureInitialized();
    
    try {
      AppLogger.debug('[NARRATOR_STORAGE] Setting default narrator with ID: $id');
      
      // Remove default flag from all narrators
      for (int i = 0; i < _cachedNarrators.length; i++) {
        if (_cachedNarrators[i].isDefault) {
          _cachedNarrators[i] = _cachedNarrators[i].copyWith(isDefault: false);
        }
      }

      // Set new default
      final index = _cachedNarrators.indexWhere((n) => n.id == id);
      if (index >= 0) {
        _cachedNarrators[index] = _cachedNarrators[index].copyWith(isDefault: true);
        await _saveNarratorsToFile();
        AppLogger.debug('[NARRATOR_STORAGE] Successfully set default narrator: ${_cachedNarrators[index].name}');
      } else {
        throw Exception('Narrator not found with ID: $id');
      }
    } catch (e) {
      AppLogger.error('[NARRATOR_STORAGE] Failed to set default narrator with ID: $id', e);
      throw Exception('Failed to set default narrator: $e');
    }
  }

  /// Private methods

  Future<void> _ensureInitialized() async {
    if (!_isInitialized) {
      await initialize();
    }
  }

  Future<void> _loadNarrators() async {
    try {
      final file = File('${_storageDirectory!.path}/narrators/$_narratorsFileName');
      
      if (!await file.exists()) {
        AppLogger.debug('[NARRATOR_STORAGE] Narrators file does not exist, starting with empty list');
        _cachedNarrators = [];
        return;
      }

      final jsonString = await file.readAsString();
      final List<dynamic> jsonList = jsonDecode(jsonString);
      
      _cachedNarrators = jsonList
          .map((json) => NarratorProfileModel.fromJson(json as Map<String, dynamic>))
          .toList();
      
      AppLogger.debug('[NARRATOR_STORAGE] Loaded ${_cachedNarrators.length} narrator profiles from file');
    } catch (e) {
      AppLogger.error('[NARRATOR_STORAGE] Failed to load narrators from file', e);
      _cachedNarrators = [];
    }
  }

  Future<void> _saveNarratorsToFile() async {
    try {
      final file = File('${_storageDirectory!.path}/narrators/$_narratorsFileName');
      final jsonList = _cachedNarrators.map((narrator) => narrator.toJson()).toList();
      final jsonString = jsonEncode(jsonList);
      
      await file.writeAsString(jsonString);
      AppLogger.debug('[NARRATOR_STORAGE] Saved ${_cachedNarrators.length} narrator profiles to file');
    } catch (e) {
      AppLogger.error('[NARRATOR_STORAGE] Failed to save narrators to file', e);
      throw Exception('Failed to save narrators to file: $e');
    }
  }

  Future<void> _ensureDefaultNarrators() async {
    if (_cachedNarrators.isEmpty) {
      AppLogger.debug('[NARRATOR_STORAGE] Creating default narrator profiles');
      await _createDefaultNarrators();
    }
  }

  Future<void> _createDefaultNarrators() async {
    final defaultNarrators = [
      NarratorProfileModel(
        id: 'default_grandparent',
        name: 'Grandma Rose',
        description: 'A warm, loving grandmother who tells stories with gentle wisdom',
        voice: const VoiceModel(
          name: 'en-US-Wavenet-A',
          pitch: 0.9,
          rate: 0.8,
          volume: 0.9,
        ),
        category: NarratorCategory.grandparent,
        gender: NarratorGender.female,
        ageRange: NarratorAgeRange.elderly,
        personalities: [NarratorPersonality.gentle, NarratorPersonality.wise],
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        isDefault: true,
      ),
      NarratorProfileModel(
        id: 'default_teacher',
        name: 'Mr. Story',
        description: 'An encouraging teacher who makes learning fun through stories',
        voice: const VoiceModel(
          name: 'en-US-Wavenet-B',
          pitch: 1.0,
          rate: 1.0,
          volume: 1.0,
        ),
        category: NarratorCategory.teacher,
        gender: NarratorGender.male,
        ageRange: NarratorAgeRange.adult,
        personalities: [NarratorPersonality.cheerful, NarratorPersonality.energetic],
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      ),
    ];

    for (final narrator in defaultNarrators) {
      _cachedNarrators.add(narrator);
    }

    await _saveNarratorsToFile();
    AppLogger.debug('[NARRATOR_STORAGE] Created ${defaultNarrators.length} default narrator profiles');
  }
}
