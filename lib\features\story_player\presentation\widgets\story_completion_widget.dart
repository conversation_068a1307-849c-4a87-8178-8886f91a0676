import 'package:flutter/material.dart';
import 'package:choice_once_upon_a_time/models/enhanced_story_model.dart';
import 'package:choice_once_upon_a_time/core/services/story_rewards_service.dart';

/// Widget that displays story completion summary and rewards
class StoryCompletionWidget extends StatefulWidget {
  final EnhancedStoryModel story;
  final List<String> visitedScenes;
  final VoidCallback onRestart;
  final VoidCallback onExit;
  final StoryRewardsService rewardsService;

  const StoryCompletionWidget({
    super.key,
    required this.story,
    required this.visitedScenes,
    required this.onRestart,
    required this.onExit,
    required this.rewardsService,
  });

  @override
  State<StoryCompletionWidget> createState() => _StoryCompletionWidgetState();
}

class _StoryCompletionWidgetState extends State<StoryCompletionWidget>
    with TickerProviderStateMixin {
  late final AnimationController _celebrationController;
  late final AnimationController _rewardsController;
  late final AnimationController _summaryController;
  
  late final Animation<double> _celebrationAnimation;
  late final Animation<double> _rewardsAnimation;
  late final Animation<Offset> _summarySlideAnimation;

  List<RewardEarned> _earnedRewards = [];
  bool _showRewards = false;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _loadRewards();
    _startCelebrationAnimation();
  }

  void _initializeAnimations() {
    _celebrationController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );

    _rewardsController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _summaryController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );

    _celebrationAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _celebrationController,
      curve: Curves.elasticOut,
    ));

    _rewardsAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _rewardsController,
      curve: Curves.easeOutBack,
    ));

    _summarySlideAnimation = Tween<Offset>(
      begin: const Offset(0.0, 1.0),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _summaryController,
      curve: Curves.easeOutCubic,
    ));
  }

  Future<void> _startCelebrationAnimation() async {
    await _celebrationController.forward();
    await Future.delayed(const Duration(milliseconds: 500));
    await _summaryController.forward();
    await Future.delayed(const Duration(milliseconds: 300));
    _showRewardsWithAnimation();
  }

  Future<void> _showRewardsWithAnimation() async {
    setState(() {
      _showRewards = true;
    });
    await _rewardsController.forward();
  }

  void _loadRewards() {
    // Get rewards for this story
    final storyRewards = widget.rewardsService.getStoryRewards(widget.story.storyId);
    // For now, create mock rewards - in a real implementation, these would come from the service
    _earnedRewards = [
      RewardEarned(
        id: 'completion_${widget.story.storyId}',
        storyId: widget.story.storyId,
        type: 'completion',
        title: 'Story Complete!',
        description: 'You finished "${widget.story.title}"',
        earnedAt: DateTime.now(),
      ),
      RewardEarned(
        id: 'moral_${widget.story.moral}',
        storyId: widget.story.storyId,
        type: 'moral',
        title: '${widget.story.moral} Champion!',
        description: 'You learned about ${widget.story.moral}',
        earnedAt: DateTime.now(),
      ),
    ];
  }

  int get _totalScenes => widget.story.scenes.length;
  int get _visitedScenesCount => widget.visitedScenes.length;
  double get _completionPercentage => _visitedScenesCount / _totalScenes;

  @override
  void dispose() {
    _celebrationController.dispose();
    _rewardsController.dispose();
    _summaryController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final screenSize = MediaQuery.of(context).size;
    final isSmallScreen = screenSize.width < 600;

    return Container(
      width: double.infinity,
      height: double.infinity,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            theme.colorScheme.primary.withOpacity(0.1),
            theme.colorScheme.surface,
            theme.colorScheme.secondary.withOpacity(0.1),
          ],
        ),
      ),
      child: SafeArea(
        child: SingleChildScrollView(
          padding: EdgeInsets.all(isSmallScreen ? 16.0 : 24.0),
          child: Column(
            children: [
              // Celebration header
              AnimatedBuilder(
                animation: _celebrationAnimation,
                builder: (context, child) {
                  return Transform.scale(
                    scale: _celebrationAnimation.value,
                    child: Opacity(
                      opacity: _celebrationAnimation.value,
                      child: Column(
                        children: [
                          Icon(
                            Icons.celebration,
                            size: isSmallScreen ? 64 : 80,
                            color: theme.colorScheme.primary,
                          ),
                          const SizedBox(height: 16),
                          Text(
                            'Congratulations!',
                            style: theme.textTheme.headlineMedium?.copyWith(
                              fontWeight: FontWeight.bold,
                              color: theme.colorScheme.primary,
                              fontSize: isSmallScreen ? 28 : 32,
                            ),
                          ),
                          const SizedBox(height: 8),
                          Text(
                            'You completed "${widget.story.title}"',
                            style: theme.textTheme.titleMedium?.copyWith(
                              color: theme.colorScheme.onSurfaceVariant,
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ],
                      ),
                    ),
                  );
                },
              ),

              const SizedBox(height: 32),

              // Story summary
              SlideTransition(
                position: _summarySlideAnimation,
                child: Card(
                  elevation: 4,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: Padding(
                    padding: const EdgeInsets.all(20),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Your Journey',
                          style: theme.textTheme.titleLarge?.copyWith(
                            fontWeight: FontWeight.bold,
                            color: theme.colorScheme.primary,
                          ),
                        ),
                        const SizedBox(height: 16),
                        
                        // Completion progress
                        Row(
                          children: [
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    'Scenes Explored',
                                    style: theme.textTheme.bodyMedium?.copyWith(
                                      color: theme.colorScheme.onSurfaceVariant,
                                    ),
                                  ),
                                  const SizedBox(height: 4),
                                  LinearProgressIndicator(
                                    value: _completionPercentage,
                                    backgroundColor: Colors.grey[300],
                                    valueColor: AlwaysStoppedAnimation<Color>(
                                      theme.colorScheme.primary,
                                    ),
                                  ),
                                  const SizedBox(height: 4),
                                  Text(
                                    '$_visitedScenesCount of $_totalScenes scenes',
                                    style: theme.textTheme.bodySmall,
                                  ),
                                ],
                              ),
                            ),
                            const SizedBox(width: 16),
                            CircularProgressIndicator(
                              value: _completionPercentage,
                              backgroundColor: Colors.grey[300],
                              valueColor: AlwaysStoppedAnimation<Color>(
                                theme.colorScheme.primary,
                              ),
                            ),
                          ],
                        ),
                        
                        const SizedBox(height: 16),
                        
                        // Moral lesson
                        Container(
                          padding: const EdgeInsets.all(16),
                          decoration: BoxDecoration(
                            color: theme.colorScheme.primaryContainer.withOpacity(0.5),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Row(
                            children: [
                              Icon(
                                Icons.lightbulb,
                                color: theme.colorScheme.primary,
                              ),
                              const SizedBox(width: 12),
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      'Lesson Learned',
                                      style: theme.textTheme.labelMedium?.copyWith(
                                        color: theme.colorScheme.primary,
                                        fontWeight: FontWeight.w600,
                                      ),
                                    ),
                                    Text(
                                      'The importance of ${widget.story.moral}',
                                      style: theme.textTheme.bodyMedium?.copyWith(
                                        fontWeight: FontWeight.w500,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),

              const SizedBox(height: 24),

              // Rewards section
              if (_showRewards)
                AnimatedBuilder(
                  animation: _rewardsAnimation,
                  builder: (context, child) {
                    return Transform.scale(
                      scale: _rewardsAnimation.value,
                      child: Opacity(
                        opacity: _rewardsAnimation.value,
                        child: Card(
                          elevation: 4,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(16),
                          ),
                          child: Padding(
                            padding: const EdgeInsets.all(20),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Row(
                                  children: [
                                    const Icon(
                                      Icons.emoji_events,
                                      color: Colors.amber,
                                      size: 24,
                                    ),
                                    const SizedBox(width: 8),
                                    Text(
                                      'Rewards Earned',
                                      style: theme.textTheme.titleLarge?.copyWith(
                                        fontWeight: FontWeight.bold,
                                        color: theme.colorScheme.primary,
                                      ),
                                    ),
                                  ],
                                ),
                                const SizedBox(height: 16),
                                
                                ..._earnedRewards.map((reward) => Container(
                                  margin: const EdgeInsets.only(bottom: 12),
                                  padding: const EdgeInsets.all(12),
                                  decoration: BoxDecoration(
                                    color: theme.colorScheme.secondaryContainer.withOpacity(0.5),
                                    borderRadius: BorderRadius.circular(8),
                                  ),
                                  child: Row(
                                    children: [
                                      Container(
                                        width: 40,
                                        height: 40,
                                        decoration: const BoxDecoration(
                                          color: Colors.amber,
                                          shape: BoxShape.circle,
                                        ),
                                        child: const Icon(
                                          Icons.star,
                                          color: Colors.white,
                                          size: 20,
                                        ),
                                      ),
                                      const SizedBox(width: 12),
                                      Expanded(
                                        child: Column(
                                          crossAxisAlignment: CrossAxisAlignment.start,
                                          children: [
                                            Text(
                                              reward.title,
                                              style: theme.textTheme.titleSmall?.copyWith(
                                                fontWeight: FontWeight.bold,
                                              ),
                                            ),
                                            Text(
                                              reward.description,
                                              style: theme.textTheme.bodySmall?.copyWith(
                                                color: theme.colorScheme.onSurfaceVariant,
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                    ],
                                  ),
                                )),
                              ],
                            ),
                          ),
                        ),
                      ),
                    );
                  },
                ),

              const SizedBox(height: 32),

              // Action buttons
              SlideTransition(
                position: _summarySlideAnimation,
                child: Column(
                  children: [
                    // Replay encouragement
                    if (_completionPercentage < 1.0)
                      Container(
                        padding: const EdgeInsets.all(16),
                        margin: const EdgeInsets.only(bottom: 16),
                        decoration: BoxDecoration(
                          color: theme.colorScheme.tertiaryContainer.withOpacity(0.5),
                          borderRadius: BorderRadius.circular(12),
                          border: Border.all(
                            color: theme.colorScheme.tertiary.withOpacity(0.3),
                          ),
                        ),
                        child: Row(
                          children: [
                            Icon(
                              Icons.explore,
                              color: theme.colorScheme.tertiary,
                            ),
                            const SizedBox(width: 12),
                            Expanded(
                              child: Text(
                                'Try different choices to explore more of the story!',
                                style: theme.textTheme.bodyMedium?.copyWith(
                                  color: theme.colorScheme.onTertiaryContainer,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),

                    // Action buttons
                    Row(
                      children: [
                        Expanded(
                          child: OutlinedButton(
                            onPressed: widget.onRestart,
                            style: OutlinedButton.styleFrom(
                              padding: const EdgeInsets.symmetric(vertical: 16),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(12),
                              ),
                            ),
                            child: const Text('Play Again'),
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: ElevatedButton(
                            onPressed: widget.onExit,
                            style: ElevatedButton.styleFrom(
                              padding: const EdgeInsets.symmetric(vertical: 16),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(12),
                              ),
                            ),
                            child: const Text('More Stories'),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
