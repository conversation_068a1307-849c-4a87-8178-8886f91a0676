import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:choice_once_upon_a_time/models/enhanced_story_model.dart';
// OLD REPOSITORY - TEMPORARILY DISABLED
// import 'package:choice_once_upon_a_time/features/story_library/data/enhanced_story_repository.dart';
// NEW SERVICE
import 'package:choice_once_upon_a_time/core/services/new_story_service.dart';
import 'package:choice_once_upon_a_time/core/services/story_narration_service_interface.dart';
import 'package:choice_once_upon_a_time/core/services/enhanced_story_narration_service.dart';
import 'package:choice_once_upon_a_time/shared_widgets/loading_indicator.dart';
import 'package:choice_once_upon_a_time/features/story_player/presentation/widgets/character_profiles_widget.dart';
import 'package:choice_once_upon_a_time/core/utils/app_logger.dart';

/// Dedicated screen for character introduction phase
/// Handles character profiles display and setup before story begins
class CharacterIntroductionScreen extends StatefulWidget {
  final String storyId;

  const CharacterIntroductionScreen({
    super.key,
    required this.storyId,
  });

  @override
  State<CharacterIntroductionScreen> createState() => _CharacterIntroductionScreenState();
}

class _CharacterIntroductionScreenState extends State<CharacterIntroductionScreen>
    with TickerProviderStateMixin {
  // Services
  late final NewStoryService _storyService;
  late final IStoryNarrationService _narrationService;

  // Animation controllers
  late final AnimationController _fadeController;
  late final Animation<double> _fadeAnimation;

  // State
  EnhancedStoryModel? _story;
  bool _isLoading = true;
  String? _error;

  @override
  void initState() {
    super.initState();
    _initializeServices();
    _initializeAnimations();
    _loadStory();
  }

  /// Initialize services
  void _initializeServices() {
    AppLogger.debug('[CHAR_INTRO] Initializing services');
    _storyService = NewStoryService();
    _narrationService = EnhancedStoryNarrationService();
  }

  /// Initialize animations
  void _initializeAnimations() {
    AppLogger.debug('[CHAR_INTRO] Initializing animations');
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _fadeController,
      curve: Curves.easeInOut,
    ));
  }

  /// Load the story and initialize narration service
  Future<void> _loadStory() async {
    AppLogger.debug('[CHAR_INTRO] Function: _loadStory called with parameters: storyId=${widget.storyId}');

    try {
      setState(() {
        _isLoading = true;
        _error = null;
      });

      // Initialize narration service
      await _narrationService.initialize();

      // Load the story
      final story = await _storyService.loadStory(widget.storyId);

      if (story == null) {
        throw Exception('Story not found: ${widget.storyId}');
      }

      // Log the story JSON asset path
      final storyJsonPath = 'assets/stories/${widget.storyId}/story.json';
      AppLogger.debug('[CHAR_INTRO] Asset loaded: $storyJsonPath');

      setState(() {
        _story = story;
        _isLoading = false;
      });

      // Start fade-in animation
      _fadeController.forward();

      AppLogger.debug('[CHAR_INTRO] Function: _loadStory completed successfully');
    } catch (e) {
      AppLogger.error('[CHAR_INTRO] Function: _loadStory failed', e);
      setState(() {
        _error = 'Failed to load story: $e';
        _isLoading = false;
      });
    }
  }

  /// Navigate to story playback screen
  Future<void> _navigateToStoryPlayback() async {
    AppLogger.debug('[CHAR_INTRO] Navigating to new story playback screen');

    // Fade out current screen
    await _fadeController.reverse();

    if (mounted) {
      // Navigate to new story player screen
      AppLogger.debug('[CHAR_INTRO] Navigation to new story player: /new_story/play/${widget.storyId}');
      context.push('/new_story/play/${widget.storyId}');
    }
  }

  @override
  void dispose() {
    AppLogger.debug('[CHAR_INTRO] Disposing resources');
    _fadeController.dispose();
    _narrationService.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: _buildBody(),
    );
  }

  /// Build the main body
  Widget _buildBody() {
    if (_isLoading) {
      return const Center(child: LoadingIndicator());
    }

    if (_error != null) {
      return _buildErrorWidget();
    }

    if (_story == null) {
      return const Center(
        child: Text('Story not found'),
      );
    }

    return FadeTransition(
      opacity: _fadeAnimation,
      child: CharacterProfilesWidget(
        story: _story!,
        onContinue: _navigateToStoryPlayback,
        narrationService: _narrationService,
      ),
    );
  }

  /// Build error widget
  Widget _buildErrorWidget() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(Icons.error_outline, size: 64, color: Colors.red),
          const SizedBox(height: 16),
          Text(
            _error ?? 'An error occurred',
            style: const TextStyle(color: Colors.red),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: _loadStory,
            child: const Text('Retry'),
          ),
          const SizedBox(height: 8),
          TextButton(
            onPressed: () => context.pop(),
            child: const Text('Back'),
          ),
        ],
      ),
    );
  }
}
