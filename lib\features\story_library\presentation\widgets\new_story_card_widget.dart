import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:choice_once_upon_a_time/models/story_metadata_model.dart';
import 'package:choice_once_upon_a_time/features/story_library/presentation/providers/new_story_library_provider.dart';
import 'package:choice_once_upon_a_time/features/story_library/presentation/widgets/download_permission_dialog.dart';
import 'package:choice_once_upon_a_time/core/services/download_notification_service.dart';
import 'package:choice_once_upon_a_time/core/utils/app_logger.dart';
import 'package:choice_once_upon_a_time/core/services/asset_fallback_service.dart';

/// New story card widget that displays story information with download/play button
class NewStoryCardWidget extends ConsumerStatefulWidget {
  final StoryMetadataModel story;
  final String languageCode;
  final VoidCallback onTap;
  final bool isOneColumn;

  const NewStoryCardWidget({
    super.key,
    required this.story,
    required this.languageCode,
    required this.onTap,
    this.isOneColumn = false,
  });

  @override
  ConsumerState<NewStoryCardWidget> createState() => _NewStoryCardWidgetState();
}

class _NewStoryCardWidgetState extends ConsumerState<NewStoryCardWidget> {
  String _storyStatus = 'loading';
  String? _fallbackImagePath;
  final AssetFallbackService _fallbackService = AssetFallbackService();

  @override
  void initState() {
    super.initState();
    AppLogger.debug('[COMPONENT_LOAD] Full path: lib/features/story_library/presentation/widgets/new_story_card_widget.dart - NewStoryCardWidget');
    _loadStoryStatus();
    _loadFallbackImage();
  }

  Future<void> _loadStoryStatus() async {
    try {
      final status = await ref.read(newStoryLibraryProvider.notifier).getStoryStatus(widget.story.id);
      if (mounted) {
        setState(() {
          _storyStatus = status;
        });
      }
    } catch (e) {
      AppLogger.debug('[NEW_STORY_CARD] Error loading story status: $e');
      if (mounted) {
        setState(() {
          _storyStatus = 'error';
        });
      }
    }
  }

  Future<void> _loadFallbackImage() async {
    try {
      final fallbackPath = await _fallbackService.getCoverImageOrPlaceholder(widget.story.coverImageUrl);
      if (mounted) {
        setState(() {
          _fallbackImagePath = fallbackPath;
        });
      }
    } catch (e) {
      AppLogger.debug('[NEW_STORY_CARD] Error loading fallback image: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final screenSize = MediaQuery.of(context).size;
    final isSmallScreen = screenSize.width < 600;
    final isVerySmallScreen = screenSize.width < 400;

    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: InkWell(
        onTap: widget.onTap,
        borderRadius: BorderRadius.circular(12),
        child: AspectRatio(
          // Use proper aspect ratio to prevent overflow
          aspectRatio: widget.isOneColumn ? 0.75 : 0.8, // Width:Height ratio
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              // Cover Image with flexible height (60% of card)
              Expanded(
                flex: 6,
                child: ClipRRect(
                  borderRadius: const BorderRadius.vertical(top: Radius.circular(12)),
                  child: _buildCoverImage(),
                ),
              ),

              // Story Information with flexible content (40% of card)
              Expanded(
                flex: 4,
                child: Padding(
                  padding: EdgeInsets.all(isVerySmallScreen ? 6.0 : (isSmallScreen ? 8.0 : 12.0)),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Title and metadata - flexible to prevent overflow
                      Expanded(
                        child: SingleChildScrollView(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Text(
                                widget.story.getLocalizedTitle(widget.languageCode),
                                style: theme.textTheme.titleSmall?.copyWith(
                                  fontWeight: FontWeight.bold,
                                  fontSize: isVerySmallScreen ? 11 : (isSmallScreen ? 12 : 14),
                                ),
                                maxLines: 2,
                                overflow: TextOverflow.ellipsis,
                              ),
                              SizedBox(height: isVerySmallScreen ? 2 : 4),
                              Text(
                                widget.story.targetMoralValue,
                                style: theme.textTheme.bodySmall?.copyWith(
                                  color: theme.colorScheme.primary,
                                  fontSize: isVerySmallScreen ? 9 : (isSmallScreen ? 10 : 12),
                                ),
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                              ),
                              SizedBox(height: isVerySmallScreen ? 1 : 2),
                              Text(
                                '${widget.story.estimatedDurationMinutes} min • ${widget.story.targetAgeSubSegment}',
                                style: theme.textTheme.bodySmall?.copyWith(
                                  color: theme.colorScheme.onSurfaceVariant,
                                  fontSize: isVerySmallScreen ? 8 : (isSmallScreen ? 9 : 11),
                                ),
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ],
                          ),
                        ),
                      ),

                      // Action Button - fixed at bottom with proper constraints
                      SizedBox(height: isVerySmallScreen ? 4 : 8),
                      _buildActionButton(theme, isSmallScreen, isVerySmallScreen),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildCoverImage() {
    final imagePath = _fallbackImagePath ?? widget.story.coverImageUrl;

    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: Colors.grey[200],
        borderRadius: const BorderRadius.vertical(top: Radius.circular(12)),
      ),
      child: Image.asset(
        imagePath,
        fit: BoxFit.cover,
        errorBuilder: (context, error, stackTrace) {
          AppLogger.debug('[IMAGE_LOAD] Failed to load: $imagePath | Error: $error');
          return Container(
            color: Colors.grey[300],
            child: Icon(
              Icons.book,
              size: 48,
              color: Colors.grey[600],
            ),
          );
        },
      ),
    );
  }

  Widget _buildActionButton(ThemeData theme, bool isSmallScreen, bool isVerySmallScreen) {
    IconData iconData;
    String label;
    Color backgroundColor;
    Color textColor;
    bool isEnabled = true;

    switch (_storyStatus) {
      case 'play':
        iconData = Icons.play_arrow;
        label = 'Play';
        backgroundColor = theme.colorScheme.primary;
        textColor = theme.colorScheme.onPrimary;
        break;
      case 'download':
        iconData = Icons.download;
        label = 'Download';
        backgroundColor = theme.colorScheme.secondary;
        textColor = theme.colorScheme.onSecondary;
        break;
      case 'downloading':
        iconData = Icons.downloading;
        label = 'Downloading...';
        backgroundColor = theme.colorScheme.surfaceContainerHighest;
        textColor = theme.colorScheme.onSurfaceVariant;
        isEnabled = false;
        break;
      case 'download_failed':
        iconData = Icons.refresh;
        label = 'Retry';
        backgroundColor = theme.colorScheme.errorContainer;
        textColor = theme.colorScheme.onErrorContainer;
        break;
      case 'loading':
        iconData = Icons.hourglass_empty;
        label = 'Loading...';
        backgroundColor = theme.colorScheme.surfaceContainerHighest;
        textColor = theme.colorScheme.onSurfaceVariant;
        isEnabled = false;
        break;
      case 'error':
      default:
        iconData = Icons.error_outline;
        label = 'Error';
        backgroundColor = theme.colorScheme.errorContainer;
        textColor = theme.colorScheme.onErrorContainer;
        isEnabled = false;
        break;
    }

    return ConstrainedBox(
      constraints: BoxConstraints(
        minHeight: isVerySmallScreen ? 28 : (isSmallScreen ? 32 : 36),
        maxHeight: isVerySmallScreen ? 32 : (isSmallScreen ? 36 : 40),
      ),
      child: SizedBox(
        width: double.infinity,
        child: ElevatedButton.icon(
          onPressed: !isEnabled ? null : () => _handleButtonTap(),
          icon: Icon(
            iconData,
            size: isVerySmallScreen ? 12 : (isSmallScreen ? 14 : 16),
          ),
          label: Text(
            label,
            style: TextStyle(
              fontSize: isVerySmallScreen ? 9 : (isSmallScreen ? 10 : 12),
              fontWeight: FontWeight.w600,
            ),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
          style: ElevatedButton.styleFrom(
            backgroundColor: backgroundColor,
            foregroundColor: textColor,
            elevation: 2,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
            padding: EdgeInsets.symmetric(
              horizontal: isVerySmallScreen ? 6 : (isSmallScreen ? 8 : 12),
              vertical: isVerySmallScreen ? 2 : (isSmallScreen ? 4 : 6),
            ),
          ),
        ),
      ),
    );
  }

  /// Handle button tap based on story status
  void _handleButtonTap() async {
    switch (_storyStatus) {
      case 'play':
        // Navigate to story
        widget.onTap();
        break;
      case 'download':
      case 'download_failed':
        // Start download
        await _downloadStory();
        break;
      default:
        // For other statuses, use default behavior
        widget.onTap();
        break;
    }
  }

  /// Download a story from Firebase with permission handling
  Future<void> _downloadStory() async {
    try {
      AppLogger.debug('[NEW_STORY_CARD] Starting download for story: ${widget.story.id}');

      // Show permission dialog first
      final permissionGranted = await DownloadPermissionDialog.show(
        context,
        storyTitle: widget.story.title['en-US'] ?? widget.story.id,
        onPermissionGranted: () {
          AppLogger.debug('[NEW_STORY_CARD] Permission granted for story: ${widget.story.id}');
        },
        onPermissionDenied: () {
          AppLogger.debug('[NEW_STORY_CARD] Permission denied for story: ${widget.story.id}');
        },
      );

      if (permissionGranted != true) {
        AppLogger.debug('[NEW_STORY_CARD] Download cancelled - permission not granted');
        return;
      }

      // Show download started notification
      final storyTitle = widget.story.title['en-US'] ?? widget.story.id;
      DownloadNotificationService().showDownloadStarted(context, storyTitle);

      // Update status to downloading
      setState(() {
        _storyStatus = 'downloading';
      });

      // Start download (skip permission check since we already handled it)
      final success = await ref.read(newStoryLibraryProvider.notifier).downloadStory(
        widget.story.id,
        checkPermissions: false, // We already checked permissions
      );

      if (success) {
        AppLogger.debug('[NEW_STORY_CARD] Successfully downloaded story: ${widget.story.id}');
        // Show success notification
        if (mounted) {
          DownloadNotificationService().showDownloadCompleted(context, storyTitle);
        }
        // Reload story status
        await _loadStoryStatus();
      } else {
        AppLogger.debug('[NEW_STORY_CARD] Failed to download story: ${widget.story.id}');
        // Show failure notification
        if (mounted) {
          DownloadNotificationService().showDownloadFailed(context, storyTitle);
        }
        setState(() {
          _storyStatus = 'download_failed';
        });
      }
    } catch (e) {
      AppLogger.debug('[NEW_STORY_CARD] Error downloading story: $e');
      setState(() {
        _storyStatus = 'download_failed';
      });
    }
  }
}
