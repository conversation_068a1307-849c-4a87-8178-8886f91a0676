// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'narrator_profile_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

NarratorProfileModel _$NarratorProfileModelFromJson(
        Map<String, dynamic> json) =>
    NarratorProfileModel(
      id: json['id'] as String,
      name: json['name'] as String,
      description: json['description'] as String,
      voice: VoiceModel.fromJson(json['voice'] as Map<String, dynamic>),
      category: $enumDecode(_$NarratorCategoryEnumMap, json['category']),
      gender: $enumDecode(_$NarratorGenderEnumMap, json['gender']),
      ageRange: $enumDecode(_$NarratorAgeRangeEnumMap, json['ageRange']),
      personalities: (json['personalities'] as List<dynamic>)
          .map((e) => $enumDecode(_$NarratorPersonalityEnumMap, e))
          .toList(),
      accentLanguage: json['accentLanguage'] as String?,
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
      isDefault: json['isDefault'] as bool? ?? false,
    );

Map<String, dynamic> _$NarratorProfileModelToJson(
        NarratorProfileModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'description': instance.description,
      'voice': instance.voice,
      'category': _$NarratorCategoryEnumMap[instance.category]!,
      'gender': _$NarratorGenderEnumMap[instance.gender]!,
      'ageRange': _$NarratorAgeRangeEnumMap[instance.ageRange]!,
      'personalities': instance.personalities
          .map((e) => _$NarratorPersonalityEnumMap[e]!)
          .toList(),
      'accentLanguage': instance.accentLanguage,
      'createdAt': instance.createdAt.toIso8601String(),
      'updatedAt': instance.updatedAt.toIso8601String(),
      'isDefault': instance.isDefault,
    };

const _$NarratorCategoryEnumMap = {
  NarratorCategory.grandparent: 'grandparent',
  NarratorCategory.teacher: 'teacher',
  NarratorCategory.friend: 'friend',
  NarratorCategory.fantasyCharacter: 'fantasyCharacter',
  NarratorCategory.custom: 'custom',
};

const _$NarratorGenderEnumMap = {
  NarratorGender.male: 'male',
  NarratorGender.female: 'female',
  NarratorGender.neutral: 'neutral',
};

const _$NarratorAgeRangeEnumMap = {
  NarratorAgeRange.child: 'child',
  NarratorAgeRange.adult: 'adult',
  NarratorAgeRange.elderly: 'elderly',
};

const _$NarratorPersonalityEnumMap = {
  NarratorPersonality.gentle: 'gentle',
  NarratorPersonality.energetic: 'energetic',
  NarratorPersonality.calm: 'calm',
  NarratorPersonality.playful: 'playful',
  NarratorPersonality.wise: 'wise',
  NarratorPersonality.cheerful: 'cheerful',
  NarratorPersonality.mysterious: 'mysterious',
  NarratorPersonality.dramatic: 'dramatic',
};
