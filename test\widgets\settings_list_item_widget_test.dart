import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:choice_once_upon_a_time/features/parent_zone/presentation/widgets/settings_list_item_widget.dart';

void main() {
  group('SettingsListItemWidget', () {
    testWidgets('should display title and icon correctly', (WidgetTester tester) async {
      // Arrange & Act
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: SettingsListItemWidget(
              title: 'Test Setting',
              icon: Icons.settings,
            ),
          ),
        ),
      );

      // Assert
      expect(find.text('Test Setting'), findsOneWidget);
      expect(find.byIcon(Icons.settings), findsOneWidget);
      expect(find.byIcon(Icons.arrow_forward_ios), findsOneWidget); // Default trailing
    });

    testWidgets('should display subtitle when provided', (WidgetTester tester) async {
      // Arrange & Act
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: SettingsListItemWidget(
              title: 'Test Setting',
              subtitle: 'Test subtitle description',
              icon: Icons.settings,
            ),
          ),
        ),
      );

      // Assert
      expect(find.text('Test Setting'), findsOneWidget);
      expect(find.text('Test subtitle description'), findsOneWidget);
    });

    testWidgets('should call onTap when tapped', (WidgetTester tester) async {
      // Arrange
      bool wasTapped = false;

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: SettingsListItemWidget(
              title: 'Tappable Setting',
              icon: Icons.settings,
              onTap: () => wasTapped = true,
            ),
          ),
        ),
      );

      // Act
      await tester.tap(find.byType(SettingsListItemWidget));
      await tester.pump();

      // Assert
      expect(wasTapped, true);
    });

    testWidgets('should display custom trailing widget when provided', (WidgetTester tester) async {
      // Arrange & Act
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: SettingsListItemWidget(
              title: 'Custom Trailing',
              icon: Icons.settings,
              trailing: Icon(Icons.star),
            ),
          ),
        ),
      );

      // Assert
      expect(find.byIcon(Icons.star), findsOneWidget);
      expect(find.byIcon(Icons.arrow_forward_ios), findsNothing); // Default trailing should not appear
    });

    testWidgets('should handle disabled state correctly', (WidgetTester tester) async {
      // Arrange
      bool wasTapped = false;

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: SettingsListItemWidget(
              title: 'Disabled Setting',
              subtitle: 'This setting is disabled',
              icon: Icons.settings,
              enabled: false,
              onTap: () => wasTapped = true,
            ),
          ),
        ),
      );

      // Act
      await tester.tap(find.byType(SettingsListItemWidget));
      await tester.pump();

      // Assert
      expect(wasTapped, false); // Should not be tapped when disabled
      expect(find.text('Disabled Setting'), findsOneWidget);
      expect(find.text('This setting is disabled'), findsOneWidget);
    });

    testWidgets('should apply correct styling for enabled state', (WidgetTester tester) async {
      // Arrange & Act
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: SettingsListItemWidget(
              title: 'Enabled Setting',
              subtitle: 'This setting is enabled',
              icon: Icons.settings,
              enabled: true,
            ),
          ),
        ),
      );

      // Assert
      final listTile = tester.widget<ListTile>(find.byType(ListTile));
      expect(listTile.enabled, true);
      
      // Check that the icon container and text are properly styled for enabled state
      expect(find.byIcon(Icons.settings), findsOneWidget);
      expect(find.text('Enabled Setting'), findsOneWidget);
      expect(find.text('This setting is enabled'), findsOneWidget);
    });

    testWidgets('should apply correct styling for disabled state', (WidgetTester tester) async {
      // Arrange & Act
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: SettingsListItemWidget(
              title: 'Disabled Setting',
              subtitle: 'This setting is disabled',
              icon: Icons.settings,
              enabled: false,
            ),
          ),
        ),
      );

      // Assert
      final listTile = tester.widget<ListTile>(find.byType(ListTile));
      expect(listTile.enabled, false);
      
      // Check that trailing is null when disabled
      expect(listTile.trailing, null);
    });

    testWidgets('should wrap content in Card widget', (WidgetTester tester) async {
      // Arrange & Act
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: SettingsListItemWidget(
              title: 'Card Test',
              icon: Icons.settings,
            ),
          ),
        ),
      );

      // Assert
      expect(find.byType(Card), findsOneWidget);
      expect(find.byType(ListTile), findsOneWidget);
      
      final card = tester.widget<Card>(find.byType(Card));
      expect(card.elevation, 1);
      expect(card.margin, EdgeInsets.zero);
    });

    testWidgets('should have proper content padding', (WidgetTester tester) async {
      // Arrange & Act
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: SettingsListItemWidget(
              title: 'Padding Test',
              icon: Icons.settings,
            ),
          ),
        ),
      );

      // Assert
      final listTile = tester.widget<ListTile>(find.byType(ListTile));
      expect(listTile.contentPadding, const EdgeInsets.symmetric(
        horizontal: 16,
        vertical: 8,
      ));
    });
  });
}
