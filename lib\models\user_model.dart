import 'package:json_annotation/json_annotation.dart';

part 'user_model.g.dart';

/// Model for user data stored in Firestore
@JsonSerializable()
class UserModel {
  /// User ID from Firebase Authentication
  final String userId;

  /// User's email (optional, for Email/Password auth)
  final String? email;

  /// Display name for Parent Zone
  final String? displayName;

  /// When the user account was created
  @JsonKey(fromJson: _timestampFromJson, toJson: _timestampToJson)
  final DateTime createdAt;

  /// Last login timestamp
  @JsonKey(fromJson: _timestampFromJson, toJson: _timestampToJson)
  final DateTime lastLoginAt;

  /// User preferences
  final AppPreferences appPreferences;

  /// Subscription information
  final SubscriptionInfo subscription;

  /// Whether FTUE (First Time User Experience) is completed
  final bool ftueCompleted;

  const UserModel({
    required this.userId,
    this.email,
    this.displayName,
    required this.createdAt,
    required this.lastLoginAt,
    required this.appPreferences,
    required this.subscription,
    this.ftueCompleted = false,
  });

  /// Creates a UserModel from JSON
  factory UserModel.fromJson(Map<String, dynamic> json) =>
      _$UserModelFromJson(json);

  /// Converts the UserModel to JSON
  Map<String, dynamic> toJson() => _$UserModelToJson(this);

  /// Creates a copy of this model with updated fields
  UserModel copyWith({
    String? userId,
    String? email,
    String? displayName,
    DateTime? createdAt,
    DateTime? lastLoginAt,
    AppPreferences? appPreferences,
    SubscriptionInfo? subscription,
    bool? ftueCompleted,
  }) {
    return UserModel(
      userId: userId ?? this.userId,
      email: email ?? this.email,
      displayName: displayName ?? this.displayName,
      createdAt: createdAt ?? this.createdAt,
      lastLoginAt: lastLoginAt ?? this.lastLoginAt,
      appPreferences: appPreferences ?? this.appPreferences,
      subscription: subscription ?? this.subscription,
      ftueCompleted: ftueCompleted ?? this.ftueCompleted,
    );
  }

  /// Helper method to convert Firestore Timestamp to DateTime
  static DateTime _timestampFromJson(dynamic timestamp) {
    if (timestamp is DateTime) return timestamp;
    if (timestamp is Map && timestamp.containsKey('_seconds')) {
      return DateTime.fromMillisecondsSinceEpoch(timestamp['_seconds'] * 1000);
    }
    return DateTime.now();
  }

  /// Helper method to convert DateTime to Firestore Timestamp format
  static Map<String, dynamic> _timestampToJson(DateTime dateTime) {
    return {
      '_seconds': dateTime.millisecondsSinceEpoch ~/ 1000,
      '_nanoseconds': (dateTime.millisecondsSinceEpoch % 1000) * 1000000,
    };
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is UserModel && other.userId == userId;
  }

  @override
  int get hashCode => userId.hashCode;

  @override
  String toString() {
    return 'UserModel(userId: $userId, email: $email, ftueCompleted: $ftueCompleted)';
  }
}

/// User app preferences
@JsonSerializable()
class AppPreferences {
  /// Master volume (0.0 to 1.0)
  final double masterVolume;

  /// Whether music is enabled
  final bool musicEnabled;

  /// Whether sound effects are enabled
  final bool sfxEnabled;

  /// Selected narration language
  final String narrationLanguage;

  const AppPreferences({
    this.masterVolume = 0.8,
    this.musicEnabled = true,
    this.sfxEnabled = true,
    this.narrationLanguage = 'en-US',
  });

  factory AppPreferences.fromJson(Map<String, dynamic> json) =>
      _$AppPreferencesFromJson(json);

  Map<String, dynamic> toJson() => _$AppPreferencesToJson(this);

  AppPreferences copyWith({
    double? masterVolume,
    bool? musicEnabled,
    bool? sfxEnabled,
    String? narrationLanguage,
  }) {
    return AppPreferences(
      masterVolume: masterVolume ?? this.masterVolume,
      musicEnabled: musicEnabled ?? this.musicEnabled,
      sfxEnabled: sfxEnabled ?? this.sfxEnabled,
      narrationLanguage: narrationLanguage ?? this.narrationLanguage,
    );
  }
}

/// User subscription information
@JsonSerializable()
class SubscriptionInfo {
  /// Subscription tier (e.g., "free", "premium_monthly", "premium_annual")
  final String tier;

  /// Subscription status (e.g., "active", "trial", "expired", "cancelled")
  final String status;

  /// Subscription expiry date (nullable for free tier)
  @JsonKey(fromJson: _nullableTimestampFromJson, toJson: _nullableTimestampToJson)
  final DateTime? expiryDate;

  /// Platform where subscription was purchased
  final String platform;

  /// Original transaction ID from the platform
  final String? originalTransactionId;

  const SubscriptionInfo({
    this.tier = 'free',
    this.status = 'active',
    this.expiryDate,
    this.platform = 'unknown',
    this.originalTransactionId,
  });

  factory SubscriptionInfo.fromJson(Map<String, dynamic> json) =>
      _$SubscriptionInfoFromJson(json);

  Map<String, dynamic> toJson() => _$SubscriptionInfoToJson(this);

  /// Whether the user has an active premium subscription
  bool get isPremium => tier != 'free' && status == 'active' && 
                       (expiryDate == null || expiryDate!.isAfter(DateTime.now()));

  SubscriptionInfo copyWith({
    String? tier,
    String? status,
    DateTime? expiryDate,
    String? platform,
    String? originalTransactionId,
  }) {
    return SubscriptionInfo(
      tier: tier ?? this.tier,
      status: status ?? this.status,
      expiryDate: expiryDate ?? this.expiryDate,
      platform: platform ?? this.platform,
      originalTransactionId: originalTransactionId ?? this.originalTransactionId,
    );
  }

  static DateTime? _nullableTimestampFromJson(dynamic timestamp) {
    if (timestamp == null) return null;
    return UserModel._timestampFromJson(timestamp);
  }

  static Map<String, dynamic>? _nullableTimestampToJson(DateTime? dateTime) {
    if (dateTime == null) return null;
    return UserModel._timestampToJson(dateTime);
  }
}
