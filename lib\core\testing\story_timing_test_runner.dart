import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:choice_once_upon_a_time/core/services/story_timing_analyzer.dart';
import 'package:choice_once_upon_a_time/core/repositories/enhanced_story_repository.dart';
import 'package:choice_once_upon_a_time/core/utils/app_logger.dart';

/// Test runner for conducting comprehensive story timing analysis
/// Tests autoplay functionality, measures scene durations, and validates child-friendly timing
class StoryTimingTestRunner {
  static const String _logPrefix = 'TIMING_TEST_RUNNER';
  
  final StoryTimingAnalyzer _timingAnalyzer;
  final EnhancedStoryRepository _storyRepository;
  
  // Test configuration
  static const List<String> testStoryIds = ['story013', 'story014'];
  static const bool enableAutoplay = true;
  static const int maxTestDurationMinutes = 10;
  
  // Test results
  final Map<String, StoryTimingTestResult> _testResults = {};
  
  StoryTimingTestRunner({
    required StoryTimingAnalyzer timingAnalyzer,
    required EnhancedStoryRepository storyRepository,
  }) : _timingAnalyzer = timingAnalyzer,
       _storyRepository = storyRepository;

  /// Run comprehensive timing tests on specified stories
  Future<Map<String, StoryTimingTestResult>> runTimingTests() async {
    try {
      AppLogger.info('$_logPrefix: Starting comprehensive timing analysis');
      AppLogger.info('$_logPrefix: Test stories: $testStoryIds');
      AppLogger.info('$_logPrefix: Autoplay enabled: $enableAutoplay');
      
      for (final storyId in testStoryIds) {
        AppLogger.info('$_logPrefix: Testing story: $storyId');
        
        try {
          final result = await _testStoryTiming(storyId);
          _testResults[storyId] = result;
          
          AppLogger.info('$_logPrefix: Completed timing test for $storyId');
          _logTestResult(result);
          
        } catch (e, stackTrace) {
          AppLogger.error('$_logPrefix: Error testing story $storyId', e, stackTrace);
          
          _testResults[storyId] = StoryTimingTestResult(
            storyId: storyId,
            success: false,
            error: e.toString(),
            testDuration: 0,
            sceneCount: 0,
            timingReport: TimingReport.empty(),
          );
        }
      }
      
      // Generate comprehensive test report
      await _generateComprehensiveTestReport();
      
      AppLogger.info('$_logPrefix: Completed all timing tests');
      return Map.from(_testResults);
      
    } catch (e, stackTrace) {
      AppLogger.error('$_logPrefix: Error running timing tests', e, stackTrace);
      return {};
    }
  }

  /// Test timing for a specific story
  Future<StoryTimingTestResult> _testStoryTiming(String storyId) async {
    final testStartTime = DateTime.now();
    
    try {
      // Clear previous timing data
      _timingAnalyzer.clearTimingData();
      
      // Load story
      final story = await _storyRepository.getStoryById(storyId);
      if (story == null) {
        throw Exception('Story not found: $storyId');
      }
      
      AppLogger.info('$_logPrefix: Loaded story $storyId with ${story.scenes.length} scenes');
      
      // Simulate story playback with timing analysis
      await _simulateStoryPlayback(story);
      
      // Generate timing report
      final timingReport = _timingAnalyzer.generateTimingReport();
      
      final testDuration = DateTime.now().difference(testStartTime).inMilliseconds;
      
      return StoryTimingTestResult(
        storyId: storyId,
        success: true,
        testDuration: testDuration,
        sceneCount: story.scenes.length,
        timingReport: timingReport,
      );
      
    } catch (e) {
      final testDuration = DateTime.now().difference(testStartTime).inMilliseconds;
      
      return StoryTimingTestResult(
        storyId: storyId,
        success: false,
        error: e.toString(),
        testDuration: testDuration,
        sceneCount: 0,
        timingReport: TimingReport.empty(),
      );
    }
  }

  /// Simulate story playback with realistic timing
  Future<void> _simulateStoryPlayback(dynamic story) async {
    AppLogger.info('$_logPrefix: Simulating playback for ${story.storyId}');
    
    for (int i = 0; i < story.scenes.length; i++) {
      final scene = story.scenes[i];
      final sceneId = scene.id;
      
      AppLogger.debug('$_logPrefix: Simulating scene $sceneId (${i + 1}/${story.scenes.length})');
      
      // Start scene timing
      _timingAnalyzer.startSceneTiming(sceneId);
      
      // Simulate scene loading time (100-500ms)
      await Future.delayed(Duration(milliseconds: 200 + (i * 50)));
      _timingAnalyzer.markSceneLoaded(sceneId);
      
      // Simulate narration
      await _simulateSceneNarration(scene);
      
      // Simulate scene transition if not last scene
      if (i < story.scenes.length - 1) {
        final nextScene = story.scenes[i + 1];
        _timingAnalyzer.startSceneTransition(sceneId, nextScene.id);
        
        // Simulate transition animation (500-1000ms)
        await Future.delayed(const Duration(milliseconds: 800));
        
        _timingAnalyzer.markSceneTransitionComplete(sceneId, nextScene.id);
      }
      
      // Complete scene timing
      _timingAnalyzer.completeSceneTiming(sceneId);
      
      // Enforce minimum scene display time for child-friendly experience
      await _enforceMinimumSceneTime(sceneId);
    }
    
    AppLogger.info('$_logPrefix: Completed simulation for ${story.storyId}');
  }

  /// Simulate realistic narration timing for a scene
  Future<void> _simulateSceneNarration(dynamic scene) async {
    final sceneText = scene.text as String;
    final sentences = _splitIntoSentences(sceneText);
    
    AppLogger.debug('$_logPrefix: Simulating narration for scene ${scene.id} with ${sentences.length} sentences');
    
    _timingAnalyzer.startNarrationTiming(scene.id, sceneText);
    
    for (int i = 0; i < sentences.length; i++) {
      final sentence = sentences[i];
      
      // Simulate sentence narration time based on length and TTS speed
      final narrationTime = _calculateSentenceNarrationTime(sentence);
      await Future.delayed(Duration(milliseconds: narrationTime));
      
      // Simulate pause between sentences (1.5 seconds as specified)
      if (i < sentences.length - 1) {
        _timingAnalyzer.startSentencePause(scene.id, i);
        await Future.delayed(const Duration(milliseconds: 1500));
        _timingAnalyzer.markSentencePauseComplete(scene.id, i);
      }
    }
    
    _timingAnalyzer.markNarrationComplete(scene.id);
  }

  /// Split text into sentences for timing analysis
  List<String> _splitIntoSentences(String text) {
    return text
        .split(RegExp(r'[.!?]+'))
        .map((s) => s.trim())
        .where((s) => s.isNotEmpty)
        .toList();
  }

  /// Calculate realistic narration time for a sentence based on TTS speed
  int _calculateSentenceNarrationTime(String sentence) {
    final wordCount = sentence.split(' ').length;
    
    // Child-friendly TTS speed: 0.35 rate
    // Average speaking rate: 150 words per minute at normal speed
    // Adjusted for TTS speed: 150 * 0.35 = 52.5 words per minute
    const wordsPerMinute = 52.5;
    const millisecondsPerMinute = 60000;
    
    final estimatedTime = (wordCount / wordsPerMinute * millisecondsPerMinute).round();
    
    // Add some variance (±20%) for realistic simulation
    final variance = (estimatedTime * 0.2).round();
    final actualTime = estimatedTime + (variance - (variance * 2 * (DateTime.now().millisecond % 100) / 100)).round();
    
    return actualTime.clamp(500, 10000); // Min 0.5s, max 10s per sentence
  }

  /// Enforce minimum scene display time (5 seconds as specified)
  Future<void> _enforceMinimumSceneTime(String sceneId) async {
    final sceneData = _timingAnalyzer.getSceneTimingData(sceneId);
    if (sceneData != null && sceneData.totalTime < StoryTimingAnalyzer.minimumSceneDisplayMs) {
      final additionalTime = StoryTimingAnalyzer.minimumSceneDisplayMs - sceneData.totalTime;
      AppLogger.debug('$_logPrefix: Enforcing minimum scene time for $sceneId, adding ${additionalTime}ms');
      await Future.delayed(Duration(milliseconds: additionalTime));
    }
  }

  /// Log test result summary
  void _logTestResult(StoryTimingTestResult result) {
    AppLogger.info('$_logPrefix: === TEST RESULT SUMMARY ===');
    AppLogger.info('$_logPrefix: Story ID: ${result.storyId}');
    AppLogger.info('$_logPrefix: Success: ${result.success}');
    AppLogger.info('$_logPrefix: Test Duration: ${result.testDuration}ms');
    AppLogger.info('$_logPrefix: Scene Count: ${result.sceneCount}');
    
    if (result.success) {
      final report = result.timingReport;
      AppLogger.info('$_logPrefix: Total Story Time: ${report.totalStoryTime}ms');
      AppLogger.info('$_logPrefix: Average Scene Time: ${report.averageSceneTime}ms');
      AppLogger.info('$_logPrefix: Child-Friendly Compliance: ${(report.childFriendlyCompliance * 100).toStringAsFixed(1)}%');
    } else {
      AppLogger.error('$_logPrefix: Test Error: ${result.error}');
    }
  }

  /// Generate comprehensive test report
  Future<void> _generateComprehensiveTestReport() async {
    try {
      AppLogger.info('$_logPrefix: === COMPREHENSIVE TIMING TEST REPORT ===');
      
      final successfulTests = _testResults.values.where((r) => r.success).toList();
      final failedTests = _testResults.values.where((r) => !r.success).toList();
      
      AppLogger.info('$_logPrefix: Total Tests: ${_testResults.length}');
      AppLogger.info('$_logPrefix: Successful: ${successfulTests.length}');
      AppLogger.info('$_logPrefix: Failed: ${failedTests.length}');
      
      if (successfulTests.isNotEmpty) {
        // Calculate aggregate metrics
        final totalScenes = successfulTests.fold(0, (sum, test) => sum + test.sceneCount);
        final averageStoryTime = successfulTests.fold(0, (sum, test) => sum + test.timingReport.totalStoryTime) / successfulTests.length;
        final averageCompliance = successfulTests.fold(0.0, (sum, test) => sum + test.timingReport.childFriendlyCompliance) / successfulTests.length;
        
        AppLogger.info('$_logPrefix: === AGGREGATE METRICS ===');
        AppLogger.info('$_logPrefix: Total Scenes Tested: $totalScenes');
        AppLogger.info('$_logPrefix: Average Story Duration: ${averageStoryTime.toStringAsFixed(1)}ms');
        AppLogger.info('$_logPrefix: Average Child-Friendly Compliance: ${(averageCompliance * 100).toStringAsFixed(1)}%');
        
        // Performance analysis
        _analyzePerformanceBottlenecks(successfulTests);
        
        // Child-friendly timing validation
        _validateChildFriendlyTiming(successfulTests);
      }
      
      // Export detailed test data
      await _exportTestData();
      
    } catch (e, stackTrace) {
      AppLogger.error('$_logPrefix: Error generating comprehensive test report', e, stackTrace);
    }
  }

  /// Analyze performance bottlenecks
  void _analyzePerformanceBottlenecks(List<StoryTimingTestResult> results) {
    AppLogger.info('$_logPrefix: === PERFORMANCE ANALYSIS ===');
    
    for (final result in results) {
      final report = result.timingReport;
      
      if (report.averageLoadTime > 1000) {
        AppLogger.warning('$_logPrefix: Slow loading detected in ${result.storyId}: ${report.averageLoadTime}ms average');
      }
      
      if (report.slowestScene != null && report.slowestScene!.totalTime > 15000) {
        AppLogger.warning('$_logPrefix: Very long scene in ${result.storyId}: ${report.slowestScene!.sceneId} (${report.slowestScene!.totalTime}ms)');
      }
      
      if (report.childFriendlyCompliance < 0.8) {
        AppLogger.warning('$_logPrefix: Low child-friendly compliance in ${result.storyId}: ${(report.childFriendlyCompliance * 100).toStringAsFixed(1)}%');
      }
    }
  }

  /// Validate child-friendly timing requirements
  void _validateChildFriendlyTiming(List<StoryTimingTestResult> results) {
    AppLogger.info('$_logPrefix: === CHILD-FRIENDLY TIMING VALIDATION ===');
    
    for (final result in results) {
      final report = result.timingReport;
      final issues = <String>[];
      
      // Check minimum scene display time compliance
      final shortScenes = report.sceneTimings.where((s) => s.totalTime < StoryTimingAnalyzer.minimumSceneDisplayMs).length;
      if (shortScenes > 0) {
        issues.add('$shortScenes scenes below minimum display time (${StoryTimingAnalyzer.minimumSceneDisplayMs}ms)');
      }
      
      // Check narration pacing
      final fastScenes = report.sceneTimings.where((s) {
        if (s.narrationTime == 0) return false;
        final paceRatio = s.narrationTime / (s.narrationTime + s.pauseTime);
        return paceRatio > 0.7; // More than 70% narration vs pause time
      }).length;
      
      if (fastScenes > 0) {
        issues.add('$fastScenes scenes with potentially fast narration pacing');
      }
      
      if (issues.isEmpty) {
        AppLogger.info('$_logPrefix: ✅ ${result.storyId} meets all child-friendly timing requirements');
      } else {
        AppLogger.warning('$_logPrefix: ⚠️ ${result.storyId} timing issues:');
        for (final issue in issues) {
          AppLogger.warning('$_logPrefix:   - $issue');
        }
      }
    }
  }

  /// Export test data for further analysis
  Future<void> _exportTestData() async {
    try {
      final exportData = {
        'test_metadata': {
          'timestamp': DateTime.now().toIso8601String(),
          'test_stories': testStoryIds,
          'autoplay_enabled': enableAutoplay,
          'child_friendly_tts_speed': StoryTimingAnalyzer.childFriendlyTTSSpeed,
          'minimum_scene_display_ms': StoryTimingAnalyzer.minimumSceneDisplayMs,
          'sentence_pause_ms': StoryTimingAnalyzer.sentencePauseMs,
        },
        'test_results': _testResults.map((key, value) => MapEntry(key, value.toJson())),
        'timing_data': _timingAnalyzer.exportTimingData(),
      };
      
      final jsonString = const JsonEncoder.withIndent('  ').convert(exportData);
      AppLogger.debug('$_logPrefix: Test data exported: ${jsonString.length} characters');
      
      // In a real implementation, you might save this to a file
      // For now, we'll just log that the export is ready
      AppLogger.info('$_logPrefix: Test data export completed');
      
    } catch (e, stackTrace) {
      AppLogger.error('$_logPrefix: Error exporting test data', e, stackTrace);
    }
  }
}

/// Result of a story timing test
class StoryTimingTestResult {
  final String storyId;
  final bool success;
  final String? error;
  final int testDuration;
  final int sceneCount;
  final TimingReport timingReport;

  const StoryTimingTestResult({
    required this.storyId,
    required this.success,
    this.error,
    required this.testDuration,
    required this.sceneCount,
    required this.timingReport,
  });

  Map<String, dynamic> toJson() {
    return {
      'story_id': storyId,
      'success': success,
      'error': error,
      'test_duration_ms': testDuration,
      'scene_count': sceneCount,
      'timing_report': timingReport.toJson(),
    };
  }
}
