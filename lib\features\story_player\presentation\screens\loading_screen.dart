import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:choice_once_upon_a_time/features/story_player/presentation/providers/story_player_provider.dart';

/// Loading screen displayed while story data is being fetched
class LoadingScreen extends ConsumerStatefulWidget {
  final String storyId;
  final String dataSource;

  const LoadingScreen({
    super.key,
    required this.storyId,
    this.dataSource = 'asset',
  });

  @override
  ConsumerState<LoadingScreen> createState() => _LoadingScreenState();
}

class _LoadingScreenState extends ConsumerState<LoadingScreen>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    
    // Initialize animations
    _animationController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );
    
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
    
    _scaleAnimation = Tween<double>(
      begin: 0.8,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.elasticOut,
    ));
    
    _animationController.forward();
    
    // Start loading the story
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(storyPlayerProvider.notifier).loadStory(widget.storyId, dataSource: widget.dataSource);
    });
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final storyPlayerState = ref.watch(storyPlayerProvider);

    // Listen for state changes and navigate when ready
    ref.listen<StoryPlayerState>(storyPlayerProvider, (previous, next) {
      debugPrint('[LoadingScreen] Story loading state changed:');
      debugPrint('- Loading: ${next.isLoading}');
      debugPrint('- Error: ${next.error}');
      debugPrint('- Has story: ${next.story != null}');
      debugPrint('- Has scene: ${next.currentScene != null}');
      
      if (!next.isLoading) {
        if (next.error != null) {
          debugPrint('[LoadingScreen] Error loading story: ${next.error}');
          // Show error and keep on screen for a moment
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(next.error!),
              backgroundColor: theme.colorScheme.error,
              duration: const Duration(seconds: 3),
            ),
          );
          // Delay navigation to allow error to be seen
          Future.delayed(const Duration(seconds: 3), () {
            if (mounted) {
              context.go('/home');
            }
          });
        } else if (next.story != null && next.currentScene != null) {
          debugPrint('[LoadingScreen] Story loaded successfully - navigating to player');
          debugPrint('- Story ID: ${next.story!.id}');
          debugPrint('- Initial Scene: ${next.currentScene!.sceneId}');
          context.go('/story_player');
        } else {
          debugPrint('[LoadingScreen] Story load completed but data is missing');
          debugPrint('- Story present: ${next.story != null}');
          debugPrint('- Scene present: ${next.currentScene != null}');
        }
      }
    });

    return Scaffold(
      backgroundColor: const Color.fromARGB(255, 215, 100, 24), // Light background
      body: SafeArea(
        child: Center(
          child: Padding(
            padding: const EdgeInsets.all(32.0),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // Animated story book icon
                AnimatedBuilder(
                  animation: _animationController,
                  builder: (context, child) {
                    return FadeTransition(
                      opacity: _fadeAnimation,
                      child: ScaleTransition(
                        scale: _scaleAnimation,
                        child: Container(
                          width: 120,
                          height: 120,
                          decoration: BoxDecoration(
                            color: theme.colorScheme.primary.withValues(alpha: 0.1),
                            borderRadius: BorderRadius.circular(60),
                          ),
                          child: Icon(
                            Icons.auto_stories,
                            size: 60,
                            color: theme.colorScheme.primary,
                          ),
                        ),
                      ),
                    );
                  },
                ),
                
                const SizedBox(height: 32),
                
                // Loading indicator
                SizedBox(
                  width: 40,
                  height: 40,
                  child: CircularProgressIndicator(
                    strokeWidth: 3,
                    valueColor: AlwaysStoppedAnimation<Color>(
                      theme.colorScheme.primary,
                    ),
                  ),
                ),
                
                const SizedBox(height: 24),
                
                // Loading text
                AnimatedBuilder(
                  animation: _fadeAnimation,
                  builder: (context, child) {
                    return FadeTransition(
                      opacity: _fadeAnimation,
                      child: Column(
                        children: [
                          Text(
                            'Preparing your story...',
                            style: theme.textTheme.headlineSmall?.copyWith(
                              color: const Color(0xFF2D3748),
                              fontWeight: FontWeight.w600,
                            ),
                            textAlign: TextAlign.center,
                          ),
                          
                          const SizedBox(height: 12),
                          
                          Text(
                            'Get ready for an amazing adventure!',
                            style: theme.textTheme.bodyLarge?.copyWith(
                              color: Colors.grey[600],
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ],
                      ),
                    );
                  },
                ),
                
                const SizedBox(height: 48),
                
                // Calming animation dots
                _buildLoadingDots(theme),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildLoadingDots(ThemeData theme) {
    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: List.generate(3, (index) {
            final delay = index * 0.2;
            final animation = Tween<double>(
              begin: 0.4,
              end: 1.0,
            ).animate(CurvedAnimation(
              parent: _animationController,
              curve: Interval(
                delay,
                delay + 0.4,
                curve: Curves.easeInOut,
              ),
            ));
            
            return FadeTransition(
              opacity: animation,
              child: Container(
                width: 8,
                height: 8,
                margin: const EdgeInsets.symmetric(horizontal: 4),
                decoration: BoxDecoration(
                  color: theme.colorScheme.primary.withValues(alpha: 0.6),
                  borderRadius: BorderRadius.circular(4),
                ),
              ),
            );
          }),
        );
      },
    );
  }
}
