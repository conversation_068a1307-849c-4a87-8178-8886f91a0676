# **Detailed Technical Design Document (TDD)**

**Project: Choice: Once Upon A Time**

Version: 1.0  
Date: May 30, 2025

## **1\. Introduction & Overview**

### **1.1. Project Purpose & Goals**

"Choice: Once Upon A Time" is an interactive bedtime story Flutter app for children aged 4-7 years. Its primary goal is to create engaging and delightful narrative experiences that subtly instill and reinforce positive moral values (honesty, empathy, perseverance, etc.), fostering social and emotional development. The app aims to provide a calming bedtime routine enhancement through choice-driven stories brought to life by a unique, empathetic AI narrator, encouraging active participation and thoughtful decision-making in a safe, ad-free environment.

### **1.2. Scope of this TDD**

This TDD covers the technical design for the Minimum Viable Product (MVP) of the "Choice: Once Upon A Time" application. The MVP will include:

* Core Flutter application for iOS and Android.  
* An initial set of 3 interactive moral stories: "Pip and the Pantry Puzzle" (Honesty), "Lila Fox and the Moonpetal Wish" (Empathy/Kindness), and "<PERSON><PERSON>'s Fantastic Flying Machine" (Perseverance), complete with branching narratives.  
* Implementation of an empathetic AI narrator using on-device Text-to-Speech (TTS) capabilities with emotional modulation.  
* User interface and experience as defined in the finalized UI/UX design specifications (Task 2.2 update).  
* Asset management for visuals and audio as per the Art Style Definition (Task 2.3) and Sound Design Concept (Task 2.4).  
* Offline access to downloaded stories.  
* Parent Zone with basic settings (sound controls) and a Parental Gate.  
* Monetization via a freemium model with a subscription to unlock all content (Task 1.8).

### **1.3. Target Audience**

(Summarized from Task 1.3)

* **Children (Ages 4-7):** Primary users. Developing cognitive/language skills, 10-20 min attention span. Understand simple narratives, cause-effect, and moral concepts via concrete examples. Proficient with basic touch-screen interactions (taps, simple swipes). Enjoy animals, fantasy, and adventure. Need clear cues, immediate feedback, and a calming experience. Represented by persona "Lily, 5".  
* **Parents/Guardians:** Purchasers and facilitators. Seek moral reinforcement, educational value, quality entertainment, calming bedtime routine, and safe screen time. Concerns include excessive screen time, addictive elements, and age-inappropriateness. Value transparency, ethical monetization, and features like offline access and parental controls. Represented by persona "Sarah".

### **1.4. Key Assumptions & Dependencies**

* **On-Device TTS Feasibility:** The primary strategy of using native on-device TTS (via flutter\_tts with SSML/cues) can achieve an acceptable level of emotional narration for the MVP. Extensive R\&D for a custom TTS engine is out of MVP scope.  
* **Flutter Development:** Flutter (latest stable version at project start) is the confirmed frontend technology for cross-platform (iOS & Android) development.  
* **Firebase BaaS:** Firebase will be used for backend services (Firestore, Cloud Storage, Authentication, Cloud Functions), reducing custom backend development time.  
* **MVP Scope Adherence:** The project will strictly adhere to the defined MVP scope (3 stories, core features) to manage timeline and budget (Task 1.9).  
* **Content Availability:** Story scripts (Story 01, 02, 03), art assets (Task 2.3), and sound design elements (Task 2.4) will be available as per the project timeline.  
* **Team Skills:** The team structure (Task 1.7) possesses the necessary skills for Flutter development, UI/UX design, content creation, and Firebase integration, with specialized AI/TTS support available.  
* **Monetization Approval:** The freemium/subscription model (Task 1.8) will be compliant with app store policies.  
* **UI/UX Design:** The "Task 2.2 update" document is the definitive source for screen specifications and user flows.  
* **Asset Design:** "Task 2.3" (Art Style) and "Task 2.4" (Sound Design) are the definitive sources for asset requirements.

## **2\. System Architecture**

### **2.1. High-Level Architecture Diagram**

graph TD  
    User\[Child/Parent User\] \--\>|Interacts with| FlutterApp\[Flutter Mobile App (iOS & Android)\];

    subgraph FlutterApp  
        direction LR  
        UI\[UI Layer (Widgets, Screens \- Task 2.2)\]  
        StateMgmt\[State Management (Riverpod \- Task 1.6)\]  
        StoryEngine\[Story Logic & Branching Engine\]  
        NarratorTTS\[Empathetic Narrator (On-Device TTS \- flutter\_tts, SSML \- Task 1.6)\]  
        OfflineStorage\[Offline Storage (IsarDB, Assets \- Task 1.6)\]  
        IAP\[In-App Purchases (App Stores)\]  
    end

    FlutterApp \--\>|API Calls/SDKs| FirebaseBaaS\[Firebase BaaS (Task 1.6)\];  
    FlutterApp \--\>|Read/Write Local Data| DeviceStorage\[Device Local Storage (Filesystem)\];  
    FlutterApp \--\>|TTS Requests (On-Device)| NativeTTS\[Native OS TTS Engines (iOS/Android)\];

    subgraph FirebaseBaaS  
        direction TB  
        Auth\[Firebase Authentication\]  
        Firestore\[Cloud Firestore (Story Data, User Data \- Task 1.6)\]  
        Storage\[Cloud Storage (Images, Audio Assets \- Task 1.6)\]  
        Functions\[Cloud Functions (Subscription Validation, etc. \- Task 1.6)\]  
    end  
      
    ContentCreators\[Content Creators (Writers, Artists, Sound Designers)\] \--\>|Upload Assets & Story Data| AdminTools\[Admin Tools/Scripts (Future \- for Content Management)\];  
    AdminTools \--\> FirebaseBaaS;

    FlutterApp \--\>|IAP Transactions| AppStoreServices\[App Store Services (Apple App Store, Google Play Store)\];

    style FlutterApp fill:\#D6EAF8,stroke:\#2980B9,stroke-width:2px  
    style FirebaseBaaS fill:\#D5F5E3,stroke:\#27AE60,stroke-width:2px  
    style DeviceStorage fill:\#FCF3CF,stroke:\#F39C12,stroke-width:1px  
    style NativeTTS fill:\#FCF3CF,stroke:\#F39C12,stroke-width:1px  
    style AppStoreServices fill:\#FDEDEC,stroke:\#C0392B,stroke-width:1px  
    style AdminTools fill:\#EBDEF0,stroke:\#8E44AD,stroke-width:1px  
    style User fill:\#E8DAEF,stroke:\#8E44AD,stroke-width:2px  
    style ContentCreators fill:\#E8DAEF,stroke:\#8E44AD,stroke-width:2px

Description:  
The system features a Flutter frontend application for iOS and Android. This app directly interacts with Firebase BaaS for data (story content, user profiles via Firestore), assets (images, sound effects, music via Cloud Storage), and authentication. The core narration relies on on-device native TTS engines, controlled by the Flutter app. In-app purchases are processed through platform-specific app store services. Future admin tools will interface with Firebase for content management.

### **2.2. Chosen Technology Stack Summary**

* **Frontend:** Flutter (Latest stable version, e.g., 3.19.x or newer).  
* **Backend:** Firebase BaaS.  
  * **Authentication:** Firebase Authentication (Anonymous for guest access, Email/Password for Parent Zone accounts).  
  * **Database:** Cloud Firestore (for story structure, metadata, user preferences, subscription status).  
  * **Asset Storage:** Firebase Cloud Storage (for images, audio files, story asset manifests). Firebase Hosting will be used for its CDN capabilities for these assets.  
  * **Serverless Functions:** Firebase Cloud Functions (for tasks like validating IAP receipts, potential future content aggregation).  
* **On-Device Emotional TTS Strategy:**  
  * **Primary Engine:** Native OS TTS capabilities (iOS: AVSpeechSynthesizer; Android: TextToSpeech) accessed via the flutter\_tts package.  
  * **Emotional Modulation:** Achieved through a combination of:  
    * SSML (Speech Synthesis Markup Language) tags (e.g., \<emphasis\>, \<prosody rate="slow" pitch="-2st"\>text\</prosody\>) passed to flutter\_tts.  
    * Programmatic adjustments to pitch, rate, and volume via flutter\_tts API calls, based on emotionCue fields in the story data.  
  * **Voice Selection:** Utilizing high-quality system voices available on the user's device. The app will check for and guide users if a required voice/language pack is missing (Screen 15).  
  * **Fallback Strategy (if on-device emotional quality is insufficient after MVP R\&D):**  
    1. **Server-Side High-Quality TTS with Caching:** Generate audio files using services like Google Cloud TTS (WaveNet) or Amazon Polly (Neural), then download and cache these audio files on the device for offline playback. This is the preferred fallback for maintaining quality and scalability.  
    2. **Hybrid Approach:** Use pre-recorded human narration for critical emotional beats or flagship stories, blended with on-device TTS for the remainder. This adds content creation complexity.

## **3\. Frontend Design (Flutter Application)**

### **3.1. Overall App Structure & Modularization**

* **Proposed Project Folder Structure (Feature-First):**  
  lib/  
  ├── app/                    \# App-wide setup (main.dart, app\_widget.dart), routing (GoRouter config), themes  
  ├── core/                   \# Core utilities (constants, enums, error handling, base classes, DI setup)  
  │   ├── audio/              \# TTS service, sound effect player service  
  │   ├── network/            \# Network connectivity checker  
  │   └── storage/            \# Offline storage service (Isar, file\_storage)  
  ├── features/               \# Feature modules  
  │   ├── app\_init/           \# Splash screen, FTUE logic  
  │   ├── story\_library/  
  │   │   ├── presentation/   \# Screens (story\_library\_screen.dart), widgets (story\_cover\_card.dart), providers (story\_library\_provider.dart)  
  │   │   ├── domain/         \# Entities (story\_metadata.dart), use\_cases (fetch\_stories\_use\_case.dart)  
  │   │   └── data/           \# Repositories (story\_repository.dart), data\_sources (firestore\_story\_data\_source.dart)  
  │   ├── story\_player/       \# Screen (story\_player\_screen.dart), widgets (choice\_button.dart, narrator\_controls.dart), providers (story\_player\_provider.dart), story\_engine\_service.dart  
  │   ├── parent\_zone/        \# Screens (parent\_zone\_dashboard.dart, etc.), widgets, providers  
  │   ├── auth/               \# Authentication logic, screens (parental\_gate\_screen.dart)  
  │   └── subscription/       \# IAP logic, screens (subscription\_screen.dart)  
  ├── shared\_widgets/         \# Common reusable UI components (e.g., PrimaryButton, ConfirmationPopup)  
  ├── models/                 \# Data models (story\_model.dart, scene\_model.dart, user\_model.dart \- shared across features)  
  └── l10n/                   \# Localization files

* **Strategy for Modular Design:**  
  * **Separation of Concerns:** Adhere to Clean Architecture principles where feasible, separating UI (Presentation), Business Logic (Domain/Use Cases), and Data layers within each feature.  
  * **Reusable Widgets:** shared\_widgets/ will house globally reusable UI components. Feature-specific widgets will reside within their respective feature's presentation/widgets/ directory.  
  * **Feature Modules:** Each major app section (Story Library, Story Player, Parent Zone, Auth, Subscription) will be a self-contained module, promoting better organization and scalability.  
  * **Dependency Injection (DI):** Riverpod will be used for providing dependencies (services, repositories, providers) throughout the widget tree and to business logic classes.

### **3.2. UI Component Library Plan**

(Based on Task 2.2 update \- Screen Specifications and Task 2.3 \- Art Style)

* **StoryCoverCardWidget (Screen 2):**  
  * Properties: StoryMetadataModel storyMetadata, Function(StoryMetadataModel) onTap, bool isLocked, bool isNew, bool hasUpdate.  
  * States: Default, focused/hover (subtle glow/scale), tapped.  
  * Displays cover art, title, and badges.  
* **ChoiceButtonWidget (Screen 6):**  
  * Properties: ChoiceModel choice, Function(ChoiceModel) onSelected, bool isActive.  
  * States: Default, active (gentle pulse/glow as per Task 1.5), selected (visual confirmation).  
  * Displays choice icon and minimal text. Styled as per Task 2.3 (soft orb, card, leaf shape).  
* **ParentalGateDialogWidget (Screen 9):**  
  * Properties: Function onGatePassed, Function onCancel.  
  * Mechanism: "Press and hold for 3 seconds" button with visual feedback (filling circle).  
* **NarratorTextDisplayWidget (Screen 5):**  
  * Properties: List\<TextSegmentModel\> textSegments, String currentSegmentId, TextStyle narratorStyle, TextStyle characterStyle, Color highlightColor.  
  * Functionality: Displays current narrator text, supports highlighting of spoken words/phrases (requires TTS progress callbacks).  
* **PrimaryButtonWidget (Shared):**  
  * Properties: String label, VoidCallback onPressed, Widget? icon, bool isLoading, ButtonStyle? style.  
  * States: Default, pressed, disabled.  
* **NarrationControlsWidget (Screen 5, 6):**  
  * Properties: bool isPlaying, VoidCallback onPauseToggle, VoidCallback onReplaySegment.  
  * Displays Pause/Play, Replay Segment icons.  
* **SettingsListItemWidget (Screen 10):**  
  * Properties: String title, IconData icon, VoidCallback onTap.  
* SubscriptionPlanCardWidget (Screen **12):**  
  * Properties: SubscriptionPlanModel plan, VoidCallback onSubscribe.  
  * Displays plan details (price, benefits).  
* **ConfirmationDialogWidget (Shared):**  
  * Properties: String title, String content, String confirmText, String cancelText, VoidCallback onConfirm, VoidCallback? onCancel.  
* **LoadingIndicatorWidget (Screen 4, Shared):**  
  * Properties: Color color, double size.  
  * Calm animation (pulsing star/leaf).  
* **CalmAnimatedBackgroundWidget (Screen 1, 4, 20):**  
  * Properties: AnimationType type.  
  * Subtle, looping background animation.

### **3.3. State Management Approach**

* **Chosen Solution:** **Riverpod** (Task 1.6).  
* **Rationale:** Riverpod offers compile-safe DI, testability, and flexibility for managing various types of state without excessive boilerplate. It supports a reactive programming style.  
* **Application:**  
  * **App-Wide** State (Provider / StateNotifierProvider in **app/providers/):**  
    * AuthService: Manages Firebase Authentication state (User?).  
    * SettingsProvider: Manages user preferences (sound on/off, music volume, selected language \- persisted locally using Isar).  
    * SubscriptionProvider: Manages user's subscription status (fetched from Firestore, updated via IAP).  
    * ConnectivityProvider: Monitors network status.  
  * **Screen/Feature State (StateNotifierProvider, FutureProvider, StreamProvider within each feature module):**  
    * StoryLibraryProvider (in features/story\_library/presentation/providers/):  
      * Fetches and holds the list of StoryMetadataModel from StoryRepository.  
      * Manages loading/error states for the library.  
    * StoryPlayerProvider (in features/story\_player/presentation/providers/):  
      * Holds the currently loaded StoryModel.  
      * Manages current SceneId, TextSegmentId.  
      * Handles choice selection logic and updates the current scene.  
      * Manages TTS playback state (speaking, paused, current word/sentence for highlighting).  
      * Interacts with TTSService and OfflineStorageService.  
    * ParentZoneDashboardProvider, SoundSettingsProvider, etc., for Parent Zone screens.  
  * **Data Fetching:** FutureProvider for one-off data fetches, StreamProvider for real-time updates from Firestore (e.g., subscription status).  
  * **Repositories & Services:** Will be provided using Provider to the relevant business logic providers.

### **3.4. Navigation Strategy**

* **Chosen Approach:** **Navigator** 2.0 with GoRouter (Task 1.6).  
* **Rationale:** GoRouter provides a robust, declarative, URL-based routing system, simplifying complex navigation flows, deep linking, and back-button handling. It's well-suited for modular app structures.  
* **Implementation** (Routes from Task 2.2 **update):**  
  * A central AppRouter class (e.g., in app/routing/app\_router.dart) will define all routes using GoRoute.  
  * **Route Definitions:**  
    // Example GoRouter configuration  
    final GoRouter router \= GoRouter(  
      initialLocation: '/launch', // Or '/home' after FTUE  
      routes: \[  
        GoRoute(path: '/launch', builder: (context, state) \=\> AppLaunchScreen()), // Screen 1  
        GoRoute(path: '/home', builder: (context, state) \=\> StoryLibraryScreen()), // Screen 2  
        GoRoute(  
          path: '/story/:storyId',  
          builder: (context, state) \=\> StoryIntroScreen(storyId: state.pathParameters\['storyId'\]\!), // Screen 3  
          routes: \[  
            GoRoute(  
              path: 'play/:sceneId', // sceneId can be optional or a default  
              builder: (context, state) \=\> StoryPlayerScreen(  
                storyId: state.pathParameters\['storyId'\]\!,  
                initialSceneId: state.pathParameters\['sceneId'\], // Handle initial scene  
              ), // Screens 5 & 6  
            ),  
            GoRoute(  
              path: 'end',  
              builder: (context, state) \=\> StoryEndScreen(storyId: state.pathParameters\['storyId'\]\!), // Screen 8  
            ),  
          \],  
        ),  
        GoRoute(  
          path: '/parent\_zone',  
          builder: (context, state) \=\> ParentZoneDashboardScreen(), // Screen 10  
          routes: \[  
            GoRoute(path: 'sound\_settings', builder: (context, state) \=\> SoundSettingsScreen()), // Screen 11  
            GoRoute(path: 'subscription', builder: (context, state) \=\> SubscriptionScreen()), // Screen 12  
            // ... other parent zone routes  
          \],  
        ),  
        // ... other top-level routes like FTUE (Screen 4.1), Parental Gate (Screen 9\) if needed as full routes  
      \],  
      // errorBuilder for 404s  
    );

  * **Navigation:** Use context.go('/home'), context.push('/story/pip\_pantry\_puzzle'), etc.  
  * **Popups/Dialogs (Screens 13-19, 20):** Implemented using showDialog() or custom modal routes managed by GoRouter if they need to be part of the navigation stack history. For simplicity, showDialog() is preferred for most confirmation popups.  
  * **FTUE (Screen 4.1):** Conditionally displayed as an overlay on /home route based on a flag (e.g., from SharedPreferences or user profile).  
  * **Parental Gate (Screen 9):** Can be a route that, upon success, navigates to /parent\_zone, or a dialog that gates access. A route is cleaner for flow control.

### **3.5. Dynamic Content Handling & Story Logic**

* **Dart** Data Models for Story JSONs (Based **on Story 01, 02, 03 and Task 1.6 schema):**  
  // In lib/models/story\_content\_model.dart (refined from Task 1.6)

  // Main Story Metadata (for library display, less detail)  
  class StoryMetadataModel {  
    final String id; // e.g., pip\_pantry\_puzzle  
    final Map\<String, String\> title; // Localized  
    final String coverImageUrl;  
    final String loglineShort; // Localized  
    final String targetMoralValue;  
    final String version;  
    final bool isNew;  
    final bool hasUpdate; // Client-side check against local version  
    final bool isLocked; // Based on subscription status  
    final List\<String\> supportedLanguages;  
    final String defaultLanguage;

    StoryMetadataModel({ /\* constructor, fromJson \*/ });  
  }

  // Full Story Model (for player, includes all scenes)  
  class StoryModel {  
    final String id;  
    final Map\<String, String\> title;  
    final String coverImageUrl;  
    final String targetMoralValue;  
    final String targetAgeSubSegment;  
    final String version;  
    final String initialSceneId;  
    final Map\<String, SceneModel\> scenes; // Key: sceneId  
    final String narratorPersonaGuidance; // e.g., "warm, gentle"  
    final List\<String\> supportedLanguages;  
    final String defaultLanguage;  
    final String assetManifestUrl; // URL to asset manifest in Cloud Storage

    StoryModel({ /\* constructor, fromJson \*/ });  
  }

  class SceneModel {  
    final String sceneId;  
    final int order;  
    final String sceneType; // "narration\_illustration", "choice\_point", "conclusion"  
    final String backgroundImageUrl;  
    final List\<TextSegmentModel\> textSegmentsForTTS;  
    final BackgroundMusicConfig? backgroundMusic;  
    final List\<SoundEffectConfig\>? soundEffects;  
    final String? nextSceneId; // For linear scenes  
    final List\<ChoiceModel\>? choices; // For choice points  
    final Map\<String, String\>? promptTextForTTS; // Localized, for choice points  
    final String? promptEmotionCue; // For choice points  
    final Map\<String, String\>? conclusionMessageKey; // Localized, for conclusion scenes

    SceneModel({ /\* constructor, fromJson \*/ });  
  }

  class TextSegmentModel {  
    final String id;  
    final Map\<String, String\> text; // Localized: {"en-US": "Hello..."}  
    final String emotionCue; // e.g., "warm, gentle", "sad, whisper"  
    final Map\<String, String\>? ssml; // Optional direct SSML per language  
    final int? durationEstimateMs; // Optional, for pacing or pre-loading

    TextSegmentModel({ /\* constructor, fromJson \*/ });  
  }

  class ChoiceModel {  
    final String choiceId;  
    final Map\<String, String\> displayTextKey; // Localization key for UI text  
    final String displayIconUrl; // Path for choice icon  
    final Map\<String, String\> narratorGuidanceTextForTTS; // Localized TTS guidance  
    final String guidanceEmotionCue;  
    final String leadsToSceneId;

    ChoiceModel({ /\* constructor, fromJson \*/ });  
  }

  class BackgroundMusicConfig {  
    final String trackUrl;  
    final double volume;  
    final bool loop;  
    BackgroundMusicConfig({ /\* constructor, fromJson \*/ });  
  }

  class SoundEffectConfig {  
    final String trigger; // e.g., "onSegmentStart", "onChoiceMade"  
    final String? segmentId; // if onSegmentStart  
    final String sfxUrl;  
    final double volume;  
    SoundEffectConfig({ /\* constructor, fromJson \*/ });  
  }

  * These models will have fromJson factory constructors to parse data from Firestore.  
* **Logic for Loading, Parsing, and Displaying Story Content:**  
  * A StoryRepository (in features/story\_library/data/) will be responsible for:  
    * Fetching story metadata list from Firestore for the Story Library screen.  
    * Fetching the full StoryModel JSON for a selected story from Firestore (or local Isar DB if offline).  
  * The StoryPlayerProvider will:  
    * Use StoryRepository to load the selected StoryModel.  
    * Parse the JSON into the Dart StoryModel object.  
    * Hold the current StoryModel and manage progression (current SceneModel).  
  * Widgets in story\_player/presentation/ will listen to StoryPlayerProvider to display the current scene's background, narrator text segments, and choices.  
* **Implementation Plan for Branching Narrative Engine:**  
  * Resides within StoryPlayerProvider and a potential StoryEngineService.  
  * StoryPlayerProvider.currentScene will hold the SceneModel currently being displayed.  
  * When a SceneModel of type choice\_point is active, its choices list is rendered as ChoiceButtonWidgets.  
  * When a choice is tapped:  
    1. The ChoiceButtonWidget calls a method on StoryPlayerProvider (e.g., selectChoice(String choiceId)).  
    2. StoryPlayerProvider finds the selected ChoiceModel and retrieves its leadsToSceneId.  
    3. It updates its internal state to set the currentScene to the SceneModel corresponding to leadsToSceneId from the StoryModel.scenes map.  
    4. The UI rebuilds to display the new scene.  
  * For linear scenes (sceneType \== "narration\_illustration"), after all textSegmentsForTTS are played, the StoryPlayerProvider automatically transitions to the nextSceneId if present. If nextSceneId is null and it's not a choice point, it's considered the end of a branch or story (leading to a conclusion scene).

### **3.6. Empathetic Narrator Implementation**

* **On-Device TTS Integration Plan (TTSService in core/audio/):**  
  * **Package:** flutter\_tts (Task 1.6).  
  * **Initialization:** TTSService will initialize flutter\_tts instance. Call setCompletionHandler, setProgressHandler (for text highlighting), setErrorHandler.  
  * **Voice Selection & Language:**  
    * On app start or when language preference changes, TTSService.setLanguage(String languageCode) will be called.  
    * It will use flutter\_tts.getVoices and flutter\_tts.getLanguages to find a suitable, high-quality voice for the selected language (e.g., prefer voices containing "Siri", "WaveNet", or specific quality indicators if available).  
    * If no suitable voice is found, TTSService will notify the UI to trigger Screen 15 (Missing TTS Voice Data Popup).  
    * flutter\_tts.setVoice({"name": "voiceName", "locale": "languageCode"}).  
  * **Emotional Control (TTSService.speakSegment(TextSegmentModel segment)):**  
    1. Retrieve localized text from segment.text.  
    2. If segment.ssml exists for the current language, use that directly.  
    3. Otherwise, use EmotionCueMapperService to translate segment.emotionCue (e.g., "warm, gentle") into TTS parameters:  
       * flutter\_tts.setPitch(double pitch)  
       * flutter\_tts.setSpeechRate(double rate)  
       * flutter\_tts.setVolume(double volume)  
       * For more complex cues like "whisper" or specific emphasis, construct SSML strings (e.g., \<speak\>\<prosody rate="slow" pitch="-2st" volume="soft"\>${text}\</prosody\>\</speak\>). This requires robust SSML generation logic.  
    4. Call flutter\_tts.speak(processedTextOrSSML).  
  * **State Management:** StoryPlayerProvider will call TTSService methods and listen to its state changes (e.g., speaking, paused, completed segment, current word for highlighting) to update the UI.  
* **Pre-recorded Audio System (Fallback/Hybrid \- handled by AudioPlaybackService in core/audio/):**  
  * Package: just\_audio.  
  * If used, SceneModel or TextSegmentModel would contain audioUrl fields.  
  * AudioPlaybackService would manage downloading (if not pre-bundled), caching, playing, pausing, and seeking these audio files.  
  * Synchronization with on-screen text would be challenging:  
    * Option 1: Timed subtitles (LRC files or similar) associated with each audio segment.  
    * Option 2: Break audio into very small segments, matching text segments.  
  * This adds significant complexity to asset management and content creation.  
* **UI for Narrator Controls (Screen 5, 6, 7 \- NarrationControlsWidget):**  
  * **Pause/Play Button:**  
    * Calls TTSService.pause() or TTSService.resume() (or TTSService.speak() if pause doesn't preserve queue).  
    * Visually updates icon based on StoryPlayerProvider.ttsState.  
  * **Replay Segment Button:**  
    * Calls TTSService.stop() then TTSService.speakSegment(currentSegment).  
  * Controls will be styled to be unobtrusive yet accessible.

### **3.7. Offline Storage & Access Mechanism**

* **Strategy:** Stories (data JSON and binary assets) will be downloadable for full offline playback.  
* **Local Database/Storage Choice (OfflineStorageService in core/storage/):**  
  * **IsarDB:** For storing structured story metadata (StoryMetadataModel), full story data (StoryModel as a JSON string or embedded Isar objects), download status, and user preferences. Schema defined below.  
  * **path\_provider \+ File System:** For storing binary assets (images from Cloud Storage, SFX, music). Assets will be saved in the application's support directory, organized by storyId and version.  
* **Schema for Downloaded Content Tracking (Isar DownloadedStoryEntry collection):**  
  import 'package:isar/isar.dart';

  part 'downloaded\_story\_entry.g.dart'; // Isar generator

  @collection  
  class DownloadedStoryEntry {  
    Id id \= Isar.autoIncrement;

    @Index(unique: true, replace: true)  
    String storyId;

    String version;  
    DateTime downloadedAt;  
    String localStoryJsonPath; // Path to the stored full story JSON file  
    // Or: String fullStoryJson; // Store JSON directly in Isar if not too large  
    List\<String\> downloadedAssetIds; // List of asset filenames/ids stored locally  
    bool isFullyDownloaded;  
    int totalSizeMb; // Estimated  
  }

* **Logic for Downloading and Serving Content (OfflineStorageService):**  
  * **Download Trigger:** User action on StoryCoverCardWidget (if update available or not downloaded) or from Parent Zone (Screen 12.2).  
  * **Process:**  
    1. OfflineStorageService.downloadStory(StoryMetadataModel storyMeta):  
    2. Check available device storage. If insufficient, show Screen 17 (Storage Full Popup).  
    3. Fetch StoryModel JSON from Firestore for storyMeta.id.  
    4. Fetch assetManifestUrl from StoryModel and download assetManifest.json.  
    5. Parse assetManifest.json to get list of all binary asset URLs (images, sfx, music).  
    6. Download each binary asset from Firebase Cloud Storage, saving to local app directory (e.g., getApplicationSupportDirectory()/stories/{storyId}/{version}/{asset\_filename}).  
    7. Store the full StoryModel JSON as a local file (path saved in Isar) or directly in Isar.  
    8. Create/Update DownloadedStoryEntry in Isar with isFullyDownloaded \= true and paths/ids.  
    9. Update UI to reflect download progress and completion.  
  * **Offline Access Logic (StoryRepository):**  
    1. When a story is selected, StoryRepository first checks OfflineStorageService.isStoryDownloaded(storyId, version).  
    2. If true and isFullyDownloaded, it loads StoryModel from local Isar/file and asset paths from DownloadedStoryEntry.  
    3. If false or offline, and story not downloaded, it's marked as unavailable/locked.  
    4. If online and not downloaded/outdated, it fetches from Firestore.  
  * **TTS Voice Data:** As stated, app relies on OS-managed voice data. TTSService checks availability.

### **3.8. Interaction Logic**

* **General UI Feedback (Task 1.5, 2.2, 2.4):**  
  * Buttons: Implement subtle scale/color shift on press (PrimaryButtonWidget, ChoiceButtonWidget).  
  * Audio: Play soft, organic UI sounds for taps, selections, transitions via SoundEffectPlayerService.  
* **Choice Presentation & Selection (Screen 6):**  
  * StoryPlayerProvider updates state to SceneType.choice\_point.  
  * StoryPlayerScreen renders choices using ChoiceButtonWidget.  
  * Narrator audio (from promptTextForTTS and narratorGuidanceTextForTTS for each choice) is played by TTSService.  
  * ChoiceButtonWidget onSelected callback updates StoryPlayerProvider to transition to leadsToSceneId.  
* **"Discovery" Hotspots (Screen 5 \- MVP: Optional, Low Priority):**  
  * If implemented, SceneModel would contain a list of DiscoveryHotspotModel { Rect area; String sfxUrl; String? narratorCueKey; }.  
  * StoryPlayerScreen would overlay tappable areas. Tap triggers SFX and potentially a brief narrator cue.  
* **Narration Controls (NarrationControlsWidget):**  
  * Directly call methods on TTSService via StoryPlayerProvider.  
* **Parental** Gate (Screen 9 **\- ParentalGateScreen):**  
  * Uses a GestureDetector for press-and-hold. Timer updates a visual progress indicator (e.g., filling circle). On completion, navigates to Parent Zone.

## **4\. Backend Design (Firebase)**

### **4.1. API Specifications**

Direct Firebase SDKs (FlutterFire) will be used for most interactions. No custom REST/GraphQL APIs for MVP. Cloud Functions will be callable HTTPS functions if needed for specific actions (e.g., receipt validation).

### **4.2. Server-Side Logic (Firebase Cloud Functions)**

(Based on Task 1.6 and Task 1.8)

* **validateAppleReceipt(data, context) / validateGoogleReceipt(data, context) (HTTPS Callable):**  
  * Trigger: Called by the app after an IAP.  
  * Input: data: { receiptData: string, productId: string }.  
  * Logic: Validates the receipt with Apple/Google servers. If valid, updates the user's subscription status and expiry date in their Firestore users/{userId} document.  
  * Output: { success: boolean, newSubscriptionStatus: string }.  
* **onNewUserCreate(user) (Auth Trigger):**  
  * Trigger: Firebase Authentication onCreate user event.  
  * Logic: Creates a corresponding document in users/{userId} in Firestore with default preferences, trial status (if applicable), createdAt timestamp.  
* **checkStoryUpdates(data, context) (HTTPS Callable \- Optional for MVP, client can manage for 3 stories):**  
  * Input: data: { currentStoryVersions: Map\<String, String\> } (storyId: version).  
  * Logic: Compares with latest versions in stories collection.  
  * Output: Map\<String, String\> of stories that have updates (storyId: newVersion).

### **4.3. BaaS Configuration Details**

* **Cloud Firestore:**  
  * **Collections/Document Structures:**  
    * stories/{storyId}: (As defined in Section 5.1)  
      * scenes/{sceneId}: (As defined in Section 5.1)  
    * users/{userId}: (UID from Firebase Auth)  
      * Fields: email (String, optional), displayName (String, optional), createdAt (Timestamp), subscriptionTier (String: free, story\_explorer\_monthly, story\_explorer\_annual), subscriptionExpiryDate (Timestamp, nullable), lastActiveLanguage (String, e.g., "en-US").  
    * app\_config/global\_settings: (Single document)  
      * Fields: minAppVersion (String), isMaintenanceMode (Boolean), latestNews (Map\<String, String\>, localized).  
  * **Security Rules:**  
    rules\_version \= '2';  
    service cloud.firestore {  
      match /databases/{database}/documents {  
        // Public read for stories and app config  
        match /stories/{storyId} {  
          allow read: if true;  
          allow write: if false; // Admin only  
          match /scenes/{sceneId} {  
            allow read: if true;  
            allow write: if false; // Admin only  
          }  
        }  
        match /app\_config/global\_settings {  
          allow read: if true;  
          allow write: if false; // Admin only  
        }

        // Users can only read/write their own data  
        match /users/{userId} {  
          allow read, write: if request.auth \!= null && request.auth.uid \== userId;  
        }  
        // Allow admin to read user data for support, etc. (implement via custom claims or trusted functions)  
      }  
    }

* **Firebase Cloud Storage:**  
  * **Bucket Structure (as per Task 2.3):**  
    * stories/{storyId}/{version}/images/{imageName.png}  
    * stories/{storyId}/{version}/audio/sfx/{sfxName.mp3}  
    * stories/{storyId}/{version}/audio/music/{musicName.mp3}  
    * stories/{storyId}/{version}/assetManifest.json (Lists all assets for this story version)  
    * ui\_assets/global/{iconName.svg} (For general UI icons)  
    * story\_covers/{storyId}\_cover\_{version}.png  
  * **Access Rules:**  
    rules\_version \= '2';  
    service firebase.storage {  
      match /b/{bucket}/o {  
        // Public read for all story assets and UI assets  
        match /stories/{storyId}/{version}/{allPaths=\*\*} {  
          allow read: if true;  
          allow write: if request.auth \!= null && request.auth.token.admin \== true; // Admin only writes  
        }  
        match /story\_covers/{allPaths=\*\*} {  
          allow read: if true;  
          allow write: if request.auth \!= null && request.auth.token.admin \== true;  
        }  
        match /ui\_assets/global/{allPaths=\*\*} {  
          allow read: if true;  
          allow write: if request.auth \!= null && request.auth.token.admin \== true;  
        }  
        // User-specific private files (if any in future)  
        // match /users/{userId}/{allPaths=\*\*} {  
        //   allow read, write: if request.auth \!= null && request.auth.uid \== userId;  
        // }  
      }  
    }

* **Firebase Authentication:**  
  * Enabled Providers:  
    * Anonymous Authentication (for initial access to free content).  
    * Email/Password (for Parent Zone account creation to manage subscriptions).  
  * Link anonymous accounts to permanent accounts upon Parent Zone sign-up/login to preserve any local progress/favorites if implemented.  
* **Firebase Hosting:**  
  * Will serve a simple promotional landing page.  
  * Configured to act as a CDN for assets in Cloud Storage to improve global delivery performance.

## **5\. Database Design (Detailed)**

### **5.1. Detailed Schema**

**Firestore Collections (Elaborated from Section 4.3):**

1. **stories** (Collection)  
   * Document ID: storyId (String, e.g., pip\_pantry\_puzzle)  
   * Fields:  
     * title (Map\<String, String\>): Localized, e.g., {"en-US": "Pip and the Pantry Puzzle", "es-ES": "..."}  
     * logline (Map\<String, String\>): Localized short description.  
     * coverImageUrl (String): Full path in Firebase Storage (e.g., story\_covers/pip\_pantry\_puzzle\_cover\_1.0.0.png).  
     * targetMoralValue (String): e.g., "Honesty" (references key from Task 1.2).  
     * targetAgeSubSegment (String): e.g., "4-6".  
     * initialSceneId (String): ID of the first scene in this story.  
     * version (String): e.g., "1.0.0". Used for content updates.  
     * published (Boolean): true if the story is live and visible to users.  
     * createdAt (Timestamp).  
     * updatedAt (Timestamp).  
     * assetManifestUrl (String): Full path in Firebase Storage to assetManifest.json for this story version.  
     * supportedLanguages (List): e.g., \["en-US", "es-ES"\].  
     * defaultLanguage (String): e.g., "en-US".  
     * narratorPersonaGuidance (String): Hint for TTS voice style, e.g., "warm, gentle, grandmotherly".  
     * estimatedDurationMinutes (Number): Approximate story length.  
     * isFree (Boolean): true if part of the freemium offering.  
2. **stories/{storyId}/scenes** (Subcollection)  
   * Document ID: sceneId (String, e.g., SCENE\_1\_INTRO from story scripts)  
   * Fields:  
     * order (Number): For sequencing if needed, though nextSceneId primarily drives flow.  
     * sceneType (String): Enum-like: "narration\_illustration", "choice\_point", "conclusion".  
     * backgroundImageUrl (String): Path in Cloud Storage.  
     * textSegmentsForTTS (List\<Map\<String, dynamic\>\>): Array of text segments.  
       * id (String): Unique ID for the segment, e.g., seg\_01.  
       * text (Map\<String, String\>): Localized text for TTS, e.g., {"en-US": "Hello there...", "es-ES": "Hola..."}.  
       * emotionCue (String): Descriptive cue for TTS emotional delivery, e.g., "warm, gentle", "sad, whisper", "excited".  
       * ssml (Map\<String, String\>, optional): Pre-crafted SSML for specific languages if direct SSML is better, e.g., {"en-US": "\<speak\>...\</speak\>"}.  
       * durationEstimateMs (Number, optional): Estimated duration for this segment.  
     * backgroundMusic (Map\<String, dynamic\>, optional):  
       * trackUrl (String): Path in Cloud Storage.  
       * volume (Number): 0.0 to 1.0.  
       * loop (Boolean).  
     * soundEffects (List\<Map\<String, dynamic\>\>, optional): Array of SFX configurations.  
       * trigger (String): e.g., "onSegmentStart", "onChoiceMade", "onDiscoveryTap".  
       * segmentIdRef (String, optional): If trigger is onSegmentStart, refers to textSegmentForTTS.id.  
       * sfxUrl (String): Path in Cloud Storage.  
       * volume (Number).  
     * nextSceneId (String, nullable): For linear narration\_illustration scenes, ID of the next scene. Null if it's the end of a branch or leads to choices.  
     * choices (List\<Map\<String, dynamic\>\>, nullable): For choice\_point scenes.  
       * choiceId (String): e.g., tell\_truth.  
       * displayTextKey (Map\<String, String\>): Localization key for UI button text.  
       * displayIconUrl (String): Path in Cloud Storage for choice button icon.  
       * narratorGuidanceTextForTTS (Map\<String, String\>): Localized TTS for narrator explaining this choice.  
       * guidanceEmotionCue (String).  
       * leadsToSceneId (String): ID of the scene this choice leads to.  
     * promptTextForTTS (Map\<String, String\>, nullable): Localized TTS for the main question at a choice\_point.  
     * promptEmotionCue (String, nullable): Emotion for the main choice prompt.  
     * conclusionMessageKey (Map\<String, String\>, nullable): Localization key for the final message on a conclusion scene (Screen 8).  
3. **users** (Collection)  
   * Document ID: userId (String, UID from Firebase Authentication)  
   * Fields:  
     * email (String, nullable): If Email/Password auth used.  
     * displayName (String, nullable): Parent's name for Parent Zone.  
     * createdAt (Timestamp).  
     * lastLoginAt (Timestamp).  
     * appPreferences (Map\<String, dynamic\>):  
       * masterVolume (Number): 0.0-1.0.  
       * musicEnabled (Boolean).  
       * sfxEnabled (Boolean).  
       * narrationLanguage (String): e.g., "en-US".  
     * subscription (Map\<String, dynamic\>):  
       * tier (String): e.g., "free", "premium\_monthly", "premium\_annual".  
       * status (String): e.g., "active", "trial", "expired", "cancelled".  
       * expiryDate (Timestamp, nullable).  
       * platform (String): "ios" or "android".  
       * originalTransactionId (String, nullable).  
     * ftueCompleted (Boolean): true if First Time User Experience tutorial is done.  
     * (Future) childProfiles (List\<Map\<String, dynamic\>\>): For multi-child support.  
4. **app\_config** (Collection)  
   * Document ID: global\_settings (Single document)  
   * Fields:  
     * minRequiredAppVersion (String): e.g., "1.1.0".  
     * isMaintenanceMode (Boolean).  
     * maintenanceMessage (Map\<String, String\>): Localized.  
     * latestNewsUrl (Map\<String, String\>, optional): Localized URL for news/updates.  
     * defaultMoralValuesOrder (List): Default display order for values in Parent Zone.

### **5.2. Finalized Story JSON Structure**

The structure described in Section 5.1 for stories/{storyId} and its subcollection scenes/{sceneId} represents the finalized JSON structure that will be stored in Firestore and parsed by the app. The story scripts (Story 01, 02, 03\) will be converted into this JSON format. Each story script will map to one document in the stories collection, and its narrative flow, choices, and text segments will populate the scenes subcollection.

**Example Snippet (Conceptual pip\_pantry\_puzzle.json \- simplified for brevity):**

// Document: stories/pip\_pantry\_puzzle  
{  
  "title": {"en-US": "Pip and the Pantry Puzzle"},  
  "coverImageUrl": "story\_covers/pip\_pantry\_puzzle\_cover\_1.0.0.png",  
  "initialSceneId": "SCENE\_1\_INTRO",  
  "version": "1.0.0",  
  "isFree": true,  
  // ... other story metadata  
  // Scenes would be in subcollection stories/pip\_pantry\_puzzle/scenes/SCENE\_1\_INTRO  
}

// Document: stories/pip\_pantry\_puzzle/scenes/SCENE\_1\_INTRO  
{  
  "sceneType": "narration\_illustration",  
  "backgroundImageUrl": "stories/pip\_pantry\_puzzle/1.0.0/images/kitchen\_intro.png",  
  "textSegmentsForTTS": \[  
    {  
      "id": "seg\_01",  
      "text": {"en-US": "Hello there, my little sleepyhead..."},  
      "emotionCue": "warm, gentle, welcoming"  
    },  
    // ... more segments  
  \],  
  "nextSceneId": "SCENE\_1\_DILEMMA"  
}

// Document: stories/pip\_pantry\_puzzle/scenes/SCENE\_1\_DILEMMA  
{  
  "sceneType": "choice\_point",  
  "backgroundImageUrl": "stories/pip\_pantry\_puzzle/1.0.0/images/kitchen\_dilemma.png",  
  "promptTextForTTS": {"en-US": "Oh, Pip needs your help to decide\! What should he do now?"},  
  "promptEmotionCue": "gentle, empathetic, questioning",  
  "choices": \[  
    {  
      "choiceId": "tell\_truth",  
      "displayTextKey": {"en-US": "Tell Mama the truth."},  
      "displayIconUrl": "ui\_assets/global/icon\_truth.svg",  
      "narratorGuidanceTextForTTS": {"en-US": "Maybe Pip could be a 'truth helper' and tell Mama what happened?"},  
      "guidanceEmotionCue": "encouraging, thoughtful",  
      "leadsToSceneId": "BRANCH\_A\_1\_TRUTH"  
    },  
    // ... other choices  
  \]  
}

## **6\. Asset Management & Pipeline**

### **6.1. Confirmation of Asset Naming Conventions & Folder Structure**

(As per Task 2.3 \- Art Style Definition & Asset Creation Plan)

* **Naming Convention:** StoryShortCode\_AssetType\_SpecificName\_StateVariation\#\#.format  
  * StoryShortCode: Pip, Lila, Finley, GlobalUI  
  * AssetType: Char, BG, Obj, Icon, Btn, Cover, Effect, Anim, SFX, Music  
  * SpecificName: Descriptive (e.g., PipSqueakerton, Kitchen, MoonpetalFlower)  
  * StateVariation\#\#: (e.g., Happy01, Glowing, Pressed, Loop01)  
  * Example: Pip\_Char\_PipSqueakerton\_Worried01.png, GlobalUI\_SFX\_ButtonTap\_Gentle01.mp3  
* **Folder Structure (Firebase Cloud Storage \- mirrored locally for offline):**  
  /stories/{storyId}/{version}/  
      images/  
          {imageName.png} / {imageName.webp}  
      audio/  
          sfx/{sfxName.mp3}  
          music/{musicName.mp3}  
      assetManifest.json  // Lists all assets for this story version with their paths and hashes  
  /story\_covers/  
      {storyId}\_cover\_{version}.png / .webp  
  /ui\_assets/  
      global/  
          icons/{iconName.svg} / {iconName.png}  
          sfx/{sfxName.mp3}

### **6.2. Process for Optimizing Assets**

* **Images (Task 2.3):**  
  * Source files (PSD, Affinity, Procreate) will be kept.  
  * Export to high-resolution PNGs.  
  * Optimize PNGs using tools like TinyPNG, ImageOptim, or flutter\_image\_compress.  
  * Consider using WebP format for better compression where supported by target devices (Flutter supports WebP). Test compatibility.  
  * Provide assets at different resolutions (@1x, @2x, @3x) or use vector graphics (SVG for UI elements) where possible to handle various screen densities. Flutter's asset system supports resolution-aware assets.  
* **Audio (SFX, Music \- Task 2.4):**  
  * Source files (WAV).  
  * Export to compressed formats:  
    * Music: MP3 (192-256 kbps VBR) or OGG Vorbis.  
    * SFX & UI Sounds: MP3 or M4A (AAC) for good quality/size. Very short, frequently used UI sounds could be WAV if small.  
  * Ensure seamless loops for background music.  
  * Normalize audio levels.  
* **Asset Manifest (assetManifest.json per story version):**  
  * This JSON file, stored in Cloud Storage alongside story assets, will list all asset paths for a specific story version.  
  * The app will download this manifest first to know which assets to fetch for offline use.  
  * May include hashes/checksums for asset integrity and update checks.

### **6.3. How Assets Will Be Linked from Database/Story Data to Flutter App**

* Firestore documents (StoryModel, SceneModel, etc.) will store relative paths or unique asset IDs that correspond to the filenames in Cloud Storage (and thus the local offline cache).  
* Example: SceneModel.backgroundImageUrl might store "images/kitchen\_intro.png".  
* The app will construct the full Cloud Storage URL or local file path by combining a base path (derived from storyId and version) with this relative asset path.  
* For Flutter's asset system (if bundling some global UI assets): Declare in pubspec.yaml.  
  flutter:  
    assets:  
      \- assets/ui\_assets/global/icons/  
      \- assets/ui\_assets/global/sfx/

* Dynamically loaded assets (story images, audio) will be fetched from Firebase Storage (if online and not cached) or the local file system (if offline/cached) using their constructed paths.

## **7\. Third-Party Service Integrations**

### **7.1. TTS Services**

* **Primary:** Native OS TTS engines (iOS AVSpeechSynthesizer, Android TextToSpeech) via flutter\_tts. No direct third-party service integration for *on-device playback* itself.  
* **Fallback/Audio Generation (If Needed):**  
  * If the on-device TTS quality for emotional delivery is insufficient, services like **Google Cloud Text-to-Speech** (with WaveNet voices) or **Amazon Polly** (Neural TTS) would be used *server-side* (via Cloud Functions or a separate admin tool) to pre-generate audio files for story segments. These files would then be stored in Firebase Cloud Storage and downloaded by the app. This is not a direct client-side SDK integration for playback.

### **7.2. Analytics Platform**

* **Firebase Analytics:** Will be integrated by default with firebase\_core and firebase\_analytics packages.  
* **Events to Track (MVP):**  
  * story\_selected (parameters: story\_id, story\_title)  
  * story\_completed (parameters: story\_id, branch\_chosen (optional))  
  * choice\_made (parameters: story\_id, scene\_id, choice\_id)  
  * subscription\_initiated (parameters: plan\_type)  
  * subscription\_successful (parameters: plan\_type)  
  * parent\_zone\_accessed  
  * ftue\_completed / ftue\_skipped  
  * Screen views (automatic with Firebase Analytics).

### **7.3. In-App Purchase (IAP) Platform Integration**

* **Package:** in\_app\_purchase (official Flutter package).  
* **Alternative/Wrapper (Consider):** RevenueCat (simplifies cross-platform subscription management, receipt validation, and analytics). Decision based on team familiarity and desired abstraction level. For MVP, in\_app\_purchase with server-side validation via Cloud Functions is viable.  
* **Flow:**  
  1. App fetches product details (subscription plans) from App Store Connect / Google Play Console using in\_app\_purchase.  
  2. User initiates purchase (Screen 12).  
  3. App uses in\_app\_purchase to make the purchase request.  
  4. Platform (iOS/Android) handles the purchase flow.  
  5. On completion, app receives purchase details/receipt.  
  6. App sends receipt data to a Firebase Cloud Function (validateAppleReceipt/validateGoogleReceipt).  
  7. Cloud Function validates receipt with Apple/Google servers.  
  8. If valid, Cloud Function updates user's subscription status in their Firestore document.  
  9. App listens to Firestore changes (or gets response from function) to unlock premium content.  
  10. Implement "Restore Purchases" functionality.

## **8\. Security Design**

### **8.1. Data Protection Measures**

* **User Data (Firestore users collection):**  
  * Protected by Firestore Security Rules: Only the authenticated user can read/write their own data. Admin access via trusted mechanisms (e.g., service accounts for support tools, not direct app access).  
  * Minimize PII collection. For MVP, parent's email (if used for auth) is the main PII. Child names for profiles (future) would be stored within the parent's protected document.  
  * No sensitive child data (e.g., precise location, photos) will be collected.  
* **Story Content (Firestore stories collection):**  
  * Read-only for app users. Write access restricted to admin roles (via Firebase console or admin SDK).  
* **COPPA/GDPR-K Compliance:** Design and data handling practices will adhere to relevant children's privacy regulations. This includes clear privacy policy, parental consent mechanisms (implicit via Parent Zone for purchases), and data minimization.

### **8.2. Secure API Communication**

* All communication with Firebase services (Firestore, Cloud Storage, Auth, Functions) uses HTTPS by default, handled by Firebase SDKs.  
* Firebase Cloud Functions (HTTPS Callable) are protected by Firebase's infrastructure. context.auth will be checked in callable functions to ensure requests come from authenticated users where necessary.

### **8.3. Implementation details for Parental Gate (Screen 9\)**

* **Mechanism:** "Press and hold a button for 3 seconds" (as per Task 2.2).  
* **Implementation:**  
  * A GestureDetector widget wrapping a button.  
  * onTapDown: Start a timer and visual feedback (e.g., a circular progress indicator starts filling).  
  * onTapUp / onTapCancel: Cancel the timer, reset visual feedback.  
  * If timer completes (3 seconds) while still pressed, trigger navigation to Parent Zone.  
* Alternative simple gates (e.g., "Solve 15 \+ 22 \= ?") could be considered if hold proves unreliable, but hold is simpler for MVP.

### **8.4. Content Security for Assets**

* **Firebase Cloud Storage Rules:** Configured for public read access for story assets (images, audio) to allow CDN delivery. Write access is restricted to admin accounts.  
* While assets are publicly readable via their URLs, the URLs themselves are not easily guessable if not listed in manifests or story data.  
* No sensitive information will be embedded directly in asset files.

## **9\. Performance Considerations**

### **9.1. Strategies for Optimizing App Startup Time**

* Minimize synchronous work in main.dart before runApp().  
* Use FlutterNativeSplash or similar to show a native splash screen quickly.  
* Lazy load services and providers where possible using Riverpod's autoDispose or manual management.  
* Optimize initial data fetching for the Story Library (e.g., fetch only essential metadata initially).  
* Code splitting (deferred loading of Flutter modules) if app size becomes very large (less likely for MVP).

### **9.2. Efficient Loading and Display of Story Scenes and Assets**

* **Images:**  
  * Use FadeInImage or similar to show placeholders while images load.  
  * Use appropriate image caching strategies (e.g., cached\_network\_image if fetching from network, or ensure local assets are readily available).  
  * Load only assets needed for the current and immediately next scene if pre-caching.  
  * Use optimized image formats (WebP) and resolutions.  
* **Audio:**  
  * Pre-load or buffer audio for the current/next segment to ensure smooth playback. just\_audio offers good buffering capabilities.  
  * Stream music if not fully downloaded for offline use.  
* **Story Data:**  
  * Load full story data (StoryModel) when a story is selected. For very large stories (future), consider partial loading, but for MVP (3 stories), full load is acceptable.  
  * Efficient parsing of JSON into Dart models.

### **9.3. Smooth Animations and Transitions**

* Keep animations simple and performant (as per Task 2.3 and Task 2.4). Favor opacity, transform, and color animations over those that trigger relayouts.  
* Use Flutter's animation framework correctly (e.g., AnimatedWidget, ImplicitlyAnimatedWidget).  
* Profile animations using Flutter DevTools to identify jank.  
* Ensure screen transitions (GoRouter) are smooth. Use appropriate PageTransition types if custom transitions are needed.

### **9.4. Minimizing Battery Consumption**

* **TTS:** On-device TTS is generally more battery-efficient than continuous network streaming for audio generation. However, CPU usage during synthesis can impact battery. Optimize TTS usage (e.g., synthesize only what's needed).  
* **Audio Playback:** Dispose of audio players (just\_audio, flutter\_tts) when not in use.  
* **Animations:** Pause or disable non-essential animations when the app is in the background or on screens where they are not visible.  
* **Background Processes:** Minimize background activity. Offline downloads should provide clear progress and allow pausing.  
* **Network Requests:** Batch requests where possible. Use efficient data formats.

## **10\. Technical Testing Strategy**

### **10.1. Approach to Unit Testing**

* **Scope:** Test individual functions, methods, and classes in isolation.  
  * Dart models (StoryModel, SceneModel, etc.): Test fromJson parsing logic, any utility methods.  
  * Services (TTSService, EmotionCueMapperService, OfflineStorageService, StoryEngineService): Mock dependencies and test business logic.  
  * Riverpod Providers (StoryPlayerProvider, StoryLibraryProvider): Test state changes and logic, mocking repositories/services.  
* **Tools:** flutter\_test package, mockito or mocktail for mocking.  
* **Goal:** High code coverage for core logic and data manipulation.

### **10.2. Approach to Widget Testing**

* **Scope:** Test individual Flutter widgets in isolation.  
  * Verify UI rendering based on input properties.  
  * Test widget interactions (tapping buttons, entering text if any).  
  * Verify correct state changes within the widget or calls to callbacks.  
  * Examples: StoryCoverCardWidget, \`