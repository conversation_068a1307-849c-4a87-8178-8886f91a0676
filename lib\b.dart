import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'firebase_options.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await Firebase.initializeApp(
    options: DefaultFirebaseOptions.currentPlatform,
  );
  // Configure Analytics for COPPA compliance (kids' app)
  FirebaseAnalytics.instance.setAnalyticsCollectionEnabled(true);
  FirebaseAnalytics.instance.setUserProperty(
    name: 'restricted_data_processing',
    value: 'true',
  );
  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Kids Storytelling App',
      theme: ThemeData(
        primarySwatch: Colors.blue,
        textTheme: const TextTheme(
          bodyMedium: TextStyle(fontSize: 24), // Large text for kids
        ),
      ),
      home: const StoryListScreen(),
    );
  }
}

class StoryListScreen extends StatefulWidget {
  const StoryListScreen({super.key});

  @override
  _StoryListScreenState createState() => _StoryListScreenState();
}

class _StoryListScreenState extends State<StoryListScreen> {
  List<Map<String, String>> stories = [];
  bool isLoading = true;
  String? errorMessage;

  @override
  void initState() {
    super.initState();
    _loadStories();
  }

  Future<void> _loadStories() async {
    try {
      // Load stories from both sources concurrently
      final assetStoriesFuture = _loadAssetStories();
      final firestoreStoriesFuture = _loadFirestoreStories();
      final results = await Future.wait([assetStoriesFuture, firestoreStoriesFuture]);

      final assetStories = results[0] as List<Map<String, String>>;
      final firestoreStories = results[1] as List<Map<String, String>>;

      setState(() {
        stories = [...assetStories, ...firestoreStories];
        isLoading = false;
      });

      // Log app open event with story count (COPPA-compliant)
      await FirebaseAnalytics.instance.logEvent(
        name: 'app_open',
        parameters: {'story_count': stories.length},
      );
    } catch (e) {
      print('Error loading stories: $e');
      setState(() {
        errorMessage = 'Failed to load stories';
        isLoading = false;
      });
    }
  }

  Future<List<Map<String, String>>> _loadAssetStories() async {
    try {
      // Load asset manifest
      final manifest = await DefaultAssetBundle.of(context).loadString('AssetManifest.json');
      final Map<String, dynamic> manifestMap = json.decode(manifest);

      // Filter for story folders (assets/stories/<storyid>/)
      final storyIds = manifestMap.keys
          .where((key) => key.startsWith('assets/stories/'))
          .map((key) {
            final parts = key.split('/');
            if (parts.length > 2) return parts[2]; // Get <storyid>
            return null;
          })
          .whereType<String>()
          .toSet()
          .toList();

      // Debug: Print asset story IDs
      print('Asset stories found: $storyIds');

      // Map to story objects with source
      return storyIds.map((id) => {
            'id': id,
            'title': id, // Use ID as title (can parse story.json for actual title later)
            'source': 'assets',
          }).toList();
    } catch (e) {
      print('Error loading asset stories: $e');
      return [];
    }
  }

  Future<List<Map<String, String>>> _loadFirestoreStories() async {
    try {
      // Query Firestore for stories
      final querySnapshot = await FirebaseFirestore.instance.collection('stories').get();

      // Debug: Print Firestore story IDs
      print('Firestore stories found: ${querySnapshot.docs.map((doc) => doc.id).toList()}');

      // Map to story objects with source
      return querySnapshot.docs.map((doc) {
        final data = doc.data();
        return {
          'id': doc.id,
          'title': data['title']?.toString() ?? doc.id, // Use title if available, else ID
          'source': 'firestore',
        };
      }).toList();
    } catch (e) {
      print('Error loading Firestore stories: $e');
      return [];
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('All Stories'),
      ),
      body: isLoading
          ? const Center(child: CircularProgressIndicator())
          : errorMessage != null
              ? Center(
                  child: Text(
                    errorMessage!,
                    style: const TextStyle(fontSize: 24, color: Colors.red),
                    textAlign: TextAlign.center,
                  ),
                )
              : stories.isEmpty
                  ? const Center(
                      child: Text(
                        'No stories available',
                        style: TextStyle(fontSize: 24),
                        textAlign: TextAlign.center,
                      ),
                    )
                  : ListView.builder(
                      padding: const EdgeInsets.all(16),
                      itemCount: stories.length,
                      itemBuilder: (context, index) {
                        final story = stories[index];
                        return Card(
                          elevation: 2,
                          margin: const EdgeInsets.symmetric(vertical: 8),
                          child: ListTile(
                            title: Text(
                              story['title']!,
                              style: const TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
                            ),
                            subtitle: Text(
                              'Source: ${story['source']}',
                              style: const TextStyle(fontSize: 16),
                            ),
                            onTap: () async {
                              // Log story selection (COPPA-compliant)
                              await FirebaseAnalytics.instance.logEvent(
                                name: 'select_story',
                                parameters: {
                                  'story_id': story['id'],
                                  'source': story['source'],
                                },
                              );
                              print('Tapped story: ${story['id']} (${story['source']})');
                            },
                          ),
                        );
                      },
                    ),
    );
  }
}