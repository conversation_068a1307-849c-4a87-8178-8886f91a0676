import 'dart:io';
import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:logger/logger.dart';
import 'package:choice_once_upon_a_time/core/utils/app_logger.dart';

/// Service for managing automatic cleanup of downloaded stories
class StoryCleanupService {
  static final StoryCleanupService _instance = StoryCleanupService._internal();
  factory StoryCleanupService() => _instance;
  StoryCleanupService._internal();

  final Logger _logger = Logger();
  static const String _downloadTrackingKey = 'story_download_tracking';
  static const String _lastCleanupKey = 'last_cleanup_date';
  static const int _maxAgeInDays = 30;

  /// Records when a story was downloaded
  Future<void> recordStoryDownload(String storyId, String localPath) async {
    try {
      AppLogger.debug('[CLEANUP_SERVICE] Recording download for story: $storyId');
      
      final prefs = await SharedPreferences.getInstance();
      final trackingData = await _getDownloadTrackingData();
      
      trackingData[storyId] = {
        'downloadedAt': DateTime.now().toIso8601String(),
        'localPath': localPath,
        'lastAccessedAt': DateTime.now().toIso8601String(),
        'accessCount': 1,
      };
      
      await prefs.setString(_downloadTrackingKey, jsonEncode(trackingData));
      AppLogger.debug('[CLEANUP_SERVICE] Recorded download for story: $storyId');
    } catch (e) {
      _logger.e('[CLEANUP_SERVICE] Failed to record story download: $e');
    }
  }

  /// Updates the last accessed time for a story
  Future<void> updateStoryAccess(String storyId) async {
    try {
      AppLogger.debug('[CLEANUP_SERVICE] Updating access for story: $storyId');
      
      final prefs = await SharedPreferences.getInstance();
      final trackingData = await _getDownloadTrackingData();
      
      if (trackingData.containsKey(storyId)) {
        final storyData = trackingData[storyId] as Map<String, dynamic>;
        storyData['lastAccessedAt'] = DateTime.now().toIso8601String();
        storyData['accessCount'] = (storyData['accessCount'] as int? ?? 0) + 1;
        
        await prefs.setString(_downloadTrackingKey, jsonEncode(trackingData));
        AppLogger.debug('[CLEANUP_SERVICE] Updated access for story: $storyId');
      }
    } catch (e) {
      _logger.e('[CLEANUP_SERVICE] Failed to update story access: $e');
    }
  }

  /// Removes a story from tracking (when manually deleted)
  Future<void> removeStoryFromTracking(String storyId) async {
    try {
      AppLogger.debug('[CLEANUP_SERVICE] Removing story from tracking: $storyId');
      
      final prefs = await SharedPreferences.getInstance();
      final trackingData = await _getDownloadTrackingData();
      
      trackingData.remove(storyId);
      
      await prefs.setString(_downloadTrackingKey, jsonEncode(trackingData));
      AppLogger.debug('[CLEANUP_SERVICE] Removed story from tracking: $storyId');
    } catch (e) {
      _logger.e('[CLEANUP_SERVICE] Failed to remove story from tracking: $e');
    }
  }

  /// Performs automatic cleanup of old downloaded stories
  Future<CleanupResult> performAutomaticCleanup({bool force = false}) async {
    try {
      AppLogger.debug('[CLEANUP_SERVICE] Starting automatic cleanup (force: $force)');
      
      final prefs = await SharedPreferences.getInstance();
      final lastCleanup = prefs.getString(_lastCleanupKey);
      final now = DateTime.now();
      
      // Check if cleanup is needed (daily check unless forced)
      if (!force && lastCleanup != null) {
        final lastCleanupDate = DateTime.parse(lastCleanup);
        final daysSinceLastCleanup = now.difference(lastCleanupDate).inDays;
        
        if (daysSinceLastCleanup < 1) {
          AppLogger.debug('[CLEANUP_SERVICE] Cleanup not needed, last cleanup was recent');
          return CleanupResult(
            storiesChecked: 0,
            storiesDeleted: 0,
            spaceFreed: 0,
            errors: [],
          );
        }
      }
      
      final trackingData = await _getDownloadTrackingData();
      final result = CleanupResult(
        storiesChecked: trackingData.length,
        storiesDeleted: 0,
        spaceFreed: 0,
        errors: [],
      );
      
      final storiesToDelete = <String>[];
      
      // Check each tracked story
      for (final entry in trackingData.entries) {
        final storyId = entry.key;
        final storyData = entry.value as Map<String, dynamic>;
        
        try {
          final downloadedAt = DateTime.parse(storyData['downloadedAt'] as String);
          final ageInDays = now.difference(downloadedAt).inDays;
          
          if (ageInDays >= _maxAgeInDays) {
            AppLogger.debug('[CLEANUP_SERVICE] Story $storyId is $ageInDays days old, marking for deletion');
            storiesToDelete.add(storyId);
          }
        } catch (e) {
          _logger.w('[CLEANUP_SERVICE] Invalid date format for story $storyId: $e');
          result.errors.add('Invalid date format for story $storyId');
        }
      }
      
      // Delete old stories
      for (final storyId in storiesToDelete) {
        try {
          final storyData = trackingData[storyId] as Map<String, dynamic>;
          final localPath = storyData['localPath'] as String;
          
          final storyDir = Directory(localPath);
          if (await storyDir.exists()) {
            final dirSize = await _calculateDirectorySize(storyDir);
            await storyDir.delete(recursive: true);
            result.spaceFreed += dirSize;
            AppLogger.debug('[CLEANUP_SERVICE] Deleted story directory: $localPath');
          }
          
          // Remove from tracking
          trackingData.remove(storyId);
          result.storiesDeleted++;
          
        } catch (e) {
          _logger.e('[CLEANUP_SERVICE] Failed to delete story $storyId: $e');
          result.errors.add('Failed to delete story $storyId: $e');
        }
      }
      
      // Update tracking data and last cleanup time
      await prefs.setString(_downloadTrackingKey, jsonEncode(trackingData));
      await prefs.setString(_lastCleanupKey, now.toIso8601String());
      
      AppLogger.debug('[CLEANUP_SERVICE] Cleanup completed: ${result.storiesDeleted} stories deleted, ${result.spaceFreed} bytes freed');
      return result;
      
    } catch (e) {
      _logger.e('[CLEANUP_SERVICE] Failed to perform automatic cleanup: $e');
      return CleanupResult(
        storiesChecked: 0,
        storiesDeleted: 0,
        spaceFreed: 0,
        errors: ['Failed to perform cleanup: $e'],
      );
    }
  }

  /// Gets information about downloaded stories and their ages
  Future<List<DownloadedStoryInfo>> getDownloadedStoriesInfo() async {
    try {
      final trackingData = await _getDownloadTrackingData();
      final storiesInfo = <DownloadedStoryInfo>[];
      final now = DateTime.now();
      
      for (final entry in trackingData.entries) {
        final storyId = entry.key;
        final storyData = entry.value as Map<String, dynamic>;
        
        try {
          final downloadedAt = DateTime.parse(storyData['downloadedAt'] as String);
          final lastAccessedAt = DateTime.parse(storyData['lastAccessedAt'] as String);
          final localPath = storyData['localPath'] as String;
          final accessCount = storyData['accessCount'] as int? ?? 0;
          
          final ageInDays = now.difference(downloadedAt).inDays;
          final daysSinceLastAccess = now.difference(lastAccessedAt).inDays;
          
          // Calculate directory size
          final storyDir = Directory(localPath);
          final sizeInBytes = await storyDir.exists() 
              ? await _calculateDirectorySize(storyDir)
              : 0;
          
          storiesInfo.add(DownloadedStoryInfo(
            storyId: storyId,
            downloadedAt: downloadedAt,
            lastAccessedAt: lastAccessedAt,
            localPath: localPath,
            ageInDays: ageInDays,
            daysSinceLastAccess: daysSinceLastAccess,
            accessCount: accessCount,
            sizeInBytes: sizeInBytes,
            willBeDeletedSoon: ageInDays >= _maxAgeInDays - 7, // Warning 7 days before deletion
            isExpired: ageInDays >= _maxAgeInDays,
          ));
        } catch (e) {
          _logger.w('[CLEANUP_SERVICE] Invalid data for story $storyId: $e');
        }
      }
      
      // Sort by age (oldest first)
      storiesInfo.sort((a, b) => b.ageInDays.compareTo(a.ageInDays));
      
      return storiesInfo;
    } catch (e) {
      _logger.e('[CLEANUP_SERVICE] Failed to get downloaded stories info: $e');
      return [];
    }
  }

  /// Gets the download tracking data from SharedPreferences
  Future<Map<String, dynamic>> _getDownloadTrackingData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final trackingJson = prefs.getString(_downloadTrackingKey);
      
      if (trackingJson != null) {
        return jsonDecode(trackingJson) as Map<String, dynamic>;
      }
      
      return <String, dynamic>{};
    } catch (e) {
      _logger.e('[CLEANUP_SERVICE] Failed to get download tracking data: $e');
      return <String, dynamic>{};
    }
  }

  /// Calculates the total size of a directory in bytes
  Future<int> _calculateDirectorySize(Directory directory) async {
    try {
      int totalSize = 0;
      
      await for (final entity in directory.list(recursive: true)) {
        if (entity is File) {
          final stat = await entity.stat();
          totalSize += stat.size;
        }
      }
      
      return totalSize;
    } catch (e) {
      _logger.w('[CLEANUP_SERVICE] Failed to calculate directory size: $e');
      return 0;
    }
  }

  /// Gets the maximum age in days before stories are deleted
  int get maxAgeInDays => _maxAgeInDays;

  /// Checks if automatic cleanup is enabled
  Future<bool> isAutomaticCleanupEnabled() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getBool('automatic_cleanup_enabled') ?? true;
    } catch (e) {
      return true; // Default to enabled
    }
  }

  /// Sets whether automatic cleanup is enabled
  Future<void> setAutomaticCleanupEnabled(bool enabled) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool('automatic_cleanup_enabled', enabled);
    } catch (e) {
      _logger.e('[CLEANUP_SERVICE] Failed to set automatic cleanup preference: $e');
    }
  }
}

/// Result of a cleanup operation
class CleanupResult {
  final int storiesChecked;
  int storiesDeleted;
  int spaceFreed; // in bytes
  final List<String> errors;

  CleanupResult({
    required this.storiesChecked,
    required this.storiesDeleted,
    required this.spaceFreed,
    required this.errors,
  });

  bool get hasErrors => errors.isNotEmpty;
  bool get hasDeletedStories => storiesDeleted > 0;
  
  String get spaceFreedFormatted {
    if (spaceFreed < 1024) return '${spaceFreed}B';
    if (spaceFreed < 1024 * 1024) return '${(spaceFreed / 1024).toStringAsFixed(1)}KB';
    if (spaceFreed < 1024 * 1024 * 1024) return '${(spaceFreed / (1024 * 1024)).toStringAsFixed(1)}MB';
    return '${(spaceFreed / (1024 * 1024 * 1024)).toStringAsFixed(1)}GB';
  }
}

/// Information about a downloaded story
class DownloadedStoryInfo {
  final String storyId;
  final DateTime downloadedAt;
  final DateTime lastAccessedAt;
  final String localPath;
  final int ageInDays;
  final int daysSinceLastAccess;
  final int accessCount;
  final int sizeInBytes;
  final bool willBeDeletedSoon;
  final bool isExpired;

  DownloadedStoryInfo({
    required this.storyId,
    required this.downloadedAt,
    required this.lastAccessedAt,
    required this.localPath,
    required this.ageInDays,
    required this.daysSinceLastAccess,
    required this.accessCount,
    required this.sizeInBytes,
    required this.willBeDeletedSoon,
    required this.isExpired,
  });

  String get sizeFormatted {
    if (sizeInBytes < 1024) return '${sizeInBytes}B';
    if (sizeInBytes < 1024 * 1024) return '${(sizeInBytes / 1024).toStringAsFixed(1)}KB';
    if (sizeInBytes < 1024 * 1024 * 1024) return '${(sizeInBytes / (1024 * 1024)).toStringAsFixed(1)}MB';
    return '${(sizeInBytes / (1024 * 1024 * 1024)).toStringAsFixed(1)}GB';
  }
}
