// Mocks generated by <PERSON><PERSON><PERSON> 5.4.4 from annotations
// in choice_once_upon_a_time/test/features/story_library/data/story_repository_firebase_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i3;

import 'package:choice_once_upon_a_time/core/services/asset_only_story_service.dart'
    as _i6;
import 'package:choice_once_upon_a_time/core/services/enhanced_story_service.dart'
    as _i9;
import 'package:choice_once_upon_a_time/core/services/firebase_storage_service.dart'
    as _i2;
import 'package:choice_once_upon_a_time/core/services/zip_extraction_service.dart'
    as _i5;
import 'package:choice_once_upon_a_time/models/enhanced_story_model.dart'
    as _i10;
import 'package:choice_once_upon_a_time/models/story_metadata_model.dart'
    as _i7;
import 'package:choice_once_upon_a_time/models/story_model.dart' as _i8;
import 'package:mockito/mockito.dart' as _i1;
import 'package:mockito/src/dummies.dart' as _i4;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

/// A class which mocks [FirebaseStorageService].
///
/// See the documentation for Mockito's code generation for more information.
class MockFirebaseStorageService extends _i1.Mock
    implements _i2.FirebaseStorageService {
  MockFirebaseStorageService() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i3.Future<String> downloadStoryZip(
    String? storyId, {
    dynamic Function(double)? onProgress,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #downloadStoryZip,
          [storyId],
          {#onProgress: onProgress},
        ),
        returnValue: _i3.Future<String>.value(_i4.dummyValue<String>(
          this,
          Invocation.method(
            #downloadStoryZip,
            [storyId],
            {#onProgress: onProgress},
          ),
        )),
      ) as _i3.Future<String>);

  @override
  _i3.Future<Map<String, dynamic>?> downloadStoryMetadata(String? storyId) =>
      (super.noSuchMethod(
        Invocation.method(
          #downloadStoryMetadata,
          [storyId],
        ),
        returnValue: _i3.Future<Map<String, dynamic>?>.value(),
      ) as _i3.Future<Map<String, dynamic>?>);

  @override
  _i3.Future<bool> storyExists(String? storyId) => (super.noSuchMethod(
        Invocation.method(
          #storyExists,
          [storyId],
        ),
        returnValue: _i3.Future<bool>.value(false),
      ) as _i3.Future<bool>);

  @override
  _i3.Future<String?> getAssetDownloadUrl(
    String? storyId,
    String? assetPath,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #getAssetDownloadUrl,
          [
            storyId,
            assetPath,
          ],
        ),
        returnValue: _i3.Future<String?>.value(),
      ) as _i3.Future<String?>);

  @override
  _i3.Future<List<String>> listAvailableStories() => (super.noSuchMethod(
        Invocation.method(
          #listAvailableStories,
          [],
        ),
        returnValue: _i3.Future<List<String>>.value(<String>[]),
      ) as _i3.Future<List<String>>);
}

/// A class which mocks [ZipExtractionService].
///
/// See the documentation for Mockito's code generation for more information.
class MockZipExtractionService extends _i1.Mock
    implements _i5.ZipExtractionService {
  MockZipExtractionService() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i3.Future<String> extractStoryZip(
    String? zipFilePath,
    String? storyId, {
    dynamic Function(double)? onProgress,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #extractStoryZip,
          [
            zipFilePath,
            storyId,
          ],
          {#onProgress: onProgress},
        ),
        returnValue: _i3.Future<String>.value(_i4.dummyValue<String>(
          this,
          Invocation.method(
            #extractStoryZip,
            [
              zipFilePath,
              storyId,
            ],
            {#onProgress: onProgress},
          ),
        )),
      ) as _i3.Future<String>);

  @override
  _i3.Future<bool> validateStoryStructure(String? storyPath) =>
      (super.noSuchMethod(
        Invocation.method(
          #validateStoryStructure,
          [storyPath],
        ),
        returnValue: _i3.Future<bool>.value(false),
      ) as _i3.Future<bool>);

  @override
  _i3.Future<String> getStoryPath(String? storyId) => (super.noSuchMethod(
        Invocation.method(
          #getStoryPath,
          [storyId],
        ),
        returnValue: _i3.Future<String>.value(_i4.dummyValue<String>(
          this,
          Invocation.method(
            #getStoryPath,
            [storyId],
          ),
        )),
      ) as _i3.Future<String>);

  @override
  _i3.Future<bool> isStoryDownloaded(String? storyId) => (super.noSuchMethod(
        Invocation.method(
          #isStoryDownloaded,
          [storyId],
        ),
        returnValue: _i3.Future<bool>.value(false),
      ) as _i3.Future<bool>);

  @override
  _i3.Future<bool> deleteLocalStory(String? storyId) => (super.noSuchMethod(
        Invocation.method(
          #deleteLocalStory,
          [storyId],
        ),
        returnValue: _i3.Future<bool>.value(false),
      ) as _i3.Future<bool>);

  @override
  _i3.Future<int> getLocalStorySize(String? storyId) => (super.noSuchMethod(
        Invocation.method(
          #getLocalStorySize,
          [storyId],
        ),
        returnValue: _i3.Future<int>.value(0),
      ) as _i3.Future<int>);

  @override
  _i3.Future<List<String>> listLocalStories() => (super.noSuchMethod(
        Invocation.method(
          #listLocalStories,
          [],
        ),
        returnValue: _i3.Future<List<String>>.value(<String>[]),
      ) as _i3.Future<List<String>>);
}

/// A class which mocks [AssetOnlyStoryService].
///
/// See the documentation for Mockito's code generation for more information.
class MockAssetOnlyStoryService extends _i1.Mock
    implements _i6.AssetOnlyStoryService {
  MockAssetOnlyStoryService() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i3.Future<List<_i7.StoryMetadataModel>> getAllStoryMetadata() =>
      (super.noSuchMethod(
        Invocation.method(
          #getAllStoryMetadata,
          [],
        ),
        returnValue: _i3.Future<List<_i7.StoryMetadataModel>>.value(
            <_i7.StoryMetadataModel>[]),
      ) as _i3.Future<List<_i7.StoryMetadataModel>>);

  @override
  _i3.Future<_i8.StoryModel?> loadStory(String? storyId) => (super.noSuchMethod(
        Invocation.method(
          #loadStory,
          [storyId],
        ),
        returnValue: _i3.Future<_i8.StoryModel?>.value(),
      ) as _i3.Future<_i8.StoryModel?>);

  @override
  void clearCache() => super.noSuchMethod(
        Invocation.method(
          #clearCache,
          [],
        ),
        returnValueForMissingStub: null,
      );
}

/// A class which mocks [EnhancedStoryService].
///
/// See the documentation for Mockito's code generation for more information.
class MockEnhancedStoryService extends _i1.Mock
    implements _i9.EnhancedStoryService {
  MockEnhancedStoryService() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i3.Future<List<String>> scanAvailableStories() => (super.noSuchMethod(
        Invocation.method(
          #scanAvailableStories,
          [],
        ),
        returnValue: _i3.Future<List<String>>.value(<String>[]),
      ) as _i3.Future<List<String>>);

  @override
  _i3.Future<List<_i7.StoryMetadataModel>> getAllStoryMetadata() =>
      (super.noSuchMethod(
        Invocation.method(
          #getAllStoryMetadata,
          [],
        ),
        returnValue: _i3.Future<List<_i7.StoryMetadataModel>>.value(
            <_i7.StoryMetadataModel>[]),
      ) as _i3.Future<List<_i7.StoryMetadataModel>>);

  @override
  _i3.Future<_i10.EnhancedStoryModel?> loadStory(String? storyId) =>
      (super.noSuchMethod(
        Invocation.method(
          #loadStory,
          [storyId],
        ),
        returnValue: _i3.Future<_i10.EnhancedStoryModel?>.value(),
      ) as _i3.Future<_i10.EnhancedStoryModel?>);

  @override
  _i3.Future<List<_i10.CharacterModel>> getCharacterProfiles(String? storyId) =>
      (super.noSuchMethod(
        Invocation.method(
          #getCharacterProfiles,
          [storyId],
        ),
        returnValue: _i3.Future<List<_i10.CharacterModel>>.value(
            <_i10.CharacterModel>[]),
      ) as _i3.Future<List<_i10.CharacterModel>>);

  @override
  _i3.Future<_i10.StorySetupModel?> getStorySetup(String? storyId) =>
      (super.noSuchMethod(
        Invocation.method(
          #getStorySetup,
          [storyId],
        ),
        returnValue: _i3.Future<_i10.StorySetupModel?>.value(),
      ) as _i3.Future<_i10.StorySetupModel?>);

  @override
  _i3.Future<_i10.NarratorProfileModel?> getNarratorProfile(String? storyId) =>
      (super.noSuchMethod(
        Invocation.method(
          #getNarratorProfile,
          [storyId],
        ),
        returnValue: _i3.Future<_i10.NarratorProfileModel?>.value(),
      ) as _i3.Future<_i10.NarratorProfileModel?>);

  @override
  _i3.Future<_i10.PostStoryModel?> getPostStoryContent(String? storyId) =>
      (super.noSuchMethod(
        Invocation.method(
          #getPostStoryContent,
          [storyId],
        ),
        returnValue: _i3.Future<_i10.PostStoryModel?>.value(),
      ) as _i3.Future<_i10.PostStoryModel?>);

  @override
  _i3.Future<bool> storyExists(String? storyId) => (super.noSuchMethod(
        Invocation.method(
          #storyExists,
          [storyId],
        ),
        returnValue: _i3.Future<bool>.value(false),
      ) as _i3.Future<bool>);

  @override
  void clearCache() => super.noSuchMethod(
        Invocation.method(
          #clearCache,
          [],
        ),
        returnValueForMissingStub: null,
      );

  @override
  Map<String, dynamic> getCacheStats() => (super.noSuchMethod(
        Invocation.method(
          #getCacheStats,
          [],
        ),
        returnValue: <String, dynamic>{},
      ) as Map<String, dynamic>);
}
