// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'text_segment_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

TextSegmentModel _$TextSegmentModelFromJson(Map<String, dynamic> json) =>
    TextSegmentModel(
      id: json['segmentId'] as String,
      text: Map<String, String>.from(json['text'] as Map),
      emotionCue: json['emotionCue'] as String,
      ssml: (json['ssml'] as Map<String, dynamic>?)?.map(
        (k, e) => MapEntry(k, e as String),
      ),
      durationEstimateMs: (json['durationEstimateMs'] as num?)?.toInt(),
    );

Map<String, dynamic> _$TextSegmentModelToJson(TextSegmentModel instance) =>
    <String, dynamic>{
      'segmentId': instance.id,
      'text': instance.text,
      'emotionCue': instance.emotionCue,
      'ssml': instance.ssml,
      'durationEstimateMs': instance.durationEstimateMs,
    };
