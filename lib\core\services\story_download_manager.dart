import 'dart:async';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:logger/logger.dart';
import 'package:choice_once_upon_a_time/core/services/firebase_storage_service.dart';
import 'package:choice_once_upon_a_time/core/services/zip_extraction_service.dart';
import 'package:choice_once_upon_a_time/core/services/permission_service.dart';
import 'package:choice_once_upon_a_time/core/services/story_cleanup_service.dart';
import 'package:choice_once_upon_a_time/models/firebase_story_metadata.dart';
import 'package:choice_once_upon_a_time/core/utils/app_logger.dart';

/// Service for managing story downloads from Firebase Storage
class StoryDownloadManager {
  static final StoryDownloadManager _instance = StoryDownloadManager._internal();
  factory StoryDownloadManager() => _instance;
  StoryDownloadManager._internal();

  final Logger _logger = Logger();
  final FirebaseStorageService _firebaseStorage = FirebaseStorageService();
  final ZipExtractionService _zipExtraction = ZipExtractionService();
  final PermissionService _permissionService = PermissionService();
  final StoryCleanupService _cleanupService = StoryCleanupService();
  
  final StreamController<DownloadProgress> _progressController = 
      StreamController<DownloadProgress>.broadcast();
  
  final Map<String, DownloadProgress> _activeDownloads = {};

  /// Stream of download progress updates
  Stream<DownloadProgress> get downloadProgress => _progressController.stream;

  /// Gets current download progress for a specific story
  DownloadProgress? getDownloadProgress(String storyId) {
    return _activeDownloads[storyId];
  }

  /// Gets all active downloads
  List<DownloadProgress> get activeDownloads => _activeDownloads.values.toList();

  /// Downloads a story from Firebase Storage with permission checking
  Future<bool> downloadStory(String storyId, {
    Function(double)? onProgress,
    bool checkPermissions = true,
  }) async {
    try {
      AppLogger.debug('[DOWNLOAD_MANAGER] Starting download for story: $storyId');

      // Check storage permissions first (if requested)
      if (checkPermissions) {
        final permissionResult = await _permissionService.checkAndRequestStoragePermission();
        if (!permissionResult.isGranted) {
          AppLogger.debug('[DOWNLOAD_MANAGER] Storage permission not granted for story: $storyId');
          _updateDownloadProgress(storyId, 0.0, DownloadStatus.failed,
              'Storage permission required for download');
          return false;
        }
      }

      // Check if already downloaded
      if (await _zipExtraction.isStoryDownloaded(storyId)) {
        AppLogger.debug('[DOWNLOAD_MANAGER] Story $storyId already downloaded');
        return true;
      }

      // Check if story exists in Firebase
      if (!await _firebaseStorage.storyExists(storyId)) {
        throw Exception('Story $storyId not found in Firebase Storage');
      }

      // Initialize download progress
      _updateDownloadProgress(storyId, 0.0, DownloadStatus.downloading);

      // Download the ZIP file with progress tracking
      final zipPath = await _firebaseStorage.downloadStoryZip(
        storyId,
        onProgress: (progress) {
          // Update progress (download is 70% of total process)
          final totalProgress = progress * 0.7;
          _updateDownloadProgress(storyId, totalProgress, DownloadStatus.downloading);
          onProgress?.call(totalProgress);
        },
      );

      AppLogger.debug('[DOWNLOAD_MANAGER] Downloaded ZIP for story: $storyId');

      // Extract the ZIP file with progress tracking
      final extractedPath = await _zipExtraction.extractStoryZip(
        zipPath,
        storyId,
        onProgress: (extractProgress) {
          // Update progress (extraction is 30% of total process, starting from 70%)
          final totalProgress = 0.7 + (extractProgress * 0.3);
          _updateDownloadProgress(storyId, totalProgress, DownloadStatus.downloading);
          onProgress?.call(totalProgress);
        },
      );

      // Validate the extracted story
      final isValid = await _zipExtraction.validateStoryStructure(extractedPath);
      if (!isValid) {
        _updateDownloadProgress(storyId, 0.0, DownloadStatus.failed, 
            'Invalid story structure after extraction');
        throw Exception('Downloaded story has invalid structure');
      }

      // Mark as completed
      _updateDownloadProgress(storyId, 1.0, DownloadStatus.downloaded);

      // Record the download for cleanup tracking
      await _cleanupService.recordStoryDownload(storyId, extractedPath);

      // Perform automatic cleanup check (non-blocking)
      _performCleanupCheck();

      AppLogger.debug('[DOWNLOAD_MANAGER] Successfully downloaded story: $storyId');
      return true;

    } catch (e) {
      _logger.e('[DOWNLOAD_MANAGER] Failed to download story $storyId: $e');
      _updateDownloadProgress(storyId, 0.0, DownloadStatus.failed, e.toString());
      return false;
    }
  }

  /// Cancels an active download
  Future<void> cancelDownload(String storyId) async {
    try {
      AppLogger.debug('[DOWNLOAD_MANAGER] Cancelling download for story: $storyId');
      
      // Update status to cancelled
      _updateDownloadProgress(storyId, 0.0, DownloadStatus.cancelled);
      
      // Clean up any partial downloads
      await _cleanupPartialDownload(storyId);
      
      AppLogger.debug('[DOWNLOAD_MANAGER] Cancelled download for story: $storyId');
    } catch (e) {
      _logger.e('[DOWNLOAD_MANAGER] Failed to cancel download for $storyId: $e');
    }
  }

  /// Deletes a downloaded story
  Future<bool> deleteDownloadedStory(String storyId) async {
    try {
      AppLogger.debug('[DOWNLOAD_MANAGER] Deleting downloaded story: $storyId');

      final storyPath = await _zipExtraction.getStoryPath(storyId);
      final storyDir = Directory(storyPath);

      if (await storyDir.exists()) {
        await storyDir.delete(recursive: true);
        AppLogger.debug('[DOWNLOAD_MANAGER] Deleted story directory: $storyPath');
      }

      // Remove from active downloads if present
      _activeDownloads.remove(storyId);

      // Remove from cleanup tracking
      await _cleanupService.removeStoryFromTracking(storyId);

      AppLogger.debug('[DOWNLOAD_MANAGER] Successfully deleted story: $storyId');
      return true;

    } catch (e) {
      _logger.e('[DOWNLOAD_MANAGER] Failed to delete story $storyId: $e');
      return false;
    }
  }

  /// Checks if a story is currently being downloaded
  bool isDownloading(String storyId) {
    final progress = _activeDownloads[storyId];
    return progress?.status == DownloadStatus.downloading;
  }

  /// Checks if a story download failed
  bool isDownloadFailed(String storyId) {
    final progress = _activeDownloads[storyId];
    return progress?.status == DownloadStatus.failed;
  }

  /// Gets the download progress percentage (0.0 to 1.0)
  double getDownloadProgressPercentage(String storyId) {
    final progress = _activeDownloads[storyId];
    return progress?.progress ?? 0.0;
  }

  /// Updates download progress and notifies listeners
  void _updateDownloadProgress(String storyId, double progress, 
      DownloadStatus status, [String? errorMessage]) {
    final downloadProgress = DownloadProgress(
      storyId: storyId,
      progress: progress,
      status: status,
      errorMessage: errorMessage,
      lastUpdated: DateTime.now(),
    );

    _activeDownloads[storyId] = downloadProgress;
    
    if (!_progressController.isClosed) {
      _progressController.add(downloadProgress);
    }

    // Remove from active downloads if completed, failed, or cancelled
    if (status == DownloadStatus.downloaded || 
        status == DownloadStatus.failed || 
        status == DownloadStatus.cancelled) {
      // Keep it for a short time for UI feedback, then remove
      Timer(const Duration(seconds: 5), () {
        _activeDownloads.remove(storyId);
      });
    }
  }

  /// Cleans up partial downloads
  Future<void> _cleanupPartialDownload(String storyId) async {
    try {
      final storyPath = await _zipExtraction.getStoryPath(storyId);
      final storyDir = Directory(storyPath);

      if (await storyDir.exists()) {
        await storyDir.delete(recursive: true);
      }
    } catch (e) {
      _logger.w('[DOWNLOAD_MANAGER] Failed to cleanup partial download for $storyId: $e');
    }
  }

  /// Performs automatic cleanup check (non-blocking)
  void _performCleanupCheck() {
    // Run cleanup in the background without blocking
    Future.microtask(() async {
      try {
        if (await _cleanupService.isAutomaticCleanupEnabled()) {
          final result = await _cleanupService.performAutomaticCleanup();
          if (result.hasDeletedStories) {
            AppLogger.debug('[DOWNLOAD_MANAGER] Automatic cleanup completed: ${result.storiesDeleted} stories deleted, ${result.spaceFreedFormatted} freed');
          }
        }
      } catch (e) {
        _logger.w('[DOWNLOAD_MANAGER] Automatic cleanup failed: $e');
      }
    });
  }

  /// Gets information about downloaded stories
  Future<List<DownloadedStoryInfo>> getDownloadedStoriesInfo() async {
    return await _cleanupService.getDownloadedStoriesInfo();
  }

  /// Performs manual cleanup of old stories
  Future<CleanupResult> performManualCleanup() async {
    return await _cleanupService.performAutomaticCleanup(force: true);
  }

  /// Updates story access time (call when story is played)
  Future<void> updateStoryAccess(String storyId) async {
    await _cleanupService.updateStoryAccess(storyId);
  }

  /// Disposes the download manager
  void dispose() {
    _progressController.close();
    _activeDownloads.clear();
  }
}
