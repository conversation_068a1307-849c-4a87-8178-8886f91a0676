import 'dart:async';
import 'package:choice_once_upon_a_time/core/services/story_narration_service_interface.dart';
import 'package:choice_once_upon_a_time/core/audio/narration_tts_service_interface.dart';
import 'package:choice_once_upon_a_time/core/audio/enhanced_narration_tts_service.dart';
import 'package:choice_once_upon_a_time/models/enhanced_story_model.dart';
import 'package:choice_once_upon_a_time/models/narration_models.dart';
import 'package:choice_once_upon_a_time/models/tts_models.dart';
import 'package:choice_once_upon_a_time/core/utils/app_logger.dart';

/// Enhanced story narration service implementation
class EnhancedStoryNarrationService implements IEnhancedStoryNarrationService {
  static final EnhancedStoryNarrationService _instance = EnhancedStoryNarrationService._internal();
  factory EnhancedStoryNarrationService() => _instance;
  EnhancedStoryNarrationService._internal();

  // Dependencies
  late final IEnhancedNarrationTTSService _ttsService;
  
  // State
  NarrationState _currentState = NarrationState.idle;
  NarrationConfig _currentConfig = NarrationConfig.defaultConfig;
  bool _isInitialized = false;
  
  // Current narration data
  EnhancedSceneModel? _currentScene;
  String? _currentStoryId;
  List<String> _sentences = [];
  List<WordHighlight> _wordHighlights = [];
  int _currentSentenceIndex = 0;
  int _currentWordIndex = 0;
  
  // Character voices
  final Map<String, NarrationConfig> _characterVoices = {};
  
  // Progress tracking
  NarrationProgress? _currentProgress;
  
  // Stream controllers
  final StreamController<NarrationState> _stateController = StreamController<NarrationState>.broadcast();
  final StreamController<NarrationProgress> _progressController = StreamController<NarrationProgress>.broadcast();
  final StreamController<WordHighlight> _wordHighlightController = StreamController<WordHighlight>.broadcast();
  final StreamController<SentenceBoundary> _sentenceBoundaryController = StreamController<SentenceBoundary>.broadcast();
  final StreamController<EmotionChange> _emotionChangeController = StreamController<EmotionChange>.broadcast();
  
  // Timers and subscriptions
  Timer? _progressTimer;
  StreamSubscription<TTSEvent>? _ttsEventSubscription;
  StreamSubscription<TTSEvent>? _wordBoundarySubscription;

  @override
  Stream<NarrationState> get stateStream => _stateController.stream;

  @override
  Stream<NarrationProgress> get progressStream => _progressController.stream;

  @override
  Stream<WordHighlight> get wordHighlightStream => _wordHighlightController.stream;

  @override
  Stream<SentenceBoundary> get sentenceBoundaryStream => _sentenceBoundaryController.stream;

  @override
  Stream<EmotionChange> get emotionChangeStream => _emotionChangeController.stream;

  @override
  NarrationState get currentState => _currentState;

  @override
  NarrationConfig get currentConfig => _currentConfig;

  @override
  bool get isInitialized => _isInitialized;

  @override
  bool get isNarrating => _currentState.status == NarrationStatus.playing;

  @override
  bool get isPaused => _currentState.status == NarrationStatus.paused;

  @override
  Future<void> initialize({NarrationConfig? config}) async {
    if (_isInitialized) {
      AppLogger.debug('[StoryNarration] Service already initialized');
      return;
    }

    try {
      AppLogger.info('[StoryNarration] Initializing story narration service');
      
      // Initialize TTS service
      _ttsService = EnhancedNarrationTTSService();
      final ttsInitialized = await _ttsService.initialize();
      
      if (!ttsInitialized) {
        throw const StoryNarrationException('Failed to initialize TTS service');
      }

      // Set up TTS event listeners
      _setupTTSEventListeners();

      // Configure narration settings
      if (config != null) {
        await configure(config);
      }

      _isInitialized = true;
      _updateState(_currentState.copyWith(status: NarrationStatus.idle));
      
      AppLogger.info('[StoryNarration] Service initialized successfully');
      
    } catch (e) {
      final errorMsg = 'Failed to initialize story narration service: $e';
      AppLogger.error('[StoryNarration] $errorMsg', e);
      _updateState(NarrationState.createError(errorMsg));
      throw StoryNarrationException(errorMsg, originalError: e);
    }
  }

  @override
  Future<void> configure(NarrationConfig config) async {
    _currentConfig = config;
    
    // Update TTS parameters
    final ttsParams = TTSParameters(
      rate: config.speechRate,
      pitch: config.speechPitch,
      volume: config.speechVolume,
      language: config.language,
    );
    
    await _ttsService.setParameters(ttsParams);
    AppLogger.debug('[StoryNarration] Configuration updated');
  }

  @override
  Future<void> narrateScene(EnhancedSceneModel scene, {String? storyId}) async {
    if (!_isInitialized) {
      throw const StoryNarrationException('Service not initialized');
    }

    try {
      final startTime = DateTime.now();
      AppLogger.info('[STORY_PLAYER] Scene narration started: ${scene.id} (Story: ${storyId ?? 'unknown'})');
      AppLogger.debug('[STORY_PLAYER] Scene text length: ${scene.text.length} characters');
      AppLogger.debug('[STORY_PLAYER] Scene speaker: ${scene.speaker}, emotion: ${scene.emotion}');

      // Stop any current narration
      await stop();

      // Set up scene data
      _currentScene = scene;
      _currentStoryId = storyId;
      _sentences = _extractSentences(scene.text);
      _wordHighlights = _generateWordHighlights(scene.text);
      _currentSentenceIndex = 0;
      _currentWordIndex = 0;

      AppLogger.debug('[STORY_PLAYER] Scene processing complete - ${_sentences.length} sentences, ${_wordHighlights.length} words');

      // Create progress tracking
      _currentProgress = NarrationProgress(
        storyId: storyId ?? 'unknown',
        sceneId: scene.id,
        totalWords: _wordHighlights.length,
        totalSentences: _sentences.length,
        lastUpdated: DateTime.now(),
      );

      // Update state
      _updateState(_currentState.copyWith(
        status: NarrationStatus.loading,
        currentText: scene.text,
        currentWordIndex: 0,
        progress: 0.0,
        autoProgress: _currentConfig.autoProgress,
      ));

      AppLogger.debug('[STORY_PLAYER] TTS initialization started for scene ${scene.id}');

      // Start narrating the first sentence
      await _narrateCurrentSentence();

      final initTime = DateTime.now().difference(startTime);
      AppLogger.debug('[STORY_PLAYER] Scene narration initialization completed in ${initTime.inMilliseconds}ms');

    } catch (e) {
      final errorMsg = 'Failed to start scene narration: $e';
      AppLogger.error('[STORY_PLAYER] Scene narration error for ${scene.id}', e);
      _updateState(NarrationState.createError(errorMsg));
      throw StoryNarrationException(errorMsg, originalError: e);
    }
  }

  @override
  Future<void> narrateText(String text, {String? emotionCue, String? storyId, String? sceneId}) async {
    if (!_isInitialized) {
      throw const StoryNarrationException('Service not initialized');
    }

    try {
      AppLogger.info('[StoryNarration] Starting text narration');
      
      // Stop any current narration
      await stop();
      
      // Create a temporary scene for the text
      final tempScene = EnhancedSceneModel(
        id: sceneId ?? 'temp_${DateTime.now().millisecondsSinceEpoch}',
        text: text,
        speaker: 'Narrator',
        emotion: emotionCue ?? 'neutral',
        image: '',
        pauseDuration: _currentConfig.sentencePauseMs,
      );
      
      await narrateScene(tempScene, storyId: storyId);
      
    } catch (e) {
      final errorMsg = 'Failed to start text narration: $e';
      AppLogger.error('[StoryNarration] $errorMsg', e);
      throw StoryNarrationException(errorMsg, originalError: e);
    }
  }

  @override
  Future<void> play() async {
    AppLogger.debug('[STORY_PLAYER] Play requested - current state: ${_currentState.status}');

    if (!isNarrating && !isPaused) {
      // Start narration if not already started
      if (_currentScene != null) {
        AppLogger.info('[STORY_PLAYER] Starting narration for scene: ${_currentScene!.id}');
        await _narrateCurrentSentence();
      } else {
        AppLogger.warning('[STORY_PLAYER] Play requested but no current scene available');
      }
    } else if (isPaused) {
      // Resume narration
      AppLogger.info('[STORY_PLAYER] Resuming narration for scene: ${_currentScene?.id ?? 'unknown'}');
      await _ttsService.resume();
      _updateState(_currentState.copyWith(status: NarrationStatus.playing));
      _startProgressTracking();
    } else {
      AppLogger.debug('[STORY_PLAYER] Play requested but already playing');
    }
  }

  @override
  Future<void> pause() async {
    AppLogger.debug('[STORY_PLAYER] Pause requested - current state: ${_currentState.status}');

    if (isNarrating) {
      AppLogger.info('[STORY_PLAYER] Pausing narration for scene: ${_currentScene?.id ?? 'unknown'}');
      await _ttsService.pause();
      _updateState(_currentState.copyWith(status: NarrationStatus.paused));
      _stopProgressTracking();
    } else {
      AppLogger.debug('[STORY_PLAYER] Pause requested but not currently narrating');
    }
  }

  @override
  Future<void> stop() async {
    AppLogger.debug('[STORY_PLAYER] Stop requested - current state: ${_currentState.status}');

    if (_currentScene != null) {
      AppLogger.info('[STORY_PLAYER] Stopping narration for scene: ${_currentScene!.id}');
    }

    await _ttsService.stop();
    _stopProgressTracking();
    _updateState(_currentState.copyWith(
      status: NarrationStatus.idle,
      currentText: null,
      currentWordIndex: 0,
      progress: 0.0,
    ));

    // Clear current narration data
    final clearedScene = _currentScene?.id;
    _currentScene = null;
    _currentStoryId = null;
    _sentences.clear();
    _wordHighlights.clear();
    _currentSentenceIndex = 0;
    _currentWordIndex = 0;
    _currentProgress = null;

    AppLogger.debug('[STORY_PLAYER] Narration stopped and data cleared for scene: ${clearedScene ?? 'unknown'}');
  }

  @override
  Future<void> skipToNextSentence() async {
    if (_currentSentenceIndex < _sentences.length - 1) {
      _currentSentenceIndex++;
      await _narrateCurrentSentence();
    } else {
      // Reached end of scene
      await _completeNarration();
    }
  }

  @override
  Future<void> skipToPreviousSentence() async {
    if (_currentSentenceIndex > 0) {
      _currentSentenceIndex--;
      await _narrateCurrentSentence();
    }
  }

  @override
  Future<void> seekToWord(int wordIndex) async {
    if (wordIndex >= 0 && wordIndex < _wordHighlights.length) {
      _currentWordIndex = wordIndex;
      
      // Find the sentence containing this word
      final wordHighlight = _wordHighlights[wordIndex];
      _currentSentenceIndex = wordHighlight.sentenceIndex;
      
      await _narrateCurrentSentence();
    }
  }

  @override
  Future<void> seekToSentence(int sentenceIndex) async {
    if (sentenceIndex >= 0 && sentenceIndex < _sentences.length) {
      _currentSentenceIndex = sentenceIndex;
      await _narrateCurrentSentence();
    }
  }

  @override
  Future<void> replayCurrentSentence() async {
    await _narrateCurrentSentence();
  }

  @override
  Future<void> replayScene() async {
    if (_currentScene != null) {
      _currentSentenceIndex = 0;
      _currentWordIndex = 0;
      await _narrateCurrentSentence();
    }
  }

  @override
  Future<void> setSpeechRate(double rate) async {
    final newConfig = _currentConfig.copyWith(speechRate: rate);
    await configure(newConfig);
  }

  @override
  Future<void> setSpeechPitch(double pitch) async {
    final newConfig = _currentConfig.copyWith(speechPitch: pitch);
    await configure(newConfig);
  }

  @override
  Future<void> setSpeechVolume(double volume) async {
    final newConfig = _currentConfig.copyWith(speechVolume: volume);
    await configure(newConfig);
  }

  @override
  Future<void> setAutoProgression(bool enabled) async {
    final newConfig = _currentConfig.copyWith(autoProgress: enabled);
    await configure(newConfig);
  }

  @override
  Future<void> setWordHighlighting(bool enabled) async {
    final newConfig = _currentConfig.copyWith(highlightWords: enabled);
    await configure(newConfig);
  }

  @override
  NarrationProgress? getCurrentProgress() => _currentProgress;

  @override
  Future<void> saveProgress() async {
    if (_currentProgress != null) {
      // TODO: Implement progress persistence
      AppLogger.debug('[StoryNarration] Progress saved for ${_currentProgress!.storyId}/${_currentProgress!.sceneId}');
    }
  }

  @override
  Future<void> loadProgress(String storyId, String sceneId) async {
    // TODO: Implement progress loading
    AppLogger.debug('[StoryNarration] Loading progress for $storyId/$sceneId');
  }

  @override
  Future<void> clearProgress(String storyId, String sceneId) async {
    // TODO: Implement progress clearing
    AppLogger.debug('[StoryNarration] Clearing progress for $storyId/$sceneId');
  }

  // Enhanced features implementation

  @override
  Future<void> narrateSceneWithCharacterVoices(
    EnhancedSceneModel scene, {
    required String storyId,
    Map<String, NarrationConfig>? characterVoices,
  }) async {
    // Store character voices if provided
    if (characterVoices != null) {
      _characterVoices.addAll(characterVoices);
    }

    // Use character-specific voice if available
    final characterVoice = _characterVoices[scene.speaker];
    if (characterVoice != null) {
      await configure(characterVoice);
    }

    await narrateScene(scene, storyId: storyId);
  }

  @override
  Future<void> narrateWithBackgroundMusic(
    EnhancedSceneModel scene, {
    required String storyId,
    String? backgroundMusicPath,
    double musicVolume = 0.3,
  }) async {
    // TODO: Implement background music integration
    AppLogger.debug('[StoryNarration] Background music not yet implemented');
    await narrateScene(scene, storyId: storyId);
  }

  @override
  Future<void> narrateWithEmotionTransitions(
    EnhancedSceneModel scene, {
    required String storyId,
    Duration transitionDuration = const Duration(milliseconds: 500),
  }) async {
    // Apply emotion-based voice modulation
    await _ttsService.setEmotionParameters(scene.emotion);
    await narrateScene(scene, storyId: storyId);
  }

  @override
  Future<void> setCharacterVoice(String characterId, NarrationConfig config) async {
    _characterVoices[characterId] = config;
    AppLogger.debug('[StoryNarration] Character voice set for: $characterId');
  }

  @override
  NarrationConfig? getCharacterVoice(String characterId) {
    return _characterVoices[characterId];
  }

  @override
  Future<void> setBackgroundMusicEnabled(bool enabled) async {
    // TODO: Implement background music control
    AppLogger.debug('[StoryNarration] Background music enabled: $enabled');
  }

  @override
  Future<void> setBackgroundMusicVolume(double volume) async {
    // TODO: Implement background music volume control
    AppLogger.debug('[StoryNarration] Background music volume: $volume');
  }

  @override
  Future<void> setEmotionModulationEnabled(bool enabled) async {
    // TODO: Implement emotion modulation control
    AppLogger.debug('[StoryNarration] Emotion modulation enabled: $enabled');
  }

  @override
  Future<void> preloadScene(EnhancedSceneModel scene, String storyId) async {
    await _ttsService.preloadText(scene.text);
    AppLogger.debug('[StoryNarration] Scene preloaded: ${scene.id}');
  }

  @override
  Future<void> clearPreloadedScenes() async {
    await _ttsService.clearPreloadedText();
    AppLogger.debug('[StoryNarration] Preloaded scenes cleared');
  }

  @override
  Future<Duration?> estimateSceneDuration(EnhancedSceneModel scene) async {
    final durationMs = await _ttsService.estimateSpeechDuration(scene.text);
    return durationMs != null ? Duration(milliseconds: durationMs) : null;
  }

  @override
  Future<NarrationAnalytics> getNarrationAnalytics(String storyId) async {
    // TODO: Implement analytics collection
    return NarrationAnalytics(
      storyId: storyId,
      totalScenesNarrated: 0,
      totalNarrationTime: Duration.zero,
      totalWordsNarrated: 0,
      emotionUsageCount: {},
      characterSpeakingTime: {},
      averageSpeechRate: _currentConfig.speechRate,
      lastNarrationDate: DateTime.now(),
    );
  }

  @override
  Future<void> dispose() async {
    await stop();
    await _ttsService.dispose();

    await _ttsEventSubscription?.cancel();
    await _wordBoundarySubscription?.cancel();

    await _stateController.close();
    await _progressController.close();
    await _wordHighlightController.close();
    await _sentenceBoundaryController.close();
    await _emotionChangeController.close();

    _isInitialized = false;
    AppLogger.debug('[StoryNarration] Service disposed');
  }

  // Private helper methods

  /// Set up TTS event listeners
  void _setupTTSEventListeners() {
    _ttsEventSubscription = _ttsService.eventStream.listen((event) {
      _handleTTSEvent(event);
    });

    _wordBoundarySubscription = _ttsService.wordBoundaryStream.listen((event) {
      _handleWordBoundaryEvent(event);
    });
  }

  /// Handle TTS events
  void _handleTTSEvent(TTSEvent event) {
    switch (event.type) {
      case TTSEventType.started:
        _updateState(_currentState.copyWith(status: NarrationStatus.playing));
        _startProgressTracking();
        break;
      case TTSEventType.completed:
        _onSentenceCompleted();
        break;
      case TTSEventType.paused:
        _updateState(_currentState.copyWith(status: NarrationStatus.paused));
        _stopProgressTracking();
        break;
      case TTSEventType.resumed:
        _updateState(_currentState.copyWith(status: NarrationStatus.playing));
        _startProgressTracking();
        break;
      case TTSEventType.stopped:
        _updateState(_currentState.copyWith(status: NarrationStatus.idle));
        _stopProgressTracking();
        break;
      case TTSEventType.error:
        _updateState(NarrationState.createError(event.error ?? 'TTS error'));
        _stopProgressTracking();
        break;
      default:
        break;
    }
  }

  /// Handle word boundary events for highlighting
  void _handleWordBoundaryEvent(TTSEvent event) {
    if (event.wordIndex != null && event.wordIndex! < _wordHighlights.length) {
      final wordHighlight = _wordHighlights[event.wordIndex!].copyWith(isActive: true);
      _wordHighlightController.add(wordHighlight);

      // Update current word index
      _currentWordIndex = event.wordIndex!;
      _updateState(_currentState.copyWith(currentWordIndex: _currentWordIndex));

      // Detailed logging for word highlighting
      final progress = (_currentWordIndex / _wordHighlights.length * 100).toStringAsFixed(1);
      AppLogger.debug('[STORY_PLAYER] Word highlighted: "${wordHighlight.word}" (${_currentWordIndex + 1}/${_wordHighlights.length}, $progress% complete)');
      AppLogger.debug('[STORY_PLAYER] Word position: sentence ${wordHighlight.sentenceIndex + 1}/${_sentences.length}, char ${wordHighlight.startIndex}-${wordHighlight.endIndex}');
    } else {
      AppLogger.warning('[STORY_PLAYER] Invalid word boundary event - wordIndex: ${event.wordIndex}, total words: ${_wordHighlights.length}');
    }
  }

  /// Update the current state and notify listeners
  void _updateState(NarrationState newState) {
    _currentState = newState;
    _stateController.add(newState);
  }

  /// Start progress tracking timer
  void _startProgressTracking() {
    _stopProgressTracking(); // Stop any existing timer

    _progressTimer = Timer.periodic(const Duration(milliseconds: 100), (timer) {
      _updateProgress();
    });
  }

  /// Stop progress tracking timer
  void _stopProgressTracking() {
    _progressTimer?.cancel();
    _progressTimer = null;
  }

  /// Update narration progress
  void _updateProgress() {
    if (_currentProgress == null) return;

    final totalWords = _wordHighlights.length;
    final completedWords = _currentWordIndex;
    final progress = totalWords > 0 ? completedWords / totalWords : 0.0;

    _currentProgress = _currentProgress!.copyWith(
      completedWords: completedWords,
      completedSentences: _currentSentenceIndex,
      currentPositionMs: (progress * (_currentProgress!.totalDurationMs)).round(),
      lastUpdated: DateTime.now(),
    );

    _updateState(_currentState.copyWith(progress: progress));
    _progressController.add(_currentProgress!);
  }

  /// Handle sentence completion
  void _onSentenceCompleted() {
    if (_currentConfig.autoProgress) {
      // Automatically move to next sentence
      if (_currentSentenceIndex < _sentences.length - 1) {
        _currentSentenceIndex++;
        _narrateCurrentSentence();
      } else {
        // Reached end of scene
        _completeNarration();
      }
    } else {
      // Wait for user input to continue
      _updateState(_currentState.copyWith(status: NarrationStatus.paused));
    }
  }

  /// Complete the entire narration
  Future<void> _completeNarration() async {
    _updateState(_currentState.copyWith(
      status: NarrationStatus.completed,
      progress: 1.0,
    ));

    if (_currentProgress != null) {
      _currentProgress = _currentProgress!.copyWith(
        isCompleted: true,
        completedWords: _wordHighlights.length,
        completedSentences: _sentences.length,
        currentPositionMs: _currentProgress!.totalDurationMs,
        lastUpdated: DateTime.now(),
      );
      _progressController.add(_currentProgress!);
    }

    _stopProgressTracking();
    AppLogger.info('[StoryNarration] Narration completed');
  }

  /// Narrate the current sentence
  Future<void> _narrateCurrentSentence() async {
    if (_currentSentenceIndex >= _sentences.length) {
      AppLogger.debug('[STORY_PLAYER] Sentence index out of bounds, completing narration');
      await _completeNarration();
      return;
    }

    final sentence = _sentences[_currentSentenceIndex];
    final sentenceProgress = ((_currentSentenceIndex + 1) / _sentences.length * 100).toStringAsFixed(1);

    AppLogger.info('[STORY_PLAYER] Narrating sentence ${_currentSentenceIndex + 1}/${_sentences.length} ($sentenceProgress% complete)');
    AppLogger.debug('[STORY_PLAYER] Sentence text: "$sentence"');
    AppLogger.debug('[STORY_PLAYER] Sentence length: ${sentence.length} characters');

    // Emit sentence boundary event
    final startTime = DateTime.now();
    _sentenceBoundaryController.add(SentenceBoundary(
      sentenceIndex: _currentSentenceIndex,
      sentence: sentence,
      startCharIndex: _getSentenceStartIndex(_currentSentenceIndex),
      endCharIndex: _getSentenceEndIndex(_currentSentenceIndex),
      timestamp: startTime,
    ));

    // Apply emotion if available from current scene
    String emotionCue = 'neutral';
    if (_currentScene != null) {
      emotionCue = _currentScene!.emotion;
    }

    AppLogger.debug('[STORY_PLAYER] TTS parameters - emotion: $emotionCue, speaker: ${_currentScene?.speaker ?? 'Narrator'}');

    // Speak the sentence with emotion
    try {
      await _ttsService.speakWithEmotion(sentence, emotionCue);
      final duration = DateTime.now().difference(startTime);
      AppLogger.debug('[STORY_PLAYER] Sentence narration completed in ${duration.inMilliseconds}ms');
    } catch (e) {
      AppLogger.error('[STORY_PLAYER] Error during sentence narration', e);
      rethrow;
    }
  }

  /// Extract sentences from text
  List<String> _extractSentences(String text) {
    // Remove emotion cues in brackets
    final cleanText = text.replaceAll(RegExp(r'\[.*?\]'), '');

    // Split by sentence endings
    final sentences = cleanText
        .split(RegExp(r'[.!?]+'))
        .map((s) => s.trim())
        .where((s) => s.isNotEmpty)
        .toList();

    return sentences;
  }

  /// Generate word highlights for the text
  List<WordHighlight> _generateWordHighlights(String text) {
    final words = <WordHighlight>[];
    final cleanText = text.replaceAll(RegExp(r'\[.*?\]'), '');
    final wordMatches = RegExp(r'\b\w+\b').allMatches(cleanText);

    int sentenceIndex = 0;
    int sentenceStart = 0;

    for (final match in wordMatches) {
      // Determine which sentence this word belongs to
      while (sentenceIndex < _sentences.length - 1) {
        final sentenceEnd = sentenceStart + _sentences[sentenceIndex].length;
        if (match.start <= sentenceEnd) break;
        sentenceStart = sentenceEnd + 1;
        sentenceIndex++;
      }

      words.add(WordHighlight(
        startIndex: match.start,
        endIndex: match.end,
        word: match.group(0) ?? '',
        startTimeMs: 0, // Will be calculated during playback
        endTimeMs: 0,   // Will be calculated during playback
        sentenceIndex: sentenceIndex,
      ));
    }

    return words;
  }

  /// Get start character index for a sentence
  int _getSentenceStartIndex(int sentenceIndex) {
    if (sentenceIndex == 0) return 0;

    int index = 0;
    for (int i = 0; i < sentenceIndex; i++) {
      index += _sentences[i].length + 1; // +1 for sentence separator
    }
    return index;
  }

  /// Get end character index for a sentence
  int _getSentenceEndIndex(int sentenceIndex) {
    if (sentenceIndex >= _sentences.length) return 0;
    return _getSentenceStartIndex(sentenceIndex) + _sentences[sentenceIndex].length;
  }
}
