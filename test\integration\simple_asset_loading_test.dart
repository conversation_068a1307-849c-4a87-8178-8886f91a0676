import 'package:flutter_test/flutter_test.dart';
import 'package:flutter/foundation.dart';
import 'package:choice_once_upon_a_time/core/services/asset_story_service.dart';

void main() {
  group('Simple Asset Loading Test', () {
    late AssetStoryService assetStoryService;

    setUpAll(() {
      TestWidgetsFlutterBinding.ensureInitialized();
    });

    setUp(() {
      assetStoryService = AssetStoryService();
    });

    tearDown(() {
      assetStoryService.clearCache();
    });

    test('should discover available story IDs from assets', () async {
      // Act
      final storyIds = await assetStoryService.getAvailableStoryIds();

      // Assert
      expect(storyIds, isNotEmpty);
      debugPrint('Found story IDs: $storyIds');
      
      // Should include the courage story
      expect(storyIds, contains('courage_city_adventure'));
    });

    test('should load the courage story from assets', () async {
      // Act
      final story = await assetStoryService.loadStoryFromAssets('courage_city_adventure');

      // Assert
      expect(story, isNotNull);
      expect(story!.id, 'courage_city_adventure');
      expect(story.title, isNotEmpty);
      expect(story.scenes, isNotEmpty);

      debugPrint('Loaded story: ${story.title}');
      debugPrint('Number of scenes: ${story.scenes.length}');
    });

    test('should validate story assets correctly', () async {
      // Act
      final validation = await assetStoryService.validateStoryAssets('courage_city_adventure');

      // Assert
      expect(validation['storyId'], 'courage_city_adventure');
      expect(validation['hasStoryJson'], true);
      expect(validation['isValid'], true);
      
      debugPrint('Validation result: $validation');
    });

    test('should handle non-existent story gracefully', () async {
      // Act
      final story = await assetStoryService.loadStoryFromAssets('non_existent_story');

      // Assert
      expect(story, isNull);
    });
  });
}
