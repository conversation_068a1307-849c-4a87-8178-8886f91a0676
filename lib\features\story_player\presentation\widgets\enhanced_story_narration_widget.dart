import 'package:flutter/material.dart';
import 'package:choice_once_upon_a_time/core/services/story_narration_service_interface.dart';
import 'package:choice_once_upon_a_time/models/enhanced_story_model.dart';
import 'package:choice_once_upon_a_time/models/narration_models.dart';
import 'package:choice_once_upon_a_time/features/story_player/presentation/widgets/word_highlight_widget.dart';
import 'package:choice_once_upon_a_time/features/story_player/presentation/widgets/narration_controls_widget.dart';
import 'package:choice_once_upon_a_time/core/utils/app_logger.dart';

/// Enhanced story narration widget that integrates with the story player
/// Provides scene-by-scene narration with word highlighting and controls
class EnhancedStoryNarrationWidget extends StatefulWidget {
  final IStoryNarrationService narrationService;
  final EnhancedStoryModel story;
  final EnhancedSceneModel scene;
  final bool autoStart;
  final bool showControls;
  final bool enableWordHighlighting;
  final VoidCallback? onNarrationComplete;
  final VoidCallback? onSceneComplete;

  const EnhancedStoryNarrationWidget({
    super.key,
    required this.narrationService,
    required this.story,
    required this.scene,
    this.autoStart = false,
    this.showControls = true,
    this.enableWordHighlighting = true,
    this.onNarrationComplete,
    this.onSceneComplete,
  });

  @override
  State<EnhancedStoryNarrationWidget> createState() => _EnhancedStoryNarrationWidgetState();
}

class _EnhancedStoryNarrationWidgetState extends State<EnhancedStoryNarrationWidget>
    with TickerProviderStateMixin {
  
  // Animation controllers
  late AnimationController _fadeController;
  late Animation<double> _fadeAnimation;

  // State
  NarrationState _narrationState = NarrationState.idle;
  bool _isInitialized = false;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _setupNarrationListeners();
    _initializeNarration();
  }

  @override
  void didUpdateWidget(EnhancedStoryNarrationWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    
    // If scene changed, start narrating the new scene
    if (oldWidget.scene.id != widget.scene.id) {
      _narrateCurrentScene();
    }
  }

  @override
  void dispose() {
    _fadeController.dispose();
    super.dispose();
  }

  /// Initialize animations
  void _initializeAnimations() {
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _fadeController,
      curve: Curves.easeInOut,
    ));
  }

  /// Set up narration service listeners
  void _setupNarrationListeners() {
    widget.narrationService.stateStream.listen((state) {
      if (mounted) {
        setState(() {
          _narrationState = state;
        });

        // Handle state changes
        switch (state.status) {
          case NarrationStatus.completed:
            widget.onNarrationComplete?.call();
            _handleNarrationComplete();
            break;
          case NarrationStatus.error:
            AppLogger.error('[EnhancedStoryNarration] Narration error: ${state.error}');
            break;
          default:
            break;
        }
      }
    });
  }

  /// Initialize narration service
  Future<void> _initializeNarration() async {
    try {
      if (!widget.narrationService.isInitialized) {
        await widget.narrationService.initialize();
      }
      
      setState(() {
        _isInitialized = true;
      });

      // Start fade-in animation
      _fadeController.forward();

      // Auto-start narration if enabled
      if (widget.autoStart) {
        await _narrateCurrentScene();
      }

    } catch (e) {
      AppLogger.error('[EnhancedStoryNarration] Failed to initialize narration', e);
    }
  }

  /// Start narrating the current scene
  Future<void> _narrateCurrentScene() async {
    if (!_isInitialized) return;

    try {
      AppLogger.info('[EnhancedStoryNarration] Starting scene narration: ${widget.scene.id}');
      
      await widget.narrationService.narrateScene(
        widget.scene,
        storyId: widget.story.storyId,
      );

    } catch (e) {
      AppLogger.error('[EnhancedStoryNarration] Failed to start scene narration', e);
    }
  }

  /// Handle narration completion
  void _handleNarrationComplete() {
    // If auto-progression is enabled and there's a next scene, signal completion
    if (widget.narrationService.currentConfig.autoProgress) {
      widget.onSceneComplete?.call();
    }
  }

  /// Handle play button press
  Future<void> _handlePlay() async {
    if (_narrationState.status == NarrationStatus.idle) {
      await _narrateCurrentScene();
    } else {
      await widget.narrationService.play();
    }
  }

  @override
  Widget build(BuildContext context) {
    if (!_isInitialized) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    return FadeTransition(
      opacity: _fadeAnimation,
      child: Column(
        children: [
          // Scene text with word highlighting
          if (widget.enableWordHighlighting) ...[
            Expanded(
              child: WordHighlightWidget(
                narrationService: widget.narrationService,
                text: widget.scene.text,
                fontSize: _getResponsiveFontSize(context),
                lineHeight: 1.6,
                padding: EdgeInsets.all(_getResponsivePadding(context)),
              ),
            ),
          ] else ...[
            Expanded(
              child: _buildSimpleTextDisplay(),
            ),
          ],

          // Narration controls
          if (widget.showControls) ...[
            const SizedBox(height: 16),
            NarrationControlsWidget(
              narrationService: widget.narrationService,
              showAdvancedControls: true,
              showSpeedControl: true,
              showVolumeControl: true,
              showProgressSlider: true,
              onPlayPressed: _handlePlay,
            ),
          ],
        ],
      ),
    );
  }

  /// Build simple text display without word highlighting
  Widget _buildSimpleTextDisplay() {
    final theme = Theme.of(context);
    
    return Container(
      padding: EdgeInsets.all(_getResponsivePadding(context)),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: theme.colorScheme.outline.withValues(alpha: 0.2),
        ),
      ),
      child: SingleChildScrollView(
        child: Text(
          widget.scene.text,
          style: theme.textTheme.bodyLarge?.copyWith(
            fontSize: _getResponsiveFontSize(context),
            height: 1.6,
            color: theme.colorScheme.onSurface,
          ),
          textAlign: TextAlign.left,
        ),
      ),
    );
  }

  /// Get responsive font size based on screen size
  double _getResponsiveFontSize(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    
    if (screenWidth < 400) {
      return 16.0; // Small screens
    } else if (screenWidth < 700) {
      return 18.0; // Medium screens
    } else if (screenWidth < 1000) {
      return 20.0; // Large screens
    } else {
      return 22.0; // Extra large screens
    }
  }

  /// Get responsive padding based on screen size
  double _getResponsivePadding(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    
    if (screenWidth < 400) {
      return 12.0; // Small screens
    } else if (screenWidth < 700) {
      return 16.0; // Medium screens
    } else if (screenWidth < 1000) {
      return 20.0; // Large screens
    } else {
      return 24.0; // Extra large screens
    }
  }
}

/// Simplified narration widget for basic use cases
class SimpleStoryNarrationWidget extends StatelessWidget {
  final IStoryNarrationService narrationService;
  final String text;
  final String? emotionCue;
  final bool autoStart;
  final VoidCallback? onComplete;

  const SimpleStoryNarrationWidget({
    super.key,
    required this.narrationService,
    required this.text,
    this.emotionCue,
    this.autoStart = false,
    this.onComplete,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          Expanded(
            child: WordHighlightWidget(
              narrationService: narrationService,
              text: text,
              enableAnimations: true,
              enableGlow: true,
            ),
          ),
          const SizedBox(height: 16),
          NarrationControlsWidget(
            narrationService: narrationService,
            showAdvancedControls: false,
            showSpeedControl: false,
            showVolumeControl: false,
            showProgressSlider: true,
          ),
        ],
      ),
    );
  }
}
