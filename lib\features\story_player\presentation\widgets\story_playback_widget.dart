import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:choice_once_upon_a_time/models/enhanced_story_model.dart';
import 'package:choice_once_upon_a_time/models/narration_models.dart';
import 'package:choice_once_upon_a_time/core/services/story_narration_service_interface.dart';
import 'package:choice_once_upon_a_time/core/services/story_settings_service.dart';
import 'package:choice_once_upon_a_time/core/utils/app_logger.dart';
import 'dart:async';

/// New story playback widget built from scratch to properly display scene images
/// and provide comprehensive TTS narration with word-level highlighting
class StoryPlaybackWidget extends StatefulWidget {
  final EnhancedStoryModel story;
  final EnhancedSceneModel scene;
  final Function(ChoiceOptionModel) onChoiceSelected;
  final VoidCallback? onSceneComplete;
  final IStoryNarrationService narrationService;
  final StorySettingsService settingsService;

  const StoryPlaybackWidget({
    super.key,
    required this.story,
    required this.scene,
    required this.onChoiceSelected,
    required this.narrationService,
    required this.settingsService,
    this.onSceneComplete,
  });

  @override
  State<StoryPlaybackWidget> createState() => _StoryPlaybackWidgetState();
}

class _StoryPlaybackWidgetState extends State<StoryPlaybackWidget>
    with TickerProviderStateMixin {
  
  // Image loading state
  bool _imageLoaded = false;
  bool _imageLoading = false;
  String? _imageError;
  ImageProvider? _imageProvider;
  DateTime? _imageDisplayStartTime;
  
  // Narration state
  bool _isNarrating = false;
  bool _isPaused = false;
  bool _hasCompleted = false;
  List<String> _sentences = [];
  List<List<String>> _sentenceWords = [];
  int _currentSentenceIndex = 0;
  int _currentWordIndex = 0;
  
  // UI state
  bool _showChoices = false;
  bool _showControls = true;
  
  // Subscriptions
  StreamSubscription? _progressSubscription;
  StreamSubscription? _narrationStateSubscription;
  
  // Animation controllers
  late AnimationController _fadeController;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    AppLogger.debug('[STORY_PLAYER] StoryPlaybackWidget initState - scene: ${widget.scene.id}, story: ${widget.story.storyId}');
    _initializeAnimations();
    _processTextForNarration();
    _setupNarrationListeners();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    if (!_imageLoaded && !_imageLoading) {
      _loadSceneImage();
    }
  }

  void _initializeAnimations() {
    AppLogger.debug('[STORY_PLAYER] Initializing animations');
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _fadeController,
      curve: Curves.easeInOut,
    ));
  }

  Future<void> _loadSceneImage() async {
    if (_imageLoading || _imageLoaded) return;
    
    setState(() {
      _imageLoading = true;
      _imageError = null;
    });

    final imagePath = widget.scene.getImagePath(widget.story.storyId);
    AppLogger.debug('[IMAGE_LOAD] Asset: $imagePath | Location: lib/features/story_player/presentation/widgets/story_playback_widget.dart:106 | Widget: StoryPlaybackWidget');
    AppLogger.debug('[STORY_PLAYER] Loading scene image: $imagePath');
    AppLogger.debug('[STORY_PLAYER] Asset loaded: $imagePath');

    try {
      _imageProvider = AssetImage(imagePath);
      AppLogger.debug('[IMAGE_LOAD] Asset: $imagePath | Location: lib/features/story_player/presentation/widgets/story_playback_widget.dart:111 | Widget: AssetImage');

      // Preload the image
      AppLogger.debug('[IMAGE_LOAD] Asset: $imagePath | Location: lib/features/story_player/presentation/widgets/story_playback_widget.dart:114 | Widget: precacheImage');
      await precacheImage(_imageProvider!, context);
      
      if (mounted) {
        setState(() {
          _imageLoaded = true;
          _imageLoading = false;
          _imageDisplayStartTime = DateTime.now();
        });
        
        AppLogger.debug('[SCENE_DEBUG] Scene ${widget.scene.id} loaded - Image: $imagePath');
        
        // Start fade-in animation
        _fadeController.forward();
        
        // Start narration after a brief delay
        Future.delayed(const Duration(milliseconds: 500), () {
          if (mounted) {
            _startNarration();
          }
        });
      }
    } catch (error) {
      AppLogger.error('[STORY_PLAYER] Failed to load scene image: $imagePath', error);
      if (mounted) {
        setState(() {
          _imageError = 'Failed to load image: $error';
          _imageLoading = false;
        });
      }
    }
  }

  void _processTextForNarration() {
    AppLogger.debug('[STORY_PLAYER] Processing text for narration: "${widget.scene.text.substring(0, widget.scene.text.length > 50 ? 50 : widget.scene.text.length)}${widget.scene.text.length > 50 ? '...' : ''}"');
    
    // Split text into sentences
    _sentences = widget.scene.text
        .split(RegExp(r'[.!?]+'))
        .map((s) => s.trim())
        .where((s) => s.isNotEmpty)
        .toList();
    
    if (_sentences.isEmpty) {
      _sentences = [widget.scene.text];
    }
    
    // Split each sentence into words
    _sentenceWords = _sentences.map((sentence) {
      return sentence.split(RegExp(r'\s+'))
          .where((word) => word.isNotEmpty)
          .toList();
    }).toList();
    
    AppLogger.debug('[SCENE_DEBUG] Processed ${_sentences.length} sentences with ${_sentenceWords.fold(0, (sum, words) => sum + words.length)} total words');
  }

  void _setupNarrationListeners() {
    AppLogger.debug('[STORY_PLAYER] Setting up narration listeners');
    
    // Clean up existing subscriptions
    _progressSubscription?.cancel();
    _narrationStateSubscription?.cancel();

    // Listen to narration progress
    _progressSubscription = widget.narrationService.progressStream.listen((progress) {
      if (mounted) {
        setState(() {
          _currentSentenceIndex = (progress.completedSentences).clamp(0, _sentences.length - 1);
          // Simple word progression simulation
          _currentWordIndex = (_currentWordIndex + 1) % 
              (_sentenceWords.isNotEmpty && _currentSentenceIndex < _sentenceWords.length 
                  ? _sentenceWords[_currentSentenceIndex].length + 1 
                  : 1);
        });
      }
    });

    // Listen to narration state changes
    _narrationStateSubscription = widget.narrationService.stateStream.listen((state) {
      if (mounted) {
        setState(() {
          _isNarrating = state.status == NarrationStatus.playing;
          _isPaused = state.status == NarrationStatus.paused;
          if (state.status == NarrationStatus.completed) {
            _hasCompleted = true;
            _currentWordIndex = 0;
            _onNarrationCompleted();
          }
        });
      }
    });
  }

  Future<void> _startNarration() async {
    if (_isNarrating || _hasCompleted) return;
    
    AppLogger.debug('[STORY_PLAYER] Starting narration for scene: ${widget.scene.id}');
    
    setState(() {
      _isNarrating = true;
      _isPaused = false;
      _currentSentenceIndex = 0;
      _currentWordIndex = 0;
    });

    try {
      await widget.narrationService.narrateScene(widget.scene);
    } catch (error) {
      AppLogger.error('[STORY_PLAYER] Narration error', error);
      await _stopNarration();
    }
  }

  Future<void> _pauseNarration() async {
    if (!_isNarrating || _isPaused) return;
    
    AppLogger.debug('[STORY_PLAYER] Pausing narration');
    setState(() {
      _isPaused = true;
    });
    await widget.narrationService.pause();
  }

  Future<void> _resumeNarration() async {
    if (!_isNarrating || !_isPaused) return;
    
    AppLogger.debug('[STORY_PLAYER] Resuming narration');
    setState(() {
      _isPaused = false;
    });
    await widget.narrationService.play();
  }

  Future<void> _stopNarration() async {
    AppLogger.debug('[STORY_PLAYER] Stopping narration');
    setState(() {
      _isNarrating = false;
      _isPaused = false;
    });
    await widget.narrationService.stop();
  }

  void _onNarrationCompleted() async {
    AppLogger.debug('[SCENE_DEBUG] Narration completed successfully');
    
    // Ensure minimum image display time
    await _ensureMinimumImageDisplayTime();
    
    // Show choices if available, otherwise enable next button
    if (widget.scene.choices?.isNotEmpty ?? false) {
      setState(() {
        _showChoices = true;
      });
    } else {
      // Manual progression - user must tap next button
      AppLogger.debug('[STORY_PLAYER] Autoplay disabled - manual progression required');
    }
  }

  Future<void> _ensureMinimumImageDisplayTime() async {
    if (_imageDisplayStartTime != null) {
      final elapsed = DateTime.now().difference(_imageDisplayStartTime!);
      const minimumDisplayTime = Duration(seconds: 5);

      if (elapsed < minimumDisplayTime) {
        final remainingTime = minimumDisplayTime - elapsed;
        AppLogger.debug('[SCENE_DEBUG] Ensuring minimum image display time - waiting ${remainingTime.inMilliseconds}ms more');
        await Future.delayed(remainingTime);
      }
    }
  }

  /// Show scene transition loading when user triggers next scene
  void _showSceneTransitionLoading() {
    AppLogger.debug('[SCENE_TRANSITION] User triggered scene transition - showing loading');

    // Show a brief loading indicator
    showDialog(
      context: context,
      barrierDismissible: false,
      barrierColor: Colors.black.withValues(alpha: 0.7),
      builder: (context) => const Center(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            CircularProgressIndicator(
              valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
            ),
            SizedBox(height: 16),
            Text(
              'Loading next scene...',
              style: TextStyle(
                color: Colors.white,
                fontSize: 16,
              ),
            ),
          ],
        ),
      ),
    );

    // Auto-dismiss after a short delay
    Future.delayed(const Duration(milliseconds: 1500), () {
      if (mounted && Navigator.canPop(context)) {
        Navigator.pop(context);
      }
    });
  }

  /// Show choice transition loading when user selects a choice
  void _showChoiceTransitionLoading(String choiceText) {
    AppLogger.debug('[CHOICE_TRANSITION] User selected choice: $choiceText - showing loading');

    // Show a brief loading indicator with choice feedback
    showDialog(
      context: context,
      barrierDismissible: false,
      barrierColor: Colors.black.withValues(alpha: 0.7),
      builder: (context) => Center(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const CircularProgressIndicator(
              valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
            ),
            const SizedBox(height: 16),
            const Text(
              'Processing your choice...',
              style: TextStyle(
                color: Colors.white,
                fontSize: 16,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              '"$choiceText"',
              style: const TextStyle(
                color: Colors.white70,
                fontSize: 14,
                fontStyle: FontStyle.italic,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );

    // Auto-dismiss after a short delay
    Future.delayed(const Duration(milliseconds: 1800), () {
      if (mounted && Navigator.canPop(context)) {
        Navigator.pop(context);
      }
    });
  }

  Future<void> _navigateToCalmExitScreen() async {
    AppLogger.debug('[STORY_PLAYER] Navigating to calm exit screen');
    
    try {
      // Pause narration first
      if (_isNarrating && !_isPaused) {
        await _pauseNarration();
      }
      
      if (mounted) {
        context.push('/calm_exit');
      }
    } catch (error) {
      AppLogger.error('[STORY_PLAYER] Error navigating to calm exit screen', error);
    }
  }

  @override
  void dispose() {
    AppLogger.debug('[STORY_PLAYER] Disposing StoryPlaybackWidget');
    _progressSubscription?.cancel();
    _narrationStateSubscription?.cancel();
    _fadeController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      onPopInvokedWithResult: (bool didPop, Object? result) async {
        if (didPop) return;
        AppLogger.debug('[SCENE_DEBUG] Device back button pressed - navigating to Calm Exit Screen');
        await _navigateToCalmExitScreen();
      },
      child: Scaffold(
        backgroundColor: Colors.black,
        body: _buildBody(),
      ),
    );
  }

  Widget _buildBody() {
    if (_imageLoading) {
      return _buildLoadingScreen();
    }
    
    if (_imageError != null) {
      return _buildErrorScreen();
    }
    
    if (!_imageLoaded) {
      return _buildLoadingScreen();
    }
    
    return FadeTransition(
      opacity: _fadeAnimation,
      child: _buildMainContent(),
    );
  }

  Widget _buildLoadingScreen() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(
            valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
          ),
          SizedBox(height: 24),
          Text(
            'Loading Scene...',
            style: TextStyle(
              color: Colors.white,
              fontSize: 18,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorScreen() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(Icons.error_outline, size: 64, color: Colors.red),
          const SizedBox(height: 16),
          Text(
            _imageError ?? 'Failed to load scene',
            style: const TextStyle(color: Colors.red),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: () {
              setState(() {
                _imageError = null;
                _imageLoaded = false;
              });
              _loadSceneImage();
            },
            child: const Text('Retry'),
          ),
        ],
      ),
    );
  }

  Widget _buildMainContent() {
    return Stack(
      children: [
        // Full-screen background image
        _buildFullScreenImage(),

        // Control interface overlay
        if (_showControls) _buildControlOverlay(),

        // Choice selection overlay
        if (_showChoices) _buildChoiceOverlay(),

        // Settings button
        _buildSettingsButton(),
      ],
    );
  }

  Widget _buildFullScreenImage() {
    AppLogger.debug('[IMAGE_LOAD] Asset: ${widget.scene.getImagePath(widget.story.storyId)} | Location: lib/features/story_player/presentation/widgets/story_playback_widget.dart:418 | Widget: _buildFullScreenImage');

    return Positioned.fill(
      child: _imageProvider != null
          ? Image(
              image: _imageProvider!,
              fit: BoxFit.cover,
              width: double.infinity,
              height: double.infinity,
              errorBuilder: (context, error, stackTrace) {
                AppLogger.error('[STORY_PLAYER] Full-screen image render error', error);
                return Container(
                  width: double.infinity,
                  height: double.infinity,
                  color: Colors.black,
                  child: const Center(
                    child: Icon(
                      Icons.broken_image,
                      color: Colors.white54,
                      size: 64,
                    ),
                  ),
                );
              },
            )
          : Container(
              width: double.infinity,
              height: double.infinity,
              color: Colors.black,
            ),
    );
  }

  Widget _buildSettingsButton() {
    return Positioned(
      top: 16,
      right: 16,
      child: Container(
        decoration: BoxDecoration(
          color: Colors.black.withValues(alpha: 0.7),
          shape: BoxShape.circle,
        ),
        child: IconButton(
          onPressed: () {
            setState(() {
              _showControls = !_showControls;
            });
          },
          icon: const Icon(
            Icons.settings,
            color: Colors.white,
            size: 24,
          ),
        ),
      ),
    );
  }

  Widget _buildControlOverlay() {
    final screenSize = MediaQuery.of(context).size;
    final isLandscape = screenSize.width > screenSize.height;
    final isTablet = screenSize.width > 768;

    if (isLandscape && isTablet) {
      // Tablet landscape: Side panel overlay
      return _buildLandscapeControlOverlay();
    } else {
      // Mobile portrait/landscape: Bottom overlay
      return _buildPortraitControlOverlay();
    }
  }

  Widget _buildPortraitControlOverlay() {
    return Positioned(
      bottom: 0,
      left: 0,
      right: 0,
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Colors.transparent,
              Colors.black.withValues(alpha: 0.8),
            ],
          ),
        ),
        child: SafeArea(
          child: _buildControlInterface(),
        ),
      ),
    );
  }

  Widget _buildLandscapeControlOverlay() {
    return Positioned(
      right: 0,
      top: 0,
      bottom: 0,
      width: 300,
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.centerLeft,
            end: Alignment.centerRight,
            colors: [
              Colors.transparent,
              Colors.black.withValues(alpha: 0.8),
            ],
          ),
        ),
        child: SafeArea(
          child: _buildLandscapeControlInterface(),
        ),
      ),
    );
  }

  Widget _buildControlInterface() {
    return Row(
      children: [
        // Play/Pause control
        _buildPlayPauseButton(),

        const SizedBox(width: 16),

        // Text display with highlighting
        Expanded(
          child: _buildTextDisplay(),
        ),

        const SizedBox(width: 16),

        // Navigation control
        _buildNavigationButton(),
      ],
    );
  }

  Widget _buildLandscapeControlInterface() {
    return Column(
      children: [
        // Text display with highlighting
        Expanded(
          flex: 3,
          child: _buildTextDisplay(),
        ),

        const SizedBox(height: 16),

        // Control buttons
        Expanded(
          flex: 1,
          child: Column(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              _buildPlayPauseButton(),
              _buildNavigationButton(),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildPlayPauseButton() {
    IconData iconData = _isNarrating
        ? (_isPaused ? Icons.play_arrow : Icons.pause)
        : Icons.play_arrow;

    return Container(
      decoration: BoxDecoration(
        color: Colors.black.withValues(alpha: 0.7),
        shape: BoxShape.circle,
      ),
      child: IconButton(
        onPressed: () {
          if (_isNarrating) {
            if (_isPaused) {
              _resumeNarration();
            } else {
              _pauseNarration();
            }
          } else {
            _startNarration();
          }
        },
        icon: Icon(
          iconData,
          color: Colors.white,
          size: 32,
        ),
      ),
    );
  }

  Widget _buildTextDisplay() {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.black.withValues(alpha: 0.7),
        borderRadius: BorderRadius.circular(8),
      ),
      child: _buildHighlightedText(),
    );
  }

  Widget _buildHighlightedText() {
    if (_sentences.isEmpty) {
      return Text(
        widget.scene.text,
        style: const TextStyle(
          color: Colors.white,
          fontSize: 18,
        ),
      );
    }

    return RichText(
      text: TextSpan(
        children: _buildHighlightedTextSpans(),
      ),
    );
  }

  List<TextSpan> _buildHighlightedTextSpans() {
    final spans = <TextSpan>[];

    for (int sentenceIndex = 0; sentenceIndex < _sentences.length; sentenceIndex++) {
      final words = _sentenceWords[sentenceIndex];

      for (int wordIndex = 0; wordIndex < words.length; wordIndex++) {
        final word = words[wordIndex];
        final isCurrentWord = sentenceIndex == _currentSentenceIndex &&
                             wordIndex == _currentWordIndex;

        spans.add(TextSpan(
          text: '$word ',
          style: TextStyle(
            color: isCurrentWord ? Colors.yellow : Colors.white,
            fontSize: 18,
            fontWeight: isCurrentWord ? FontWeight.bold : FontWeight.normal,
          ),
        ));
      }
    }

    return spans;
  }

  Widget _buildNavigationButton() {
    final hasChoices = widget.scene.choices?.isNotEmpty ?? false;
    final canAdvance = _hasCompleted && !hasChoices;
    final canShowChoices = hasChoices && _hasCompleted;

    IconData iconData;
    VoidCallback? onPressed;
    Color backgroundColor;
    String tooltip;

    if (canAdvance) {
      iconData = Icons.arrow_forward;
      onPressed = () {
        AppLogger.debug('[STORY_PLAYER] Manual scene progression triggered - advancing to next scene');
        // Show loading screen before calling scene complete
        _showSceneTransitionLoading();
        widget.onSceneComplete?.call();
      };
      backgroundColor = Colors.green.withValues(alpha: 0.9);
      tooltip = 'Next Scene';
    } else if (canShowChoices) {
      iconData = Icons.touch_app;
      onPressed = () {
        AppLogger.debug('[STORY_PLAYER] Showing choices for scene ${widget.scene.id}');
        setState(() {
          _showChoices = true;
        });
      };
      backgroundColor = Colors.orange.withValues(alpha: 0.9);
      tooltip = 'Make Choice';
    } else {
      iconData = Icons.hourglass_empty;
      onPressed = null;
      backgroundColor = Colors.grey.withValues(alpha: 0.5);
      tooltip = 'Wait for narration';
    }

    return Tooltip(
      message: tooltip,
      child: Container(
        decoration: BoxDecoration(
          color: backgroundColor,
          shape: BoxShape.circle,
        ),
        child: IconButton(
          onPressed: onPressed,
          icon: Icon(
            iconData,
            color: Colors.white,
            size: 32,
          ),
        ),
      ),
    );
  }

  Widget _buildChoiceOverlay() {
    final screenSize = MediaQuery.of(context).size;
    final isLandscape = screenSize.width > screenSize.height;
    final maxWidth = isLandscape ? screenSize.width * 0.7 : screenSize.width * 0.9;

    return Positioned.fill(
      child: Container(
        color: Colors.black.withValues(alpha: 0.8),
        child: Center(
          child: Container(
            constraints: BoxConstraints(
              maxWidth: maxWidth.clamp(300, 800),
              maxHeight: screenSize.height * 0.8,
            ),
            margin: const EdgeInsets.all(16),
            padding: const EdgeInsets.all(24),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(16),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.3),
                  blurRadius: 20,
                  offset: const Offset(0, 10),
                ),
              ],
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Text(
                  'Choose your path:',
                  style: TextStyle(
                    color: Colors.black87,
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 24),
                ...widget.scene.choices!.map((choice) => _buildChoiceButton(choice)),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildChoiceButton(ChoiceOptionModel choice) {
    return Container(
      width: double.infinity,
      margin: const EdgeInsets.only(bottom: 12),
      child: ElevatedButton(
        onPressed: () {
          AppLogger.debug('[STORY_PLAYER] Choice selected: "${choice.option}" -> ${choice.next}');
          setState(() {
            _showChoices = false;
          });
          // Show loading screen before processing choice
          _showChoiceTransitionLoading(choice.option);
          widget.onChoiceSelected(choice);
        },
        style: ElevatedButton.styleFrom(
          padding: const EdgeInsets.all(16),
          backgroundColor: Colors.blue[600],
          foregroundColor: Colors.white,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
        ),
        child: Text(
          choice.option,
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w500,
          ),
          textAlign: TextAlign.center,
        ),
      ),
    );
  }
}
