// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'rewards_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

RewardsModel _$RewardsModelFromJson(Map<String, dynamic> json) => RewardsModel(
      completion: json['completion'] as String?,
      moralChoices: (json['moralChoices'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
    );

Map<String, dynamic> _$RewardsModelToJson(RewardsModel instance) =>
    <String, dynamic>{
      'completion': instance.completion,
      'moralChoices': instance.moralChoices,
    };

EarnedReward _$EarnedRewardFromJson(Map<String, dynamic> json) => EarnedReward(
      rewardId: json['rewardId'] as String,
      rewardType: json['rewardType'] as String,
      earnedAt: DateTime.parse(json['earnedAt'] as String),
      storyId: json['storyId'] as String,
      sceneId: json['sceneId'] as String?,
    );

Map<String, dynamic> _$EarnedRewardToJson(EarnedReward instance) =>
    <String, dynamic>{
      'rewardId': instance.rewardId,
      'rewardType': instance.rewardType,
      'earnedAt': instance.earnedAt.toIso8601String(),
      'storyId': instance.storyId,
      'sceneId': instance.sceneId,
    };
