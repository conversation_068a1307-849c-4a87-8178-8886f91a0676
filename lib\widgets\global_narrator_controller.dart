import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:choice_once_upon_a_time/core/audio/tts_service_interface.dart';
import 'package:choice_once_upon_a_time/core/localization/screen_narration_service.dart';
import 'package:choice_once_upon_a_time/app/providers/service_providers.dart';
import 'package:choice_once_upon_a_time/app/providers/settings_provider.dart';

class GlobalNarratorState {
  final bool isPlaying;
  final String currentText;
  final String currentRoute;

  const GlobalNarratorState({
    this.isPlaying = false,
    this.currentText = '',
    this.currentRoute = '',
  });

  GlobalNarratorState copyWith({
    bool? isPlaying,
    String? currentText,
    String? currentRoute,
  }) {
    return GlobalNarratorState(
      isPlaying: isPlaying ?? this.isPlaying,
      currentText: currentText ?? this.currentText,
      currentRoute: currentRoute ?? this.currentRoute,
    );
  }
}

class GlobalNarratorController extends StateNotifier<GlobalNarratorState> {
  final TTSServiceInterface _ttsService;
  final ScreenNarrationService _narrationService;
  final Ref _ref;
  final Set<String> _narratedRoutes = {};

  GlobalNarratorController(this._ttsService, this._narrationService, this._ref)
      : super(const GlobalNarratorState());

  Future<void> playNarrationForRoute(String route) async {
    final settings = _ref.read(settingsProvider);
    if (!settings.isVoiceGuideEnabled) {
      return;
    }

    if (_narratedRoutes.contains(route)) {
      return;
    }

    if (route.startsWith('/story/')) {
      stopNarration();
      return;
    }

    final narrationKey = _getNarrationKeyForRoute(route);
    if (narrationKey != null) {
      final narration = _narrationService.getNarrationForScreen(narrationKey);
      if (narration != null) {
        _narratedRoutes.add(route);
        state = state.copyWith(currentText: narration.text, isPlaying: true, currentRoute: route);

        try {
          await _ttsService.speakScreenIntroduction(
            text: narration.text,
            emotionCue: narration.emotionCue,
          );
          // Update state when narration completes
          if (state.currentRoute == route) {
            state = state.copyWith(isPlaying: false);
          }
        } catch (e) {
          // Handle TTS errors gracefully
          state = state.copyWith(isPlaying: false);
        }
      }
    } else {
      stopNarration();
      state = state.copyWith(currentText: '', currentRoute: route);
    }
  }

  Future<void> stopNarration() async {
    await _ttsService.stop();
    state = state.copyWith(isPlaying: false, currentText: '');
  }

  Future<void> pauseNarration() async {
    await _ttsService.pause();
    state = state.copyWith(isPlaying: false);
  }

  Future<void> resumeNarration() async {
    if (state.currentText.isNotEmpty) {
      state = state.copyWith(isPlaying: true);
      try {
        await _ttsService.speakText(state.currentText);
        // Update state when narration completes
        if (state.isPlaying) {
          state = state.copyWith(isPlaying: false);
        }
      } catch (e) {
        // Handle TTS errors gracefully
        state = state.copyWith(isPlaying: false);
      }
    }
  }

  /// Check if TTS is currently speaking and update state accordingly
  void updatePlayingState() {
    final isTTSSpeaking = _ttsService.isSpeaking;
    if (state.isPlaying != isTTSSpeaking && state.currentText.isNotEmpty) {
      state = state.copyWith(isPlaying: isTTSSpeaking);
    }
  }

  /// Clear narrated routes (useful for testing or when user wants to hear introductions again)
  void clearNarratedRoutes() {
    _narratedRoutes.clear();
  }

  String? _getNarrationKeyForRoute(String route) {
    const routeToNarrationKey = {
      '/launch': 'screen_launch_welcome',
      '/home': 'screen_home_library_intro',
      '/ftue': 'screen_ftue_intro',
      '/parent_zone': 'screen_parent_zone_intro',
      '/parent_zone/sound_settings': 'screen_sound_settings_intro',
      '/parent_zone/subscription': 'screen_subscription_intro',
      '/parent_zone/about_stories': 'screen_about_stories_intro',
      '/parent_zone/manage_downloads': 'screen_manage_downloads_intro',
      '/parent_zone/help_support': 'screen_help_support_intro',
      '/parent_gate_entry': 'screen_parental_gate_intro',
    };
    if (route.startsWith('/story/')) {
        return 'screen_story_intro_welcome';
    }
    return routeToNarrationKey[route];
  }
}

final globalNarratorProvider =
    StateNotifierProvider<GlobalNarratorController, GlobalNarratorState>((ref) {
  final ttsService = ref.watch(ttsServiceProvider);
  final narrationService = ref.watch(screenNarrationServiceProvider);
  return GlobalNarratorController(ttsService, narrationService, ref);
});
