import 'dart:async';
import 'dart:io';
import 'package:flutter/services.dart';
import 'package:choice_once_upon_a_time/core/services/fallback_asset_manager.dart';
import 'package:choice_once_upon_a_time/core/utils/app_logger.dart';

/// Test runner for comprehensive fallback asset system validation
/// Tests missing images, audio files, and asset resolution with proper fallbacks
class FallbackAssetTestRunner {
  static const String _logPrefix = 'FALLBACK_ASSET_TEST';
  
  final FallbackAssetManager _fallbackManager;
  
  // Test assets - mix of existing and non-existing files
  static const List<String> testImagePaths = [
    'assets/stories/story013/images/scene1.jpg', // Should exist
    'assets/stories/story013/images/missing_image.jpg', // Should not exist
    'assets/stories/story014/images/scene1.jpg', // Should exist
    'assets/stories/nonexistent/images/test.jpg', // Should not exist
    'assets/default/default_image.png', // Default fallback - should exist
  ];
  
  static const List<String> testAudioPaths = [
    'assets/stories/story013/audio/scene1.mp3', // May or may not exist
    'assets/stories/story013/audio/missing_audio.mp3', // Should not exist
    'assets/stories/story014/audio/scene1.mp3', // May or may not exist
    'assets/stories/nonexistent/audio/test.mp3', // Should not exist
    'assets/default/default_happy.mp3', // Default fallback - should exist
  ];
  
  static const List<String> testStoryIds = ['story013', 'story014', 'nonexistent_story'];
  
  // Test results
  final List<FallbackAssetTestResult> _testResults = [];
  
  FallbackAssetTestRunner({
    required FallbackAssetManager fallbackManager,
  }) : _fallbackManager = fallbackManager;

  /// Run comprehensive fallback asset tests
  Future<List<FallbackAssetTestResult>> runFallbackTests() async {
    try {
      AppLogger.info('$_logPrefix: Starting comprehensive fallback asset tests');
      
      // Test 1: Validate default assets exist
      await _testDefaultAssetsExist();
      
      // Test 2: Test image fallback resolution
      await _testImageFallbackResolution();
      
      // Test 3: Test audio fallback resolution
      await _testAudioFallbackResolution();
      
      // Test 4: Test story-specific asset resolution
      await _testStorySpecificAssetResolution();
      
      // Test 5: Test batch asset resolution
      await _testBatchAssetResolution();
      
      // Test 6: Test asset metadata retrieval
      await _testAssetMetadataRetrieval();
      
      // Test 7: Test cache performance
      await _testCachePerformance();
      
      // Test 8: Test edge cases
      await _testEdgeCases();
      
      AppLogger.info('$_logPrefix: Completed all fallback asset tests');
      return List.from(_testResults);
      
    } catch (e, stackTrace) {
      AppLogger.error('$_logPrefix: Error running fallback tests', e, stackTrace);
      return _testResults;
    }
  }

  /// Test that default fallback assets exist
  Future<void> _testDefaultAssetsExist() async {
    final testName = 'Default Assets Validation';
    AppLogger.info('$_logPrefix: Running test: $testName');
    
    try {
      final isValid = await _fallbackManager.validateDefaultAssets();
      
      if (isValid) {
        _addTestResult(testName, true, 'Default assets validated successfully');
      } else {
        _addTestResult(testName, false, 'Default assets validation failed');
      }
      
    } catch (e) {
      _addTestResult(testName, false, 'Error validating default assets: $e');
    }
  }

  /// Test image fallback resolution
  Future<void> _testImageFallbackResolution() async {
    final testName = 'Image Fallback Resolution';
    AppLogger.info('$_logPrefix: Running test: $testName');
    
    try {
      final results = <String, String>{};
      
      for (final imagePath in testImagePaths) {
        final resolvedPath = await _fallbackManager.getImageAssetPath(imagePath);
        results[imagePath] = resolvedPath;
        
        AppLogger.debug('$_logPrefix: Image $imagePath -> $resolvedPath');
        
        // Verify that missing images fall back to default
        if (!await _assetExists(imagePath)) {
          if (resolvedPath != FallbackAssetManager.defaultImagePath) {
            _addTestResult(testName, false, 'Missing image $imagePath did not fall back to default');
            return;
          }
        }
      }
      
      _addTestResult(testName, true, 'All image fallbacks resolved correctly', results);
      
    } catch (e) {
      _addTestResult(testName, false, 'Error testing image fallbacks: $e');
    }
  }

  /// Test audio fallback resolution
  Future<void> _testAudioFallbackResolution() async {
    final testName = 'Audio Fallback Resolution';
    AppLogger.info('$_logPrefix: Running test: $testName');
    
    try {
      final results = <String, String>{};
      
      for (final audioPath in testAudioPaths) {
        final resolvedPath = await _fallbackManager.getAudioAssetPath(audioPath);
        results[audioPath] = resolvedPath;
        
        AppLogger.debug('$_logPrefix: Audio $audioPath -> $resolvedPath');
        
        // Verify that missing audio files fall back to default
        if (!await _assetExists(audioPath)) {
          if (resolvedPath != FallbackAssetManager.defaultAudioPath) {
            _addTestResult(testName, false, 'Missing audio $audioPath did not fall back to default');
            return;
          }
        }
      }
      
      _addTestResult(testName, true, 'All audio fallbacks resolved correctly', results);
      
    } catch (e) {
      _addTestResult(testName, false, 'Error testing audio fallbacks: $e');
    }
  }

  /// Test story-specific asset resolution
  Future<void> _testStorySpecificAssetResolution() async {
    final testName = 'Story-Specific Asset Resolution';
    AppLogger.info('$_logPrefix: Running test: $testName');
    
    try {
      final results = <String, Map<String, String>>{};
      
      for (final storyId in testStoryIds) {
        final storyResults = <String, String>{};
        
        // Test story-specific image
        final imagePath = await _fallbackManager.getStoryAssetWithFallback(
          storyId: storyId,
          assetPath: 'images/scene1.jpg',
        );
        storyResults['image'] = imagePath;
        
        // Test story-specific audio
        final audioPath = await _fallbackManager.getStoryAssetWithFallback(
          storyId: storyId,
          assetPath: 'audio/scene1.mp3',
        );
        storyResults['audio'] = audioPath;
        
        results[storyId] = storyResults;
        
        AppLogger.debug('$_logPrefix: Story $storyId assets resolved');
      }
      
      _addTestResult(testName, true, 'Story-specific assets resolved correctly', results);
      
    } catch (e) {
      _addTestResult(testName, false, 'Error testing story-specific assets: $e');
    }
  }

  /// Test batch asset resolution
  Future<void> _testBatchAssetResolution() async {
    final testName = 'Batch Asset Resolution';
    AppLogger.info('$_logPrefix: Running test: $testName');
    
    try {
      final allTestPaths = [...testImagePaths, ...testAudioPaths];
      final results = await _fallbackManager.batchResolveAssets(allTestPaths);
      
      if (results.length == allTestPaths.length) {
        _addTestResult(testName, true, 'Batch resolution completed successfully', results);
      } else {
        _addTestResult(testName, false, 'Batch resolution incomplete: ${results.length}/${allTestPaths.length}');
      }
      
    } catch (e) {
      _addTestResult(testName, false, 'Error testing batch resolution: $e');
    }
  }

  /// Test asset metadata retrieval
  Future<void> _testAssetMetadataRetrieval() async {
    final testName = 'Asset Metadata Retrieval';
    AppLogger.info('$_logPrefix: Running test: $testName');
    
    try {
      final metadataResults = <String, Map<String, dynamic>>{};
      
      for (final assetPath in [...testImagePaths.take(3), ...testAudioPaths.take(3)]) {
        final metadata = await _fallbackManager.getAssetMetadata(assetPath);
        
        metadataResults[assetPath] = {
          'original_path': metadata.originalPath,
          'resolved_path': metadata.resolvedPath,
          'exists': metadata.exists,
          'asset_type': metadata.assetType,
          'needs_fallback': metadata.needsFallback,
        };
        
        AppLogger.debug('$_logPrefix: Metadata for $assetPath: $metadata');
      }
      
      _addTestResult(testName, true, 'Asset metadata retrieved successfully', metadataResults);
      
    } catch (e) {
      _addTestResult(testName, false, 'Error testing asset metadata: $e');
    }
  }

  /// Test cache performance
  Future<void> _testCachePerformance() async {
    final testName = 'Cache Performance';
    AppLogger.info('$_logPrefix: Running test: $testName');
    
    try {
      final testAsset = testImagePaths.first;
      
      // First call (should populate cache)
      final startTime1 = DateTime.now();
      await _fallbackManager.getImageAssetPath(testAsset);
      final firstCallTime = DateTime.now().difference(startTime1).inMilliseconds;
      
      // Second call (should use cache)
      final startTime2 = DateTime.now();
      await _fallbackManager.getImageAssetPath(testAsset);
      final secondCallTime = DateTime.now().difference(startTime2).inMilliseconds;
      
      // Get cache statistics
      final cacheStats = _fallbackManager.getCacheStats();
      
      final results = {
        'first_call_ms': firstCallTime,
        'second_call_ms': secondCallTime,
        'cache_stats': cacheStats,
      };
      
      if (secondCallTime <= firstCallTime) {
        _addTestResult(testName, true, 'Cache performance validated', results);
      } else {
        _addTestResult(testName, false, 'Cache not improving performance', results);
      }
      
    } catch (e) {
      _addTestResult(testName, false, 'Error testing cache performance: $e');
    }
  }

  /// Test edge cases
  Future<void> _testEdgeCases() async {
    final testName = 'Edge Cases';
    AppLogger.info('$_logPrefix: Running test: $testName');
    
    try {
      final edgeCaseResults = <String, String>{};
      
      // Test empty path
      final emptyPath = await _fallbackManager.getAssetPath('');
      edgeCaseResults['empty_path'] = emptyPath;
      
      // Test null story ID
      final nullStoryPath = await _fallbackManager.getStoryAssetWithFallback(
        storyId: '',
        assetPath: 'test.jpg',
      );
      edgeCaseResults['null_story_id'] = nullStoryPath;
      
      // Test unknown file extension
      final unknownExtPath = await _fallbackManager.getAssetPath('test.unknown');
      edgeCaseResults['unknown_extension'] = unknownExtPath;
      
      // Test very long path
      final longPath = 'assets/stories/${'very' * 20}/images/test.jpg';
      final longPathResult = await _fallbackManager.getImageAssetPath(longPath);
      edgeCaseResults['long_path'] = longPathResult;
      
      _addTestResult(testName, true, 'Edge cases handled correctly', edgeCaseResults);
      
    } catch (e) {
      _addTestResult(testName, false, 'Error testing edge cases: $e');
    }
  }

  /// Check if an asset exists
  Future<bool> _assetExists(String assetPath) async {
    try {
      if (assetPath.startsWith('assets/')) {
        await rootBundle.load(assetPath);
        return true;
      } else {
        final file = File(assetPath);
        return await file.exists();
      }
    } catch (e) {
      return false;
    }
  }

  /// Add test result
  void _addTestResult(String testName, bool success, String message, [Map<String, dynamic>? data]) {
    final result = FallbackAssetTestResult(
      testName: testName,
      success: success,
      message: message,
      data: data,
      timestamp: DateTime.now(),
    );
    
    _testResults.add(result);
    
    if (success) {
      AppLogger.info('$_logPrefix: ✅ $testName: $message');
    } else {
      AppLogger.error('$_logPrefix: ❌ $testName: $message');
    }
  }

  /// Generate comprehensive test report
  String generateTestReport() {
    final buffer = StringBuffer();
    
    buffer.writeln('=== FALLBACK ASSET SYSTEM TEST REPORT ===');
    buffer.writeln('Generated: ${DateTime.now().toIso8601String()}');
    buffer.writeln('Total Tests: ${_testResults.length}');
    
    final successfulTests = _testResults.where((r) => r.success).length;
    final failedTests = _testResults.length - successfulTests;
    
    buffer.writeln('Successful: $successfulTests');
    buffer.writeln('Failed: $failedTests');
    buffer.writeln('Success Rate: ${(successfulTests / _testResults.length * 100).toStringAsFixed(1)}%');
    buffer.writeln();
    
    buffer.writeln('=== TEST RESULTS ===');
    for (final result in _testResults) {
      buffer.writeln('${result.success ? "✅" : "❌"} ${result.testName}');
      buffer.writeln('   Message: ${result.message}');
      if (result.data != null) {
        buffer.writeln('   Data: ${result.data}');
      }
      buffer.writeln('   Time: ${result.timestamp.toIso8601String()}');
      buffer.writeln();
    }
    
    buffer.writeln('=== FALLBACK CONFIGURATION ===');
    buffer.writeln('Default Image: ${FallbackAssetManager.defaultImagePath}');
    buffer.writeln('Default Audio: ${FallbackAssetManager.defaultAudioPath}');
    
    return buffer.toString();
  }
}

/// Result of a fallback asset test
class FallbackAssetTestResult {
  final String testName;
  final bool success;
  final String message;
  final Map<String, dynamic>? data;
  final DateTime timestamp;

  const FallbackAssetTestResult({
    required this.testName,
    required this.success,
    required this.message,
    this.data,
    required this.timestamp,
  });

  Map<String, dynamic> toJson() {
    return {
      'test_name': testName,
      'success': success,
      'message': message,
      'data': data,
      'timestamp': timestamp.toIso8601String(),
    };
  }
}
