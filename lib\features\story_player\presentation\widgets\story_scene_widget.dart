import 'package:flutter/material.dart';
import 'package:choice_once_upon_a_time/models/enhanced_story_model.dart';
import 'package:choice_once_upon_a_time/core/services/story_narration_service_interface.dart';
import 'package:choice_once_upon_a_time/core/services/story_settings_service.dart';

import 'package:choice_once_upon_a_time/core/utils/app_logger.dart';

/// Widget for displaying and playing story scenes
class StorySceneWidget extends StatefulWidget {
  final EnhancedStoryModel story;
  final EnhancedSceneModel scene;
  final Function(ChoiceOptionModel) onChoiceSelected;
  final VoidCallback onSceneComplete;
  final IStoryNarrationService narrationService;
  final StorySettingsService settingsService;

  const StorySceneWidget({
    super.key,
    required this.story,
    required this.scene,
    required this.onChoiceSelected,
    required this.onSceneComplete,
    required this.narrationService,
    required this.settingsService,
  });

  @override
  State<StorySceneWidget> createState() => _StorySceneWidgetState();
}

class _StorySceneWidgetState extends State<StorySceneWidget>
    with TickerProviderStateMixin {
  late final AnimationController _sceneController;
  late final AnimationController _emotionController;
  late final AnimationController _choicesController;

  late final Animation<double> _sceneAnimation;
  late final Animation<double> _emotionAnimation;
  late final Animation<Offset> _choicesSlideAnimation;

  bool _showChoices = false;
  bool _hasNarrated = false;
  bool _narrationComplete = false;

  // Image stability and caching
  ImageProvider? _cachedImageProvider;
  String? _currentImagePath;
  bool _imageLoaded = false;

  // Scene stability tracking
  String? _currentSceneId;
  bool _sceneContentStable = false;

  @override
  void initState() {
    super.initState();
    AppLogger.debug('[COMPONENT_LOAD] Full path: lib/features/story_player/presentation/widgets/story_scene_widget.dart - StorySceneWidget');
    _initializeSceneStability();
    _initializeAnimations();
    _startSceneAnimation();
  }

  /// Initialize scene stability and image caching
  void _initializeSceneStability() {
    _currentSceneId = widget.scene.id;
    _currentImagePath = widget.scene.getImagePath(widget.story.storyId);
    _sceneContentStable = false;

    // Pre-cache the image to prevent reloading
    //_cacheSceneImage();

    AppLogger.debug('[SCENE_DEBUG] Scene ${widget.scene.id} initialized - Image: $_currentImagePath');
  }

  /// Cache the scene image to prevent reloading
  void _cacheSceneImage() {
    AppLogger.debug('[SCENE_DEBUG] Caching scene image');
    if (_currentImagePath != null) {
      AppLogger.debug('[IMAGE_LOAD] Asset: $_currentImagePath | Location: lib/features/story_player/presentation/widgets/story_scene_widget.dart:79 | Widget: AssetImage');
      _cachedImageProvider = AssetImage(_currentImagePath!);

      // Pre-load the image
      _cachedImageProvider!.resolve(const ImageConfiguration()).addListener(
        ImageStreamListener((ImageInfo info, bool synchronousCall) {
          if (mounted) {
            setState(() {
              _imageLoaded = true;
              _sceneContentStable = true;
            });
            AppLogger.debug('[SCENE_DEBUG] Scene ${widget.scene.id} image loaded successfully');
          }
        }, onError: (dynamic exception, StackTrace? stackTrace) {
          AppLogger.error('[SCENE_DEBUG] Failed to load image for scene ${widget.scene.id}', exception);
        }),
      );
    }
  }

  void _initializeAnimations() {
    _sceneController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );

    _emotionController = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );

    _choicesController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );

    _sceneAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _sceneController,
      curve: Curves.easeInOut,
    ));

    _emotionAnimation = Tween<double>(
      begin: 1.0,
      end: 1.2,
    ).animate(CurvedAnimation(
      parent: _emotionController,
      curve: Curves.elasticOut,
    ));

    _choicesSlideAnimation = Tween<Offset>(
      begin: const Offset(0.0, 1.0),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _choicesController,
      curve: Curves.easeOutBack,
    ));
  }

  Future<void> _startSceneAnimation() async {
    await _sceneController.forward();
    _triggerEmotionEffect();
  }

  void _triggerEmotionEffect() {
    // Trigger emotion-based visual effect
    switch (widget.scene.emotion.toLowerCase()) {
      case 'happy':
      case 'excited':
      case 'joyful':
        _emotionController.forward().then((_) => _emotionController.reverse());
        break;
      case 'sad':
      case 'disappointed':
        // Could add a different animation for sad emotions
        break;
      case 'surprised':
      case 'amazed':
        _emotionController.repeat(reverse: true, period: const Duration(milliseconds: 800));
        Future.delayed(const Duration(seconds: 2), () {
          _emotionController.stop();
          _emotionController.reset();
        });
        break;
    }
  }

  void _onNarrationComplete() {
    AppLogger.debug('[SCENE_DEBUG] Narration completed for scene ${widget.scene.id}');

    setState(() {
      _hasNarrated = true;
      _narrationComplete = true;
    });

    // Show choices or continue after narration - ONLY on explicit triggers
    if (widget.scene.hasChoices) {
      AppLogger.debug('[SCENE_DEBUG] Showing choices for scene ${widget.scene.id}');
      Future.delayed(const Duration(milliseconds: 500), () {
        _showChoicesWithAnimation();
      });
    } else {
      AppLogger.debug('[SCENE_DEBUG] Scene ${widget.scene.id} ready for continuation');
      // Don't auto-advance - wait for explicit user action or scene completion trigger
    }
  }

  Future<void> _showChoicesWithAnimation() async {
    setState(() {
      _showChoices = true;
    });
    await _choicesController.forward();
  }

  void _onChoiceSelected(ChoiceOptionModel choice) {
    AppLogger.debug('[SCENE_DEBUG] Choice selected: ${choice.option} - "${choice.option}" (Next: ${choice.next})');

    // Add choice feedback animation
    _choicesController.reverse().then((_) {
      widget.onChoiceSelected(choice);
    });
  }

  /// Build stable image that doesn't reload during narration
  Widget _buildStableImage(ThemeData theme, bool isSmallScreen) {
    // Use cached image provider if available, otherwise fallback to direct asset loading
    if (_cachedImageProvider != null && _sceneContentStable) {
      AppLogger.debug('[IMAGE_LOAD] Asset: $_currentImagePath | Location: lib/features/story_player/presentation/widgets/story_scene_widget.dart:208 | Widget: Image');
      return Image(
        image: _cachedImageProvider!,
        fit: BoxFit.cover,
        width: double.infinity,
        height: double.infinity,
        errorBuilder: (context, error, stackTrace) {
          AppLogger.error('[SCENE_DEBUG] Error loading cached image for scene ${widget.scene.id}', error);
          return _buildImageErrorWidget(theme, isSmallScreen);
        },
      );
    } else {
      // Fallback to direct asset loading with error handling
      final imagePath = widget.scene.getImagePath(widget.story.storyId);
      AppLogger.debug('[IMAGE_LOAD] Asset: $imagePath | Location: lib/features/story_player/presentation/widgets/story_scene_widget.dart:219 | Widget: Image.asset');
      return Image.asset(
        imagePath,
        fit: BoxFit.cover,
        width: double.infinity,
        height: double.infinity,
        errorBuilder: (context, error, stackTrace) {
          AppLogger.error('[SCENE_DEBUG] Error loading asset image for scene ${widget.scene.id}', error);
          return _buildImageErrorWidget(theme, isSmallScreen);
        },
      );
    }
  }

  /// Build error widget for failed image loads
  Widget _buildImageErrorWidget(ThemeData theme, bool isSmallScreen) {
    return Container(
      width: double.infinity,
      height: double.infinity,
      color: theme.colorScheme.errorContainer,
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.broken_image,
              size: isSmallScreen ? 64 : 80,
              color: theme.colorScheme.onErrorContainer,
            ),
            const SizedBox(height: 16),
            Text(
              'Failed to load scene image',
              style: theme.textTheme.bodyMedium?.copyWith(
                color: theme.colorScheme.onErrorContainer,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Color _getEmotionColor(String emotion) {
    switch (emotion.toLowerCase()) {
      case 'happy':
      case 'excited':
      case 'joyful':
        return Colors.amber;
      case 'sad':
      case 'disappointed':
        return Colors.blue;
      case 'surprised':
      case 'amazed':
        return Colors.purple;
      case 'angry':
        return Colors.red;
      case 'calm':
      case 'peaceful':
        return Colors.green;
      default:
        return Colors.grey;
    }
  }

  @override
  void dispose() {
    _sceneController.dispose();
    _emotionController.dispose();
    _choicesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final screenSize = MediaQuery.of(context).size;
    final isSmallScreen = screenSize.width < 600;
    final isLandscape = screenSize.width > screenSize.height;

    return Stack(
      children: [
        // Full-width background image
        Positioned.fill(
          child: AnimatedBuilder(
            animation: _sceneAnimation,
            builder: (context, child) {
              return FadeTransition(
                opacity: _sceneAnimation,
                child: _buildFullWidthImage(theme, isSmallScreen),
              );
            },
          ),
        ),

        // Content overlay
        SafeArea(
          child: _buildContentOverlay(theme, screenSize, isSmallScreen, isLandscape),
        ),
      ],
    );
  }

  Widget _buildFullWidthImage(ThemeData theme, bool isSmallScreen) {
    return SizedBox(
      width: double.infinity,
      height: double.infinity,
      child: _buildStableImage(theme, isSmallScreen),
    );
  }

  Widget _buildContentOverlay(ThemeData theme, Size screenSize, bool isSmallScreen, bool isLandscape) {
    if (isLandscape) {
      return _buildLandscapeOverlay(theme, screenSize, isSmallScreen);
    } else {
      return _buildPortraitOverlay(theme, screenSize, isSmallScreen);
    }
  }

  Widget _buildPortraitOverlay(ThemeData theme, Size screenSize, bool isSmallScreen) {
    return Column(
      children: [
        // Spacer to push content to bottom
        const Expanded(flex: 2, child: SizedBox()),

        // Text overlay at bottom
        Container(
          width: double.infinity,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [
                Colors.transparent,
                Colors.black.withValues(alpha: 0.8),
              ],
            ),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Narration text
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.black.withValues(alpha: 0.7),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  widget.scene.text,
                  style: theme.textTheme.bodyLarge?.copyWith(
                    color: Colors.white,
                    fontSize: isSmallScreen ? 16 : 18,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),

              const SizedBox(height: 16),

              // Choices or continue button
              _buildActionButtons(theme, isSmallScreen),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildLandscapeOverlay(ThemeData theme, Size screenSize, bool isSmallScreen) {
    return Row(
      children: [
        // Left side - spacer for image
        const Expanded(flex: 2, child: SizedBox()),

        // Right side - content panel
        Expanded(
          flex: 1,
          child: Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.centerLeft,
                end: Alignment.centerRight,
                colors: [
                  Colors.transparent,
                  Colors.black.withValues(alpha: 0.8),
                ],
              ),
            ),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // Narration text
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.black.withValues(alpha: 0.7),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    widget.scene.text,
                    style: theme.textTheme.bodyLarge?.copyWith(
                      color: Colors.white,
                      fontSize: 16,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),

                const SizedBox(height: 24),

                // Choices or continue button
                _buildActionButtons(theme, isSmallScreen),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildActionButtons(ThemeData theme, bool isSmallScreen) {
    if (_showChoices && widget.scene.hasChoices) {
      return Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            'What should happen next?',
            style: theme.textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
          const SizedBox(height: 16),
          ...widget.scene.choices!.map((choice) => Container(
            width: double.infinity,
            margin: const EdgeInsets.only(bottom: 8),
            child: ElevatedButton(
              onPressed: () => _onChoiceSelected(choice),
              style: ElevatedButton.styleFrom(
                padding: const EdgeInsets.all(16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                backgroundColor: theme.colorScheme.primary,
                foregroundColor: theme.colorScheme.onPrimary,
              ),
              child: Text(
                choice.option,
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                ),
                textAlign: TextAlign.center,
              ),
            ),
          )),
        ],
      );
    } else if (!widget.scene.hasChoices && _hasNarrated) {
      return SizedBox(
        width: double.infinity,
        child: ElevatedButton(
          onPressed: widget.onSceneComplete,
          style: ElevatedButton.styleFrom(
            padding: const EdgeInsets.symmetric(vertical: 16),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            backgroundColor: theme.colorScheme.primary,
            foregroundColor: theme.colorScheme.onPrimary,
          ),
          child: const Text(
            'Continue',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
      );
    } else {
      return const SizedBox.shrink();
    }
  }
}
