import 'dart:convert';
import 'dart:io';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:path/path.dart' as path;
import 'package:choice_once_upon_a_time/core/models/story_status.dart';
import 'package:choice_once_upon_a_time/core/services/story_download_service.dart';
import 'package:choice_once_upon_a_time/core/utils/app_logger.dart';
import 'package:choice_once_upon_a_time/models/enhanced_story_model.dart';

/// Enhanced repository for managing stories from multiple sources
class EnhancedStoryRepository {
  static const String _logPrefix = 'ENHANCED_STORY_REPO';
  
  final FirebaseFirestore _firestore;
  final StoryDownloadService _downloadService;

  EnhancedStoryRepository({
    FirebaseFirestore? firestore,
    required StoryDownloadService downloadService,
  }) : _firestore = firestore ?? FirebaseFirestore.instance,
       _downloadService = downloadService;

  /// Get all stories with their current status
  Future<List<StoryMetadata>> getAllStories() async {
    try {
      AppLogger.info('$_logPrefix: Loading all stories from multiple sources');
      
      // Load stories from both sources concurrently
      final results = await Future.wait([
        _loadAssetStories(),
        _loadFirebaseStories(),
      ]);

      final assetStories = results[0];
      final firebaseStories = results[1];

      // Combine and deduplicate stories (Firebase takes precedence)
      final allStories = <String, StoryMetadata>{};
      
      // Add asset stories first
      for (final story in assetStories) {
        allStories[story.id] = story;
      }
      
      // Add Firebase stories (will override asset stories with same ID)
      for (final story in firebaseStories) {
        allStories[story.id] = story;
      }

      // Update status for downloaded stories
      final downloadedStories = await _downloadService.getDownloadedStories();
      for (final storyId in downloadedStories) {
        if (allStories.containsKey(storyId)) {
          allStories[storyId] = allStories[storyId]!.copyWith(
            status: StoryStatus.downloaded,
          );
        }
      }

      final stories = allStories.values.toList();
      AppLogger.info('$_logPrefix: Loaded ${stories.length} total stories');
      return stories;

    } catch (e, stackTrace) {
      AppLogger.error('$_logPrefix: Error loading stories', e, stackTrace);
      return [];
    }
  }

  /// Get story by ID with full details
  Future<EnhancedStoryModel?> getStoryById(String storyId) async {
    try {
      AppLogger.info('$_logPrefix: Loading story details for $storyId');

      // Check if story is downloaded locally
      final isDownloaded = await _downloadService.isStoryDownloaded(storyId);
      
      String? storyJsonPath;
      
      if (isDownloaded) {
        // Load from downloaded location
        storyJsonPath = await _downloadService.getStoryAssetPath(storyId, 'story.json');
      } else {
        // Try to load from assets
        try {
          final assetPath = 'assets/stories/$storyId/story.json';
          await rootBundle.loadString(assetPath);
          storyJsonPath = assetPath;
        } catch (e) {
          AppLogger.warning('$_logPrefix: Story $storyId not found in assets');
        }
      }

      if (storyJsonPath == null) {
        AppLogger.error('$_logPrefix: Story $storyId not found locally');
        return null;
      }

      // Load story JSON
      String jsonContent;
      if (storyJsonPath.startsWith('assets/')) {
        jsonContent = await rootBundle.loadString(storyJsonPath);
      } else {
        final file = File(storyJsonPath);
        jsonContent = await file.readAsString();
      }

      final storyData = jsonDecode(jsonContent) as Map<String, dynamic>;
      
      // Create story with proper asset paths
      final story = EnhancedStoryModel.fromJson(storyData);

      AppLogger.info('$_logPrefix: Successfully loaded story $storyId');
      return story;

    } catch (e, stackTrace) {
      AppLogger.error('$_logPrefix: Error loading story $storyId', e, stackTrace);
      return null;
    }
  }

  /// Download story from Firebase
  Future<bool> downloadStory(String storyId, {Function(double)? onProgress}) async {
    try {
      AppLogger.info('$_logPrefix: Starting download for story $storyId');
      return await _downloadService.downloadStory(storyId, onProgress: onProgress);
    } catch (e, stackTrace) {
      AppLogger.error('$_logPrefix: Error downloading story $storyId', e, stackTrace);
      return false;
    }
  }

  /// Delete downloaded story
  Future<bool> deleteDownloadedStory(String storyId) async {
    try {
      AppLogger.info('$_logPrefix: Deleting downloaded story $storyId');
      return await _downloadService.deleteStory(storyId);
    } catch (e, stackTrace) {
      AppLogger.error('$_logPrefix: Error deleting story $storyId', e, stackTrace);
      return false;
    }
  }

  /// Check story status
  Future<StoryStatus> getStoryStatus(String storyId) async {
    try {
      // Check if downloaded
      final isDownloaded = await _downloadService.isStoryDownloaded(storyId);
      if (isDownloaded) {
        return StoryStatus.downloaded;
      }

      // Check if available in assets
      try {
        await rootBundle.loadString('assets/stories/$storyId/story.json');
        return StoryStatus.offline;
      } catch (e) {
        // Not in assets
      }

      // Check if available in Firebase
      final doc = await _firestore.collection('stories').doc(storyId).get();
      if (doc.exists) {
        final data = doc.data() as Map<String, dynamic>;
        final isPremium = data['is_premium'] as bool? ?? false;
        
        if (isPremium) {
          // TODO: Check user subscription status
          return StoryStatus.locked;
        }
        
        return StoryStatus.downloadable;
      }

      return StoryStatus.unknown;
    } catch (e) {
      AppLogger.warning('$_logPrefix: Error checking status for story $storyId: $e');
      return StoryStatus.unknown;
    }
  }

  /// Load stories from assets
  Future<List<StoryMetadata>> _loadAssetStories() async {
    try {
      AppLogger.debug('$_logPrefix: Loading asset stories');
      
      // Load stories.json manifest
      final manifestContent = await rootBundle.loadString('assets/stories/stories.json');
      final manifest = jsonDecode(manifestContent) as Map<String, dynamic>;
      final storiesData = manifest['stories'] as List<dynamic>;

      final stories = <StoryMetadata>[];
      
      for (final storyData in storiesData) {
        final storyMap = storyData as Map<String, dynamic>;
        final storyId = storyMap['id'] as String;
        final assetPath = 'assets/stories/$storyId';
        
        final metadata = StoryMetadata.fromAsset(storyId, storyMap, assetPath);
        stories.add(metadata);
      }

      AppLogger.info('$_logPrefix: Loaded ${stories.length} asset stories');
      return stories;
    } catch (e, stackTrace) {
      AppLogger.error('$_logPrefix: Error loading asset stories', e, stackTrace);
      return [];
    }
  }

  /// Load stories from Firebase
  Future<List<StoryMetadata>> _loadFirebaseStories() async {
    try {
      AppLogger.debug('$_logPrefix: Loading Firebase stories');
      
      final snapshot = await _firestore.collection('stories').get();
      final stories = <StoryMetadata>[];
      
      for (final doc in snapshot.docs) {
        final data = doc.data();
        final metadata = StoryMetadata.fromFirebase(doc.id, data);
        stories.add(metadata);
      }

      AppLogger.info('$_logPrefix: Loaded ${stories.length} Firebase stories');
      return stories;
    } catch (e, stackTrace) {
      AppLogger.error('$_logPrefix: Error loading Firebase stories', e, stackTrace);
      return [];
    }
  }


}

/// Provider for enhanced story repository
final enhancedStoryRepositoryProvider = Provider<EnhancedStoryRepository>((ref) {
  final downloadService = ref.watch(storyDownloadServiceProvider);
  return EnhancedStoryRepository(downloadService: downloadService);
});

/// Provider for all stories with metadata
final allStoriesProvider = FutureProvider<List<StoryMetadata>>((ref) async {
  final repository = ref.watch(enhancedStoryRepositoryProvider);
  return await repository.getAllStories();
});

/// Provider for story by ID
final storyByIdProvider = FutureProvider.family<EnhancedStoryModel?, String>((ref, storyId) async {
  final repository = ref.watch(enhancedStoryRepositoryProvider);
  return await repository.getStoryById(storyId);
});

/// Provider for story status
final storyStatusProvider = FutureProvider.family<StoryStatus, String>((ref, storyId) async {
  final repository = ref.watch(enhancedStoryRepositoryProvider);
  return await repository.getStoryStatus(storyId);
});
