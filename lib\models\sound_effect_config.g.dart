// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'sound_effect_config.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

SoundEffectConfig _$SoundEffectConfigFromJson(Map<String, dynamic> json) =>
    SoundEffectConfig(
      trigger: json['trigger'] as String,
      segmentIdRef: json['segmentIdRef'] as String?,
      sfxUrl: json['sfxUrl'] as String,
      volume: (json['volume'] as num?)?.toDouble() ?? 1.0,
    );

Map<String, dynamic> _$SoundEffectConfigToJson(SoundEffectConfig instance) =>
    <String, dynamic>{
      'trigger': instance.trigger,
      'segmentIdRef': instance.segmentIdRef,
      'sfxUrl': instance.sfxUrl,
      'volume': instance.volume,
    };
