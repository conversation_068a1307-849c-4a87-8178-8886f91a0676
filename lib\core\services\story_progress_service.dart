import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:choice_once_upon_a_time/core/utils/app_logger.dart';

/// Model for story progress data
class StoryProgress {
  final String storyId;
  final String currentSceneId;
  final List<String> visitedScenes;
  final Map<String, dynamic> choicesMade;
  final DateTime lastPlayedAt;
  final int totalPlayTime; // in seconds
  final bool isCompleted;
  final double progressPercentage;

  const StoryProgress({
    required this.storyId,
    required this.currentSceneId,
    required this.visitedScenes,
    required this.choicesMade,
    required this.lastPlayedAt,
    this.totalPlayTime = 0,
    this.isCompleted = false,
    this.progressPercentage = 0.0,
  });

  factory StoryProgress.fromJson(Map<String, dynamic> json) {
    return StoryProgress(
      storyId: json['storyId'] as String,
      currentSceneId: json['currentSceneId'] as String,
      visitedScenes: List<String>.from(json['visitedScenes'] as List),
      choicesMade: Map<String, dynamic>.from(json['choicesMade'] as Map),
      lastPlayedAt: DateTime.parse(json['lastPlayedAt'] as String),
      totalPlayTime: json['totalPlayTime'] as int? ?? 0,
      isCompleted: json['isCompleted'] as bool? ?? false,
      progressPercentage: (json['progressPercentage'] as num?)?.toDouble() ?? 0.0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'storyId': storyId,
      'currentSceneId': currentSceneId,
      'visitedScenes': visitedScenes,
      'choicesMade': choicesMade,
      'lastPlayedAt': lastPlayedAt.toIso8601String(),
      'totalPlayTime': totalPlayTime,
      'isCompleted': isCompleted,
      'progressPercentage': progressPercentage,
    };
  }

  StoryProgress copyWith({
    String? storyId,
    String? currentSceneId,
    List<String>? visitedScenes,
    Map<String, dynamic>? choicesMade,
    DateTime? lastPlayedAt,
    int? totalPlayTime,
    bool? isCompleted,
    double? progressPercentage,
  }) {
    return StoryProgress(
      storyId: storyId ?? this.storyId,
      currentSceneId: currentSceneId ?? this.currentSceneId,
      visitedScenes: visitedScenes ?? this.visitedScenes,
      choicesMade: choicesMade ?? this.choicesMade,
      lastPlayedAt: lastPlayedAt ?? this.lastPlayedAt,
      totalPlayTime: totalPlayTime ?? this.totalPlayTime,
      isCompleted: isCompleted ?? this.isCompleted,
      progressPercentage: progressPercentage ?? this.progressPercentage,
    );
  }
}

/// Service for tracking and persisting story progress
class StoryProgressService {
  static const String _progressKeyPrefix = 'story_progress_';
  static const String _allProgressKey = 'all_story_progress';

  /// Save story progress
  Future<void> saveProgress(StoryProgress progress) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final progressKey = '$_progressKeyPrefix${progress.storyId}';
      
      // Save individual progress
      await prefs.setString(progressKey, jsonEncode(progress.toJson()));
      
      // Update all progress list
      await _updateAllProgressList(progress.storyId);
      
      AppLogger.debug('[STORY_PROGRESS] Saved progress for story ${progress.storyId}: ${progress.progressPercentage.toStringAsFixed(1)}%');
    } catch (e) {
      AppLogger.debug('[STORY_PROGRESS] Error saving progress: $e');
    }
  }

  /// Load story progress
  Future<StoryProgress?> loadProgress(String storyId) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final progressKey = '$_progressKeyPrefix$storyId';
      final progressJson = prefs.getString(progressKey);
      
      if (progressJson != null) {
        final progressData = jsonDecode(progressJson) as Map<String, dynamic>;
        final progress = StoryProgress.fromJson(progressData);
        AppLogger.debug('[STORY_PROGRESS] Loaded progress for story $storyId: ${progress.progressPercentage.toStringAsFixed(1)}%');
        return progress;
      }
      
      AppLogger.debug('[STORY_PROGRESS] No progress found for story $storyId');
      return null;
    } catch (e) {
      AppLogger.debug('[STORY_PROGRESS] Error loading progress: $e');
      return null;
    }
  }

  /// Get all story progress
  Future<List<StoryProgress>> getAllProgress() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final allProgressJson = prefs.getString(_allProgressKey);
      
      if (allProgressJson != null) {
        final storyIds = List<String>.from(jsonDecode(allProgressJson) as List);
        final progressList = <StoryProgress>[];
        
        for (final storyId in storyIds) {
          final progress = await loadProgress(storyId);
          if (progress != null) {
            progressList.add(progress);
          }
        }
        
        // Sort by last played date (most recent first)
        progressList.sort((a, b) => b.lastPlayedAt.compareTo(a.lastPlayedAt));
        
        AppLogger.debug('[STORY_PROGRESS] Loaded ${progressList.length} story progress entries');
        return progressList;
      }
      
      return [];
    } catch (e) {
      AppLogger.debug('[STORY_PROGRESS] Error loading all progress: $e');
      return [];
    }
  }

  /// Delete story progress
  Future<void> deleteProgress(String storyId) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final progressKey = '$_progressKeyPrefix$storyId';
      
      // Remove individual progress
      await prefs.remove(progressKey);
      
      // Update all progress list
      await _removeFromAllProgressList(storyId);
      
      AppLogger.debug('[STORY_PROGRESS] Deleted progress for story $storyId');
    } catch (e) {
      AppLogger.debug('[STORY_PROGRESS] Error deleting progress: $e');
    }
  }

  /// Clear all progress
  Future<void> clearAllProgress() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final allProgress = await getAllProgress();
      
      // Remove all individual progress entries
      for (final progress in allProgress) {
        final progressKey = '$_progressKeyPrefix${progress.storyId}';
        await prefs.remove(progressKey);
      }
      
      // Clear the all progress list
      await prefs.remove(_allProgressKey);
      
      AppLogger.debug('[STORY_PROGRESS] Cleared all story progress');
    } catch (e) {
      AppLogger.debug('[STORY_PROGRESS] Error clearing all progress: $e');
    }
  }

  /// Check if story has progress
  Future<bool> hasProgress(String storyId) async {
    final progress = await loadProgress(storyId);
    return progress != null;
  }

  /// Get progress percentage for a story
  Future<double> getProgressPercentage(String storyId) async {
    final progress = await loadProgress(storyId);
    return progress?.progressPercentage ?? 0.0;
  }

  /// Check if story is completed
  Future<bool> isStoryCompleted(String storyId) async {
    final progress = await loadProgress(storyId);
    return progress?.isCompleted ?? false;
  }

  /// Get total play time for a story
  Future<int> getTotalPlayTime(String storyId) async {
    final progress = await loadProgress(storyId);
    return progress?.totalPlayTime ?? 0;
  }

  /// Update all progress list
  Future<void> _updateAllProgressList(String storyId) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final allProgressJson = prefs.getString(_allProgressKey);
      
      List<String> storyIds;
      if (allProgressJson != null) {
        storyIds = List<String>.from(jsonDecode(allProgressJson) as List);
      } else {
        storyIds = [];
      }
      
      if (!storyIds.contains(storyId)) {
        storyIds.add(storyId);
        await prefs.setString(_allProgressKey, jsonEncode(storyIds));
      }
    } catch (e) {
      AppLogger.debug('[STORY_PROGRESS] Error updating all progress list: $e');
    }
  }

  /// Remove from all progress list
  Future<void> _removeFromAllProgressList(String storyId) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final allProgressJson = prefs.getString(_allProgressKey);
      
      if (allProgressJson != null) {
        final storyIds = List<String>.from(jsonDecode(allProgressJson) as List);
        storyIds.remove(storyId);
        await prefs.setString(_allProgressKey, jsonEncode(storyIds));
      }
    } catch (e) {
      AppLogger.debug('[STORY_PROGRESS] Error removing from all progress list: $e');
    }
  }

  /// Calculate progress percentage based on visited scenes
  static double calculateProgressPercentage(List<String> visitedScenes, int totalScenes) {
    if (totalScenes == 0) return 0.0;
    return (visitedScenes.length / totalScenes * 100).clamp(0.0, 100.0);
  }

  /// Create initial progress for a story
  static StoryProgress createInitialProgress(String storyId, String firstSceneId) {
    return StoryProgress(
      storyId: storyId,
      currentSceneId: firstSceneId,
      visitedScenes: [firstSceneId],
      choicesMade: {},
      lastPlayedAt: DateTime.now(),
      totalPlayTime: 0,
      isCompleted: false,
      progressPercentage: 0.0,
    );
  }

  /// Update progress with new scene visit
  static StoryProgress updateProgressWithScene(
    StoryProgress currentProgress,
    String newSceneId,
    int totalScenes, {
    Map<String, dynamic>? newChoices,
    int? additionalPlayTime,
    bool? completed,
  }) {
    final updatedVisitedScenes = List<String>.from(currentProgress.visitedScenes);
    if (!updatedVisitedScenes.contains(newSceneId)) {
      updatedVisitedScenes.add(newSceneId);
    }

    final updatedChoices = Map<String, dynamic>.from(currentProgress.choicesMade);
    if (newChoices != null) {
      updatedChoices.addAll(newChoices);
    }

    final newProgressPercentage = calculateProgressPercentage(updatedVisitedScenes, totalScenes);
    final newTotalPlayTime = currentProgress.totalPlayTime + (additionalPlayTime ?? 0);

    return currentProgress.copyWith(
      currentSceneId: newSceneId,
      visitedScenes: updatedVisitedScenes,
      choicesMade: updatedChoices,
      lastPlayedAt: DateTime.now(),
      totalPlayTime: newTotalPlayTime,
      isCompleted: completed ?? (newProgressPercentage >= 100.0),
      progressPercentage: newProgressPercentage,
    );
  }
}
