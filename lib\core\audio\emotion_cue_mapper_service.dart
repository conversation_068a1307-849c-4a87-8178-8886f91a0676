import 'tts_service_interface.dart';

/// Service for mapping emotion cues to TTS speech parameters
/// This provides a centralized way to translate emotional context
/// into speech modulation parameters
class EmotionCueMapperService {
  /// Default speech parameters for neutral emotion
  static const TTSSpeechParameters _defaultParameters = TTSSpeechParameters(
    pitch: 1.0,
    rate: 0.5,
    volume: 1.0,
  );

  /// Map of emotion cues to speech parameter modifiers
  /// These values are multiplied with the base parameters
  static const Map<String, TTSSpeechParameters> _emotionMap = {
    // Positive emotions
    'cheerful': TTSSpeechParameters(pitch: 1.2, rate: 0.6, volume: 1.0),
    'happy': TTSSpeechParameters(pitch: 1.15, rate: 0.55, volume: 1.0),
    'excited': TTSSpeechParameters(pitch: 1.3, rate: 0.7, volume: 1.0),
    'joyful': TTSSpeechParameters(pitch: 1.25, rate: 0.6, volume: 1.0),
    'playful': TTSSpeechParameters(pitch: 1.2, rate: 0.65, volume: 1.0),
    'warm': TTSSpeechParameters(pitch: 1.05, rate: 0.45, volume: 0.9),
    'gentle': TTSSpeechParameters(pitch: 0.95, rate: 0.4, volume: 0.85),
    'kind': TTSSpeechParameters(pitch: 1.0, rate: 0.45, volume: 0.9),
    'loving': TTSSpeechParameters(pitch: 0.98, rate: 0.4, volume: 0.85),
    'encouraging': TTSSpeechParameters(pitch: 1.1, rate: 0.5, volume: 0.95),

    // Calm/neutral emotions
    'neutral': TTSSpeechParameters(pitch: 1.0, rate: 0.5, volume: 1.0),
    'calm': TTSSpeechParameters(pitch: 0.95, rate: 0.4, volume: 0.9),
    'peaceful': TTSSpeechParameters(pitch: 0.9, rate: 0.35, volume: 0.85),
    'serene': TTSSpeechParameters(pitch: 0.92, rate: 0.38, volume: 0.88),
    'thoughtful': TTSSpeechParameters(pitch: 0.95, rate: 0.4, volume: 0.9),
    'contemplative': TTSSpeechParameters(pitch: 0.9, rate: 0.35, volume: 0.85),

    // Sad/melancholy emotions
    'sad': TTSSpeechParameters(pitch: 0.8, rate: 0.3, volume: 0.8),
    'melancholy': TTSSpeechParameters(pitch: 0.85, rate: 0.35, volume: 0.8),
    'wistful': TTSSpeechParameters(pitch: 0.88, rate: 0.38, volume: 0.82),
    'regretful': TTSSpeechParameters(pitch: 0.82, rate: 0.32, volume: 0.78),
    'disappointed': TTSSpeechParameters(pitch: 0.85, rate: 0.35, volume: 0.8),

    // Reflective emotions
    'reflective': TTSSpeechParameters(pitch: 0.9, rate: 0.35, volume: 0.85),
    'wise': TTSSpeechParameters(pitch: 0.88, rate: 0.3, volume: 0.9),
    'understanding': TTSSpeechParameters(pitch: 0.95, rate: 0.4, volume: 0.9),
    'empathetic': TTSSpeechParameters(pitch: 0.92, rate: 0.38, volume: 0.88),

    // Surprised/curious emotions
    'surprised': TTSSpeechParameters(pitch: 1.3, rate: 0.6, volume: 1.0),
    'curious': TTSSpeechParameters(pitch: 1.1, rate: 0.55, volume: 0.95),
    'wondering': TTSSpeechParameters(pitch: 1.05, rate: 0.5, volume: 0.9),
    'intrigued': TTSSpeechParameters(pitch: 1.08, rate: 0.52, volume: 0.92),

    // Dramatic emotions
    'dramatic': TTSSpeechParameters(pitch: 1.2, rate: 0.45, volume: 1.0),
    'mysterious': TTSSpeechParameters(pitch: 0.85, rate: 0.3, volume: 0.9),
    'suspenseful': TTSSpeechParameters(pitch: 0.9, rate: 0.35, volume: 0.95),
    'ominous': TTSSpeechParameters(pitch: 0.75, rate: 0.25, volume: 0.9),

    // Urgent/concerned emotions
    'urgent': TTSSpeechParameters(pitch: 1.15, rate: 0.65, volume: 1.0),
    'concerned': TTSSpeechParameters(pitch: 1.05, rate: 0.45, volume: 0.95),
    'worried': TTSSpeechParameters(pitch: 1.1, rate: 0.5, volume: 0.9),
    'anxious': TTSSpeechParameters(pitch: 1.2, rate: 0.6, volume: 0.95),

    // Storytelling emotions
    'storytelling': TTSSpeechParameters(pitch: 1.0, rate: 0.45, volume: 0.95),
    'narrative': TTSSpeechParameters(pitch: 0.98, rate: 0.42, volume: 0.9),
    'descriptive': TTSSpeechParameters(pitch: 0.95, rate: 0.4, volume: 0.88),

    // Story-specific emotions from Pip's story
    'warmly': TTSSpeechParameters(pitch: 1.0, rate: 0.4, volume: 0.85),
    'softly': TTSSpeechParameters(pitch: 0.9, rate: 0.35, volume: 0.75),
    'invitingly': TTSSpeechParameters(pitch: 1.05, rate: 0.45, volume: 0.9),
    'descriptively': TTSSpeechParameters(pitch: 0.95, rate: 0.4, volume: 0.88),
    'affectionate': TTSSpeechParameters(pitch: 1.0, rate: 0.4, volume: 0.85),
    'fondly': TTSSpeechParameters(pitch: 0.98, rate: 0.4, volume: 0.85),
    'conspiratorially': TTSSpeechParameters(pitch: 0.9, rate: 0.35, volume: 0.7),
    'hopefully': TTSSpeechParameters(pitch: 1.05, rate: 0.45, volume: 0.9),
    'delightfully': TTSSpeechParameters(pitch: 1.15, rate: 0.5, volume: 0.9),
    'mysteriously': TTSSpeechParameters(pitch: 0.85, rate: 0.3, volume: 0.8),
    'empathetically': TTSSpeechParameters(pitch: 0.95, rate: 0.4, volume: 0.85),
    'urgently': TTSSpeechParameters(pitch: 1.1, rate: 0.6, volume: 0.95),
    'nervously': TTSSpeechParameters(pitch: 1.15, rate: 0.55, volume: 0.8),
    'relieved': TTSSpeechParameters(pitch: 1.0, rate: 0.45, volume: 0.9),
    'regretfully': TTSSpeechParameters(pitch: 0.85, rate: 0.35, volume: 0.8),
    'hesitantly': TTSSpeechParameters(pitch: 0.9, rate: 0.3, volume: 0.75),
    'questioningly': TTSSpeechParameters(pitch: 1.1, rate: 0.5, volume: 0.9),
    'validating': TTSSpeechParameters(pitch: 0.95, rate: 0.4, volume: 0.85),
    'reassuringly': TTSSpeechParameters(pitch: 1.0, rate: 0.4, volume: 0.85),
    'positively': TTSSpeechParameters(pitch: 1.05, rate: 0.45, volume: 0.9),
    'content': TTSSpeechParameters(pitch: 0.95, rate: 0.4, volume: 0.85),
    'sleepily': TTSSpeechParameters(pitch: 0.85, rate: 0.3, volume: 0.7),

    // Screen introduction specific emotions
    'warmly_welcoming': TTSSpeechParameters(pitch: 1.05, rate: 0.45, volume: 0.9),
    'warmly_familiar': TTSSpeechParameters(pitch: 1.0, rate: 0.4, volume: 0.85),
    'gently_encouraging': TTSSpeechParameters(pitch: 1.0, rate: 0.45, volume: 0.9),
    'playfully_inviting': TTSSpeechParameters(pitch: 1.15, rate: 0.5, volume: 0.95),
    'softly_magical': TTSSpeechParameters(pitch: 0.95, rate: 0.4, volume: 0.8),
    'gently_curious': TTSSpeechParameters(pitch: 1.05, rate: 0.5, volume: 0.9),
    'professionally_warm': TTSSpeechParameters(pitch: 1.0, rate: 0.45, volume: 0.9),
    'helpfully_informative': TTSSpeechParameters(pitch: 1.0, rate: 0.5, volume: 0.95),
    'invitingly_hopeful': TTSSpeechParameters(pitch: 1.1, rate: 0.5, volume: 0.95),
    'thoughtfully_sharing': TTSSpeechParameters(pitch: 0.95, rate: 0.4, volume: 0.9),
    'supportively_caring': TTSSpeechParameters(pitch: 0.98, rate: 0.42, volume: 0.88),
    'protectively_gentle': TTSSpeechParameters(pitch: 0.95, rate: 0.4, volume: 0.85),
    'sleepily_tender': TTSSpeechParameters(pitch: 0.85, rate: 0.3, volume: 0.7),

    // Parent zone specific emotions
    'calmly_informative': TTSSpeechParameters(pitch: 0.98, rate: 0.45, volume: 0.9),
    'clearly_explaining': TTSSpeechParameters(pitch: 1.0, rate: 0.42, volume: 0.95),
    'caringly_informative': TTSSpeechParameters(pitch: 0.95, rate: 0.4, volume: 0.88),
    'practically_helpful': TTSSpeechParameters(pitch: 1.0, rate: 0.48, volume: 0.92),
    'reassuringly_helpful': TTSSpeechParameters(pitch: 0.95, rate: 0.4, volume: 0.9),
    'encouragingly_helpful': TTSSpeechParameters(pitch: 1.05, rate: 0.45, volume: 0.92),
  };

  /// Get speech parameters for a given emotion cue
  /// Falls back to neutral if emotion cue is not recognized
  static TTSSpeechParameters getParametersForEmotion(String? emotionCue) {
    if (emotionCue == null || emotionCue.isEmpty) {
      return _defaultParameters;
    }

    // Normalize the emotion cue (lowercase, trim whitespace)
    final normalizedCue = emotionCue.toLowerCase().trim();
    
    // Try exact match first
    if (_emotionMap.containsKey(normalizedCue)) {
      return _emotionMap[normalizedCue]!;
    }

    // Try partial matches for compound emotion cues
    for (final entry in _emotionMap.entries) {
      if (normalizedCue.contains(entry.key) || entry.key.contains(normalizedCue)) {
        return entry.value;
      }
    }

    // Fall back to default parameters
    return _defaultParameters;
  }

  /// Get all available emotion cues
  static List<String> getAllEmotionCues() {
    return _emotionMap.keys.toList()..sort();
  }

  /// Check if an emotion cue is supported
  static bool isEmotionSupported(String emotionCue) {
    final normalizedCue = emotionCue.toLowerCase().trim();
    return _emotionMap.containsKey(normalizedCue);
  }

  /// Get a description of what an emotion cue does to speech
  static String getEmotionDescription(String emotionCue) {
    final params = getParametersForEmotion(emotionCue);
    final pitchDesc = params.pitch > 1.1 ? 'higher pitch' : 
                     params.pitch < 0.9 ? 'lower pitch' : 'normal pitch';
    final rateDesc = params.rate > 0.6 ? 'faster pace' : 
                    params.rate < 0.4 ? 'slower pace' : 'normal pace';
    final volumeDesc = params.volume > 0.95 ? 'full volume' : 
                      params.volume < 0.85 ? 'softer volume' : 'normal volume';
    
    return '$pitchDesc, $rateDesc, $volumeDesc';
  }

  /// Create custom speech parameters by blending multiple emotions
  /// Useful for complex emotional states like "sadly curious" or "gently excited"
  static TTSSpeechParameters blendEmotions(List<String> emotionCues, {
    List<double>? weights,
  }) {
    if (emotionCues.isEmpty) {
      return _defaultParameters;
    }

    // Use equal weights if not provided
    weights ??= List.filled(emotionCues.length, 1.0 / emotionCues.length);
    
    if (weights.length != emotionCues.length) {
      throw ArgumentError('Weights list must have same length as emotion cues list');
    }

    double totalPitch = 0.0;
    double totalRate = 0.0;
    double totalVolume = 0.0;
    double totalWeight = 0.0;

    for (int i = 0; i < emotionCues.length; i++) {
      final params = getParametersForEmotion(emotionCues[i]);
      final weight = weights[i];
      
      totalPitch += params.pitch * weight;
      totalRate += params.rate * weight;
      totalVolume += params.volume * weight;
      totalWeight += weight;
    }

    // Normalize by total weight
    return TTSSpeechParameters(
      pitch: totalPitch / totalWeight,
      rate: totalRate / totalWeight,
      volume: totalVolume / totalWeight,
    );
  }

  /// Apply age-appropriate modulation to speech parameters
  /// Younger children benefit from slightly slower, clearer speech
  static TTSSpeechParameters adjustForAge(TTSSpeechParameters params, String ageSegment) {
    switch (ageSegment) {
      case '3-5':
        return params.copyWith(
          rate: params.rate * 0.9, // 10% slower
          pitch: params.pitch * 1.05, // Slightly higher pitch
        );
      case '4-6':
        return params.copyWith(
          rate: params.rate * 0.95, // 5% slower
        );
      case '5-7':
      case '6-8':
        return params; // No adjustment needed
      default:
        return params;
    }
  }
}
