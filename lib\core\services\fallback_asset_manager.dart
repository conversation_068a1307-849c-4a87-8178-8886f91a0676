import 'dart:io';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:path/path.dart' as path;
import 'package:choice_once_upon_a_time/core/utils/app_logger.dart';

/// Service for managing fallback assets when story assets are missing
/// Implements the requirement that missing images use default_image.png
/// and missing audio files use default_happy.mp3
class FallbackAssetManager {
  static const String _logPrefix = 'FALLBACK_ASSET';
  
  // Default fallback assets as specified in requirements
  static const String defaultImagePath = 'assets/default/default_image.png';
  static const String defaultAudioPath = 'assets/default/default_happy.mp3';
  
  // Cache for asset existence checks to improve performance
  final Map<String, bool> _assetExistenceCache = {};
  final Map<String, String> _fallbackCache = {};

  /// Get image asset path with fallback to default_image.png
  /// Handles both local assets and downloaded story files
  Future<String> getImageAssetPath(String originalPath, {String? storyId}) async {
    try {
      AppLogger.debug('$_logPrefix: Checking image asset: $originalPath');
      
      // Check cache first
      final cacheKey = 'image_$originalPath';
      if (_fallbackCache.containsKey(cacheKey)) {
        return _fallbackCache[cacheKey]!;
      }
      
      // Check if original asset exists
      final exists = await _checkAssetExists(originalPath);
      
      if (exists) {
        _fallbackCache[cacheKey] = originalPath;
        AppLogger.debug('$_logPrefix: Image asset found: $originalPath');
        return originalPath;
      } else {
        _fallbackCache[cacheKey] = defaultImagePath;
        AppLogger.warning('$_logPrefix: Image asset not found, using fallback: $originalPath -> $defaultImagePath');
        return defaultImagePath;
      }
      
    } catch (e, stackTrace) {
      AppLogger.error('$_logPrefix: Error checking image asset: $originalPath', e, stackTrace);
      return defaultImagePath;
    }
  }

  /// Get audio asset path with fallback to default_happy.mp3
  /// Handles both local assets and downloaded story files
  Future<String> getAudioAssetPath(String originalPath, {String? storyId}) async {
    try {
      AppLogger.debug('$_logPrefix: Checking audio asset: $originalPath');
      
      // Check cache first
      final cacheKey = 'audio_$originalPath';
      if (_fallbackCache.containsKey(cacheKey)) {
        return _fallbackCache[cacheKey]!;
      }
      
      // Check if original asset exists
      final exists = await _checkAssetExists(originalPath);
      
      if (exists) {
        _fallbackCache[cacheKey] = originalPath;
        AppLogger.debug('$_logPrefix: Audio asset found: $originalPath');
        return originalPath;
      } else {
        _fallbackCache[cacheKey] = defaultAudioPath;
        AppLogger.warning('$_logPrefix: Audio asset not found, using fallback: $originalPath -> $defaultAudioPath');
        return defaultAudioPath;
      }
      
    } catch (e, stackTrace) {
      AppLogger.error('$_logPrefix: Error checking audio asset: $originalPath', e, stackTrace);
      return defaultAudioPath;
    }
  }

  /// Get any asset path with appropriate fallback based on file extension
  Future<String> getAssetPath(String originalPath, {String? storyId}) async {
    final extension = path.extension(originalPath).toLowerCase();
    
    if (_isImageExtension(extension)) {
      return await getImageAssetPath(originalPath, storyId: storyId);
    } else if (_isAudioExtension(extension)) {
      return await getAudioAssetPath(originalPath, storyId: storyId);
    } else {
      // For other file types, just check existence and return original or null
      final exists = await _checkAssetExists(originalPath);
      if (exists) {
        return originalPath;
      } else {
        AppLogger.warning('$_logPrefix: Unknown asset type not found: $originalPath');
        return originalPath; // Return original path even if not found
      }
    }
  }

  /// Check if an asset exists (handles both bundle assets and file system)
  Future<bool> _checkAssetExists(String assetPath) async {
    try {
      // Check cache first
      if (_assetExistenceCache.containsKey(assetPath)) {
        return _assetExistenceCache[assetPath]!;
      }
      
      bool exists = false;
      
      if (assetPath.startsWith('assets/')) {
        // Bundle asset - check using rootBundle
        try {
          await rootBundle.load(assetPath);
          exists = true;
        } catch (e) {
          exists = false;
        }
      } else {
        // File system asset - check if file exists
        final file = File(assetPath);
        exists = await file.exists();
      }
      
      // Cache the result
      _assetExistenceCache[assetPath] = exists;
      
      return exists;
      
    } catch (e) {
      AppLogger.warning('$_logPrefix: Error checking asset existence: $assetPath - $e');
      return false;
    }
  }

  /// Check if file extension is for images
  bool _isImageExtension(String extension) {
    const imageExtensions = {'.jpg', '.jpeg', '.png', '.gif', '.webp', '.bmp', '.svg'};
    return imageExtensions.contains(extension);
  }

  /// Check if file extension is for audio
  bool _isAudioExtension(String extension) {
    const audioExtensions = {'.mp3', '.wav', '.m4a', '.aac', '.ogg', '.flac'};
    return audioExtensions.contains(extension);
  }

  /// Preload and validate default fallback assets
  Future<bool> validateDefaultAssets() async {
    try {
      AppLogger.info('$_logPrefix: Validating default fallback assets');
      
      // Check default image
      final imageExists = await _checkAssetExists(defaultImagePath);
      if (!imageExists) {
        AppLogger.error('$_logPrefix: Default image asset not found: $defaultImagePath');
        return false;
      }
      
      // Check default audio
      final audioExists = await _checkAssetExists(defaultAudioPath);
      if (!audioExists) {
        AppLogger.error('$_logPrefix: Default audio asset not found: $defaultAudioPath');
        return false;
      }
      
      AppLogger.info('$_logPrefix: Default fallback assets validated successfully');
      return true;
      
    } catch (e, stackTrace) {
      AppLogger.error('$_logPrefix: Error validating default assets', e, stackTrace);
      return false;
    }
  }

  /// Clear asset caches (useful for testing or memory management)
  void clearCaches() {
    _assetExistenceCache.clear();
    _fallbackCache.clear();
    AppLogger.debug('$_logPrefix: Asset caches cleared');
  }

  /// Get cache statistics for debugging
  Map<String, dynamic> getCacheStats() {
    return {
      'existence_cache_size': _assetExistenceCache.length,
      'fallback_cache_size': _fallbackCache.length,
      'existence_cache': Map.from(_assetExistenceCache),
      'fallback_cache': Map.from(_fallbackCache),
    };
  }

  /// Preload commonly used assets to improve performance
  Future<void> preloadCommonAssets(List<String> assetPaths) async {
    try {
      AppLogger.info('$_logPrefix: Preloading ${assetPaths.length} common assets');
      
      for (final assetPath in assetPaths) {
        await _checkAssetExists(assetPath);
      }
      
      AppLogger.info('$_logPrefix: Preloading completed');
      
    } catch (e, stackTrace) {
      AppLogger.error('$_logPrefix: Error preloading assets', e, stackTrace);
    }
  }

  /// Get fallback asset for specific story context
  Future<String> getStoryAssetWithFallback({
    required String storyId,
    required String assetPath,
    String? assetType,
  }) async {
    try {
      // First try the specific story asset path
      final storySpecificPath = 'assets/stories/$storyId/$assetPath';
      final exists = await _checkAssetExists(storySpecificPath);
      
      if (exists) {
        AppLogger.debug('$_logPrefix: Story-specific asset found: $storySpecificPath');
        return storySpecificPath;
      }
      
      // Fall back to generic asset path
      final genericPath = 'assets/$assetPath';
      final genericExists = await _checkAssetExists(genericPath);
      
      if (genericExists) {
        AppLogger.debug('$_logPrefix: Generic asset found: $genericPath');
        return genericPath;
      }
      
      // Use appropriate default fallback
      final extension = path.extension(assetPath).toLowerCase();
      if (_isImageExtension(extension)) {
        AppLogger.warning('$_logPrefix: Using default image fallback for: $assetPath');
        return defaultImagePath;
      } else if (_isAudioExtension(extension)) {
        AppLogger.warning('$_logPrefix: Using default audio fallback for: $assetPath');
        return defaultAudioPath;
      } else {
        AppLogger.warning('$_logPrefix: No fallback available for: $assetPath');
        return assetPath; // Return original path
      }
      
    } catch (e, stackTrace) {
      AppLogger.error('$_logPrefix: Error getting story asset with fallback', e, stackTrace);
      
      // Return appropriate default based on asset type
      final extension = path.extension(assetPath).toLowerCase();
      if (_isImageExtension(extension)) {
        return defaultImagePath;
      } else if (_isAudioExtension(extension)) {
        return defaultAudioPath;
      } else {
        return assetPath;
      }
    }
  }

  /// Batch check multiple assets and return their resolved paths
  Future<Map<String, String>> batchResolveAssets(List<String> assetPaths) async {
    final results = <String, String>{};
    
    try {
      AppLogger.debug('$_logPrefix: Batch resolving ${assetPaths.length} assets');
      
      for (final assetPath in assetPaths) {
        results[assetPath] = await getAssetPath(assetPath);
      }
      
      AppLogger.debug('$_logPrefix: Batch resolution completed');
      
    } catch (e, stackTrace) {
      AppLogger.error('$_logPrefix: Error in batch asset resolution', e, stackTrace);
    }
    
    return results;
  }

  /// Check if asset needs fallback (for UI indicators)
  Future<bool> needsFallback(String assetPath) async {
    try {
      final exists = await _checkAssetExists(assetPath);
      return !exists;
    } catch (e) {
      return true; // Assume fallback needed if check fails
    }
  }

  /// Get asset metadata including fallback status
  Future<AssetMetadata> getAssetMetadata(String assetPath) async {
    try {
      final exists = await _checkAssetExists(assetPath);
      final extension = path.extension(assetPath).toLowerCase();
      final assetType = _getAssetType(extension);
      final fallbackPath = exists ? assetPath : _getFallbackPath(extension);
      
      return AssetMetadata(
        originalPath: assetPath,
        resolvedPath: fallbackPath,
        exists: exists,
        assetType: assetType,
        needsFallback: !exists,
      );
      
    } catch (e, stackTrace) {
      AppLogger.error('$_logPrefix: Error getting asset metadata', e, stackTrace);
      
      final extension = path.extension(assetPath).toLowerCase();
      return AssetMetadata(
        originalPath: assetPath,
        resolvedPath: _getFallbackPath(extension),
        exists: false,
        assetType: _getAssetType(extension),
        needsFallback: true,
      );
    }
  }

  /// Get asset type from extension
  String _getAssetType(String extension) {
    if (_isImageExtension(extension)) {
      return 'image';
    } else if (_isAudioExtension(extension)) {
      return 'audio';
    } else {
      return 'unknown';
    }
  }

  /// Get fallback path for extension
  String _getFallbackPath(String extension) {
    if (_isImageExtension(extension)) {
      return defaultImagePath;
    } else if (_isAudioExtension(extension)) {
      return defaultAudioPath;
    } else {
      return '';
    }
  }
}

/// Metadata about an asset including fallback information
class AssetMetadata {
  final String originalPath;
  final String resolvedPath;
  final bool exists;
  final String assetType;
  final bool needsFallback;

  const AssetMetadata({
    required this.originalPath,
    required this.resolvedPath,
    required this.exists,
    required this.assetType,
    required this.needsFallback,
  });

  @override
  String toString() {
    return 'AssetMetadata(original: $originalPath, resolved: $resolvedPath, exists: $exists, type: $assetType, needsFallback: $needsFallback)';
  }
}

/// Provider for fallback asset manager
final fallbackAssetManagerProvider = Provider<FallbackAssetManager>((ref) {
  return FallbackAssetManager();
});

/// Provider for asset metadata
final assetMetadataProvider = FutureProvider.family<AssetMetadata, String>((ref, assetPath) async {
  final manager = ref.watch(fallbackAssetManagerProvider);
  return await manager.getAssetMetadata(assetPath);
});
