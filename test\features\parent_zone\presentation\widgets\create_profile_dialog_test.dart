import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:choice_once_upon_a_time/features/parent_zone/presentation/widgets/create_profile_dialog.dart';
import 'package:choice_once_upon_a_time/features/parent_zone/data/user_profile_service.dart';

void main() {
  group('CreateProfileDialog', () {
    late UserProfile? createdProfile;

    setUp(() {
      createdProfile = null;
    });

    Widget createWidget() {
      return MaterialApp(
        home: Scaffold(
          body: CreateProfileDialog(
            onProfileCreated: (profile) {
              createdProfile = profile;
            },
          ),
        ),
      );
    }

    testWidgets('displays all required fields', (tester) async {
      await tester.pumpWidget(createWidget());

      expect(find.text('Create New Profile'), findsOneWidget);
      expect(find.text('Child\'s Name'), findsOneWidget);
      expect(find.text('Age:'), findsOneWidget);
      expect(find.text('Avatar Color:'), findsOneWidget);
      expect(find.text('Cancel'), findsOneWidget);
      expect(find.text('Create Profile'), findsOneWidget);
    });

    testWidgets('name field accepts text input', (tester) async {
      await tester.pumpWidget(createWidget());

      final nameField = find.byType(TextField);
      await tester.enterText(nameField, 'Alice');
      await tester.pump();

      expect(find.text('Alice'), findsOneWidget);
    });

    testWidgets('age dropdown shows correct options', (tester) async {
      await tester.pumpWidget(createWidget());

      // Tap age dropdown
      await tester.tap(find.byType(DropdownButtonFormField<int>));
      await tester.pumpAndSettle();

      // Check age options (3-12 years)
      expect(find.text('3 years old'), findsOneWidget);
      expect(find.text('5 years old'), findsOneWidget);
      expect(find.text('12 years old'), findsOneWidget);
    });

    testWidgets('can select different age', (tester) async {
      await tester.pumpWidget(createWidget());

      // Tap age dropdown
      await tester.tap(find.byType(DropdownButtonFormField<int>));
      await tester.pumpAndSettle();

      // Select age 7
      await tester.tap(find.text('7 years old').last);
      await tester.pumpAndSettle();

      // Verify selection
      expect(find.text('7 years old'), findsOneWidget);
    });

    testWidgets('displays avatar color options', (tester) async {
      await tester.pumpWidget(createWidget());

      // Find color selection containers
      final colorContainers = find.byType(GestureDetector);
      expect(colorContainers, findsAtLeast(8)); // Should have 8 color options
    });

    testWidgets('can select different avatar color', (tester) async {
      await tester.pumpWidget(createWidget());

      // Find color containers
      final colorContainers = find.byType(GestureDetector);
      
      // Tap second color option
      await tester.tap(colorContainers.at(1));
      await tester.pump();

      // Should show check mark on selected color
      expect(find.byIcon(Icons.check), findsOneWidget);
    });

    testWidgets('preview updates with entered information', (tester) async {
      await tester.pumpWidget(createWidget());

      // Enter name
      await tester.enterText(find.byType(TextField), 'Alice');
      await tester.pump();

      // Change age
      await tester.tap(find.byType(DropdownButtonFormField<int>));
      await tester.pumpAndSettle();
      await tester.tap(find.text('7 years old').last);
      await tester.pumpAndSettle();

      // Check preview shows updated information
      expect(find.text('Alice'), findsAtLeast(1)); // In preview
      expect(find.text('7 years old'), findsAtLeast(1)); // In preview
    });

    testWidgets('create button is disabled when name is empty', (tester) async {
      await tester.pumpWidget(createWidget());

      final createButton = find.text('Create Profile');
      final button = tester.widget<ElevatedButton>(
        find.ancestor(
          of: createButton,
          matching: find.byType(ElevatedButton),
        ),
      );

      expect(button.onPressed, isNull); // Button should be disabled
    });

    testWidgets('create button is enabled when name is entered', (tester) async {
      await tester.pumpWidget(createWidget());

      // Enter name
      await tester.enterText(find.byType(TextField), 'Alice');
      await tester.pump();

      final createButton = find.text('Create Profile');
      final button = tester.widget<ElevatedButton>(
        find.ancestor(
          of: createButton,
          matching: find.byType(ElevatedButton),
        ),
      );

      expect(button.onPressed, isNotNull); // Button should be enabled
    });

    testWidgets('creates profile with correct data when submitted', (tester) async {
      await tester.pumpWidget(createWidget());

      // Enter profile data
      await tester.enterText(find.byType(TextField), 'Alice');
      await tester.pump();

      // Change age to 7
      await tester.tap(find.byType(DropdownButtonFormField<int>));
      await tester.pumpAndSettle();
      await tester.tap(find.text('7 years old').last);
      await tester.pumpAndSettle();

      // Submit form
      await tester.tap(find.text('Create Profile'));
      await tester.pump();

      // Verify profile was created with correct data
      expect(createdProfile, isNotNull);
      expect(createdProfile!.name, equals('Alice'));
      expect(createdProfile!.age, equals(7));
      expect(createdProfile!.favoriteStories, isEmpty);
      expect(createdProfile!.totalReadingTime, equals(0));
      expect(createdProfile!.storiesCompleted, equals(0));
    });

    testWidgets('cancel button closes dialog without creating profile', (tester) async {
      await tester.pumpWidget(createWidget());

      // Enter some data
      await tester.enterText(find.byType(TextField), 'Alice');
      await tester.pump();

      // Tap cancel
      await tester.tap(find.text('Cancel'));
      await tester.pump();

      // Verify no profile was created
      expect(createdProfile, isNull);
    });

    testWidgets('dialog is scrollable for small screens', (tester) async {
      // Set small screen size
      await tester.binding.setSurfaceSize(const Size(300, 400));
      
      await tester.pumpWidget(createWidget());

      // Verify SingleChildScrollView is present
      expect(find.byType(SingleChildScrollView), findsOneWidget);

      // Reset surface size
      await tester.binding.setSurfaceSize(null);
    });

    testWidgets('preview shows placeholder when name is empty', (tester) async {
      await tester.pumpWidget(createWidget());

      // Check preview shows placeholder
      expect(find.text('Child\'s Name'), findsAtLeast(1)); // Placeholder in preview
    });

    testWidgets('generates unique ID for each profile', (tester) async {
      UserProfile? firstProfile;
      UserProfile? secondProfile;

      // Create first profile
      await tester.pumpWidget(MaterialApp(
        home: Scaffold(
          body: CreateProfileDialog(
            onProfileCreated: (profile) {
              firstProfile = profile;
            },
          ),
        ),
      ));

      await tester.enterText(find.byType(TextField), 'Alice');
      await tester.pump();
      await tester.tap(find.text('Create Profile'));
      await tester.pump();

      // Create second profile
      await tester.pumpWidget(MaterialApp(
        home: Scaffold(
          body: CreateProfileDialog(
            onProfileCreated: (profile) {
              secondProfile = profile;
            },
          ),
        ),
      ));

      await tester.enterText(find.byType(TextField), 'Bob');
      await tester.pump();
      await tester.tap(find.text('Create Profile'));
      await tester.pump();

      // Verify unique IDs
      expect(firstProfile, isNotNull);
      expect(secondProfile, isNotNull);
      expect(firstProfile!.id, isNot(equals(secondProfile!.id)));
    });

    testWidgets('sets creation and last active timestamps', (tester) async {
      final beforeCreation = DateTime.now();
      
      await tester.pumpWidget(createWidget());

      await tester.enterText(find.byType(TextField), 'Alice');
      await tester.pump();
      await tester.tap(find.text('Create Profile'));
      await tester.pump();

      final afterCreation = DateTime.now();

      expect(createdProfile, isNotNull);
      expect(createdProfile!.createdAt.isAfter(beforeCreation.subtract(const Duration(seconds: 1))), isTrue);
      expect(createdProfile!.createdAt.isBefore(afterCreation.add(const Duration(seconds: 1))), isTrue);
      expect(createdProfile!.lastActiveAt.isAfter(beforeCreation.subtract(const Duration(seconds: 1))), isTrue);
      expect(createdProfile!.lastActiveAt.isBefore(afterCreation.add(const Duration(seconds: 1))), isTrue);
    });
  });
}
