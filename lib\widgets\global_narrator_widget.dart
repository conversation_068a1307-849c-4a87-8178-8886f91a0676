import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:choice_once_upon_a_time/widgets/global_narrator_controller.dart';

class GlobalNarratorWidget extends ConsumerWidget {
  const GlobalNarratorWidget({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final narratorState = ref.watch(globalNarratorProvider);
    final narratorController = ref.read(globalNarratorProvider.notifier);
    final theme = Theme.of(context);
    final screenSize = MediaQuery.of(context).size;
    final isSmallScreen = screenSize.width < 600;

    if (narratorState.currentText.isEmpty) {
      return const SizedBox.shrink();
    }

    return Material(
      color: theme.colorScheme.secondary.withValues(alpha: 0.95),
      elevation: 4,
      child: SafeArea(
        child: Padding(
          padding: EdgeInsets.symmetric(
            horizontal: screenSize.width * 0.04, // 4% of screen width
            vertical: isSmallScreen ? 6.0 : 8.0,
          ),
          child: Row(
            children: [
              // Play/Pause button with responsive sizing
              Material(
                color: Colors.transparent,
                child: InkWell(
                  onTap: () {
                    if (narratorState.isPlaying) {
                      narratorController.pauseNarration();
                    } else {
                      narratorController.resumeNarration();
                    }
                  },
                  borderRadius: BorderRadius.circular(22),
                  child: Container(
                    width: isSmallScreen ? 36 : 44,
                    height: isSmallScreen ? 36 : 44,
                    alignment: Alignment.center,
                    child: Icon(
                      narratorState.isPlaying ? Icons.pause : Icons.play_arrow,
                      color: theme.colorScheme.onSecondary,
                      size: isSmallScreen ? 20 : 24,
                    ),
                  ),
                ),
              ),

              // Narration text with responsive typography
              Expanded(
                child: Padding(
                  padding: EdgeInsets.symmetric(horizontal: isSmallScreen ? 8.0 : 12.0),
                  child: Text(
                    narratorState.currentText,
                    style: theme.textTheme.bodyMedium?.copyWith(
                      color: theme.colorScheme.onSecondary,
                      fontSize: isSmallScreen ? 13 : 14,
                      fontWeight: FontWeight.w500,
                    ),
                    textAlign: TextAlign.center,
                    maxLines: isSmallScreen ? 2 : 3,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ),

              // Stop button with responsive sizing
              Material(
                color: Colors.transparent,
                child: InkWell(
                  onTap: () {
                    narratorController.stopNarration();
                  },
                  borderRadius: BorderRadius.circular(22),
                  child: Container(
                    width: isSmallScreen ? 36 : 44,
                    height: isSmallScreen ? 36 : 44,
                    alignment: Alignment.center,
                    child: Icon(
                      Icons.stop,
                      color: theme.colorScheme.onSecondary,
                      size: isSmallScreen ? 20 : 24,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
