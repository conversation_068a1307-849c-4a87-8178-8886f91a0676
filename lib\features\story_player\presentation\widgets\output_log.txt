import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:choice_once_upon_a_time/models/enhanced_story_model.dart';
import 'package:choice_once_upon_a_time/models/narration_models.dart';
import 'package:choice_once_upon_a_time/core/services/story_narration_service_interface.dart';
import 'package:choice_once_upon_a_time/core/services/story_settings_service.dart';
import 'package:choice_once_upon_a_time/core/utils/app_logger.dart';
import 'dart:async';

/// New story playback widget built to display scene images and provide TTS narration with word-level highlighting
class StoryPlaybackWidget extends StatefulWidget {
  final EnhancedStoryModel story;
  final EnhancedSceneModel scene;
  final Function(ChoiceOptionModel) onChoiceSelected;
  final VoidCallback? onSceneComplete;
  final IStoryNarrationService narrationService;
  final StorySettingsService settingsService;

  const StoryPlaybackWidget({
    super.key,
    required this.story,
    required this.scene,
    required this.onChoiceSelected,
    required this.narrationService,
    required this.settingsService,
    this.onSceneComplete,
  });

  @override
  State<StoryPlaybackWidget> createState() => _StoryPlaybackWidgetState();
}

class _StoryPlaybackWidgetState extends State<StoryPlaybackWidget> with TickerProviderStateMixin {
  // Image loading state
  bool _imageLoaded = false;
  bool _imageLoading = false;
  String? _imageError;
  ImageProvider? _imageProvider;
  DateTime? _imageDisplayStartTime;

  // Narration state
  bool _isNarrating = false;
  bool _isPaused = false;
  bool _hasCompleted = false;
  List<String> _sentences = [];
  List<List<String>> _sentenceWords = [];
  int _currentSentenceIndex = 0;
  int _currentWordIndex = 0;
  bool _showTTSConsent = false; // Skipped for debugging

  // UI state
  bool _showChoices = false;
  bool _showControls = true;

  // Subscriptions
  StreamSubscription? _progressSubscription;
  StreamSubscription? _narrationStateSubscription;

  // Animation controllers
  late AnimationController _fadeController;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    AppLogger.debug('[STORY_PLAYER] Function: initState called');
    super.initState();
    AppLogger.debug('[STORY_PLAYER] StoryPlaybackWidget initState - scene: ${widget.scene.id}, story: ${widget.story.storyId}');
    _initializeAnimations();
    _processTextForNarration();
    _setupNarrationListeners();
    // Start narration automatically for debugging
    Future.delayed(const Duration(milliseconds: 500), () {
      if (mounted) {
        _startNarration();
      }
    });
  }

  @override
  void didChangeDependencies() {
    AppLogger.debug('[STORY_PLAYER] Function: didChangeDependencies called');
    super.didChangeDependencies();
    if (!_imageLoaded && !_imageLoading) {
      AppLogger.debug('[ABC] Call loading scene image in didChangeDependencies');
      _loadSceneImage();
    }
  }

  void _initializeAnimations() {
    AppLogger.debug('[STORY_PLAYER] Function: _initializeAnimations called');
    AppLogger.debug('[STORY_PLAYER] Initializing animations');
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _fadeController,
      curve: Curves.easeInOut,
    ));
  }

  Future<void> _loadSceneImage() async {
    AppLogger.debug('[STORY_PLAYER] Function: _loadSceneImage called');
    if (_imageLoading || _imageLoaded) return;

    setState(() {
      _imageLoading = true;
      _imageError = null;
    });

    final imagePath = widget.scene.getImagePath(widget.story.storyId);
    AppLogger.debug('[IMAGE_LOAD] Asset: $imagePath | Location: lib/features/story_player/presentation/widgets/story_playback_widget.dart | Widget: StoryPlaybackWidget');
    AppLogger.debug('[STORY_PLAYER] Loading scene image: $imagePath');

    try {
      AppLogger.debug('[STORY_PLAYER] Verifying asset existence: $imagePath');
      await DefaultAssetBundle.of(context).load(imagePath);
      AppLogger.debug('[STORY_PLAYER] Asset verified: $imagePath');

      _imageProvider = AssetImage(imagePath);
      AppLogger.debug('[IMAGE_LOAD] Asset: $imagePath | Location: lib/features/story_player/presentation/widgets/story_playback_widget.dart | Widget: AssetImage');

      AppLogger.debug('[STORY_PLAYER] Precaching image: $imagePath');
      await precacheImage(_imageProvider!, context);
      AppLogger.debug('[IMAGE_LOAD] Asset: $imagePath | Location: lib/features/story_player/presentation/widgets/story_playback_widget.dart | Widget: precacheImage');

      if (mounted) {
        setState(() {
          _imageLoaded = true;
          _imageLoading = false;
          _imageDisplayStartTime = DateTime.now();
        });

        AppLogger.debug('[SCENE_DEBUG] Scene ${widget.scene.id} loaded - Image: $imagePath');
        _fadeController.forward();
      }
    } catch (error) {
      AppLogger.error('[STORY_PLAYER] Failed to load scene image: $imagePath', error);
      if (mounted) {
        setState(() {
          _imageError = 'Failed to load image: $error';
          _imageLoading = false;
        });
      }
    }
  }

  void _processTextForNarration() {
    AppLogger.debug('[STORY_PLAYER] Function: _processTextForNarration called');
    AppLogger.debug('[STORY_PLAYER] Processing text for narration: "${widget.scene.text.substring(0, widget.scene.text.length > 50 ? 50 : widget.scene.text.length)}${widget.scene.text.length > 50 ? '...' : ''}"');

    _sentences = widget.scene.text
        .split(RegExp(r'[.!?]+'))
        .map((s) => s.trim())
        .where((s) => s.isNotEmpty)
        .toList();

    if (_sentences.isEmpty) {
      _sentences = [widget.scene.text];
    }

    _sentenceWords = _sentences.map((sentence) {
      return sentence.split(RegExp(r'\s+')).where((word) => word.isNotEmpty).toList();
    }).toList();

    AppLogger.debug('[SCENE_DEBUG] Processed ${_sentences.length} sentences with ${_sentenceWords.fold(0, (sum, words) => sum + words.length)} total words');
  }

  void _setupNarrationListeners() {
    AppLogger.debug('[STORY_PLAYER] Function: _setupNarrationListeners called');
    AppLogger.debug('[STORY_PLAYER] Setting up narration listeners');

    _progressSubscription?.cancel();
    _narrationStateSubscription?.cancel();

    _progressSubscription = widget.narrationService.progressStream.listen((progress) {
      if (mounted) {
        setState(() {
          _currentSentenceIndex = (progress.completedSentences).clamp(0, _sentences.length - 1);
          _currentWordIndex = (_currentWordIndex + 1) %
              (_sentenceWords.isNotEmpty && _currentSentenceIndex < _sentenceWords.length
                  ? _sentenceWords[_currentSentenceIndex].length + 1
                  : 1);
        });
      }
    });

    _narrationStateSubscription = widget.narrationService.stateStream.listen((state) {
      if (mounted) {
        setState(() {
          _isNarrating = state.status == NarrationStatus.playing;
          _isPaused = state.status == NarrationStatus.paused;
          if (state.status == NarrationStatus.completed) {
            _hasCompleted = true;
            _currentWordIndex = 0;
            _onNarrationCompleted();
          }
        });
      }
    });
  }

  Future<void> _startNarration() async {
    AppLogger.debug('[STORY_PLAYER] Function: _startNarration called');
    if (_isNarrating || _hasCompleted) return;

    AppLogger.debug('[STORY_PLAYER] Starting narration for scene: ${widget.scene.id}');

    setState(() {
      _isNarrating = true;
      _isPaused = false;
      _currentSentenceIndex = 0;
      _currentWordIndex = 0;
      _showTTSConsent = false;
    });

    try {
      await widget.narrationService.narrateScene(widget.scene);
    } catch (error) {
      AppLogger.error('[STORY_PLAYER] Narration error', error);
      await _stopNarration();
    }
  }

  Future<void> _pauseNarration() async {
    AppLogger.debug('[STORY_PLAYER] Function: _pauseNarration called');
    if (!_isNarrating || _isPaused) return;

    AppLogger.debug('[STORY_PLAYER] Pausing narration');
    setState(() {
      _isPaused = true;
    });
    await widget.narrationService.pause();
  }

  Future<void> _resumeNarration() async {
    AppLogger.debug('[STORY_PLAYER] Function: _resumeNarration called');
    if (!_isNarrating || !_isPaused) return;

    AppLogger.debug('[STORY_PLAYER] Resuming narration');
    setState(() {
      _isPaused = false;
    });
    await widget.narrationService.play();
  }

  Future<void> _stopNarration() async {
    AppLogger.debug('[STORY_PLAYER] Function: _stopNarration called');
    AppLogger.debug('[STORY_PLAYER] Stopping narration');
    setState(() {
      _isNarrating = false;
      _isPaused = false;
    });
    await widget.narrationService.stop();
  }

  void _onNarrationCompleted() async {
    AppLogger.debug('[STORY_PLAYER] Function: _onNarrationCompleted called');
    AppLogger.debug('[SCENE_DEBUG] Narration completed successfully');

    await _ensureMinimumImageDisplayTime();

    if (widget.scene.choices?.isNotEmpty ?? false) {
      setState(() {
        _showChoices = true;
      });
    } else {
      AppLogger.debug('[STORY_PLAYER] Autoplay disabled - manual progression required');
    }
  }

  Future<void> _ensureMinimumImageDisplayTime() async {
    AppLogger.debug('[STORY_PLAYER] Function: _ensureMinimumImageDisplayTime called');
    if (_imageDisplayStartTime != null) {
      final elapsed = DateTime.now().difference(_imageDisplayStartTime!);
      const minimumDisplayTime = Duration(seconds: 5);

      if (elapsed < minimumDisplayTime) {
        final remainingTime = minimumDisplayTime - elapsed;
        AppLogger.debug('[SCENE_DEBUG] Ensuring minimum image display time - waiting ${remainingTime.inMilliseconds}ms more');
        await Future.delayed(remainingTime);
      }
    }
  }

  Future<void> _navigateToCalmExitScreen() async {
    AppLogger.debug('[STORY_PLAYER] Function: _navigateToCalmExitScreen called');
    AppLogger.debug('[STORY_PLAYER] Navigating to calm exit screen');

    try {
      if (_isNarrating && !_isPaused) {
        await _pauseNarration();
      }

      if (mounted) {
        context.push('/calm_exit');
      }
    } catch (error) {
      AppLogger.error('[STORY_PLAYER] Error navigating to calm exit screen', error);
    }
  }

  @override
  void dispose() {
    AppLogger.debug('[STORY_PLAYER] Function: dispose called');
    AppLogger.debug('[STORY_PLAYER] Disposing StoryPlaybackWidget');
    _progressSubscription?.cancel();
    _narrationStateSubscription?.cancel();
    _fadeController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    AppLogger.debug('[STORY_PLAYER] Function: build called');
    return PopScope(
      canPop: false,
      onPopInvokedWithResult: (bool didPop, Object? result) async {
        AppLogger.debug('[STORY_PLAYER] Function: onPopInvokedWithResult called');
        if (didPop) return;
        AppLogger.debug('[SCENE_DEBUG] Device back button pressed - navigating to Calm Exit Screen');
        await _navigateToCalmExitScreen();
      },
      child: Scaffold(
        backgroundColor: Colors.black,
        body: _buildBody(),
      ),
    );
  }

  Widget _buildBody() {
    AppLogger.debug('[STORY_PLAYER] Function: _buildBody called');
    if (_showTTSConsent) {
      return _buildTTSConsentScreen();
    }

    if (_imageLoading) {
      return _buildLoadingScreen();
    }

    if (_imageError != null) {
      return _buildErrorScreen();
    }

    if (!_imageLoaded) {
      return _buildLoadingScreen();
    }

    return FadeTransition(
      opacity: _fadeAnimation,
      child: _buildMainContent(),
    );
  }

  Widget _buildTTSConsentScreen() {
    AppLogger.debug('[STORY_PLAYER] Function: _buildTTSConsentScreen called');
    return Center(
      child: Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          color: Colors.black.withOpacity(0.8),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text(
              'Enable Narration?',
              style: TextStyle(color: Colors.white, fontSize: 24, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            const Text(
              'This story includes audio narration. Press "Start" to begin.',
              style: TextStyle(color: Colors.white, fontSize: 16),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () {
                AppLogger.debug('[STORY_PLAYER] TTS consent Start button pressed');
                setState(() {
                  _showTTSConsent = false;
                });
                Future.delayed(const Duration(milliseconds: 500), () {
                  if (mounted) {
                    _startNarration();
                  }
                });
              },
              child: const Text('Start'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLoadingScreen() {
    AppLogger.debug('[STORY_PLAYER] Function: _buildLoadingScreen called');
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(
            valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
          ),
          SizedBox(height: 24),
          Text(
            'Loading Scene...',
            style: TextStyle(
              color: Colors.white,
              fontSize: 18,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorScreen() {
    AppLogger.debug('[STORY_PLAYER] Function: _buildErrorScreen called');
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(Icons.error_outline, size: 64, color: Colors.red),
          const SizedBox(height: 16),
          Text(
            _imageError ?? 'Failed to load scene',
            style: const TextStyle(color: Colors.red),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: () {
              AppLogger.debug('[STORY_PLAYER] Retry button pressed');
              setState(() {
                _imageError = null;
                _imageLoaded = false;
              });
              _loadSceneImage();
            },
            child: const Text('Retry'),
          ),
        ],
      ),
    );
  }

  Widget _buildMainContent() {
    AppLogger.debug('[STORY_PLAYER] Function: _buildMainContent called');
    return Stack(
      children: [
        _buildFullScreenImage(),
        if (_showControls) _buildControlOverlay(),
        if (_showChoices) _buildChoiceOverlay(),
        _buildSettingsButton(),
        Positioned(
          top: 16,
          left: 16,
          child: Text(
            'Image: ${_imageLoaded ? "Loaded" : "Not Loaded"}',
            style: const TextStyle(color: Colors.red, fontSize: 16),
          ),
        ),
      ],
    );
  }

  Widget _buildFullScreenImage() {
    AppLogger.debug('[STORY_PLAYER] Function: _buildFullScreenImage called');
    final imagePath = widget.scene.getImagePath(widget.story.storyId);
    return SizedBox.expand(
      child: FutureBuilder(
        future: precacheImage(AssetImage(imagePath), context),
        builder: (context, snapshot) {
          AppLogger.debug('[STORY_PLAYER] Function: _buildFullScreenImage FutureBuilder called, state: ${snapshot.connectionState}, error: ${snapshot.error}');
          if (snapshot.connectionState == ConnectionState.done && snapshot.error == null) {
            return Image(
              image: AssetImage(imagePath),
              fit: BoxFit.cover,
              errorBuilder: (context, error, stackTrace) {
                AppLogger.error('[STORY_PLAYER] Image render error: $error');
                return const Center(child: Text('Image failed to render', style: TextStyle(color: Colors.red)));
              },
            );
          } else if (snapshot.error != null) {
            AppLogger.error('[STORY_PLAYER] FutureBuilder image load error: ${snapshot.error}');
            return const Center(child: Text('Image failed to load', style: TextStyle(color: Colors.red)));
          }
          return const Center(child: CircularProgressIndicator());
        },
      ),
    );
  }

  Widget _buildSettingsButton() {
    AppLogger.debug('[STORY_PLAYER] Function: _buildSettingsButton called');
    return Positioned(
      top: 16,
      right: 16,
      child: Container(
        decoration: BoxDecoration(
          color: Colors.black.withOpacity(0.7),
          shape: BoxShape.circle,
        ),
        child: IconButton(
          onPressed: () {
            AppLogger.debug('[STORY_PLAYER] Settings button pressed');
            setState(() {
              _showControls = !_showControls;
            });
          },
          icon: const Icon(
            Icons.settings,
            color: Colors.white,
            size: 24,
          ),
        ),
      ),
    );
  }

  Widget _buildControlOverlay() {
    AppLogger.debug('[STORY_PLAYER] Function: _buildControlOverlay called');
    return Positioned(
      bottom: 0,
      left: 0,
      right: 0,
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Colors.transparent,
              Colors.black.withOpacity(0.6),
            ],
          ),
        ),
        child: _buildControlInterface(),
      ),
    );
  }

  Widget _buildControlInterface() {
    AppLogger.debug('[STORY_PLAYER] Function: _buildControlInterface called');
    return Row(
      children: [
        _buildPlayPauseButton(),
        const SizedBox(width: 16),
        Expanded(
          child: _buildTextDisplay(),
        ),
        const SizedBox(width: 16),
        _buildNavigationButton(),
      ],
    );
  }

  Widget _buildPlayPauseButton() {
    AppLogger.debug('[STORY_PLAYER] Function: _buildPlayPauseButton called');
    IconData iconData = _isNarrating
        ? (_isPaused ? Icons.play_arrow : Icons.pause)
        : Icons.play_arrow;

    return Container(
      decoration: BoxDecoration(
        color: Colors.black.withOpacity(0.7),
        shape: BoxShape.circle,
      ),
      child: IconButton(
        onPressed: () {
          AppLogger.debug('[STORY_PLAYER] Play/Pause button pressed');
          if (_isNarrating) {
            if (_isPaused) {
              _resumeNarration();
            } else {
              _pauseNarration();
            }
          } else {
            _startNarration();
          }
        },
        icon: Icon(
          iconData,
          color: Colors.white,
          size: 32,
        ),
      ),
    );
  }

  Widget _buildTextDisplay() {
    AppLogger.debug('[STORY_PLAYER] Function: _buildTextDisplay called');
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.black.withOpacity(0.7),
        borderRadius: BorderRadius.circular(8),
      ),
      child: _buildHighlightedText(),
    );
  }

  Widget _buildHighlightedText() {
    AppLogger.debug('[STORY_PLAYER] Function: _buildHighlightedText called');
    if (_sentences.isEmpty) {
      return Text(
        widget.scene.text,
        style: const TextStyle(
          color: Colors.white,
          fontSize: 18,
        ),
      );
    }

    return RichText(
      text: TextSpan(
        children: _buildHighlightedTextSpans(),
      ),
    );
  }

  List<TextSpan> _buildHighlightedTextSpans() {
    AppLogger.debug('[STORY_PLAYER] Function: _buildHighlightedTextSpans called');
    final spans = <TextSpan>[];

    for (int sentenceIndex = 0; sentenceIndex < _sentences.length; sentenceIndex++) {
      final words = _sentenceWords[sentenceIndex];

      for (int wordIndex = 0; wordIndex < words.length; wordIndex++) {
        final word = words[wordIndex];
        final isCurrentWord = sentenceIndex == _currentSentenceIndex && wordIndex == _currentWordIndex;

        spans.add(TextSpan(
          text: '$word ',
          style: TextStyle(
            color: isCurrentWord ? Colors.yellow : Colors.white,
            fontSize: 18,
            fontWeight: isCurrentWord ? FontWeight.bold : FontWeight.normal,
          ),
        ));
      }
    }

    return spans;
  }

  Widget _buildNavigationButton() {
    AppLogger.debug('[STORY_PLAYER] Function: _buildNavigationButton called');
    final hasChoices = widget.scene.choices?.isNotEmpty ?? false;
    final canAdvance = _hasCompleted && !hasChoices;
    final canShowChoices = hasChoices && _hasCompleted;

    IconData iconData;
    VoidCallback? onPressed;
    Color backgroundColor;
    String tooltip;

    if (canAdvance) {
      iconData = Icons.arrow_forward;
      onPressed = () {
        AppLogger.debug('[STORY_PLAYER] Manual scene progression triggered - advancing to next scene');
        widget.onSceneComplete?.call();
      };
      backgroundColor = Colors.green.withOpacity(0.9);
      tooltip = 'Next Scene';
    } else if (canShowChoices) {
      iconData = Icons.touch_app;
      onPressed = () {
        AppLogger.debug('[STORY_PLAYER] Showing choices for scene ${widget.scene.id}');
        setState(() {
          _showChoices = true;
        });
      };
      backgroundColor = Colors.orange.withOpacity(0.9);
      tooltip = 'Make Choice';
    } else {
      iconData = Icons.hourglass_empty;
      onPressed = null;
      backgroundColor = Colors.grey.withOpacity(0.5);
      tooltip = 'Wait for narration';
    }

    return Tooltip(
      message: tooltip,
      child: Container(
        decoration: BoxDecoration(
          color: backgroundColor,
          shape: BoxShape.circle,
        ),
        child: IconButton(
          onPressed: onPressed,
          icon: Icon(
            iconData,
            color: Colors.white,
            size: 32,
          ),
        ),
      ),
    );
  }

  Widget _buildChoiceOverlay() {
    AppLogger.debug('[STORY_PLAYER] Function: _buildChoiceOverlay called');
    return Container(
      color: Colors.black.withOpacity(0.8),
      child: Center(
        child: Container(
          constraints: const BoxConstraints(maxWidth: 600),
          padding: const EdgeInsets.all(24),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Text(
                'Choose your path:',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 24),
              ...widget.scene.choices!.map((choice) => _buildChoiceButton(choice)),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildChoiceButton(ChoiceOptionModel choice) {
    AppLogger.debug('[STORY_PLAYER] Function: _buildChoiceButton called');
    return Container(
      width: double.infinity,
      margin: const EdgeInsets.only(bottom: 12),
      child: ElevatedButton(
        onPressed: () {
          AppLogger.debug('[STORY_PLAYER] Choice selected: "${choice.option}" -> ${choice.next}');
          setState(() {
            _showChoices = false;
          });
          widget.onChoiceSelected(choice);
        },
        style: ElevatedButton.styleFrom(
          padding: const EdgeInsets.all(16),
          backgroundColor: Colors.blue[600],
          foregroundColor: Colors.white,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
        ),
        child: Text(
          choice.option,
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w500,
          ),
          textAlign: TextAlign.center,
        ),
      ),
    );
  }
}