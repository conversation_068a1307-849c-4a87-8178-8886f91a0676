{"@@locale": "en", "appTitle": "Choice: Once Upon A Time", "ftueScreenWelcome": "Welcome to Choice! Let me show you how to start your interactive story adventure.", "ftueFeatureInteractiveStories": "Our interactive stories let you choose your own path in every tale.", "ftueFeatureLifeValues": "These stories teach and inspire, reinforcing positive life values.", "ftueFeatureNarration": "Listen to your stories come alive with our empathetic narrator.", "ftueCompletePrompt": "Are you ready? Tap 'Start Reading' to begin!", "ftueSkipPrompt": "Or, if you're ready, you can skip the tutorial.", "storyIntroGenericPrompt": "Welcome to the story introduction. Please review the details and decide to start or download the story.", "errorStoryNotFound": "Sorry, we could not find the details for this story.", "downloadInProgressPleaseWait": "Download is in progress. Please wait.", "errorDownloadFailed": "Download failed. Please try again.", "downloadCompletePrompt": "Story downloaded successfully and is now available offline!", "storyLockedPrompt": "This story is locked. You can unlock it with a premium subscription.", "storyIntroActionStartOfflinePrompt": "The story is available offline. You can start reading now or manage your downloads.", "storyIntroActionStartDownloadPrompt": "You can start the story now, or download it to make it available offline.", "storyIntroDownloadConfirmationTitle": "Download Story?", "storyIntroDownloadConfirmationMessage": "Would you like to download this story for offline reading?", "storyIntroNotNowButton": "Not Now", "storyIntroDownloadButton": "Download", "storyIntroPremiumRequiredTitle": "Premium Required", "storyIntroPremiumRequiredMessage": "This story is part of our premium collection. Subscribe to unlock all stories and features.", "storyIntroCancelButton": "Cancel", "storyIntroSubscribeButton": "Subscribe", "homeScreenIntro": "Welcome to your Story Library. Browse the stories below and tap on one to begin your adventure!", "refreshingStories": "Refreshing your stories...", "loadingStories": "Loading your stories, please wait.", "errorLoadingStories": "Oops, there was an error loading your stories: {errorMessage}", "noStoriesAvailable": "There are currently no stories available. Please check back later or try refreshing.", "parentalGateScreenIntro": "Welcome to the Parent Zone access screen. This area is for parents and guardians only.", "parentalGateHoldButtonPrompt": "To continue, please press and hold the button below for 3 seconds.", "parentalGateKeepHoldingPrompt": "Keep holding... Almost there!", "parentalGatePassedPrompt": "Access granted. Loading Parent Zone.", "aiStoryGenerationScreenIntro": "Welcome to the AI Story Creator! Here you can create personalized stories just for your child.", "aiStoryGeneratingPrompt": "Creating your personalized story now. This may take a moment, so please be patient.", "aiStoryGeneratedPrompt": "Your story is ready! Let's start reading your personalized adventure.", "aiStoryGenerationErrorPrompt": "Sorry, there was an issue creating your story. Please check your settings and try again.", "welcomeBackUser": "Welcome back, {userName}!", "@welcomeBackUser": {"description": "Personalized welcome message for returning users", "placeholders": {"userName": {"type": "String", "example": "<PERSON>"}}}, "welcomeChild": "Hello, {child<PERSON><PERSON>}! Ready for an adventure?", "@welcomeChild": {"description": "Personalized welcome message for child profiles", "placeholders": {"childName": {"type": "String", "example": "<PERSON>"}}}, "parentZoneWelcome": "Welcome to the Parent Zone, {parentName}!", "@parentZoneWelcome": {"description": "Personalized welcome message for parent zone", "placeholders": {"parentName": {"type": "String", "example": "<PERSON>"}}}, "childProfileGreeting": "Hi {childName}! Let's continue your reading journey.", "@childProfileGreeting": {"description": "Greeting message when child profile is selected", "placeholders": {"childName": {"type": "String", "example": "<PERSON>"}}}, "storyCompletionCongrats": "Congratulations, {child<PERSON>ame}! You completed the story!", "@storyCompletionCongrats": {"description": "Congratulations message when child completes a story", "placeholders": {"childName": {"type": "String", "example": "<PERSON>"}}}, "voiceGuideWelcomeUser": "Welcome back, {userName}. I'm here to guide you through your storytelling adventure.", "@voiceGuideWelcomeUser": {"description": "Voice guide welcome message for users", "placeholders": {"userName": {"type": "String", "example": "<PERSON>"}}}, "voiceGuideWelcomeChild": "Hello {childName}! I'm your story guide. Let's explore amazing tales together!", "@voiceGuideWelcomeChild": {"description": "Voice guide welcome message for children", "placeholders": {"childName": {"type": "String", "example": "<PERSON>"}}}, "voiceGuideStorySelection": "{child<PERSON><PERSON>}, choose a story that interests you. I'll be here to help you along the way.", "@voiceGuideStorySelection": {"description": "Voice guide message for story selection", "placeholders": {"childName": {"type": "String", "example": "<PERSON>"}}}, "voiceGuideParentZone": "{parentName}, this is your parent zone where you can manage profiles and track progress.", "@voiceGuideParentZone": {"description": "Voice guide message for parent zone", "placeholders": {"parentName": {"type": "String", "example": "<PERSON>"}}}, "childProfileActivated": "{child<PERSON><PERSON>}'s profile is now active. Happy reading!", "@childProfileActivated": {"description": "Message when a child profile is activated", "placeholders": {"childName": {"type": "String", "example": "<PERSON>"}}}, "readingProgressUpdate": "Great job, {childName}! You've read for {minutes} minutes today.", "@readingProgressUpdate": {"description": "Reading progress update message", "placeholders": {"childName": {"type": "String", "example": "<PERSON>"}, "minutes": {"type": "int", "example": "15"}}}}