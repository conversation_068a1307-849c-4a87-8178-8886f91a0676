# Firebase Story Integration Guide

**Date:** 2025-01-23  
**Purpose:** Guide for integrating Firebase Storage and Firestore for story fetching and downloading  
**Target Audience:** Developers implementing cloud-based story distribution

## 📋 Overview

This document provides comprehensive instructions for integrating Firebase services to enable cloud-based story distribution, including story metadata management, asset storage, and offline downloading capabilities.

## 🏗️ Architecture Overview

### Current Implementation
- **Asset-Only Stories**: Stories loaded from `assets/stories/` folder
- **Local Storage**: All story content bundled with app
- **Offline-First**: No network dependency for story playback

### Firebase Integration Target
- **Hybrid Approach**: Asset stories + Firebase cloud stories
- **Progressive Download**: Download stories on-demand
- **Offline Capability**: Downloaded stories available offline
- **Content Management**: Dynamic story catalog via Firestore

## 🔧 Firebase Services Required

### 1. Firebase Storage
**Purpose**: Store story assets (images, audio, story.json files)

**Structure**:
```
gs://your-bucket/stories/
├── story001/
│   ├── story.json
│   ├── images/
│   │   ├── story_cover.jpg
│   │   ├── scene_1.jpg
│   │   ├── scene_2.jpg
│   │   └── vocabulary/
│   │       ├── word1.jpg
│   │       └── word2.jpg
│   └── audio/
│       ├── scene_1_narration.mp3
│       ├── scene_2_narration.mp3
│       └── background_music.mp3
├── story002/
│   └── ... (similar structure)
```

### 2. Firestore Database
**Purpose**: Store story metadata, user progress, and download tracking

**Collections**:
```
stories/
├── {storyId}/
│   ├── metadata: StoryMetadata
│   ├── downloadInfo: DownloadInfo
│   └── versions: VersionInfo

users/
├── {userId}/
│   ├── profile: UserProfile
│   ├── downloads: DownloadedStory[]
│   └── progress: StoryProgress[]

app_config/
├── story_catalog/
│   ├── featured_stories: string[]
│   ├── categories: Category[]
│   └── content_updates: UpdateInfo[]
```

## 📱 Implementation Steps

### Step 1: Firebase Setup

#### 1.1 Add Firebase Dependencies
```yaml
# pubspec.yaml
dependencies:
  firebase_core: ^2.24.2
  cloud_firestore: ^4.13.6
  firebase_storage: ^11.6.0
  firebase_auth: ^4.15.3  # For user authentication
```

#### 1.2 Initialize Firebase
```dart
// lib/core/services/firebase_service.dart
import 'package:firebase_core/firebase_core.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_storage/firebase_storage.dart';

class FirebaseService {
  static FirebaseFirestore get firestore => FirebaseFirestore.instance;
  static FirebaseStorage get storage => FirebaseStorage.instance;
  
  static Future<void> initialize() async {
    await Firebase.initializeApp();
  }
}
```

### Step 2: Story Metadata Models

#### 2.1 Enhanced Story Metadata
```dart
// lib/models/firebase_story_model.dart
@JsonSerializable()
class FirebaseStoryMetadata {
  final String storyId;
  final String title;
  final String description;
  final String coverImageUrl;
  final List<String> categories;
  final int ageRangeMin;
  final int ageRangeMax;
  final double estimatedDuration; // in minutes
  final DateTime publishedAt;
  final DateTime updatedAt;
  final String version;
  final int downloadSizeMB;
  final bool isPremium;
  final List<String> languages;
  final Map<String, dynamic> assets; // Asset URLs and metadata
  
  // Download and caching info
  final bool isDownloadable;
  final String downloadUrl; // ZIP file URL
  final String checksum; // For integrity verification
}
```

#### 2.2 Download Tracking Model
```dart
// lib/models/story_download_model.dart
@JsonSerializable()
class StoryDownloadInfo {
  final String storyId;
  final String userId;
  final DateTime downloadedAt;
  final String localPath;
  final String version;
  final int fileSizeMB;
  final DownloadStatus status;
  final double progress; // 0.0 to 1.0
  final DateTime? lastAccessedAt;
}

enum DownloadStatus {
  pending,
  downloading,
  completed,
  failed,
  paused,
  cancelled
}
```

### Step 3: Firebase Story Repository

#### 3.1 Create Firebase Story Repository
```dart
// lib/features/story_library/data/firebase_story_repository.dart
class FirebaseStoryRepository {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseStorage _storage = FirebaseStorage.instance;
  
  /// Fetch story catalog from Firestore
  Future<List<FirebaseStoryMetadata>> getStoryCatalog({
    String? category,
    int? ageRange,
    bool? isPremium,
  }) async {
    Query query = _firestore.collection('stories');
    
    if (category != null) {
      query = query.where('categories', arrayContains: category);
    }
    if (ageRange != null) {
      query = query.where('ageRangeMin', isLessThanOrEqualTo: ageRange)
                   .where('ageRangeMax', isGreaterThanOrEqualTo: ageRange);
    }
    if (isPremium != null) {
      query = query.where('isPremium', isEqualTo: isPremium);
    }
    
    final snapshot = await query.get();
    return snapshot.docs.map((doc) => 
      FirebaseStoryMetadata.fromJson(doc.data() as Map<String, dynamic>)
    ).toList();
  }
  
  /// Download story content
  Future<String> downloadStory(String storyId, {
    Function(double)? onProgress,
  }) async {
    final metadata = await getStoryMetadata(storyId);
    final localPath = await _getLocalStoryPath(storyId);
    
    // Create download task
    final downloadTask = _storage.ref(metadata.downloadUrl).writeToFile(
      File('$localPath/story.zip')
    );
    
    // Track progress
    downloadTask.snapshotEvents.listen((snapshot) {
      final progress = snapshot.bytesTransferred / snapshot.totalBytes;
      onProgress?.call(progress);
    });
    
    await downloadTask;
    
    // Extract ZIP file
    await _extractStoryAssets('$localPath/story.zip', localPath);
    
    // Update download tracking
    await _updateDownloadInfo(storyId, localPath);
    
    return localPath;
  }
  
  /// Check if story is downloaded
  Future<bool> isStoryDownloaded(String storyId) async {
    final localPath = await _getLocalStoryPath(storyId);
    return Directory(localPath).existsSync() && 
           File('$localPath/story.json').existsSync();
  }
  
  /// Get local story path
  Future<String> _getLocalStoryPath(String storyId) async {
    final appDir = await getApplicationDocumentsDirectory();
    return '${appDir.path}/stories/$storyId';
  }
}
```

### Step 4: Enhanced Story Service

#### 4.1 Hybrid Story Service
```dart
// lib/core/services/hybrid_story_service.dart
class HybridStoryService {
  final AssetOnlyStoryService _assetService;
  final FirebaseStoryRepository _firebaseRepository;
  final StoryDownloadManager _downloadManager;
  
  /// Load story with priority: Local Downloaded > Asset > Firebase
  Future<EnhancedStoryModel?> loadStory(String storyId) async {
    // 1. Try local downloaded story
    if (await _firebaseRepository.isStoryDownloaded(storyId)) {
      return await _loadDownloadedStory(storyId);
    }
    
    // 2. Try asset story
    try {
      return await _assetService.loadStory(storyId);
    } catch (e) {
      AppLogger.debug('[HYBRID_STORY] Asset story not found: $storyId');
    }
    
    // 3. Try Firebase story (streaming)
    return await _loadFirebaseStory(storyId);
  }
  
  /// Get available stories (hybrid catalog)
  Future<List<StoryMetadata>> getAvailableStories() async {
    final assetStories = await _assetService.getAvailableStories();
    final firebaseStories = await _firebaseRepository.getStoryCatalog();
    
    // Merge and deduplicate
    final allStories = <String, StoryMetadata>{};
    
    // Add asset stories (highest priority)
    for (final story in assetStories) {
      allStories[story.storyId] = story;
    }
    
    // Add Firebase stories (if not already present)
    for (final story in firebaseStories) {
      if (!allStories.containsKey(story.storyId)) {
        allStories[story.storyId] = _convertToStoryMetadata(story);
      }
    }
    
    return allStories.values.toList();
  }
}
```

### Step 5: Download Manager

#### 5.1 Story Download Manager
```dart
// lib/core/services/story_download_manager.dart
class StoryDownloadManager {
  final FirebaseStoryRepository _repository;
  final StreamController<DownloadProgress> _progressController;
  
  /// Download story with progress tracking
  Future<void> downloadStory(String storyId) async {
    try {
      _updateDownloadStatus(storyId, DownloadStatus.downloading);
      
      final localPath = await _repository.downloadStory(
        storyId,
        onProgress: (progress) {
          _progressController.add(DownloadProgress(
            storyId: storyId,
            progress: progress,
            status: DownloadStatus.downloading,
          ));
        },
      );
      
      _updateDownloadStatus(storyId, DownloadStatus.completed);
      AppLogger.debug('[DOWNLOAD_MANAGER] Story downloaded: $storyId');
      
    } catch (e) {
      _updateDownloadStatus(storyId, DownloadStatus.failed);
      AppLogger.error('[DOWNLOAD_MANAGER] Download failed: $storyId', e);
      rethrow;
    }
  }
  
  /// Get download progress stream
  Stream<DownloadProgress> get downloadProgress => _progressController.stream;
  
  /// Cancel download
  Future<void> cancelDownload(String storyId) async {
    // Implementation for cancelling downloads
    _updateDownloadStatus(storyId, DownloadStatus.cancelled);
  }
  
  /// Delete downloaded story
  Future<void> deleteDownloadedStory(String storyId) async {
    final localPath = await _repository._getLocalStoryPath(storyId);
    final directory = Directory(localPath);
    
    if (directory.existsSync()) {
      await directory.delete(recursive: true);
      await _removeDownloadInfo(storyId);
      AppLogger.debug('[DOWNLOAD_MANAGER] Deleted story: $storyId');
    }
  }
}
```

## 🔒 Security Considerations

### 1. Firebase Security Rules
```javascript
// Firestore Security Rules
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Public story metadata
    match /stories/{storyId} {
      allow read: if true;
      allow write: if request.auth != null && 
                      request.auth.token.admin == true;
    }
    
    // User-specific data
    match /users/{userId} {
      allow read, write: if request.auth != null && 
                            request.auth.uid == userId;
    }
  }
}

// Storage Security Rules
rules_version = '2';
service firebase.storage {
  match /b/{bucket}/o {
    match /stories/{allPaths=**} {
      allow read: if true; // Public read access
      allow write: if request.auth != null && 
                      request.auth.token.admin == true;
    }
  }
}
```

### 2. Content Validation
- Validate story.json structure before processing
- Verify file checksums for integrity
- Implement content filtering for inappropriate material
- Use signed URLs for premium content access

## 📊 Performance Optimization

### 1. Caching Strategy
- Cache story metadata locally using Hive/Isar
- Implement image caching for cover images
- Use progressive loading for large assets

### 2. Network Optimization
- Compress story assets before upload
- Use CDN for global content distribution
- Implement retry logic for failed downloads
- Support resume for interrupted downloads

### 3. Storage Management
- Monitor local storage usage
- Implement automatic cleanup of old downloads
- Provide storage usage analytics to users

## 🧪 Testing Strategy

### 1. Unit Tests
- Test Firebase repository methods
- Test download manager functionality
- Test hybrid story service logic

### 2. Integration Tests
- Test complete download workflow
- Test offline story playback
- Test story catalog synchronization

### 3. Performance Tests
- Test download speeds with various file sizes
- Test concurrent download handling
- Test storage cleanup operations

## 🚀 Deployment Checklist

### Pre-Deployment
- [ ] Configure Firebase project
- [ ] Set up security rules
- [ ] Upload initial story catalog
- [ ] Test download functionality
- [ ] Verify offline playback

### Post-Deployment
- [ ] Monitor download success rates
- [ ] Track storage usage metrics
- [ ] Monitor Firebase costs
- [ ] Collect user feedback on download experience

## 📈 Future Enhancements

### Phase 2 Features
- **Smart Prefetching**: Download stories based on user preferences
- **Differential Updates**: Update only changed story components
- **Peer-to-Peer Sharing**: Share downloaded stories between devices
- **Adaptive Quality**: Adjust asset quality based on device capabilities

### Advanced Features
- **Story Streaming**: Stream story content without full download
- **Collaborative Stories**: Multi-user story experiences
- **User-Generated Content**: Allow users to create and share stories
- **AI-Powered Recommendations**: Suggest stories based on reading history

This integration guide provides a comprehensive foundation for implementing Firebase-based story distribution while maintaining the app's offline-first approach and ensuring optimal user experience.
