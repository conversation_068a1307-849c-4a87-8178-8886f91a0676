# Code Management Guidelines

## Overview

This document provides comprehensive guidelines for managing code in the Choice: Once Upon A Time Flutter storytelling application. These guidelines ensure consistent development practices, maintainable code architecture, and efficient collaboration across the development team.

## 1. Manual Code Reading Protocol

### 1.1 Step-by-Step Code Analysis Process

#### Phase 1: High-Level Architecture Review
1. **Start with `lib/main.dart`** - Understand app initialization and service setup
2. **Review `lib/app/`** - Examine routing, providers, and global configuration
3. **Analyze `lib/features/`** - Identify feature modules and their relationships
4. **Check `lib/core/`** - Understand shared services, utilities, and models

#### Phase 2: Feature-Specific Analysis
1. **Identify Entry Points**: Look for screens in `presentation/screens/`
2. **Trace Widget Hierarchy**: Follow parent-child relationships in `presentation/widgets/`
3. **Understand State Management**: Examine providers in `presentation/providers/`
4. **Review Data Flow**: Analyze repositories in `data/` and services in `core/services/`

#### Phase 3: Deep Dive Investigation
1. **Read Method Signatures**: Understand input/output contracts
2. **Follow Call Chains**: Trace method calls through the codebase
3. **Identify Dependencies**: Map service injections and provider relationships
4. **Check Error Handling**: Verify try-catch blocks and error propagation

### 1.2 Widget Hierarchy Tracing Guidelines

#### Parent-Child Relationship Mapping
```
Screen (StatefulWidget)
├── Scaffold
│   ├── AppBar (optional)
│   └── Body
│       ├── Main Content Widget
│       ├── Floating Action Buttons
│       └── Overlay Widgets
```

#### State Management Flow
1. **Provider Pattern**: `ChangeNotifier` → `Consumer` → `Widget`
2. **Riverpod Pattern**: `StateNotifier` → `ConsumerWidget` → `ref.watch()`
3. **Local State**: `StatefulWidget` → `setState()` → `build()`

### 1.3 Navigation Flow Analysis

#### Route Structure Understanding
1. **Check `lib/app/routing/app_router.dart`** for route definitions
2. **Identify Navigation Patterns**: `context.go()`, `context.push()`, `Navigator.pop()`
3. **Map User Flow**: Follow documented paths in `USER_FLOW_DOCUMENTATION.md`

## 2. Index-Based Code Update System

### 2.1 Standardized Numbering System

#### Change Index Format: `YYYY.MM.DD.XXX`
- **YYYY**: Year (e.g., 2024)
- **MM**: Month (01-12)
- **DD**: Day (01-31)
- **XXX**: Sequential number for the day (001-999)

#### Example: `2024.06.17.001` - First change on June 17, 2024

### 2.2 Change Documentation Template

```markdown
## Change Index: [INDEX_NUMBER]

### Summary
Brief description of the change

### Files Modified
- `path/to/file1.dart` - [Description of changes]
- `path/to/file2.dart` - [Description of changes]

### Type of Change
- [ ] Bug Fix
- [ ] Feature Addition
- [ ] Refactoring
- [ ] Documentation
- [ ] Performance Improvement

### Impact Assessment
- **Breaking Changes**: Yes/No
- **Affected Features**: List of features
- **Testing Required**: List of test scenarios

### Implementation Details
Detailed explanation of the changes made

### Rollback Instructions
Steps to revert the change if needed
```

### 2.3 Version Control Integration

#### Commit Message Format
```
[INDEX] Type: Brief description

Detailed explanation of changes
- Specific change 1
- Specific change 2

Index: [INDEX_NUMBER]
```

#### Branch Naming Convention
```
feature/[INDEX]-brief-description
bugfix/[INDEX]-brief-description
refactor/[INDEX]-brief-description
```

## 3. Documentation Standards

### 3.1 Required Documentation Format

#### File Header Template
```dart
/// [File Purpose]
/// 
/// This file contains [brief description of contents].
/// 
/// Key Components:
/// - [Component 1]: [Description]
/// - [Component 2]: [Description]
/// 
/// Dependencies:
/// - [Dependency 1]: [Purpose]
/// - [Dependency 2]: [Purpose]
/// 
/// Last Modified: [DATE] - Index: [INDEX_NUMBER]
/// Author: [AUTHOR_NAME]
```

#### Class Documentation Template
```dart
/// [Class Name]
/// 
/// [Brief description of class purpose and responsibility]
/// 
/// Usage Example:
/// ```dart
/// final instance = ClassName(
///   parameter1: value1,
///   parameter2: value2,
/// );
/// ```
/// 
/// State Management:
/// - [State variable 1]: [Purpose]
/// - [State variable 2]: [Purpose]
/// 
/// Key Methods:
/// - [method1()]: [Description]
/// - [method2()]: [Description]
class ClassName extends StatefulWidget {
  // Implementation
}
```

#### Method Documentation Template
```dart
/// [Method Purpose]
/// 
/// [Detailed description of what the method does]
/// 
/// Parameters:
/// - [param1]: [Type] - [Description]
/// - [param2]: [Type] - [Description]
/// 
/// Returns:
/// - [Type]: [Description of return value]
/// 
/// Throws:
/// - [ExceptionType]: [When this exception is thrown]
/// 
/// Example:
/// ```dart
/// final result = await methodName(param1, param2);
/// ```
Future<ReturnType> methodName(Type param1, Type param2) async {
  // Implementation
}
```

### 3.2 Code Comment Standards

#### Inline Comments
```dart
// BUSINESS_LOGIC: Explanation of business rule
// PERFORMANCE: Note about performance consideration
// WORKAROUND: Temporary solution explanation
// TODO: Future improvement needed
// FIXME: Known issue that needs fixing
```

#### Block Comments for Complex Logic
```dart
/*
 * COMPLEX_ALGORITHM: [Algorithm Name]
 * 
 * This section implements [description of algorithm].
 * 
 * Steps:
 * 1. [Step 1 description]
 * 2. [Step 2 description]
 * 3. [Step 3 description]
 * 
 * Time Complexity: O(n)
 * Space Complexity: O(1)
 */
```

### 3.3 File Organization Standards

#### Directory Structure
```
lib/
├── app/                    # App-level configuration
│   ├── providers/         # Global providers
│   ├── routing/           # Navigation configuration
│   └── theme/             # Theme configuration
├── core/                  # Shared utilities and services
│   ├── audio/             # Audio services
│   ├── services/          # Business logic services
│   ├── utils/             # Utility functions
│   └── widgets/           # Reusable widgets
├── features/              # Feature modules
│   └── [feature_name]/    # Individual feature
│       ├── data/          # Data layer
│       ├── domain/        # Business logic
│       └── presentation/  # UI layer
└── models/                # Data models
```

#### File Naming Conventions
- **Screens**: `[feature_name]_screen.dart`
- **Widgets**: `[widget_purpose]_widget.dart`
- **Services**: `[service_name]_service.dart`
- **Providers**: `[feature_name]_provider.dart`
- **Models**: `[model_name]_model.dart`
- **Tests**: `[file_name]_test.dart`

## 4. Quality Assurance Checklist

### 4.1 Pre-Commit Code Review Checklist

#### Code Quality
- [ ] **Null Safety**: All variables properly handle null values
- [ ] **Error Handling**: Try-catch blocks for async operations
- [ ] **Memory Management**: Proper disposal of controllers and streams
- [ ] **Performance**: No unnecessary rebuilds or heavy operations in build()
- [ ] **Accessibility**: Semantic labels and proper widget hierarchy

#### Architecture Compliance
- [ ] **Feature Separation**: Code placed in correct feature module
- [ ] **Dependency Injection**: Services properly injected, not directly instantiated
- [ ] **State Management**: Consistent use of chosen state management pattern
- [ ] **Navigation**: Proper use of routing system
- [ ] **Responsive Design**: UI adapts to different screen sizes

#### Documentation
- [ ] **File Headers**: All new files have proper headers
- [ ] **Method Documentation**: Public methods documented
- [ ] **Complex Logic**: Non-obvious code explained with comments
- [ ] **README Updates**: Feature documentation updated if needed

### 4.2 Testing Requirements

#### Unit Tests (Required for all new services)
```dart
// Example test structure
group('ServiceName Tests', () {
  late ServiceName service;
  
  setUp(() {
    service = ServiceName();
  });
  
  test('should perform expected operation', () async {
    // Arrange
    final input = TestData();
    
    // Act
    final result = await service.method(input);
    
    // Assert
    expect(result, expectedValue);
  });
});
```

#### Widget Tests (Required for all new widgets)
```dart
// Example widget test
testWidgets('WidgetName should display correctly', (tester) async {
  // Arrange
  await tester.pumpWidget(
    MaterialApp(
      home: WidgetName(parameter: testValue),
    ),
  );
  
  // Act & Assert
  expect(find.text('Expected Text'), findsOneWidget);
  expect(find.byType(ExpectedWidget), findsOneWidget);
});
```

#### Integration Tests (Required for user flows)
```dart
// Example integration test
testWidgets('User can complete story flow', (tester) async {
  // Test complete user journey
  await tester.pumpWidget(MyApp());
  
  // Navigate through story flow
  await tester.tap(find.text('Start Reading'));
  await tester.pumpAndSettle();
  
  // Verify story player loads
  expect(find.byType(EnhancedStoryPlayerScreen), findsOneWidget);
});
```

### 4.3 Performance Validation

#### Performance Checklist
- [ ] **Build Time**: Widget build methods complete in <16ms
- [ ] **Memory Usage**: No memory leaks in long-running operations
- [ ] **Image Loading**: Efficient image caching and loading
- [ ] **Animation Performance**: Smooth 60fps animations
- [ ] **Network Efficiency**: Minimal and optimized API calls

#### Accessibility Validation
- [ ] **Screen Reader**: All interactive elements have semantic labels
- [ ] **Color Contrast**: Meets WCAG 2.1 AA standards
- [ ] **Touch Targets**: Minimum 44x44 logical pixels
- [ ] **Focus Management**: Proper focus order and visibility
- [ ] **Text Scaling**: Supports system text scaling

## 5. Flutter Storytelling App Specific Guidelines

### 5.1 Story System Architecture

#### Story Loading Pattern
```dart
// Preferred pattern for story loading
class StoryService {
  Future<Story> loadStory(String storyId) async {
    try {
      // 1. Check cache first
      if (_cache.containsKey(storyId)) {
        return _cache[storyId]!;
      }
      
      // 2. Load from assets
      final story = await _loadFromAssets(storyId);
      
      // 3. Cache for future use
      _cache[storyId] = story;
      
      return story;
    } catch (e) {
      AppLogger.error('Failed to load story: $storyId', e);
      rethrow;
    }
  }
}
```

### 5.2 Audio Integration Standards

#### TTS Service Usage
```dart
// Standard pattern for TTS integration
class NarrationWidget extends StatefulWidget {
  @override
  Widget build(BuildContext context) {
    return Consumer<TTSService>(
      builder: (context, ttsService, child) {
        return ElevatedButton(
          onPressed: () => ttsService.speak(text),
          child: Text('Play Narration'),
        );
      },
    );
  }
}
```

### 5.3 Responsive Design Requirements

#### Screen Size Breakpoints
- **Small**: < 600px width (phones)
- **Medium**: 600-1200px width (tablets)
- **Large**: > 1200px width (desktop/web)

#### Implementation Pattern
```dart
Widget build(BuildContext context) {
  final screenWidth = MediaQuery.of(context).size.width;
  
  if (screenWidth < 600) {
    return _buildMobileLayout();
  } else if (screenWidth < 1200) {
    return _buildTabletLayout();
  } else {
    return _buildDesktopLayout();
  }
}
```

This comprehensive guide ensures consistent, maintainable, and high-quality code development for the Choice: Once Upon A Time Flutter application.
