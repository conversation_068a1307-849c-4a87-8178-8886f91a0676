import 'package:choice_once_upon_a_time/core/utils/app_logger.dart';
import 'package:json_annotation/json_annotation.dart';

part 'enhanced_story_model.g.dart';

/// Enhanced story model matching the new JSON structure
@JsonSerializable()
class EnhancedStoryModel {
  @Json<PERSON>ey(name: 'story_id')
  final String storyId;
  
  @Json<PERSON>ey(name: 'age_group')
  final String ageGroup;
  
  final String difficulty;
  final String title;
  final String moral;
  
  @<PERSON><PERSON><PERSON><PERSON>(name: 'cover_image')
  final String coverImage;
  
  final StorySetupModel setup;
  
  @<PERSON><PERSON><PERSON><PERSON>(name: 'background_music')
  final String? backgroundMusic;
  
  @<PERSON><PERSON><PERSON><PERSON>(name: 'narrator_profile')
  final NarratorProfileModel narratorProfile;
  
  final List<CharacterModel> characters;
  final List<EnhancedSceneModel> scenes;
  
  @Json<PERSON>ey(name: 'post_story')
  final PostStoryModel postStory;

  const EnhancedStoryModel({
    required this.storyId,
    required this.ageGroup,
    required this.difficulty,
    required this.title,
    required this.moral,
    required this.coverImage,
    required this.setup,
    this.backgroundMusic,
    required this.narratorProfile,
    required this.characters,
    required this.scenes,
    required this.postStory,
  });

  factory EnhancedStoryModel.fromJson(Map<String, dynamic> json) =>
      _$EnhancedStoryModelFromJson(json);

  Map<String, dynamic> toJson() => _$EnhancedStoryModelToJson(this);

  /// Gets the initial scene
  EnhancedSceneModel get initialScene => scenes.first;

  /// Gets a scene by ID
  EnhancedSceneModel? getSceneById(String sceneId) {
    try {
      AppLogger.info('[STORY_PLAYBACK XYZ6] Getting scene by ID: $sceneId');
      return scenes.firstWhere((scene) => scene.id == sceneId);
    } catch (e) {
      return null;
    }
  }

  /// Gets character by name
  CharacterModel? getCharacterByName(String name) {
    try {
      return characters.firstWhere((char) => char.name == name);
    } catch (e) {
      return null;
    }
  }

  /// Gets the cover image path
  String get coverImagePath => 'assets/stories/$storyId/images/$coverImage';

  /// Gets the background music path
  String? get backgroundMusicPath => 
      backgroundMusic != null ? 'assets/stories/$storyId/assets/$backgroundMusic' : null;
}

/// Story setup information
@JsonSerializable()
class StorySetupModel {
  final String setting;
  final String tone;
  final String context;
  
  @JsonKey(name: 'brief_intro')
  final String? briefIntro;

  const StorySetupModel({
    required this.setting,
    required this.tone,
    required this.context,
    this.briefIntro,
  });

  factory StorySetupModel.fromJson(Map<String, dynamic> json) =>
      _$StorySetupModelFromJson(json);

  Map<String, dynamic> toJson() => _$StorySetupModelToJson(this);
}

/// Narrator profile with voice characteristics
@JsonSerializable()
class NarratorProfileModel {
  final String name;
  final VoiceModel voice;

  const NarratorProfileModel({
    required this.name,
    required this.voice,
  });

  factory NarratorProfileModel.fromJson(Map<String, dynamic> json) =>
      _$NarratorProfileModelFromJson(json);

  Map<String, dynamic> toJson() => _$NarratorProfileModelToJson(this);
}

/// Voice configuration for TTS
@JsonSerializable()
class VoiceModel {
  final String? name;
  final double pitch;
  final double rate;
  final double volume;

  const VoiceModel({
    this.name,
    required this.pitch,
    required this.rate,
    required this.volume,
  });

  factory VoiceModel.fromJson(Map<String, dynamic> json) =>
      _$VoiceModelFromJson(json);

  Map<String, dynamic> toJson() => _$VoiceModelToJson(this);
}

/// Character model with detailed profile
@JsonSerializable()
class CharacterModel {
  final String name;
  final String description;
  final String role;
  final VoiceModel voice;

  const CharacterModel({
    required this.name,
    required this.description,
    required this.role,
    required this.voice,
  });

  factory CharacterModel.fromJson(Map<String, dynamic> json) =>
      _$CharacterModelFromJson(json);

  Map<String, dynamic> toJson() => _$CharacterModelToJson(this);

  /// Gets character image path
  String getImagePath(String storyId) => 
      'assets/stories/$storyId/images/${name.toLowerCase()}.jpg';
}

/// Enhanced scene model with emotion and choices
@JsonSerializable()
class EnhancedSceneModel {
  final String id;
  final String text;
  final String speaker;
  final String emotion;
  final String image;
  
  @JsonKey(name: 'pause_duration')
  final int pauseDuration;
  
  final String? next;
  final List<ChoiceOptionModel>? choices;
  final String? outcome;
  final String? conclusion;
  final RewardsModel? rewards;
  final ReflectionModel? reflection;

  const EnhancedSceneModel({
    required this.id,
    required this.text,
    required this.speaker,
    required this.emotion,
    required this.image,
    required this.pauseDuration,
    this.next,
    this.choices,
    this.outcome,
    this.conclusion,
    this.rewards,
    this.reflection,
  });

  factory EnhancedSceneModel.fromJson(Map<String, dynamic> json) =>
      _$EnhancedSceneModelFromJson(json);

  Map<String, dynamic> toJson() => _$EnhancedSceneModelToJson(this);

  /// Whether this scene has choices
  bool get hasChoices => choices != null && choices!.isNotEmpty;

  /// Whether this is an ending scene
  bool get isEnding => next == null && !hasChoices;

  /// Gets the image path
  String getImagePath(String storyId) {
    final path = 'assets/stories/$storyId/images/$image';
    AppLogger.debug('[IMAGE_LOAD] Asset: $path | Location: lib/models/enhanced_story_model.dart:219 | Widget: getImagePath()');
    return path;
  }
}

/// Choice option model
@JsonSerializable()
class ChoiceOptionModel {
  final String option;
  final String visual;
  final String next;

  const ChoiceOptionModel({
    required this.option,
    required this.visual,
    required this.next,
  });

  factory ChoiceOptionModel.fromJson(Map<String, dynamic> json) =>
      _$ChoiceOptionModelFromJson(json);

  Map<String, dynamic> toJson() => _$ChoiceOptionModelToJson(this);

  /// Gets the visual icon path
  String getVisualPath(String storyId) {
    final path = 'assets/stories/$storyId/images/$visual';
    AppLogger.debug('[IMAGE_LOAD] Asset: $path | Location: lib/models/enhanced_story_model.dart:244 | Widget: getVisualPath()');
    return path;
  }
}

/// Rewards model for scene completion
@JsonSerializable()
class RewardsModel {
  final int completion;
  final int good;

  const RewardsModel({
    required this.completion,
    required this.good,
  });

  factory RewardsModel.fromJson(Map<String, dynamic> json) =>
      _$RewardsModelFromJson(json);

  Map<String, dynamic> toJson() => _$RewardsModelToJson(this);
}

/// Reflection model for post-scene questions
@JsonSerializable()
class ReflectionModel {
  final String text;
  final String emotion;

  const ReflectionModel({
    required this.text,
    required this.emotion,
  });

  factory ReflectionModel.fromJson(Map<String, dynamic> json) =>
      _$ReflectionModelFromJson(json);

  Map<String, dynamic> toJson() => _$ReflectionModelToJson(this);
}

/// Post-story content model
@JsonSerializable()
class PostStoryModel {
  final DiscussionModel? discussion;
  
  @JsonKey(name: 'replay_prompt')
  final ReplayPromptModel? replayPrompt;
  
  @JsonKey(name: 'good_outcome')
  final OutcomeModel? goodOutcome;
  
  @JsonKey(name: 'bad_outcome')
  final OutcomeModel? badOutcome;

  const PostStoryModel({
    this.discussion,
    this.replayPrompt,
    this.goodOutcome,
    this.badOutcome,
  });

  factory PostStoryModel.fromJson(Map<String, dynamic> json) =>
      _$PostStoryModelFromJson(json);

  Map<String, dynamic> toJson() => _$PostStoryModelToJson(this);
}

/// Discussion model for post-story reflection
@JsonSerializable()
class DiscussionModel {
  final String text;
  final String emotion;

  @JsonKey(name: 'vocabulary_discussion')
  final List<VocabularyEntryModel>? vocabularyDiscussion;

  const DiscussionModel({
    required this.text,
    required this.emotion,
    this.vocabularyDiscussion,
  });

  factory DiscussionModel.fromJson(Map<String, dynamic> json) =>
      _$DiscussionModelFromJson(json);

  Map<String, dynamic> toJson() => _$DiscussionModelToJson(this);
}

/// Vocabulary entry model for learning words
@JsonSerializable()
class VocabularyEntryModel {
  final String word;
  final String explanation;
  final String? image;

  const VocabularyEntryModel({
    required this.word,
    required this.explanation,
    this.image,
  });

  factory VocabularyEntryModel.fromJson(Map<String, dynamic> json) =>
      _$VocabularyEntryModelFromJson(json);

  Map<String, dynamic> toJson() => _$VocabularyEntryModelToJson(this);

  /// Gets the vocabulary image path
  String getImagePath(String storyId) {
    if (image == null) return '';
    return 'assets/stories/$storyId/images/vocabulary/$image';
  }
}

/// Replay prompt model
@JsonSerializable()
class ReplayPromptModel {
  final String text;
  final String emotion;

  const ReplayPromptModel({
    required this.text,
    required this.emotion,
  });

  factory ReplayPromptModel.fromJson(Map<String, dynamic> json) =>
      _$ReplayPromptModelFromJson(json);

  Map<String, dynamic> toJson() => _$ReplayPromptModelToJson(this);
}

/// Outcome model for different story endings
@JsonSerializable()
class OutcomeModel {
  final DiscussionModel discussion;
  
  @JsonKey(name: 'replay_prompt')
  final ReplayPromptModel replayPrompt;

  const OutcomeModel({
    required this.discussion,
    required this.replayPrompt,
  });

  factory OutcomeModel.fromJson(Map<String, dynamic> json) =>
      _$OutcomeModelFromJson(json);

  Map<String, dynamic> toJson() => _$OutcomeModelToJson(this);
}
