import 'package:flutter/material.dart';
import 'package:choice_once_upon_a_time/core/services/story_narration_service_interface.dart';
import 'package:choice_once_upon_a_time/core/utils/app_logger.dart';

/// Model for vocabulary discussion entry
class VocabularyEntry {
  final String word;
  final String explanation;
  final String? image;

  const VocabularyEntry({
    required this.word,
    required this.explanation,
    this.image,
  });

  factory VocabularyEntry.fromJson(Map<String, dynamic> json) {
    return VocabularyEntry(
      word: json['word'] as String,
      explanation: json['explanation'] as String,
      image: json['image'] as String?,
    );
  }
}

/// Widget for displaying vocabulary discussion with images and narration
class VocabularyDiscussionWidget extends StatefulWidget {
  final List<VocabularyEntry> vocabularyEntries;
  final String storyId;
  final IStoryNarrationService narrationService;
  final VoidCallback onComplete;
  final VoidCallback? onSkip;

  const VocabularyDiscussionWidget({
    super.key,
    required this.vocabularyEntries,
    required this.storyId,
    required this.narrationService,
    required this.onComplete,
    this.onSkip,
  });

  @override
  State<VocabularyDiscussionWidget> createState() => _VocabularyDiscussionWidgetState();
}

class _VocabularyDiscussionWidgetState extends State<VocabularyDiscussionWidget>
    with TickerProviderStateMixin {
  
  // Animation controllers
  late AnimationController _fadeController;
  late Animation<double> _fadeAnimation;
  
  // State variables
  int _currentEntryIndex = 0;
  bool _isNarrating = false;
  bool _hasStarted = false;

  @override
  void initState() {
    super.initState();
    AppLogger.debug('[VOCABULARY_DISCUSSION] Initializing with ${widget.vocabularyEntries.length} entries');
    
    _initializeAnimations();
    _startVocabularyDiscussion();
  }

  @override
  void dispose() {
    _fadeController.dispose();
    super.dispose();
  }

  /// Initialize animations
  void _initializeAnimations() {
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );
    
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _fadeController,
      curve: Curves.easeInOut,
    ));
  }

  /// Start vocabulary discussion
  Future<void> _startVocabularyDiscussion() async {
    if (widget.vocabularyEntries.isEmpty) {
      widget.onComplete();
      return;
    }

    setState(() {
      _hasStarted = true;
    });

    await _fadeController.forward();
    
    // Start with introduction
    await _narrateIntroduction();
    
    // Go through each vocabulary entry
    for (int i = 0; i < widget.vocabularyEntries.length; i++) {
      setState(() {
        _currentEntryIndex = i;
      });
      
      await _narrateVocabularyEntry(widget.vocabularyEntries[i]);
      
      // Wait between entries
      await Future.delayed(const Duration(seconds: 2));
    }
    
    // Complete the discussion
    await _narrateConclusion();
    
    // Wait a moment before completing
    await Future.delayed(const Duration(seconds: 2));
    
    widget.onComplete();
  }

  /// Narrate introduction
  Future<void> _narrateIntroduction() async {
    setState(() {
      _isNarrating = true;
    });

    try {
      await widget.narrationService.narrateText(
        "Let's learn about some important words from our story!",
        emotionCue: 'excited',
        storyId: widget.storyId,
        sceneId: 'vocabulary_intro',
      );
    } catch (e) {
      AppLogger.debug('[VOCABULARY_DISCUSSION] Error narrating introduction: $e');
    }

    setState(() {
      _isNarrating = false;
    });
  }

  /// Narrate vocabulary entry
  Future<void> _narrateVocabularyEntry(VocabularyEntry entry) async {
    setState(() {
      _isNarrating = true;
    });

    try {
      final narrationText = "The word is ${entry.word}. ${entry.explanation}";
      
      await widget.narrationService.narrateText(
        narrationText,
        emotionCue: 'educational',
        storyId: widget.storyId,
        sceneId: 'vocabulary_${entry.word}',
      );
    } catch (e) {
      AppLogger.debug('[VOCABULARY_DISCUSSION] Error narrating entry ${entry.word}: $e');
    }

    setState(() {
      _isNarrating = false;
    });
  }

  /// Narrate conclusion
  Future<void> _narrateConclusion() async {
    setState(() {
      _isNarrating = true;
    });

    try {
      await widget.narrationService.narrateText(
        "Great job learning these new words! You can use them in your own stories.",
        emotionCue: 'proud',
        storyId: widget.storyId,
        sceneId: 'vocabulary_conclusion',
      );
    } catch (e) {
      AppLogger.debug('[VOCABULARY_DISCUSSION] Error narrating conclusion: $e');
    }

    setState(() {
      _isNarrating = false;
    });
  }

  /// Skip to next entry or complete
  void _skipToNext() {
    if (_currentEntryIndex < widget.vocabularyEntries.length - 1) {
      setState(() {
        _currentEntryIndex++;
        _isNarrating = false;
      });
    } else {
      widget.onComplete();
    }
  }

  @override
  Widget build(BuildContext context) {
    if (!_hasStarted || widget.vocabularyEntries.isEmpty) {
      return _buildLoadingState();
    }

    return Container(
      color: Colors.black.withValues(alpha: 0.9),
      child: SafeArea(
        child: FadeTransition(
          opacity: _fadeAnimation,
          child: _buildVocabularyContent(),
        ),
      ),
    );
  }

  /// Build loading state
  Widget _buildLoadingState() {
    return Container(
      color: Colors.black.withValues(alpha: 0.9),
      child: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(color: Colors.white),
            SizedBox(height: 16),
            Text(
              'Preparing vocabulary discussion...',
              style: TextStyle(
                color: Colors.white,
                fontSize: 18,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Build vocabulary content
  Widget _buildVocabularyContent() {
    final screenSize = MediaQuery.of(context).size;
    final isSmallScreen = screenSize.width < 600;
    final currentEntry = widget.vocabularyEntries[_currentEntryIndex];

    return Padding(
      padding: EdgeInsets.all(isSmallScreen ? 16 : 32),
      child: Column(
        children: [
          // Header
          _buildHeader(isSmallScreen),
          
          const SizedBox(height: 24),
          
          // Main content
          Expanded(
            child: _buildVocabularyCard(currentEntry, isSmallScreen),
          ),
          
          const SizedBox(height: 24),
          
          // Progress and controls
          _buildProgressAndControls(isSmallScreen),
        ],
      ),
    );
  }

  /// Build header
  Widget _buildHeader(bool isSmallScreen) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          'Vocabulary Discussion',
          style: TextStyle(
            color: Colors.white,
            fontSize: isSmallScreen ? 20 : 24,
            fontWeight: FontWeight.bold,
          ),
        ),
        if (widget.onSkip != null)
          TextButton(
            onPressed: widget.onSkip,
            child: const Text(
              'Skip',
              style: TextStyle(
                color: Colors.white70,
                fontSize: 16,
              ),
            ),
          ),
      ],
    );
  }

  /// Build vocabulary card
  Widget _buildVocabularyCard(VocabularyEntry entry, bool isSmallScreen) {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(isSmallScreen ? 20 : 32),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.3),
            blurRadius: 20,
            offset: const Offset(0, 10),
          ),
        ],
      ),
      child: isSmallScreen
          ? _buildVerticalLayout(entry, isSmallScreen)
          : _buildHorizontalLayout(entry, isSmallScreen),
    );
  }

  /// Build horizontal layout for larger screens
  Widget _buildHorizontalLayout(VocabularyEntry entry, bool isSmallScreen) {
    return Row(
      children: [
        // Image
        Expanded(
          flex: 2,
          child: _buildVocabularyImage(entry),
        ),
        
        const SizedBox(width: 32),
        
        // Text content
        Expanded(
          flex: 3,
          child: _buildTextContent(entry, isSmallScreen),
        ),
      ],
    );
  }

  /// Build vertical layout for smaller screens
  Widget _buildVerticalLayout(VocabularyEntry entry, bool isSmallScreen) {
    return Column(
      children: [
        // Image
        Expanded(
          flex: 2,
          child: _buildVocabularyImage(entry),
        ),

        const SizedBox(height: 24),

        // Text content
        Expanded(
          flex: 1,
          child: _buildTextContent(entry, isSmallScreen),
        ),
      ],
    );
  }

  /// Build vocabulary image with fallback
  Widget _buildVocabularyImage(VocabularyEntry entry) {
    final imagePath = entry.image != null
        ? 'assets/stories/${widget.storyId}/images/vocabulary/${entry.image}'
        : null;

    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.grey[300]!, width: 2),
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(14),
        child: imagePath != null
            ? Image.asset(
                imagePath,
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) {
                  AppLogger.debug('[VOCABULARY_DISCUSSION] Failed to load image: $imagePath');
                  return _buildDefaultVocabularyImage(entry.word);
                },
              )
            : _buildDefaultVocabularyImage(entry.word),
      ),
    );
  }

  /// Build default vocabulary image
  Widget _buildDefaultVocabularyImage(String word) {
    return Container(
      color: Colors.blue[50],
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.book_outlined,
            size: 64,
            color: Colors.blue[400],
          ),
          const SizedBox(height: 16),
          Text(
            word.toUpperCase(),
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: Colors.blue[600],
              letterSpacing: 2,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  /// Build text content
  Widget _buildTextContent(VocabularyEntry entry, bool isSmallScreen) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Word title
        Text(
          entry.word.toUpperCase(),
          style: TextStyle(
            fontSize: isSmallScreen ? 24 : 32,
            fontWeight: FontWeight.bold,
            color: Colors.deepPurple,
            letterSpacing: 1,
          ),
        ),

        const SizedBox(height: 16),

        // Explanation
        Expanded(
          child: SingleChildScrollView(
            child: Text(
              entry.explanation,
              style: TextStyle(
                fontSize: isSmallScreen ? 16 : 20,
                color: Colors.grey[800],
                height: 1.5,
              ),
            ),
          ),
        ),

        // Narration indicator
        if (_isNarrating) ...[
          const SizedBox(height: 16),
          Row(
            children: [
              const SizedBox(
                width: 16,
                height: 16,
                child: CircularProgressIndicator(strokeWidth: 2),
              ),
              const SizedBox(width: 8),
              Text(
                'Listening...',
                style: TextStyle(
                  color: Colors.blue[600],
                  fontSize: 14,
                  fontStyle: FontStyle.italic,
                ),
              ),
            ],
          ),
        ],
      ],
    );
  }

  /// Build progress and controls
  Widget _buildProgressAndControls(bool isSmallScreen) {
    return Column(
      children: [
        // Progress indicator
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: List.generate(
            widget.vocabularyEntries.length,
            (index) => Container(
              margin: const EdgeInsets.symmetric(horizontal: 4),
              width: 12,
              height: 12,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: index == _currentEntryIndex
                    ? Colors.white
                    : Colors.white.withValues(alpha: 0.3),
              ),
            ),
          ),
        ),

        const SizedBox(height: 16),

        // Progress text
        Text(
          '${_currentEntryIndex + 1} of ${widget.vocabularyEntries.length}',
          style: const TextStyle(
            color: Colors.white70,
            fontSize: 16,
          ),
        ),

        const SizedBox(height: 24),

        // Control buttons
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: [
            // Previous button
            if (_currentEntryIndex > 0)
              ElevatedButton.icon(
                onPressed: () {
                  setState(() {
                    _currentEntryIndex--;
                    _isNarrating = false;
                  });
                },
                icon: const Icon(Icons.arrow_back),
                label: const Text('Previous'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.white.withValues(alpha: 0.2),
                  foregroundColor: Colors.white,
                ),
              ),

            // Next/Complete button
            ElevatedButton.icon(
              onPressed: _skipToNext,
              icon: Icon(_currentEntryIndex < widget.vocabularyEntries.length - 1
                  ? Icons.arrow_forward
                  : Icons.check),
              label: Text(_currentEntryIndex < widget.vocabularyEntries.length - 1
                  ? 'Next'
                  : 'Complete'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.white,
                foregroundColor: Colors.deepPurple,
              ),
            ),
          ],
        ),
      ],
    );
  }
}
