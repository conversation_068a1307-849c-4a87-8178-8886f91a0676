import 'package:flutter/material.dart';
import 'package:choice_once_upon_a_time/core/services/story_narration_service_interface.dart';
import 'package:choice_once_upon_a_time/models/narration_models.dart';
import 'package:choice_once_upon_a_time/core/utils/app_logger.dart';

/// Advanced narration controls widget with comprehensive playback controls
class NarrationControlsWidget extends StatefulWidget {
  final IStoryNarrationService narrationService;
  final bool showAdvancedControls;
  final bool showSpeedControl;
  final bool showVolumeControl;
  final bool showProgressSlider;
  final VoidCallback? onPlayPressed;
  final VoidCallback? onPausePressed;
  final VoidCallback? onStopPressed;
  final VoidCallback? onReplayPressed;
  final VoidCallback? onNextSentencePressed;
  final VoidCallback? onPreviousSentencePressed;

  const NarrationControlsWidget({
    super.key,
    required this.narrationService,
    this.showAdvancedControls = true,
    this.showSpeedControl = true,
    this.showVolumeControl = true,
    this.showProgressSlider = true,
    this.onPlayPressed,
    this.onPausePressed,
    this.onStopPressed,
    this.onReplayPressed,
    this.onNextSentencePressed,
    this.onPreviousSentencePressed,
  });

  @override
  State<NarrationControlsWidget> createState() => _NarrationControlsWidgetState();
}

class _NarrationControlsWidgetState extends State<NarrationControlsWidget> {
  NarrationState _narrationState = NarrationState.idle;
  NarrationConfig _narrationConfig = NarrationConfig.defaultConfig;
  double _tempSpeedValue = 0.5;
  double _tempVolumeValue = 1.0;
  bool _isSeekingProgress = false;

  @override
  void initState() {
    super.initState();
    _setupListeners();
    _initializeValues();
  }

  /// Set up narration service listeners
  void _setupListeners() {
    widget.narrationService.stateStream.listen((state) {
      if (mounted) {
        setState(() {
          _narrationState = state;
        });
      }
    });
  }

  /// Initialize values from narration service
  void _initializeValues() {
    _narrationConfig = widget.narrationService.currentConfig;
    _tempSpeedValue = _narrationConfig.speechRate;
    _tempVolumeValue = _narrationConfig.speechVolume;
  }

  /// Handle play button press
  Future<void> _handlePlay() async {
    try {
      await widget.narrationService.play();
      widget.onPlayPressed?.call();
    } catch (e) {
      AppLogger.error('[NarrationControls] Failed to play', e);
    }
  }

  /// Handle pause button press
  Future<void> _handlePause() async {
    try {
      await widget.narrationService.pause();
      widget.onPausePressed?.call();
    } catch (e) {
      AppLogger.error('[NarrationControls] Failed to pause', e);
    }
  }

  /// Handle stop button press
  Future<void> _handleStop() async {
    try {
      await widget.narrationService.stop();
      widget.onStopPressed?.call();
    } catch (e) {
      AppLogger.error('[NarrationControls] Failed to stop', e);
    }
  }

  /// Handle replay button press
  Future<void> _handleReplay() async {
    try {
      await widget.narrationService.replayScene();
      widget.onReplayPressed?.call();
    } catch (e) {
      AppLogger.error('[NarrationControls] Failed to replay', e);
    }
  }

  /// Handle next sentence button press
  Future<void> _handleNextSentence() async {
    try {
      await widget.narrationService.skipToNextSentence();
      widget.onNextSentencePressed?.call();
    } catch (e) {
      AppLogger.error('[NarrationControls] Failed to skip to next sentence', e);
    }
  }

  /// Handle previous sentence button press
  Future<void> _handlePreviousSentence() async {
    try {
      await widget.narrationService.skipToPreviousSentence();
      widget.onPreviousSentencePressed?.call();
    } catch (e) {
      AppLogger.error('[NarrationControls] Failed to skip to previous sentence', e);
    }
  }

  /// Handle speed change
  Future<void> _handleSpeedChange(double value) async {
    setState(() {
      _tempSpeedValue = value;
    });
    
    try {
      await widget.narrationService.setSpeechRate(value);
    } catch (e) {
      AppLogger.error('[NarrationControls] Failed to set speech rate', e);
    }
  }

  /// Handle volume change
  Future<void> _handleVolumeChange(double value) async {
    setState(() {
      _tempVolumeValue = value;
    });
    
    try {
      await widget.narrationService.setSpeechVolume(value);
    } catch (e) {
      AppLogger.error('[NarrationControls] Failed to set speech volume', e);
    }
  }

  /// Handle progress seek
  Future<void> _handleProgressSeek(double value) async {
    if (!_isSeekingProgress) return;
    
    try {
      final progress = widget.narrationService.getCurrentProgress();
      if (progress != null) {
        final targetWordIndex = (value * progress.totalWords).round();
        await widget.narrationService.seekToWord(targetWordIndex);
      }
    } catch (e) {
      AppLogger.error('[NarrationControls] Failed to seek progress', e);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Progress slider
          if (widget.showProgressSlider) ...[
            _buildProgressSlider(),
            const SizedBox(height: 16),
          ],
          
          // Main playback controls
          _buildMainControls(),
          
          // Advanced controls
          if (widget.showAdvancedControls) ...[
            const SizedBox(height: 16),
            _buildAdvancedControls(),
          ],
          
          // Speed and volume controls
          if (widget.showSpeedControl || widget.showVolumeControl) ...[
            const SizedBox(height: 16),
            _buildSpeedVolumeControls(),
          ],
        ],
      ),
    );
  }

  /// Build progress slider
  Widget _buildProgressSlider() {
    final theme = Theme.of(context);
    final progress = _narrationState.progress;
    
    return Column(
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Progress',
              style: theme.textTheme.bodySmall,
            ),
            Text(
              '${(progress * 100).toInt()}%',
              style: theme.textTheme.bodySmall,
            ),
          ],
        ),
        const SizedBox(height: 8),
        SliderTheme(
          data: SliderTheme.of(context).copyWith(
            trackHeight: 4,
            thumbShape: const RoundSliderThumbShape(enabledThumbRadius: 8),
            overlayShape: const RoundSliderOverlayShape(overlayRadius: 16),
          ),
          child: Slider(
            value: progress,
            onChanged: _narrationState.status == NarrationStatus.playing ||
                      _narrationState.status == NarrationStatus.paused
                ? (value) {
                    setState(() {
                      _isSeekingProgress = true;
                    });
                  }
                : null,
            onChangeEnd: (value) {
              _handleProgressSeek(value);
              setState(() {
                _isSeekingProgress = false;
              });
            },
            activeColor: theme.colorScheme.primary,
            inactiveColor: theme.colorScheme.surfaceContainerHighest,
          ),
        ),
      ],
    );
  }

  /// Build main playback controls
  Widget _buildMainControls() {
    final theme = Theme.of(context);
    final isPlaying = _narrationState.status == NarrationStatus.playing;
    final isPaused = _narrationState.status == NarrationStatus.paused;
    final isLoading = _narrationState.status == NarrationStatus.loading;
    final canControl = !isLoading;

    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: [
        // Previous sentence
        IconButton(
          onPressed: canControl ? _handlePreviousSentence : null,
          icon: const Icon(Icons.skip_previous),
          iconSize: 28,
          style: IconButton.styleFrom(
            backgroundColor: theme.colorScheme.secondaryContainer,
            foregroundColor: theme.colorScheme.onSecondaryContainer,
            padding: const EdgeInsets.all(12),
          ),
        ),
        
        // Play/Pause
        IconButton(
          onPressed: canControl ? (isPlaying ? _handlePause : _handlePlay) : null,
          icon: Icon(
            isLoading 
                ? Icons.hourglass_empty 
                : (isPlaying ? Icons.pause : Icons.play_arrow),
            size: 36,
          ),
          style: IconButton.styleFrom(
            backgroundColor: theme.colorScheme.primary,
            foregroundColor: theme.colorScheme.onPrimary,
            padding: const EdgeInsets.all(16),
          ),
        ),
        
        // Next sentence
        IconButton(
          onPressed: canControl ? _handleNextSentence : null,
          icon: const Icon(Icons.skip_next),
          iconSize: 28,
          style: IconButton.styleFrom(
            backgroundColor: theme.colorScheme.secondaryContainer,
            foregroundColor: theme.colorScheme.onSecondaryContainer,
            padding: const EdgeInsets.all(12),
          ),
        ),
      ],
    );
  }

  /// Build advanced controls
  Widget _buildAdvancedControls() {
    final theme = Theme.of(context);
    final canControl = _narrationState.status != NarrationStatus.loading;

    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: [
        // Stop
        IconButton(
          onPressed: canControl ? _handleStop : null,
          icon: const Icon(Icons.stop),
          iconSize: 24,
          style: IconButton.styleFrom(
            backgroundColor: theme.colorScheme.errorContainer,
            foregroundColor: theme.colorScheme.onErrorContainer,
            padding: const EdgeInsets.all(10),
          ),
        ),
        
        // Replay
        IconButton(
          onPressed: canControl ? _handleReplay : null,
          icon: const Icon(Icons.replay),
          iconSize: 24,
          style: IconButton.styleFrom(
            backgroundColor: theme.colorScheme.tertiaryContainer,
            foregroundColor: theme.colorScheme.onTertiaryContainer,
            padding: const EdgeInsets.all(10),
          ),
        ),
      ],
    );
  }

  /// Build speed and volume controls
  Widget _buildSpeedVolumeControls() {
    final theme = Theme.of(context);
    
    return Column(
      children: [
        // Speed control
        if (widget.showSpeedControl) ...[
          Row(
            children: [
              Icon(
                Icons.speed,
                size: 20,
                color: theme.colorScheme.onSurfaceVariant,
              ),
              const SizedBox(width: 8),
              Text(
                'Speed',
                style: theme.textTheme.bodySmall,
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Slider(
                  value: _tempSpeedValue,
                  min: 0.1,
                  max: 2.0,
                  divisions: 19,
                  label: '${_tempSpeedValue.toStringAsFixed(1)}x',
                  onChanged: _handleSpeedChange,
                  activeColor: theme.colorScheme.primary,
                ),
              ),
            ],
          ),
        ],
        
        // Volume control
        if (widget.showVolumeControl) ...[
          const SizedBox(height: 8),
          Row(
            children: [
              Icon(
                Icons.volume_up,
                size: 20,
                color: theme.colorScheme.onSurfaceVariant,
              ),
              const SizedBox(width: 8),
              Text(
                'Volume',
                style: theme.textTheme.bodySmall,
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Slider(
                  value: _tempVolumeValue,
                  min: 0.0,
                  max: 1.0,
                  divisions: 10,
                  label: '${(_tempVolumeValue * 100).toInt()}%',
                  onChanged: _handleVolumeChange,
                  activeColor: theme.colorScheme.primary,
                ),
              ),
            ],
          ),
        ],
      ],
    );
  }
}
