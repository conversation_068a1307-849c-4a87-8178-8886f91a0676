import 'package:flutter/material.dart';
import 'package:choice_once_upon_a_time/models/enhanced_story_model.dart';

/// Overlay widget for displaying story choices with enhanced visual design
class StoryChoiceOverlay extends StatefulWidget {
  final List<ChoiceOptionModel> choices;
  final Function(String) onChoiceSelected;
  final VoidCallback? onDismiss;

  const StoryChoiceOverlay({
    super.key,
    required this.choices,
    required this.onChoiceSelected,
    this.onDismiss,
  });

  @override
  State<StoryChoiceOverlay> createState() => _StoryChoiceOverlayState();
}

class _StoryChoiceOverlayState extends State<StoryChoiceOverlay>
    with TickerProviderStateMixin {
  late final AnimationController _overlayController;
  late final AnimationController _choicesController;
  late final Animation<double> _overlayAnimation;
  late final Animation<double> _choicesAnimation;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _startAnimations();
  }

  void _initializeAnimations() {
    _overlayController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _choicesController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );

    _overlayAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _overlayController,
      curve: Curves.easeInOut,
    ));

    _choicesAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _choicesController,
      curve: Curves.elasticOut,
    ));
  }

  void _startAnimations() {
    _overlayController.forward();
    Future.delayed(const Duration(milliseconds: 150), () {
      if (mounted) {
        _choicesController.forward();
      }
    });
  }

  Future<void> _dismiss() async {
    await _choicesController.reverse();
    await _overlayController.reverse();
    if (mounted) {
      widget.onDismiss?.call();
    }
  }

  @override
  void dispose() {
    _overlayController.dispose();
    _choicesController.dispose();
    super.dispose();
  }

  Color _getChoiceColor(String emotion, ThemeData theme) {
    switch (emotion.toLowerCase()) {
      case 'brave':
      case 'confident':
        return Colors.blue.shade600;
      case 'kind':
      case 'helpful':
        return Colors.green.shade600;
      case 'curious':
      case 'excited':
        return Colors.orange.shade600;
      case 'cautious':
      case 'careful':
        return Colors.purple.shade600;
      case 'mischievous':
      case 'playful':
        return Colors.pink.shade600;
      default:
        return theme.colorScheme.primary;
    }
  }

  IconData _getChoiceIcon(String emotion) {
    switch (emotion.toLowerCase()) {
      case 'brave':
      case 'confident':
        return Icons.shield;
      case 'kind':
      case 'helpful':
        return Icons.favorite;
      case 'curious':
      case 'excited':
        return Icons.explore;
      case 'cautious':
      case 'careful':
        return Icons.visibility;
      case 'mischievous':
      case 'playful':
        return Icons.emoji_emotions;
      default:
        return Icons.touch_app;
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final screenSize = MediaQuery.of(context).size;
    final isSmallScreen = screenSize.width < 600;

    return AnimatedBuilder(
      animation: _overlayAnimation,
      builder: (context, child) {
        return Opacity(
          opacity: _overlayAnimation.value,
          child: Container(
            color: Colors.black.withValues(alpha: 0.7 * _overlayAnimation.value),
            child: SafeArea(
              child: Center(
                child: SingleChildScrollView(
                  padding: EdgeInsets.all(isSmallScreen ? 16.0 : 24.0),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      // Header
                      Container(
                        margin: const EdgeInsets.only(bottom: 24),
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          color: theme.colorScheme.surface,
                          borderRadius: BorderRadius.circular(16),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withValues(alpha: 0.1),
                              blurRadius: 8,
                              offset: const Offset(0, 4),
                            ),
                          ],
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(
                              Icons.psychology,
                              color: theme.colorScheme.primary,
                              size: 24,
                            ),
                            const SizedBox(width: 12),
                            Text(
                              'What would you like to do?',
                              style: theme.textTheme.titleLarge?.copyWith(
                                color: theme.colorScheme.onSurface,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ],
                        ),
                      ),

                      // Choices
                      AnimatedBuilder(
                        animation: _choicesAnimation,
                        builder: (context, child) {
                          return Transform.scale(
                            scale: _choicesAnimation.value,
                            child: Column(
                              children: widget.choices.asMap().entries.map((entry) {
                                final index = entry.key;
                                final choice = entry.value;
                                final delay = index * 100;

                                return AnimatedBuilder(
                                  animation: _choicesController,
                                  builder: (context, child) {
                                    final progress = (_choicesController.value * 1000 - delay).clamp(0.0, 1000.0) / 1000.0;
                                    
                                    return Transform.translate(
                                      offset: Offset(0, (1 - progress) * 50),
                                      child: Opacity(
                                        opacity: progress,
                                        child: Container(
                                          width: double.infinity,
                                          margin: const EdgeInsets.only(bottom: 16),
                                          child: _buildChoiceButton(choice, theme, isSmallScreen),
                                        ),
                                      ),
                                    );
                                  },
                                );
                              }).toList(),
                            ),
                          );
                        },
                      ),

                      // Dismiss button
                      if (widget.onDismiss != null)
                        TextButton.icon(
                          onPressed: _dismiss,
                          icon: const Icon(Icons.close),
                          label: const Text('Cancel'),
                          style: TextButton.styleFrom(
                            foregroundColor: theme.colorScheme.onSurfaceVariant,
                          ),
                        ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildChoiceButton(ChoiceOptionModel choice, ThemeData theme, bool isSmallScreen) {
    final choiceColor = _getChoiceColor(choice.visual, theme);
    final choiceIcon = _getChoiceIcon(choice.visual);

    return ElevatedButton(
      onPressed: () => widget.onChoiceSelected(choice.next),
      style: ElevatedButton.styleFrom(
        backgroundColor: choiceColor,
        foregroundColor: Colors.white,
        padding: EdgeInsets.all(isSmallScreen ? 16.0 : 20.0),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        elevation: 4,
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              choiceIcon,
              color: Colors.white,
              size: isSmallScreen ? 20 : 24,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  choice.option,
                  style: TextStyle(
                    fontSize: isSmallScreen ? 16.0 : 18.0,
                    fontWeight: FontWeight.w600,
                    color: Colors.white,
                  ),
                ),
                if (choice.visual.isNotEmpty) ...[
                  const SizedBox(height: 4),
                  Text(
                    choice.visual.toUpperCase(),
                    style: TextStyle(
                      fontSize: isSmallScreen ? 12.0 : 14.0,
                      fontWeight: FontWeight.w500,
                      color: Colors.white.withValues(alpha: 0.8),
                      letterSpacing: 1.2,
                    ),
                  ),
                ],
              ],
            ),
          ),
          Icon(
            Icons.arrow_forward_ios,
            color: Colors.white.withValues(alpha: 0.8),
            size: isSmallScreen ? 16 : 20,
          ),
        ],
      ),
    );
  }
}
