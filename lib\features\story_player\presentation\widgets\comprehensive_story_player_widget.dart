import 'dart:async';
import 'dart:io';
import 'dart:math';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:choice_once_upon_a_time/models/story_model.dart';
import 'package:choice_once_upon_a_time/models/scene_model.dart';
import 'package:choice_once_upon_a_time/core/repositories/enhanced_story_repository.dart';
import 'package:choice_once_upon_a_time/core/services/story_download_service.dart';
import 'package:choice_once_upon_a_time/core/audio/unified_tts_service.dart';
import 'package:choice_once_upon_a_time/core/utils/app_logger.dart';

/// Comprehensive story player widget that implements all requirements:
/// - Scene-by-scene narration with sentence-level reading
/// - Word-by-word highlighting synchronized with TTS
/// - Proper timing for children to read subtitles
/// - Autoplay controls (off by default)
/// - Choice popup narration at child-friendly speed
/// - Background music/sound effects
/// - Fallback assets for missing files
/// - Vocabulary and moral discussion
class ComprehensiveStoryPlayerWidget extends ConsumerStatefulWidget {
  final String storyId;
  final VoidCallback? onStoryComplete;
  final VoidCallback? onExit;

  const ComprehensiveStoryPlayerWidget({
    super.key,
    required this.storyId,
    this.onStoryComplete,
    this.onExit,
  });

  @override
  ConsumerState<ComprehensiveStoryPlayerWidget> createState() => _ComprehensiveStoryPlayerWidgetState();
}

class _ComprehensiveStoryPlayerWidgetState extends ConsumerState<ComprehensiveStoryPlayerWidget>
    with TickerProviderStateMixin {
  
  // Story data
  StoryModel? _story;
  SceneModel? _currentScene;
  int _currentSceneIndex = 0;
  
  // Narration state
  List<String> _currentSentences = [];
  int _currentSentenceIndex = 0;
  List<String> _currentWords = [];
  int _currentWordIndex = 0;
  bool _isNarrating = false;
  bool _isPaused = false;
  bool _autoplayEnabled = false; // Default off as specified
  
  // UI state
  bool _showControls = true;
  bool _showChoices = false;
  bool _showVocabulary = false;
  bool _showMoralDiscussion = false;
  
  // Services
  UnifiedTTSService? _ttsService;
  StoryDownloadService? _downloadService;
  
  // Animation controllers
  late AnimationController _wordHighlightController;
  late AnimationController _sceneTransitionController;
  late Animation<double> _wordHighlightAnimation;
  late Animation<double> _sceneTransitionAnimation;
  
  // Timers
  Timer? _sentenceTimer;
  Timer? _wordTimer;
  Timer? _sceneDisplayTimer;
  
  // Settings
  double _narrationSpeed = 0.35; // Child-friendly speed (0.3-0.4 as specified)
  double _backgroundMusicVolume = 0.3;
  int _minimumSceneDisplayTime = 5000; // 5 seconds minimum as specified
  int _sentencePauseTime = 1500; // 1.5 seconds between sentences
  
  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _initializeServices();
    _loadStory();
  }

  @override
  void dispose() {
    _wordHighlightController.dispose();
    _sceneTransitionController.dispose();
    _sentenceTimer?.cancel();
    _wordTimer?.cancel();
    _sceneDisplayTimer?.cancel();
    _ttsService?.stop();
    super.dispose();
  }

  /// Initialize animation controllers
  void _initializeAnimations() {
    _wordHighlightController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    
    _sceneTransitionController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    
    _wordHighlightAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _wordHighlightController,
      curve: Curves.easeInOut,
    ));
    
    _sceneTransitionAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _sceneTransitionController,
      curve: Curves.easeInOut,
    ));
  }

  /// Initialize services
  void _initializeServices() {
    _ttsService = ref.read(unifiedTTSServiceProvider);
    _downloadService = ref.read(storyDownloadServiceProvider);
    
    // Configure TTS for child-friendly narration
    _ttsService?.setSpeechRate(_narrationSpeed);
    _ttsService?.setVolume(1.0);
  }

  /// Load story from repository
  Future<void> _loadStory() async {
    try {
      AppLogger.info('[COMPREHENSIVE_STORY_PLAYER] Loading story: ${widget.storyId}');
      
      final repository = ref.read(enhancedStoryRepositoryProvider);
      final story = await repository.getStoryById(widget.storyId);
      
      if (story == null) {
        AppLogger.error('[COMPREHENSIVE_STORY_PLAYER] Story not found: ${widget.storyId}');
        return;
      }
      
      setState(() {
        _story = story;
        _currentScene = story.scenes.isNotEmpty ? story.scenes[0] : null;
        _currentSceneIndex = 0;
      });
      
      if (_currentScene != null) {
        await _loadScene(_currentScene!);
      }
      
    } catch (e, stackTrace) {
      AppLogger.error('[COMPREHENSIVE_STORY_PLAYER] Error loading story', e, stackTrace);
    }
  }

  /// Load and prepare a scene for narration
  Future<void> _loadScene(Scene scene) async {
    try {
      AppLogger.debug('[COMPREHENSIVE_STORY_PLAYER] Loading scene: ${scene.id}');
      
      // Stop any current narration
      await _stopNarration();
      
      // Preload scene image
      await _preloadSceneImage(scene);
      
      // Prepare sentences for narration
      _prepareSentences(scene.text);
      
      // Start scene display timer (minimum 5 seconds)
      _startSceneDisplayTimer();
      
      // Start narration if autoplay is enabled
      if (_autoplayEnabled) {
        await _startSceneNarration();
      }
      
      setState(() {
        _currentScene = scene;
        _showChoices = false;
        _showVocabulary = false;
        _showMoralDiscussion = false;
      });
      
    } catch (e, stackTrace) {
      AppLogger.error('[COMPREHENSIVE_STORY_PLAYER] Error loading scene', e, stackTrace);
    }
  }

  /// Preload scene image with fallback
  Future<void> _preloadSceneImage(Scene scene) async {
    if (scene.imagePath == null) return;
    
    try {
      // Get proper asset path (handles both local and downloaded stories)
      final imagePath = await _downloadService?.getStoryAssetPath(
        widget.storyId, 
        scene.imagePath!,
      ) ?? scene.imagePath!;
      
      // Preload image
      if (imagePath.startsWith('assets/')) {
        await precacheImage(AssetImage(imagePath), context);
      } else {
        await precacheImage(FileImage(File(imagePath)), context);
      }
      
      AppLogger.debug('[COMPREHENSIVE_STORY_PLAYER] Preloaded image: $imagePath');
    } catch (e) {
      AppLogger.warning('[COMPREHENSIVE_STORY_PLAYER] Failed to preload image, will use fallback: $e');
    }
  }

  /// Prepare sentences for word-by-word narration
  void _prepareSentences(String text) {
    // Split text into sentences (considering various punctuation)
    _currentSentences = text
        .split(RegExp(r'[.!?]+'))
        .map((s) => s.trim())
        .where((s) => s.isNotEmpty)
        .toList();
    
    _currentSentenceIndex = 0;
    _currentWordIndex = 0;
    
    AppLogger.debug('[COMPREHENSIVE_STORY_PLAYER] Prepared ${_currentSentences.length} sentences for narration');
  }

  /// Start scene display timer (minimum display time)
  void _startSceneDisplayTimer() {
    _sceneDisplayTimer?.cancel();
    _sceneDisplayTimer = Timer(Duration(milliseconds: _minimumSceneDisplayTime), () {
      AppLogger.debug('[COMPREHENSIVE_STORY_PLAYER] Minimum scene display time completed');
    });
  }

  /// Start scene narration (sentence by sentence)
  Future<void> _startSceneNarration() async {
    if (_currentSentences.isEmpty || _isNarrating) return;
    
    setState(() {
      _isNarrating = true;
      _isPaused = false;
      _currentSentenceIndex = 0;
    });
    
    AppLogger.info('[COMPREHENSIVE_STORY_PLAYER] Starting scene narration');
    
    try {
      // Narrate each sentence with proper timing
      for (int i = 0; i < _currentSentences.length; i++) {
        if (!_isNarrating || _isPaused) break;
        
        setState(() {
          _currentSentenceIndex = i;
        });
        
        await _narrateSentence(_currentSentences[i]);
        
        // Pause between sentences for children to read
        if (i < _currentSentences.length - 1) {
          await Future.delayed(Duration(milliseconds: _sentencePauseTime));
        }
      }
      
      // Narration completed
      await _handleNarrationComplete();
      
    } catch (e, stackTrace) {
      AppLogger.error('[COMPREHENSIVE_STORY_PLAYER] Error during narration', e, stackTrace);
    }
  }

  /// Narrate a single sentence with word-by-word highlighting
  Future<void> _narrateSentence(String sentence) async {
    if (sentence.trim().isEmpty) return;
    
    AppLogger.debug('[COMPREHENSIVE_STORY_PLAYER] Narrating sentence: ${sentence.substring(0, min(50, sentence.length))}...');
    
    // Prepare words for highlighting
    _currentWords = sentence.split(' ').where((w) => w.trim().isNotEmpty).toList();
    _currentWordIndex = 0;
    
    // Start TTS narration
    final completer = Completer<void>();
    
    // Set up word highlighting timer
    _startWordHighlighting(sentence, completer);
    
    // Start TTS
    await _ttsService?.speakText(sentence);
    
    // Wait for completion
    await completer.future;
  }

  /// Start word-by-word highlighting synchronized with TTS
  void _startWordHighlighting(String sentence, Completer<void> completer) {
    if (_currentWords.isEmpty) {
      completer.complete();
      return;
    }
    
    // Calculate timing per word (approximate)
    final wordsPerMinute = 120; // Child-friendly pace
    final millisecondsPerWord = (60000 / wordsPerMinute).round();
    
    _wordTimer?.cancel();
    _wordTimer = Timer.periodic(Duration(milliseconds: millisecondsPerWord), (timer) {
      if (_currentWordIndex >= _currentWords.length || !_isNarrating) {
        timer.cancel();
        completer.complete();
        return;
      }
      
      setState(() {
        _currentWordIndex++;
      });
      
      // Animate word highlight
      _wordHighlightController.forward().then((_) {
        _wordHighlightController.reverse();
      });
    });
  }

  /// Handle narration completion
  Future<void> _handleNarrationComplete() async {
    setState(() {
      _isNarrating = false;
      _currentSentenceIndex = 0;
      _currentWordIndex = 0;
    });
    
    // Check if scene has choices
    if (_currentScene?.choices != null && _currentScene!.choices!.isNotEmpty) {
      await _showChoicePopup();
    } else if (_currentScene?.nextSceneId != null) {
      // Auto-advance or wait for user input based on autoplay setting
      if (_autoplayEnabled) {
        await _navigateToNextScene();
      } else {
        setState(() {
          _showControls = true;
        });
      }
    } else {
      // End of story - show vocabulary and moral discussion
      await _showStoryCompletion();
    }
  }

  /// Show choice popup with narration
  Future<void> _showChoicePopup() async {
    setState(() {
      _showChoices = true;
    });
    
    // Narrate choice prompt at child-friendly speed
    if (_currentScene?.choicePrompt != null) {
      await _ttsService?.speakText(_currentScene!.choicePrompt!);
    }
    
    // Narrate each choice option
    for (final choice in _currentScene!.choices!) {
      await Future.delayed(const Duration(milliseconds: 500));
      await _ttsService?.speakText(choice.text);
    }
  }

  /// Navigate to next scene
  Future<void> _navigateToNextScene() async {
    if (_story == null || _currentScene?.nextSceneId == null) return;
    
    final nextSceneId = _currentScene!.nextSceneId!;
    final nextScene = _story!.scenes.firstWhere(
      (scene) => scene.id == nextSceneId,
      orElse: () => throw Exception('Next scene not found: $nextSceneId'),
    );
    
    // Animate scene transition
    await _sceneTransitionController.forward();
    
    await _loadScene(nextScene);
    
    await _sceneTransitionController.reverse();
    
    setState(() {
      _currentSceneIndex++;
    });
  }

  /// Show story completion with vocabulary and moral discussion
  Future<void> _showStoryCompletion() async {
    setState(() {
      _showVocabulary = true;
    });
    
    // Narrate vocabulary explanations
    await _narrateVocabulary();
    
    // Show moral discussion
    setState(() {
      _showMoralDiscussion = true;
    });
    
    await _narrateMoralLesson();
  }

  /// Narrate vocabulary explanations
  Future<void> _narrateVocabulary() async {
    // TODO: Implement vocabulary narration based on story vocabulary data
    AppLogger.info('[COMPREHENSIVE_STORY_PLAYER] Narrating vocabulary explanations');
  }

  /// Narrate moral lesson
  Future<void> _narrateMoralLesson() async {
    // TODO: Implement moral lesson narration based on story moral data
    AppLogger.info('[COMPREHENSIVE_STORY_PLAYER] Narrating moral lesson');
  }

  /// Stop current narration
  Future<void> _stopNarration() async {
    setState(() {
      _isNarrating = false;
      _isPaused = false;
    });
    
    _sentenceTimer?.cancel();
    _wordTimer?.cancel();
    await _ttsService?.stop();
  }

  /// Toggle play/pause
  Future<void> _togglePlayPause() async {
    if (_isNarrating) {
      if (_isPaused) {
        await _ttsService?.resume();
        setState(() {
          _isPaused = false;
        });
      } else {
        await _ttsService?.pause();
        setState(() {
          _isPaused = true;
        });
      }
    } else {
      await _startSceneNarration();
    }
  }

  /// Toggle autoplay setting
  void _toggleAutoplay() {
    setState(() {
      _autoplayEnabled = !_autoplayEnabled;
    });
    AppLogger.info('[COMPREHENSIVE_STORY_PLAYER] Autoplay ${_autoplayEnabled ? 'enabled' : 'disabled'}');
  }

  @override
  Widget build(BuildContext context) {
    if (_story == null || _currentScene == null) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    return Scaffold(
      body: Stack(
        children: [
          // Background image
          _buildSceneImage(),
          
          // Story content overlay
          _buildContentOverlay(),
          
          // Controls overlay
          if (_showControls) _buildControlsOverlay(),
          
          // Choice popup
          if (_showChoices) _buildChoicePopup(),
          
          // Vocabulary popup
          if (_showVocabulary) _buildVocabularyPopup(),
          
          // Moral discussion popup
          if (_showMoralDiscussion) _buildMoralDiscussionPopup(),
        ],
      ),
    );
  }

  /// Build scene image with fallback
  Widget _buildSceneImage() {
    // TODO: Implement scene image display with fallback to default_image.png
    return Container(
      width: double.infinity,
      height: double.infinity,
      color: Colors.blue[100],
      child: const Center(
        child: Text('Scene Image Placeholder'),
      ),
    );
  }

  /// Build content overlay with subtitles and word highlighting
  Widget _buildContentOverlay() {
    // TODO: Implement subtitle display with word-by-word highlighting
    return Positioned(
      bottom: 120,
      left: 16,
      right: 16,
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.black.withValues(alpha: 0.7),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Text(
          _currentSentences.isNotEmpty ? _currentSentences[_currentSentenceIndex] : '',
          style: const TextStyle(
            color: Colors.white,
            fontSize: 18,
            fontWeight: FontWeight.w500,
          ),
          textAlign: TextAlign.center,
        ),
      ),
    );
  }

  /// Build controls overlay
  Widget _buildControlsOverlay() {
    // TODO: Implement control buttons (play/pause, next, autoplay toggle)
    return Positioned(
      bottom: 20,
      left: 16,
      right: 16,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          IconButton(
            onPressed: _togglePlayPause,
            icon: Icon(_isNarrating && !_isPaused ? Icons.pause : Icons.play_arrow),
            iconSize: 32,
            color: Colors.white,
          ),
          IconButton(
            onPressed: _toggleAutoplay,
            icon: Icon(_autoplayEnabled ? Icons.auto_mode : Icons.touch_app),
            iconSize: 32,
            color: Colors.white,
          ),
          IconButton(
            onPressed: widget.onExit,
            icon: const Icon(Icons.exit_to_app),
            iconSize: 32,
            color: Colors.white,
          ),
        ],
      ),
    );
  }

  /// Build choice popup
  Widget _buildChoicePopup() {
    // TODO: Implement choice popup with narration
    return Container();
  }

  /// Build vocabulary popup
  Widget _buildVocabularyPopup() {
    // TODO: Implement vocabulary explanation popup
    return Container();
  }

  /// Build moral discussion popup
  Widget _buildMoralDiscussionPopup() {
    // TODO: Implement moral lesson discussion popup
    return Container();
  }
}
