import 'dart:io';
import 'dart:typed_data';
import 'package:archive/archive.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:dio/dio.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart' as path;
import 'package:choice_once_upon_a_time/core/utils/app_logger.dart';
import 'package:choice_once_upon_a_time/core/services/fallback_asset_manager.dart';

/// Service for downloading and managing story assets from Firebase
class StoryDownloadService {
  static const String _logPrefix = 'STORY_DOWNLOAD';
  final Dio _dio;
  final FirebaseFirestore _firestore;

  StoryDownloadService({
    Dio? dio,
    FirebaseFirestore? firestore,
  }) : _dio = dio ?? Dio(),
       _firestore = firestore ?? FirebaseFirestore.instance;

  /// Download story from Firebase and extract to local storage
  Future<bool> downloadStory(String storyId, {
    Function(double)? onProgress,
  }) async {
    try {
      AppLogger.info('$_logPrefix: Starting download for story $storyId');
      
      // Get story metadata from Firebase
      final storyDoc = await _firestore.collection('stories').doc(storyId).get();
      if (!storyDoc.exists) {
        AppLogger.error('$_logPrefix: Story $storyId not found in Firebase');
        return false;
      }

      final storyData = storyDoc.data() as Map<String, dynamic>;
      final zipUrl = storyData['zip_url'] as String?;
      
      if (zipUrl == null || zipUrl.isEmpty) {
        AppLogger.error('$_logPrefix: No zip_url found for story $storyId');
        return false;
      }

      // Get local storage directory
      final localDir = await _getStoryDirectory(storyId);
      if (await localDir.exists()) {
        AppLogger.info('$_logPrefix: Story $storyId already exists locally');
        return true;
      }

      // Download ZIP file
      final zipFile = await _downloadZipFile(zipUrl, storyId, onProgress);
      if (zipFile == null) {
        AppLogger.error('$_logPrefix: Failed to download ZIP for story $storyId');
        return false;
      }

      // Extract ZIP file
      final success = await _extractZipFile(zipFile, localDir);
      if (!success) {
        AppLogger.error('$_logPrefix: Failed to extract ZIP for story $storyId');
        return false;
      }

      // Clean up ZIP file
      try {
        await zipFile.delete();
      } catch (e) {
        AppLogger.warning('$_logPrefix: Failed to delete ZIP file: $e');
      }

      AppLogger.info('$_logPrefix: Successfully downloaded and extracted story $storyId');
      return true;

    } catch (e, stackTrace) {
      AppLogger.error('$_logPrefix: Error downloading story $storyId', e, stackTrace);
      return false;
    }
  }

  /// Check if story is downloaded locally
  Future<bool> isStoryDownloaded(String storyId) async {
    try {
      final storyDir = await _getStoryDirectory(storyId);
      final storyJsonFile = File(path.join(storyDir.path, 'story.json'));
      return await storyJsonFile.exists();
    } catch (e) {
      AppLogger.warning('$_logPrefix: Error checking if story $storyId is downloaded: $e');
      return false;
    }
  }

  /// Get local path for story assets with proper fallback management
  Future<String> getStoryAssetPath(String storyId, String assetPath) async {
    try {
      final storyDir = await _getStoryDirectory(storyId);
      final fullPath = path.join(storyDir.path, assetPath);

      // Check if file exists
      final file = File(fullPath);
      if (await file.exists()) {
        return fullPath;
      }

      // Use fallback asset manager for proper fallback handling
      final fallbackManager = FallbackAssetManager();
      return await fallbackManager.getStoryAssetWithFallback(
        storyId: storyId,
        assetPath: assetPath,
      );
    } catch (e) {
      AppLogger.warning('$_logPrefix: Error getting asset path for $storyId/$assetPath: $e');

      // Use fallback asset manager for error cases too
      final fallbackManager = FallbackAssetManager();
      return await fallbackManager.getStoryAssetWithFallback(
        storyId: storyId,
        assetPath: assetPath,
      );
    }
  }

  /// Delete downloaded story
  Future<bool> deleteStory(String storyId) async {
    try {
      final storyDir = await _getStoryDirectory(storyId);
      if (await storyDir.exists()) {
        await storyDir.delete(recursive: true);
        AppLogger.info('$_logPrefix: Deleted story $storyId from local storage');
        return true;
      }
      return false;
    } catch (e, stackTrace) {
      AppLogger.error('$_logPrefix: Error deleting story $storyId', e, stackTrace);
      return false;
    }
  }

  /// Get download progress for a story
  Stream<double> getDownloadProgress(String storyId) async* {
    // This would be implemented with a more sophisticated progress tracking system
    // For now, return a simple stream
    yield 0.0;
  }

  /// Get list of all downloaded stories
  Future<List<String>> getDownloadedStories() async {
    try {
      final downloadDir = await _getDownloadDirectory();
      if (!await downloadDir.exists()) {
        return [];
      }

      final stories = <String>[];
      await for (final entity in downloadDir.list()) {
        if (entity is Directory) {
          final storyId = path.basename(entity.path);
          final storyJsonFile = File(path.join(entity.path, 'story.json'));
          if (await storyJsonFile.exists()) {
            stories.add(storyId);
          }
        }
      }

      AppLogger.info('$_logPrefix: Found ${stories.length} downloaded stories');
      return stories;
    } catch (e, stackTrace) {
      AppLogger.error('$_logPrefix: Error getting downloaded stories', e, stackTrace);
      return [];
    }
  }

  /// Download ZIP file from URL
  Future<File?> _downloadZipFile(String zipUrl, String storyId, Function(double)? onProgress) async {
    try {
      final tempDir = await getTemporaryDirectory();
      final zipFile = File(path.join(tempDir.path, '$storyId.zip'));

      await _dio.download(
        zipUrl,
        zipFile.path,
        onReceiveProgress: (received, total) {
          if (total > 0 && onProgress != null) {
            final progress = received / total;
            onProgress(progress);
          }
        },
      );

      AppLogger.info('$_logPrefix: Downloaded ZIP file for story $storyId');
      return zipFile;
    } catch (e, stackTrace) {
      AppLogger.error('$_logPrefix: Error downloading ZIP file for story $storyId', e, stackTrace);
      return null;
    }
  }

  /// Extract ZIP file to story directory
  Future<bool> _extractZipFile(File zipFile, Directory targetDir) async {
    try {
      // Read ZIP file
      final bytes = await zipFile.readAsBytes();
      final archive = ZipDecoder().decodeBytes(bytes);

      // Create target directory
      await targetDir.create(recursive: true);

      // Extract files
      for (final file in archive) {
        final filename = file.name;
        final filePath = path.join(targetDir.path, filename);

        if (file.isFile) {
          final data = file.content as List<int>;
          final extractedFile = File(filePath);
          await extractedFile.create(recursive: true);
          await extractedFile.writeAsBytes(data);
        } else {
          await Directory(filePath).create(recursive: true);
        }
      }

      AppLogger.info('$_logPrefix: Extracted ${archive.length} files from ZIP');
      return true;
    } catch (e, stackTrace) {
      AppLogger.error('$_logPrefix: Error extracting ZIP file', e, stackTrace);
      return false;
    }
  }

  /// Get story directory path
  Future<Directory> _getStoryDirectory(String storyId) async {
    final downloadDir = await _getDownloadDirectory();
    return Directory(path.join(downloadDir.path, storyId));
  }

  /// Get download directory
  Future<Directory> _getDownloadDirectory() async {
    final appDir = await getApplicationDocumentsDirectory();
    return Directory(path.join(appDir.path, 'downloaded_stories'));
  }


}

/// Provider for story download service
final storyDownloadServiceProvider = Provider<StoryDownloadService>((ref) {
  return StoryDownloadService();
});

/// Provider for tracking download progress
final downloadProgressProvider = StreamProvider.family<double, String>((ref, storyId) {
  final downloadService = ref.watch(storyDownloadServiceProvider);
  return downloadService.getDownloadProgress(storyId);
});

/// Provider for downloaded stories list
final downloadedStoriesProvider = FutureProvider<List<String>>((ref) async {
  final downloadService = ref.watch(storyDownloadServiceProvider);
  return await downloadService.getDownloadedStories();
});
