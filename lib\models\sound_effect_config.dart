import 'package:json_annotation/json_annotation.dart';

part 'sound_effect_config.g.dart';

/// Configuration for sound effects in a scene
@JsonSerializable()
class SoundEffectConfig {
  /// When this sound effect should be triggered
  /// (e.g., "onSegmentStart", "onChoiceMade", "onDiscoveryTap")
  final String trigger;

  /// Reference to a specific segment ID if trigger is "onSegmentStart"
  final String? segmentIdRef;

  /// URL to the sound effect file in Firebase Storage
  final String sfxUrl;

  /// Volume level (0.0 to 1.0)
  final double volume;

  const SoundEffectConfig({
    required this.trigger,
    this.segmentIdRef,
    required this.sfxUrl,
    this.volume = 1.0,
  });

  /// Creates a SoundEffectConfig from JSON
  factory SoundEffectConfig.fromJson(Map<String, dynamic> json) =>
      _$SoundEffectConfigFromJson(json);

  /// Converts the SoundEffectConfig to JSON
  Map<String, dynamic> toJson() => _$SoundEffectConfigToJson(this);

  /// Creates a copy of this model with updated fields
  SoundEffectConfig copyWith({
    String? trigger,
    String? segmentIdRef,
    String? sfxUrl,
    double? volume,
  }) {
    return SoundEffectConfig(
      trigger: trigger ?? this.trigger,
      segmentIdRef: segmentIdRef ?? this.segmentIdRef,
      sfxUrl: sfxUrl ?? this.sfxUrl,
      volume: volume ?? this.volume,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is SoundEffectConfig && 
           other.trigger == trigger &&
           other.segmentIdRef == segmentIdRef &&
           other.sfxUrl == sfxUrl &&
           other.volume == volume;
  }

  @override
  int get hashCode => Object.hash(trigger, segmentIdRef, sfxUrl, volume);

  @override
  String toString() {
    return 'SoundEffectConfig(trigger: $trigger, sfxUrl: $sfxUrl, volume: $volume)';
  }
}
