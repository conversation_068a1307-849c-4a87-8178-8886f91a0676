// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'app_settings_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

AppSettingsModel _$AppSettingsModelFromJson(Map<String, dynamic> json) =>
    AppSettingsModel(
      minRequiredAppVersion: json['minRequiredAppVersion'] as String,
      isMaintenanceMode: json['isMaintenanceMode'] as bool? ?? false,
      maintenanceMessage:
          (json['maintenanceMessage'] as Map<String, dynamic>?)?.map(
                (k, e) => MapEntry(k, e as String),
              ) ??
              const {},
      latestNewsUrl: (json['latestNewsUrl'] as Map<String, dynamic>?)?.map(
        (k, e) => MapEntry(k, e as String),
      ),
      defaultMoralValuesOrder:
          (json['defaultMoralValuesOrder'] as List<dynamic>?)
                  ?.map((e) => e as String)
                  .toList() ??
              const [
                'Honesty',
                'Empathy',
                'Perseverance',
                'Kindness',
                'Courage',
                'Responsibility'
              ],
    );

Map<String, dynamic> _$AppSettingsModelToJson(AppSettingsModel instance) =>
    <String, dynamic>{
      'minRequiredAppVersion': instance.minRequiredAppVersion,
      'isMaintenanceMode': instance.isMaintenanceMode,
      'maintenanceMessage': instance.maintenanceMessage,
      'latestNewsUrl': instance.latestNewsUrl,
      'defaultMoralValuesOrder': instance.defaultMoralValuesOrder,
    };
