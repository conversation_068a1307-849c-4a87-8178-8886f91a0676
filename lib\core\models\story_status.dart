import 'package:cloud_firestore/cloud_firestore.dart';

/// Enum representing the availability status of a story
enum StoryStatus {
  /// Story is available offline (bundled with app)
  offline,
  
  /// Story is available for download from Firebase
  downloadable,
  
  /// Story has been downloaded and is available locally
  downloaded,
  
  /// Story is currently being downloaded
  downloading,
  
  /// Story download failed
  downloadFailed,
  
  /// Story is locked (requires premium subscription)
  locked,
  
  /// Story status is unknown or being determined
  unknown,
}

/// Extension to provide human-readable descriptions for story status
extension StoryStatusExtension on StoryStatus {
  /// Get display text for the status
  String get displayText {
    switch (this) {
      case StoryStatus.offline:
        return 'Available Offline';
      case StoryStatus.downloadable:
        return 'Download Available';
      case StoryStatus.downloaded:
        return 'Downloaded';
      case StoryStatus.downloading:
        return 'Downloading...';
      case StoryStatus.downloadFailed:
        return 'Download Failed';
      case StoryStatus.locked:
        return 'Premium Required';
      case StoryStatus.unknown:
        return 'Checking...';
    }
  }

  /// Get icon name for the status
  String get iconName {
    switch (this) {
      case StoryStatus.offline:
        return 'offline_pin';
      case StoryStatus.downloadable:
        return 'download';
      case StoryStatus.downloaded:
        return 'download_done';
      case StoryStatus.downloading:
        return 'downloading';
      case StoryStatus.downloadFailed:
        return 'error';
      case StoryStatus.locked:
        return 'lock';
      case StoryStatus.unknown:
        return 'help';
    }
  }

  /// Check if story can be played
  bool get canPlay {
    return this == StoryStatus.offline || 
           this == StoryStatus.downloaded;
  }

  /// Check if story can be downloaded
  bool get canDownload {
    return this == StoryStatus.downloadable || 
           this == StoryStatus.downloadFailed;
  }

  /// Check if story is in a loading state
  bool get isLoading {
    return this == StoryStatus.downloading || 
           this == StoryStatus.unknown;
  }
}

/// Model representing story metadata with status information
class StoryMetadata {
  final String id;
  final String title;
  final String description;
  final String? coverImageUrl;
  final String? coverImagePath; // Local asset path
  final String? zipUrl;
  final StoryStatus status;
  final double downloadProgress;
  final bool isPremium;
  final List<String> tags;
  final int ageRange;
  final int estimatedDuration; // in minutes
  final DateTime? lastUpdated;

  const StoryMetadata({
    required this.id,
    required this.title,
    required this.description,
    this.coverImageUrl,
    this.coverImagePath,
    this.zipUrl,
    required this.status,
    this.downloadProgress = 0.0,
    this.isPremium = false,
    this.tags = const [],
    this.ageRange = 5,
    this.estimatedDuration = 10,
    this.lastUpdated,
  });

  /// Create from Firebase document
  factory StoryMetadata.fromFirebase(String id, Map<String, dynamic> data) {
    return StoryMetadata(
      id: id,
      title: data['title'] as String? ?? 'Untitled Story',
      description: data['description'] as String? ?? '',
      coverImageUrl: data['cover_image_url'] as String?,
      zipUrl: data['zip_url'] as String?,
      status: StoryStatus.downloadable, // Will be updated by repository
      isPremium: data['is_premium'] as bool? ?? false,
      tags: List<String>.from(data['tags'] as List? ?? []),
      ageRange: data['age_range'] as int? ?? 5,
      estimatedDuration: data['estimated_duration'] as int? ?? 10,
      lastUpdated: data['last_updated'] != null 
          ? (data['last_updated'] as Timestamp).toDate()
          : null,
    );
  }

  /// Create from local asset
  factory StoryMetadata.fromAsset(String id, Map<String, dynamic> data, String assetPath) {
    return StoryMetadata(
      id: id,
      title: data['title'] as String? ?? 'Untitled Story',
      description: data['description'] as String? ?? '',
      coverImagePath: '$assetPath/cover.jpg', // Assuming cover image naming convention
      status: StoryStatus.offline,
      isPremium: false, // Asset stories are always free
      tags: List<String>.from(data['tags'] as List? ?? []),
      ageRange: data['age_range'] as int? ?? 5,
      estimatedDuration: data['estimated_duration'] as int? ?? 10,
    );
  }

  /// Copy with updated fields
  StoryMetadata copyWith({
    String? id,
    String? title,
    String? description,
    String? coverImageUrl,
    String? coverImagePath,
    String? zipUrl,
    StoryStatus? status,
    double? downloadProgress,
    bool? isPremium,
    List<String>? tags,
    int? ageRange,
    int? estimatedDuration,
    DateTime? lastUpdated,
  }) {
    return StoryMetadata(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      coverImageUrl: coverImageUrl ?? this.coverImageUrl,
      coverImagePath: coverImagePath ?? this.coverImagePath,
      zipUrl: zipUrl ?? this.zipUrl,
      status: status ?? this.status,
      downloadProgress: downloadProgress ?? this.downloadProgress,
      isPremium: isPremium ?? this.isPremium,
      tags: tags ?? this.tags,
      ageRange: ageRange ?? this.ageRange,
      estimatedDuration: estimatedDuration ?? this.estimatedDuration,
      lastUpdated: lastUpdated ?? this.lastUpdated,
    );
  }

  /// Get cover image path (local or URL)
  String? get coverImage {
    return coverImagePath ?? coverImageUrl;
  }

  /// Check if story has a cover image
  bool get hasCoverImage {
    return coverImagePath != null || coverImageUrl != null;
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is StoryMetadata && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'StoryMetadata(id: $id, title: $title, status: $status)';
  }
}
