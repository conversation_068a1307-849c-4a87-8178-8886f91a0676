/// Web-compatible version of DownloadedStoryEntry
/// Uses regular Dart classes instead of Isar annotations
class DownloadedStoryEntry {
  /// Auto-increment ID
  int id;

  /// Unique story identifier
  String storyId;

  /// Story version
  String version;

  /// When the story was downloaded
  DateTime downloadedAt;

  /// Path to the stored full story JSON file
  String localStoryJsonPath;

  /// List of asset filenames/IDs stored locally
  List<String> downloadedAssetIds;

  /// Whether the story is fully downloaded and ready for offline use
  bool isFullyDownloaded;

  /// Estimated total size in MB
  int totalSizeMb;

  /// Last time this entry was accessed
  DateTime lastAccessedAt;

  DownloadedStoryEntry({
    this.id = 0,
    required this.storyId,
    required this.version,
    required this.downloadedAt,
    required this.localStoryJsonPath,
    required this.downloadedAssetIds,
    this.isFullyDownloaded = false,
    this.totalSizeMb = 0,
    required this.lastAccessedAt,
  });

  /// Creates a copy of this entry with updated fields
  DownloadedStoryEntry copyWith({
    int? id,
    String? storyId,
    String? version,
    DateTime? downloadedAt,
    String? localStoryJsonPath,
    List<String>? downloadedAssetIds,
    bool? isFullyDownloaded,
    int? totalSizeMb,
    DateTime? lastAccessedAt,
  }) {
    return DownloadedStoryEntry(
      id: id ?? this.id,
      storyId: storyId ?? this.storyId,
      version: version ?? this.version,
      downloadedAt: downloadedAt ?? this.downloadedAt,
      localStoryJsonPath: localStoryJsonPath ?? this.localStoryJsonPath,
      downloadedAssetIds: downloadedAssetIds ?? this.downloadedAssetIds,
      isFullyDownloaded: isFullyDownloaded ?? this.isFullyDownloaded,
      totalSizeMb: totalSizeMb ?? this.totalSizeMb,
      lastAccessedAt: lastAccessedAt ?? this.lastAccessedAt,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is DownloadedStoryEntry && 
           other.storyId == storyId && 
           other.version == version;
  }

  @override
  int get hashCode => Object.hash(storyId, version);

  @override
  String toString() {
    return 'DownloadedStoryEntry(storyId: $storyId, version: $version, fullyDownloaded: $isFullyDownloaded)';
  }
}
