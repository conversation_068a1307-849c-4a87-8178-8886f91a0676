import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:url_launcher/url_launcher.dart';

/// Help & Support / FAQ screen (Screen 12.3)
class HelpSupportScreen extends StatefulWidget {
  const HelpSupportScreen({super.key});

  @override
  State<HelpSupportScreen> createState() => _HelpSupportScreenState();
}

class _HelpSupportScreenState extends State<HelpSupportScreen> {
  final TextEditingController _searchController = TextEditingController();
  String _searchQuery = '';

  final List<FAQCategory> _faqCategories = [
    FAQCategory(
      title: 'Getting Started',
      icon: Icons.play_circle_outline,
      faqs: [
        FAQ(
          question: 'How do I start a story?',
          answer: 'Simply tap on any story cover from the main library screen. Your child can then tap "Play" to begin the adventure!',
        ),
        FAQ(
          question: 'What age is this app suitable for?',
          answer: 'Choice: Once Upon A Time is designed specifically for children ages 4-7, with age-appropriate content and simple interactions.',
        ),
        FAQ(
          question: 'Do I need an internet connection?',
          answer: 'You can download stories for offline reading. Once downloaded, no internet connection is needed to enjoy the stories.',
        ),
      ],
    ),
    FAQCategory(
      title: 'Stories & Content',
      icon: Icons.auto_stories,
      faqs: [
        FAQ(
          question: 'How do the interactive choices work?',
          answer: 'During stories, your child will encounter decision points where they can choose what the character should do. Each choice leads to different story outcomes.',
        ),
        FAQ(
          question: 'Are the stories educational?',
          answer: 'Yes! Each story is designed to teach important moral values like honesty, kindness, and responsibility through engaging narratives.',
        ),
        FAQ(
          question: 'Can my child replay stories?',
          answer: 'Absolutely! Children can replay stories as many times as they like and explore different choices to see various outcomes.',
        ),
      ],
    ),
    FAQCategory(
      title: 'Audio & Settings',
      icon: Icons.volume_up,
      faqs: [
        FAQ(
          question: 'How do I adjust the volume?',
          answer: 'Go to Parent Zone > Sound Settings to control master volume, background music, and sound effects.',
        ),
        FAQ(
          question: 'Can I turn off the narrator?',
          answer: 'The narrator is an essential part of the experience, but you can adjust the volume or use headphones for quieter listening.',
        ),
        FAQ(
          question: 'Why isn\'t the audio working?',
          answer: 'Check your device volume, ensure the app has audio permissions, and verify that "Do Not Disturb" mode isn\'t blocking app sounds.',
        ),
      ],
    ),
    FAQCategory(
      title: 'Downloads & Storage',
      icon: Icons.download,
      faqs: [
        FAQ(
          question: 'How do I download stories for offline use?',
          answer: 'Tap the download icon on any story cover. Downloaded stories will be available even without an internet connection.',
        ),
        FAQ(
          question: 'How much storage do stories use?',
          answer: 'Stories typically use 15-25 MB each. You can check exact sizes in Parent Zone > Manage Downloads.',
        ),
        FAQ(
          question: 'How do I delete downloaded stories?',
          answer: 'Go to Parent Zone > Manage Downloads and tap the delete button next to any story you want to remove.',
        ),
      ],
    ),
    FAQCategory(
      title: 'Account & Subscription',
      icon: Icons.account_circle,
      faqs: [
        FAQ(
          question: 'Do I need to create an account?',
          answer: 'An account is optional but recommended for accessing parent settings and managing subscriptions across devices.',
        ),
        FAQ(
          question: 'What\'s included in the free version?',
          answer: 'The free version includes access to select stories. Premium subscription unlocks the complete library of stories.',
        ),
        FAQ(
          question: 'How do I cancel my subscription?',
          answer: 'You can manage your subscription through your device\'s app store settings (App Store or Google Play Store).',
        ),
      ],
    ),
  ];

  List<FAQCategory> get _filteredCategories {
    if (_searchQuery.isEmpty) return _faqCategories;
    
    return _faqCategories.map((category) {
      final filteredFAQs = category.faqs.where((faq) =>
        faq.question.toLowerCase().contains(_searchQuery.toLowerCase()) ||
        faq.answer.toLowerCase().contains(_searchQuery.toLowerCase())
      ).toList();
      
      return FAQCategory(
        title: category.title,
        icon: category.icon,
        faqs: filteredFAQs,
      );
    }).where((category) => category.faqs.isNotEmpty).toList();
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Help & Support'),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => context.go('/parent_zone'),
        ),
      ),
      body: Column(
        children: [
          // Header and search
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: theme.colorScheme.primary.withOpacity(0.1),
            ),
            child: Column(
              children: [
                Row(
                  children: [
                    Icon(
                      Icons.help_outline,
                      color: theme.colorScheme.primary,
                      size: 28,
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Text(
                        'Frequently Asked Questions',
                        style: theme.textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: theme.colorScheme.primary,
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                TextField(
                  controller: _searchController,
                  decoration: InputDecoration(
                    hintText: 'Search for help...',
                    prefixIcon: const Icon(Icons.search),
                    suffixIcon: _searchQuery.isNotEmpty
                        ? IconButton(
                            icon: const Icon(Icons.clear),
                            onPressed: () {
                              _searchController.clear();
                              setState(() => _searchQuery = '');
                            },
                          )
                        : null,
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: BorderSide.none,
                    ),
                    filled: true,
                    fillColor: Colors.white,
                  ),
                  onChanged: (value) {
                    setState(() => _searchQuery = value);
                  },
                ),
              ],
            ),
          ),

          // FAQ content
          Expanded(
            child: _filteredCategories.isEmpty
                ? _buildNoResultsState(theme)
                : ListView.builder(
                    padding: const EdgeInsets.all(16),
                    itemCount: _filteredCategories.length,
                    itemBuilder: (context, index) {
                      final category = _filteredCategories[index];
                      return _buildCategoryCard(theme, category);
                    },
                  ),
          ),

          // Contact section
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.blue[50],
              border: Border(
                top: BorderSide(color: Colors.blue[200]!),
              ),
            ),
            child: Column(
              children: [
                Row(
                  children: [
                    Icon(
                      Icons.contact_support,
                      color: Colors.blue[700],
                    ),
                    const SizedBox(width: 8),
                    Text(
                      'Still need help?',
                      style: theme.textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: Colors.blue[700],
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                Text(
                  'Can\'t find what you\'re looking for? We\'re here to help!',
                  style: theme.textTheme.bodyMedium?.copyWith(
                    color: Colors.blue[700],
                  ),
                ),
                const SizedBox(height: 12),
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton.icon(
                    onPressed: _launchContactSupport,
                    icon: const Icon(Icons.email),
                    label: const Text('Contact Support'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.blue[700],
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 12),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNoResultsState(ThemeData theme) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.search_off,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            'No results found',
            style: theme.textTheme.titleLarge?.copyWith(
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Try searching with different keywords',
            style: theme.textTheme.bodyMedium?.copyWith(
              color: Colors.grey[500],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCategoryCard(ThemeData theme, FAQCategory category) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey[200]!),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            spreadRadius: 1,
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: ExpansionTile(
        leading: Icon(
          category.icon,
          color: theme.colorScheme.primary,
        ),
        title: Text(
          category.title,
          style: theme.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        children: category.faqs.map((faq) => _buildFAQItem(theme, faq)).toList(),
      ),
    );
  }

  Widget _buildFAQItem(ThemeData theme, FAQ faq) {
    return ExpansionTile(
      title: Text(
        faq.question,
        style: theme.textTheme.bodyLarge?.copyWith(
          fontWeight: FontWeight.w500,
        ),
      ),
      children: [
        Padding(
          padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
          child: Text(
            faq.answer,
            style: theme.textTheme.bodyMedium?.copyWith(
              color: Colors.grey[700],
            ),
          ),
        ),
      ],
    );
  }

  Future<void> _launchContactSupport() async {
    const email = '<EMAIL>';
    const subject = 'Support Request - Choice: Once Upon A Time';
    final uri = Uri(
      scheme: 'mailto',
      path: email,
      query: 'subject=$subject',
    );

    try {
      if (await canLaunchUrl(uri)) {
        await launchUrl(uri);
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Please email us at: <EMAIL>'),
              duration: Duration(seconds: 4),
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Please email us at: <EMAIL>'),
            duration: Duration(seconds: 4),
          ),
        );
      }
    }
  }
}

class FAQCategory {
  final String title;
  final IconData icon;
  final List<FAQ> faqs;

  FAQCategory({
    required this.title,
    required this.icon,
    required this.faqs,
  });
}

class FAQ {
  final String question;
  final String answer;

  FAQ({
    required this.question,
    required this.answer,
  });
}
