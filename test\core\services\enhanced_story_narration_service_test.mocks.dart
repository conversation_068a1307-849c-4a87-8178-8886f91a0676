// Mocks generated by <PERSON><PERSON><PERSON> 5.4.4 from annotations
// in choice_once_upon_a_time/test/core/services/enhanced_story_narration_service_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i4;

import 'package:choice_once_upon_a_time/core/audio/narration_tts_service_interface.dart'
    as _i3;
import 'package:choice_once_upon_a_time/models/tts_models.dart' as _i2;
import 'package:mockito/mockito.dart' as _i1;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeTTSState_0 extends _i1.SmartFake implements _i2.TTSState {
  _FakeTTSState_0(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeTTSParameters_1 extends _i1.SmartFake implements _i2.TTSParameters {
  _FakeTTSParameters_1(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

/// A class which mocks [IEnhancedNarrationTTSService].
///
/// See the documentation for Mockito's code generation for more information.
class MockIEnhancedNarrationTTSService extends _i1.Mock
    implements _i3.IEnhancedNarrationTTSService {
  MockIEnhancedNarrationTTSService() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i4.Stream<_i2.TTSEvent> get wordBoundaryStream => (super.noSuchMethod(
        Invocation.getter(#wordBoundaryStream),
        returnValue: _i4.Stream<_i2.TTSEvent>.empty(),
      ) as _i4.Stream<_i2.TTSEvent>);

  @override
  _i4.Stream<_i2.TTSEvent> get sentenceBoundaryStream => (super.noSuchMethod(
        Invocation.getter(#sentenceBoundaryStream),
        returnValue: _i4.Stream<_i2.TTSEvent>.empty(),
      ) as _i4.Stream<_i2.TTSEvent>);

  @override
  _i4.Stream<_i2.TTSEvent> get eventStream => (super.noSuchMethod(
        Invocation.getter(#eventStream),
        returnValue: _i4.Stream<_i2.TTSEvent>.empty(),
      ) as _i4.Stream<_i2.TTSEvent>);

  @override
  _i4.Stream<_i2.TTSState> get stateStream => (super.noSuchMethod(
        Invocation.getter(#stateStream),
        returnValue: _i4.Stream<_i2.TTSState>.empty(),
      ) as _i4.Stream<_i2.TTSState>);

  @override
  _i2.TTSState get currentState => (super.noSuchMethod(
        Invocation.getter(#currentState),
        returnValue: _FakeTTSState_0(
          this,
          Invocation.getter(#currentState),
        ),
      ) as _i2.TTSState);

  @override
  bool get isAvailable => (super.noSuchMethod(
        Invocation.getter(#isAvailable),
        returnValue: false,
      ) as bool);

  @override
  bool get isSpeaking => (super.noSuchMethod(
        Invocation.getter(#isSpeaking),
        returnValue: false,
      ) as bool);

  @override
  bool get isPaused => (super.noSuchMethod(
        Invocation.getter(#isPaused),
        returnValue: false,
      ) as bool);

  @override
  _i4.Future<bool> speakWithWordTracking(
    String? text, {
    _i2.TTSParameters? parameters,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #speakWithWordTracking,
          [text],
          {#parameters: parameters},
        ),
        returnValue: _i4.Future<bool>.value(false),
      ) as _i4.Future<bool>);

  @override
  _i4.Future<bool> speakWithSentenceTracking(
    String? text, {
    _i2.TTSParameters? parameters,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #speakWithSentenceTracking,
          [text],
          {#parameters: parameters},
        ),
        returnValue: _i4.Future<bool>.value(false),
      ) as _i4.Future<bool>);

  @override
  _i4.Future<bool> speakWithFullTracking(
    String? text, {
    _i2.TTSParameters? parameters,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #speakWithFullTracking,
          [text],
          {#parameters: parameters},
        ),
        returnValue: _i4.Future<bool>.value(false),
      ) as _i4.Future<bool>);

  @override
  _i4.Future<void> setEmotionParameters(String? emotionCue) =>
      (super.noSuchMethod(
        Invocation.method(
          #setEmotionParameters,
          [emotionCue],
        ),
        returnValue: _i4.Future<void>.value(),
        returnValueForMissingStub: _i4.Future<void>.value(),
      ) as _i4.Future<void>);

  @override
  _i2.TTSParameters getEmotionParameters(String? emotionCue) =>
      (super.noSuchMethod(
        Invocation.method(
          #getEmotionParameters,
          [emotionCue],
        ),
        returnValue: _FakeTTSParameters_1(
          this,
          Invocation.method(
            #getEmotionParameters,
            [emotionCue],
          ),
        ),
      ) as _i2.TTSParameters);

  @override
  _i4.Future<bool> preloadText(String? text) => (super.noSuchMethod(
        Invocation.method(
          #preloadText,
          [text],
        ),
        returnValue: _i4.Future<bool>.value(false),
      ) as _i4.Future<bool>);

  @override
  _i4.Future<void> clearPreloadedText() => (super.noSuchMethod(
        Invocation.method(
          #clearPreloadedText,
          [],
        ),
        returnValue: _i4.Future<void>.value(),
        returnValueForMissingStub: _i4.Future<void>.value(),
      ) as _i4.Future<void>);

  @override
  _i4.Future<void> setCharacterVoice(
    String? characterId,
    _i2.TTSParameters? parameters,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #setCharacterVoice,
          [
            characterId,
            parameters,
          ],
        ),
        returnValue: _i4.Future<void>.value(),
        returnValueForMissingStub: _i4.Future<void>.value(),
      ) as _i4.Future<void>);

  @override
  _i4.Future<bool> speakAsCharacter(
    String? text,
    String? characterId,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #speakAsCharacter,
          [
            text,
            characterId,
          ],
        ),
        returnValue: _i4.Future<bool>.value(false),
      ) as _i4.Future<bool>);

  @override
  _i4.Future<void> setSSMLEnabled(bool? enabled) => (super.noSuchMethod(
        Invocation.method(
          #setSSMLEnabled,
          [enabled],
        ),
        returnValue: _i4.Future<void>.value(),
        returnValueForMissingStub: _i4.Future<void>.value(),
      ) as _i4.Future<void>);

  @override
  _i4.Future<bool> speakSSML(String? ssmlText) => (super.noSuchMethod(
        Invocation.method(
          #speakSSML,
          [ssmlText],
        ),
        returnValue: _i4.Future<bool>.value(false),
      ) as _i4.Future<bool>);

  @override
  _i4.Future<bool> initialize() => (super.noSuchMethod(
        Invocation.method(
          #initialize,
          [],
        ),
        returnValue: _i4.Future<bool>.value(false),
      ) as _i4.Future<bool>);

  @override
  _i4.Future<bool> speak(
    String? text, {
    _i2.TTSParameters? parameters,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #speak,
          [text],
          {#parameters: parameters},
        ),
        returnValue: _i4.Future<bool>.value(false),
      ) as _i4.Future<bool>);

  @override
  _i4.Future<bool> pause() => (super.noSuchMethod(
        Invocation.method(
          #pause,
          [],
        ),
        returnValue: _i4.Future<bool>.value(false),
      ) as _i4.Future<bool>);

  @override
  _i4.Future<bool> resume() => (super.noSuchMethod(
        Invocation.method(
          #resume,
          [],
        ),
        returnValue: _i4.Future<bool>.value(false),
      ) as _i4.Future<bool>);

  @override
  _i4.Future<bool> stop() => (super.noSuchMethod(
        Invocation.method(
          #stop,
          [],
        ),
        returnValue: _i4.Future<bool>.value(false),
      ) as _i4.Future<bool>);

  @override
  _i4.Future<void> setParameters(_i2.TTSParameters? parameters) =>
      (super.noSuchMethod(
        Invocation.method(
          #setParameters,
          [parameters],
        ),
        returnValue: _i4.Future<void>.value(),
        returnValueForMissingStub: _i4.Future<void>.value(),
      ) as _i4.Future<void>);

  @override
  _i4.Future<List<_i2.TTSVoice>> getAvailableVoices() => (super.noSuchMethod(
        Invocation.method(
          #getAvailableVoices,
          [],
        ),
        returnValue: _i4.Future<List<_i2.TTSVoice>>.value(<_i2.TTSVoice>[]),
      ) as _i4.Future<List<_i2.TTSVoice>>);

  @override
  _i4.Future<bool> setVoice(_i2.TTSVoice? voice) => (super.noSuchMethod(
        Invocation.method(
          #setVoice,
          [voice],
        ),
        returnValue: _i4.Future<bool>.value(false),
      ) as _i4.Future<bool>);

  @override
  _i4.Future<bool> setLanguage(String? languageCode) => (super.noSuchMethod(
        Invocation.method(
          #setLanguage,
          [languageCode],
        ),
        returnValue: _i4.Future<bool>.value(false),
      ) as _i4.Future<bool>);

  @override
  _i4.Future<bool> speakWithEmotion(
    String? text,
    String? emotionCue,
  ) =>
      (super.noSuchMethod(
        Invocation.method(
          #speakWithEmotion,
          [
            text,
            emotionCue,
          ],
        ),
        returnValue: _i4.Future<bool>.value(false),
      ) as _i4.Future<bool>);

  @override
  _i4.Future<bool> isLanguageSupported(String? languageCode) =>
      (super.noSuchMethod(
        Invocation.method(
          #isLanguageSupported,
          [languageCode],
        ),
        returnValue: _i4.Future<bool>.value(false),
      ) as _i4.Future<bool>);

  @override
  _i4.Future<int?> estimateSpeechDuration(String? text) => (super.noSuchMethod(
        Invocation.method(
          #estimateSpeechDuration,
          [text],
        ),
        returnValue: _i4.Future<int?>.value(),
      ) as _i4.Future<int?>);

  @override
  _i4.Future<void> dispose() => (super.noSuchMethod(
        Invocation.method(
          #dispose,
          [],
        ),
        returnValue: _i4.Future<void>.value(),
        returnValueForMissingStub: _i4.Future<void>.value(),
      ) as _i4.Future<void>);
}
