import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:choice_once_upon_a_time/core/utils/app_logger.dart';
import 'package:choice_once_upon_a_time/features/parent_zone/data/user_profile_service.dart';
import 'package:choice_once_upon_a_time/core/services/child_progress_service.dart';
import 'package:choice_once_upon_a_time/models/child_progress_model.dart';

/// Screen for tracking reading progress and analytics
class ProgressTrackingScreen extends ConsumerStatefulWidget {
  const ProgressTrackingScreen({super.key});

  @override
  ConsumerState<ProgressTrackingScreen> createState() => _ProgressTrackingScreenState();
}

class _ProgressTrackingScreenState extends ConsumerState<ProgressTrackingScreen> {
  String? _selectedProfileId;

  @override
  void initState() {
    super.initState();
    AppLogger.debug('[COMPONENT_LOAD] Full path: lib/features/parent_zone/presentation/screens/progress_tracking_screen.dart - ProgressTrackingScreen');

    // Auto-select current profile if available
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final selectedProfile = ref.read(selectedProfileProvider);
      if (selectedProfile != null) {
        setState(() {
          _selectedProfileId = selectedProfile.id;
        });
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final screenSize = MediaQuery.of(context).size;
    final isSmallScreen = screenSize.width < 600;
    final profilesAsync = ref.watch(userProfilesProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Reading Progress'),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => context.pop(),
        ),
      ),
      body: SafeArea(
        child: profilesAsync.when(
          data: (profiles) => _buildContent(context, theme, screenSize, isSmallScreen, profiles),
          loading: () => const Center(child: CircularProgressIndicator()),
          error: (error, stack) => _buildErrorState(theme, error),
        ),
      ),
    );
  }

  Widget _buildContent(BuildContext context, ThemeData theme, Size screenSize, bool isSmallScreen, List<UserProfile> profiles) {
    if (profiles.isEmpty) {
      return _buildNoProfilesState(theme);
    }

    return SingleChildScrollView(
      padding: EdgeInsets.all(isSmallScreen ? 12.0 : 16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Profile selector
          _buildProfileSelector(theme, profiles),

          const SizedBox(height: 24),

          // Progress content
          if (_selectedProfileId != null)
            _buildProgressContent(theme, isSmallScreen)
          else
            _buildSelectProfilePrompt(theme),
        ],
      ),
    );
  }

  Widget _buildProfileSelector(ThemeData theme, List<UserProfile> profiles) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Select Child Profile',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            DropdownButtonFormField<String>(
              value: _selectedProfileId,
              decoration: const InputDecoration(
                border: OutlineInputBorder(),
                hintText: 'Choose a child profile',
              ),
              items: profiles.map((profile) {
                return DropdownMenuItem(
                  value: profile.id,
                  child: Row(
                    children: [
                      Container(
                        width: 24,
                        height: 24,
                        decoration: BoxDecoration(
                          color: profile.avatarColor,
                          shape: BoxShape.circle,
                        ),
                        child: const Icon(
                          Icons.child_care,
                          size: 14,
                          color: Colors.white,
                        ),
                      ),
                      const SizedBox(width: 12),
                      Text('${profile.name} (${profile.age} years)'),
                    ],
                  ),
                );
              }).toList(),
              onChanged: (profileId) {
                setState(() {
                  _selectedProfileId = profileId;
                });
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildProgressContent(ThemeData theme, bool isSmallScreen) {
    return FutureBuilder<ProgressSummary>(
      future: ref.read(childProgressServiceProvider).getProgressSummary(_selectedProfileId!),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Center(child: CircularProgressIndicator());
        }

        if (snapshot.hasError) {
          return _buildErrorCard(theme, 'Failed to load progress data');
        }

        final summary = snapshot.data!;
        return Column(
          children: [
            // Overview cards
            _buildOverviewCards(theme, summary, isSmallScreen),

            const SizedBox(height: 24),

            // Detailed progress
            _buildDetailedProgress(theme, summary),
          ],
        );
      },
    );
  }

  Widget _buildOverviewCards(ThemeData theme, ProgressSummary summary, bool isSmallScreen) {
    final cards = [
      _buildStatCard(
        theme,
        'Stories Read',
        '${summary.completedStories}/${summary.totalStories}',
        Icons.book,
        Colors.blue,
      ),
      _buildStatCard(
        theme,
        'Vocabulary Learned',
        '${summary.totalVocabularyLearned}',
        Icons.school,
        Colors.green,
      ),
      _buildStatCard(
        theme,
        'Reading Time',
        summary.totalReadingTimeFormatted,
        Icons.timer,
        Colors.orange,
      ),
      _buildStatCard(
        theme,
        'Moral Score',
        '${(summary.averageMoralScore * 100).toInt()}%',
        Icons.favorite,
        Colors.red,
      ),
    ];

    if (isSmallScreen) {
      return Column(
        children: cards.map((card) => Padding(
          padding: const EdgeInsets.only(bottom: 12),
          child: card,
        )).toList(),
      );
    } else {
      return GridView.count(
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        crossAxisCount: 2,
        crossAxisSpacing: 12,
        mainAxisSpacing: 12,
        childAspectRatio: 2.5,
        children: cards,
      );
    }
  }

  Widget _buildStatCard(ThemeData theme, String title, String value, IconData icon, Color color) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: color.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(icon, color: color, size: 24),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: theme.textTheme.bodyMedium?.copyWith(
                      color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    value,
                    style: theme.textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDetailedProgress(ThemeData theme, ProgressSummary summary) {
    return FutureBuilder<List<ChildProgressModel>>(
      future: ref.read(childProgressServiceProvider).loadProgressForProfile(_selectedProfileId!),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Card(
            child: Padding(
              padding: EdgeInsets.all(24),
              child: Center(child: CircularProgressIndicator()),
            ),
          );
        }

        if (snapshot.hasError) {
          return _buildErrorCard(theme, 'Failed to load detailed progress');
        }

        final progressList = snapshot.data ?? [];

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Story Progress',
              style: theme.textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),

            if (progressList.isEmpty)
              _buildNoProgressCard(theme)
            else
              ...progressList.map((progress) => Padding(
                padding: const EdgeInsets.only(bottom: 12),
                child: _buildStoryProgressCard(theme, progress),
              )),
          ],
        );
      },
    );
  }

  Widget _buildStoryProgressCard(ThemeData theme, ChildProgressModel progress) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Expanded(
                  child: Text(
                    'Story ${progress.storyId}',
                    style: theme.textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                if (progress.isCompleted)
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: Colors.green.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      'Completed',
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: Colors.green,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
              ],
            ),
            const SizedBox(height: 12),

            // Progress bar
            LinearProgressIndicator(
              value: progress.progressPercentage / 100,
              backgroundColor: theme.colorScheme.outline.withValues(alpha: 0.2),
              valueColor: AlwaysStoppedAnimation<Color>(
                progress.isCompleted ? Colors.green : theme.colorScheme.primary,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              '${progress.progressPercentage.toInt()}% Complete',
              style: theme.textTheme.bodySmall,
            ),

            const SizedBox(height: 12),

            // Stats row
            Row(
              children: [
                _buildProgressStat(theme, 'Scenes', '${progress.visitedScenes.length}', Icons.visibility),
                const SizedBox(width: 16),
                _buildProgressStat(theme, 'Vocabulary', '${progress.totalVocabularyLearned}', Icons.school),
                const SizedBox(width: 16),
                _buildProgressStat(theme, 'Time', _formatDuration(progress.totalPlayTime), Icons.timer),
              ],
            ),

            const SizedBox(height: 8),
            Text(
              'Last played: ${_formatDate(progress.lastPlayedAt)}',
              style: theme.textTheme.bodySmall?.copyWith(
                color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildProgressStat(ThemeData theme, String label, String value, IconData icon) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Icon(icon, size: 16, color: theme.colorScheme.onSurface.withValues(alpha: 0.6)),
        const SizedBox(width: 4),
        Text(
          '$value $label',
          style: theme.textTheme.bodySmall?.copyWith(
            color: theme.colorScheme.onSurface.withValues(alpha: 0.8),
          ),
        ),
      ],
    );
  }

  Widget _buildErrorState(ThemeData theme, Object error) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 64,
            color: theme.colorScheme.error,
          ),
          const SizedBox(height: 16),
          Text(
            'Error loading progress',
            style: theme.textTheme.titleLarge,
          ),
          const SizedBox(height: 8),
          Text(
            error.toString(),
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildNoProfilesState(ThemeData theme) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.child_care,
            size: 64,
            color: theme.colorScheme.onSurface.withValues(alpha: 0.3),
          ),
          const SizedBox(height: 16),
          Text(
            'No Child Profiles',
            style: theme.textTheme.titleLarge,
          ),
          const SizedBox(height: 8),
          Text(
            'Create a child profile to start tracking progress',
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          ElevatedButton(
            onPressed: () => context.go('/parent_zone/user_profiles'),
            child: const Text('Create Profile'),
          ),
        ],
      ),
    );
  }

  Widget _buildSelectProfilePrompt(ThemeData theme) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          children: [
            Icon(
              Icons.person_search,
              size: 48,
              color: theme.colorScheme.primary,
            ),
            const SizedBox(height: 16),
            Text(
              'Select a Profile',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Choose a child profile above to view their reading progress',
              style: theme.textTheme.bodyMedium?.copyWith(
                color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildErrorCard(ThemeData theme, String message) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          children: [
            Icon(
              Icons.error_outline,
              size: 48,
              color: theme.colorScheme.error,
            ),
            const SizedBox(height: 16),
            Text(
              'Error',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              message,
              style: theme.textTheme.bodyMedium?.copyWith(
                color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildNoProgressCard(ThemeData theme) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          children: [
            Icon(
              Icons.menu_book,
              size: 48,
              color: theme.colorScheme.onSurface.withValues(alpha: 0.3),
            ),
            const SizedBox(height: 16),
            Text(
              'No Stories Started',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'This child hasn\'t started reading any stories yet',
              style: theme.textTheme.bodyMedium?.copyWith(
                color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  String _formatDuration(int seconds) {
    final minutes = seconds ~/ 60;
    if (minutes < 60) {
      return '${minutes}m';
    } else {
      final hours = minutes ~/ 60;
      final remainingMinutes = minutes % 60;
      return '${hours}h ${remainingMinutes}m';
    }
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays == 0) {
      return 'Today';
    } else if (difference.inDays == 1) {
      return 'Yesterday';
    } else if (difference.inDays < 7) {
      return '${difference.inDays} days ago';
    } else {
      return '${date.day}/${date.month}/${date.year}';
    }
  }
}
