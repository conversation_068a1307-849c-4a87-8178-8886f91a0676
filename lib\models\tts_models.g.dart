// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'tts_models.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

TTSEvent _$TTSEventFromJson(Map<String, dynamic> json) => TTSEvent(
      type: $enumDecode(_$TTSEventTypeEnumMap, json['type']),
      text: json['text'] as String?,
      error: json['error'] as String?,
      wordIndex: (json['wordIndex'] as num?)?.toInt(),
      charIndex: (json['charIndex'] as num?)?.toInt(),
      length: (json['length'] as num?)?.toInt(),
      timestamp: DateTime.parse(json['timestamp'] as String),
    );

Map<String, dynamic> _$TTSEventToJson(TTSEvent instance) => <String, dynamic>{
      'type': _$TTSEventTypeEnumMap[instance.type]!,
      'text': instance.text,
      'error': instance.error,
      'wordIndex': instance.wordIndex,
      'charIndex': instance.charIndex,
      'length': instance.length,
      'timestamp': instance.timestamp.toIso8601String(),
    };

const _$TTSEventTypeEnumMap = {
  TTSEventType.started: 'started',
  TTSEventType.completed: 'completed',
  TTSEventType.paused: 'paused',
  TTSEventType.resumed: 'resumed',
  TTSEventType.stopped: 'stopped',
  TTSEventType.error: 'error',
  TTSEventType.wordBoundary: 'wordBoundary',
  TTSEventType.sentenceBoundary: 'sentenceBoundary',
};

TTSParameters _$TTSParametersFromJson(Map<String, dynamic> json) =>
    TTSParameters(
      rate: (json['rate'] as num?)?.toDouble() ?? 0.35,
      pitch: (json['pitch'] as num?)?.toDouble() ?? 1.0,
      volume: (json['volume'] as num?)?.toDouble() ?? 1.0,
      language: json['language'] as String? ?? 'en-US',
      voiceId: json['voiceId'] as String?,
    );

Map<String, dynamic> _$TTSParametersToJson(TTSParameters instance) =>
    <String, dynamic>{
      'rate': instance.rate,
      'pitch': instance.pitch,
      'volume': instance.volume,
      'language': instance.language,
      'voiceId': instance.voiceId,
    };

TTSVoice _$TTSVoiceFromJson(Map<String, dynamic> json) => TTSVoice(
      id: json['id'] as String,
      name: json['name'] as String,
      language: json['language'] as String,
      gender: json['gender'] as String?,
      isSelected: json['isSelected'] as bool? ?? false,
    );

Map<String, dynamic> _$TTSVoiceToJson(TTSVoice instance) => <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'language': instance.language,
      'gender': instance.gender,
      'isSelected': instance.isSelected,
    };

TTSState _$TTSStateFromJson(Map<String, dynamic> json) => TTSState(
      status: $enumDecodeNullable(_$TTSStatusEnumMap, json['status']) ??
          TTSStatus.uninitialized,
      parameters: json['parameters'] == null
          ? TTSParameters.defaultParams
          : TTSParameters.fromJson(json['parameters'] as Map<String, dynamic>),
      selectedVoice: json['selectedVoice'] == null
          ? null
          : TTSVoice.fromJson(json['selectedVoice'] as Map<String, dynamic>),
      availableVoices: (json['availableVoices'] as List<dynamic>?)
              ?.map((e) => TTSVoice.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      error: json['error'] as String?,
      isAvailable: json['isAvailable'] as bool? ?? false,
    );

Map<String, dynamic> _$TTSStateToJson(TTSState instance) => <String, dynamic>{
      'status': _$TTSStatusEnumMap[instance.status]!,
      'parameters': instance.parameters,
      'selectedVoice': instance.selectedVoice,
      'availableVoices': instance.availableVoices,
      'error': instance.error,
      'isAvailable': instance.isAvailable,
    };

const _$TTSStatusEnumMap = {
  TTSStatus.uninitialized: 'uninitialized',
  TTSStatus.initializing: 'initializing',
  TTSStatus.ready: 'ready',
  TTSStatus.speaking: 'speaking',
  TTSStatus.paused: 'paused',
  TTSStatus.error: 'error',
};
