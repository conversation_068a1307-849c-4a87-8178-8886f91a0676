import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:choice_once_upon_a_time/models/story_metadata_model.dart';
import 'package:choice_once_upon_a_time/features/story_library/presentation/providers/new_story_library_provider.dart';
import 'package:choice_once_upon_a_time/features/story_library/presentation/widgets/new_story_card_widget.dart';
import 'package:choice_once_upon_a_time/app/providers/settings_provider.dart';
import 'package:choice_once_upon_a_time/shared_widgets/loading_indicator_widget.dart';
import 'package:choice_once_upon_a_time/core/utils/app_logger.dart';

/// A dedicated screen to display the entire story library with search and filtering.
class StoryLibraryScreen extends ConsumerStatefulWidget {
  const StoryLibraryScreen({super.key});

  @override
  ConsumerState<StoryLibraryScreen> createState() => _StoryLibraryScreenState();
}

class _StoryLibraryScreenState extends ConsumerState<StoryLibraryScreen> {
  final TextEditingController _searchController = TextEditingController();

  @override
  void initState() {
    super.initState();
    AppLogger.debug('[COMPONENT_LOAD] Full path: lib/features/story_library/presentation/screens/story_library_screen.dart - StoryLibraryScreen');
    _searchController.addListener(() {
      ref.read(newStoryLibraryProvider.notifier).search(_searchController.text);
    });
    // Trigger initial refresh to ensure fresh data
    Future.microtask(() {
      ref.read(newStoryLibraryProvider.notifier).refresh();
    });
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  void _onStoryTap(BuildContext context, StoryMetadataModel story) {
    AppLogger.debug('[NEW_STORY_LIBRARY] Story tapped: ${story.id}');

    // Navigate to new story introduction screen
    if (story.dataSource == 'new_asset' || story.dataSource == 'firebase') {
      AppLogger.debug('[NEW_STORY_LIBRARY] Navigating to new story introduction: ${story.id}');
      context.go('/new_story/introduction/${story.id}');
    } else {
      // Show message for stories not yet migrated
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('This story is being updated to the new format. Please check back soon!'),
          duration: Duration(seconds: 2),
        ),
      );
    }
  }

  Future<void> _onRefresh() async {
    AppLogger.debug('[NEW_STORY_LIBRARY] User triggered refresh');
    await ref.read(newStoryLibraryProvider.notifier).refresh();
  }

  @override
  Widget build(BuildContext context) {
    final storyLibraryState = ref.watch(newStoryLibraryProvider);
    final filteredStories = ref.watch(newFilteredStoriesProvider);
    final languageCode = ref.watch(narrationLanguageProvider);
    final theme = Theme.of(context);
    final screenSize = MediaQuery.of(context).size;
    final isSmallScreen = screenSize.width < 600;

    // Debug: Log story counts
    AppLogger.debug('[StoryLibraryScreen] Raw stories: ${storyLibraryState.stories.length}, Filtered stories: ${filteredStories.length}, Search query: "${storyLibraryState.searchQuery}"');

    return Scaffold(
      appBar: AppBar(
        title: Text(
          'Full Story Library',
          style: theme.textTheme.titleLarge?.copyWith(
            fontSize: isSmallScreen ? 18 : 20,
          ),
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
        ),
        backgroundColor: theme.colorScheme.surface,
        foregroundColor: theme.colorScheme.onSurface,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => context.go('/home'),
        ),
      ),
      body: SafeArea(
        child: Column(
          children: [
            // Responsive search bar
            Padding(
              padding: EdgeInsets.symmetric(
                horizontal: screenSize.width * 0.04,
                vertical: isSmallScreen ? 12.0 : 16.0,
              ),
              child: TextField(
                controller: _searchController,
                style: theme.textTheme.bodyMedium?.copyWith(
                  fontSize: isSmallScreen ? 14 : 16,
                ),
                decoration: InputDecoration(
                  hintText: 'Search stories by title...',
                  hintStyle: theme.textTheme.bodyMedium?.copyWith(
                    color: theme.colorScheme.onSurfaceVariant,
                    fontSize: isSmallScreen ? 14 : 16,
                  ),
                  prefixIcon: Icon(
                    Icons.search,
                    color: theme.colorScheme.onSurfaceVariant,
                    size: isSmallScreen ? 20 : 24,
                  ),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12.0),
                    borderSide: BorderSide(color: theme.colorScheme.outline),
                  ),
                  enabledBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12.0),
                    borderSide: BorderSide(color: theme.colorScheme.outline),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12.0),
                    borderSide: BorderSide(color: theme.colorScheme.primary, width: 2),
                  ),
                  filled: true,
                  fillColor: theme.colorScheme.surfaceContainerHighest,
                  contentPadding: EdgeInsets.symmetric(
                    horizontal: isSmallScreen ? 12 : 16,
                    vertical: isSmallScreen ? 12 : 16,
                  ),
                  suffixIcon: _searchController.text.isNotEmpty
                      ? IconButton(
                          icon: Icon(
                            Icons.clear,
                            color: theme.colorScheme.onSurfaceVariant,
                            size: isSmallScreen ? 20 : 24,
                          ),
                          onPressed: () {
                            _searchController.clear();
                          },
                          tooltip: 'Clear search',
                        )
                      : null,
                ),
              ),
            ),
            Expanded(
              child: _buildBody(context, storyLibraryState, filteredStories, languageCode),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBody(
    BuildContext context,
    NewStoryLibraryState state,
    List<StoryMetadataModel> stories,
    String languageCode,
  ) {
    final theme = Theme.of(context);
    final screenSize = MediaQuery.of(context).size;
    final isSmallScreen = screenSize.width < 600;

    if (state.isLoading && stories.isEmpty) {
      return const Center(child: LoadingIndicatorWidget());
    }

    if (state.error != null) {
      return Center(
        child: Padding(
          padding: EdgeInsets.all(screenSize.width * 0.08),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.error_outline,
                size: isSmallScreen ? 48 : 64,
                color: theme.colorScheme.error,
              ),
              const SizedBox(height: 16),
              Text(
                'Error: ${state.error}',
                style: theme.textTheme.bodyLarge?.copyWith(
                  color: theme.colorScheme.error,
                  fontSize: isSmallScreen ? 14 : 16,
                ),
                textAlign: TextAlign.center,
                maxLines: 3,
                overflow: TextOverflow.ellipsis,
              ),
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: _onRefresh,
                child: const Text('Retry'),
              ),
            ],
          ),
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: _onRefresh,
      color: theme.colorScheme.primary,
      child: LayoutBuilder(
        builder: (context, constraints) {
          if (stories.isEmpty) {
            return SingleChildScrollView(
              physics: const AlwaysScrollableScrollPhysics(),
              child: ConstrainedBox(
                constraints: BoxConstraints(minHeight: constraints.maxHeight),
                child: Center(
                  child: Padding(
                    padding: EdgeInsets.all(screenSize.width * 0.08),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.search_off,
                          size: isSmallScreen ? 48 : 64,
                          color: theme.colorScheme.onSurfaceVariant,
                        ),
                        const SizedBox(height: 16),
                        Text(
                          state.searchQuery.isNotEmpty
                              ? 'No stories match your search.'
                              : 'No stories available. Please try refreshing.',
                          style: theme.textTheme.titleMedium?.copyWith(
                            color: theme.colorScheme.onSurfaceVariant,
                            fontSize: isSmallScreen ? 16 : 18,
                          ),
                          textAlign: TextAlign.center,
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                        if (state.searchQuery.isNotEmpty) ...[
                          const SizedBox(height: 8),
                          Text(
                            'Try adjusting your search terms',
                            style: theme.textTheme.bodyMedium?.copyWith(
                              color: theme.colorScheme.onSurfaceVariant.withValues(alpha: 0.7),
                              fontSize: isSmallScreen ? 12 : 14,
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ],
                        const SizedBox(height: 16),
                        ElevatedButton(
                          onPressed: _onRefresh,
                          child: const Text('Refresh'),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            );
          }

          final screenWidth = constraints.maxWidth;
          int crossAxisCount;
          double childAspectRatio;
          double spacing;

          if (screenWidth > 1200) {
            crossAxisCount = 5;
            childAspectRatio = 0.85;
            spacing = 20.0;
          } else if (screenWidth > 800) {
            crossAxisCount = 4;
            childAspectRatio = 0.8;
            spacing = 18.0;
          } else if (screenWidth > 500) {
            crossAxisCount = 3;
            childAspectRatio = 0.75;
            spacing = 16.0;
          } else if (screenWidth > 375) {
            crossAxisCount = 2;
            childAspectRatio = 0.7;
            spacing = 12.0;
          } else {
            crossAxisCount = 1;
            childAspectRatio = 1.3;
            spacing = 16.0;
          }

          return Column(
            children: [
              Expanded(
                child: GridView.builder(
                  padding: EdgeInsets.fromLTRB(
                    screenSize.width * 0.04,
                    0,
                    screenSize.width * 0.04,
                    16.0,
                  ),
                  gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                    crossAxisCount: crossAxisCount,
                    crossAxisSpacing: spacing,
                    mainAxisSpacing: spacing,
                    childAspectRatio: childAspectRatio,
                  ),
                  itemCount: stories.length,
                  itemBuilder: (context, index) {
                    final story = stories[index];
                    return NewStoryCardWidget(
                      story: story,
                      languageCode: languageCode,
                      onTap: () => _onStoryTap(context, story),
                      isOneColumn: crossAxisCount == 1,
                    );
                  },
                ),
              ),
              _buildStorySourceInfo(context, stories, theme),
            ],
          );
        },
      ),
    );
  }

  Widget _buildStorySourceInfo(BuildContext context, List<StoryMetadataModel> stories, ThemeData theme) {
    return FutureBuilder<Map<String, int>>(
      future: _getStorySourceCounts(stories),
      builder: (context, snapshot) {
        final counts = snapshot.data ?? {'assets': 0, 'firebase': 0, 'downloaded': 0};
        return _buildStorySourceInfoContent(context, stories, theme, counts);
      },
    );
  }

  Future<Map<String, int>> _getStorySourceCounts(List<StoryMetadataModel> stories) async {
    int assetStories = 0;
    int firebaseStories = 0;
    int downloadedStories = 0;

    for (final story in stories) {
      try {
        final storyRepository = ref.read(newStoryLibraryProvider.notifier);
        final status = await storyRepository.getStoryStatus(story.id);

        if (status == 'play') {
          if (story.dataSource == 'new_asset' || story.id.startsWith('story0') || story.id.contains('test') || story.id.contains('pip')) {
            assetStories++;
          } else {
            downloadedStories++;
          }
        } else if (status == 'download') {
          firebaseStories++;
        }
      } catch (e) {
        if (story.dataSource == 'new_asset' || story.id.startsWith('story0') || story.id.contains('test') || story.id.contains('pip')) {
          assetStories++;
        } else {
          firebaseStories++;
        }
      }
    }

    AppLogger.debug('[StoryLibraryScreen] Source counts - Assets: $assetStories, Firebase: $firebaseStories, Downloaded: $downloadedStories');

    return {
      'assets': assetStories,
      'firebase': firebaseStories,
      'downloaded': downloadedStories,
    };
  }

  Widget _buildStorySourceInfoContent(
    BuildContext context,
    List<StoryMetadataModel> stories,
    ThemeData theme,
    Map<String, int> counts,
  ) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        color: theme.colorScheme.surfaceContainerLow,
        border: Border(
          top: BorderSide(
            color: theme.colorScheme.outline.withValues(alpha: 0.2),
            width: 1,
          ),
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            'Story Sources',
            style: theme.textTheme.titleSmall?.copyWith(
              fontWeight: FontWeight.bold,
              color: theme.colorScheme.onSurfaceVariant,
            ),
          ),
          const SizedBox(height: 8),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              _buildSourceIndicator(
                context,
                theme,
                Icons.folder,
                'Assets',
                counts['assets'] ?? 0,
                Colors.blue,
              ),
              _buildSourceIndicator(
                context,
                theme,
                Icons.cloud,
                'Firebase',
                counts['firebase'] ?? 0,
                Colors.orange,
              ),
              _buildSourceIndicator(
                context,
                theme,
                Icons.download_done,
                'Downloaded',
                counts['downloaded'] ?? 0,
                Colors.green,
              ),
            ],
          ),
          const SizedBox(height: 4),
          Text(
            'Total: ${stories.length} stories available',
            style: theme.textTheme.bodySmall?.copyWith(
              color: theme.colorScheme.onSurfaceVariant.withValues(alpha: 0.8),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSourceIndicator(
    BuildContext context,
    ThemeData theme,
    IconData icon,
    String label,
    int count,
    Color color,
  ) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: color.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(
            icon,
            size: 20,
            color: color,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          label,
          style: theme.textTheme.bodySmall?.copyWith(
            fontWeight: FontWeight.w500,
            color: theme.colorScheme.onSurfaceVariant,
          ),
        ),
        Text(
          count.toString(),
          style: theme.textTheme.bodySmall?.copyWith(
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
      ],
    );
  }
}