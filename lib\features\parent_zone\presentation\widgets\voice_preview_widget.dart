import 'package:flutter/material.dart';
import 'package:choice_once_upon_a_time/core/services/story_narration_service_interface.dart';
import 'package:choice_once_upon_a_time/core/services/enhanced_story_narration_service.dart';
import 'package:choice_once_upon_a_time/models/narrator_profile_model.dart';
import 'package:choice_once_upon_a_time/core/utils/app_logger.dart';

/// Widget for previewing narrator voice with different settings
class VoicePreviewWidget extends StatefulWidget {
  final double pitch;
  final double rate;
  final double volume;
  final String? selectedVoiceName;
  final VoidCallback? onVoiceChanged;

  const VoicePreviewWidget({
    super.key,
    required this.pitch,
    required this.rate,
    required this.volume,
    this.selectedVoiceName,
    this.onVoiceChanged,
  });

  @override
  State<VoicePreviewWidget> createState() => _VoicePreviewWidgetState();
}

class _VoicePreviewWidgetState extends State<VoicePreviewWidget> {
  late IStoryNarrationService _narrationService;
  bool _isPlaying = false;
  bool _isLoading = false;
  List<String> _availableVoices = [];
  String? _selectedVoice;

  final List<String> _sampleTexts = [
    "Hello! I'm your storytelling narrator. Let me tell you an amazing adventure!",
    "Once upon a time, in a magical kingdom far, far away, there lived a brave little hero.",
    "The forest was filled with wonder and mystery, where every tree had a story to tell.",
    "And they all lived happily ever after, learning important lessons along the way.",
  ];

  int _currentSampleIndex = 0;

  @override
  void initState() {
    super.initState();
    AppLogger.debug('[COMPONENT_LOAD] Full path: lib/features/parent_zone/presentation/widgets/voice_preview_widget.dart - VoicePreviewWidget');
    
    _narrationService = EnhancedStoryNarrationService();
    _selectedVoice = widget.selectedVoiceName;
    _loadAvailableVoices();
  }

  @override
  void dispose() {
    _narrationService.stop();
    super.dispose();
  }

  /// Load available TTS voices
  Future<void> _loadAvailableVoices() async {
    setState(() => _isLoading = true);

    try {
      // For now, use a default list of voices since the interface doesn't have getAvailableVoices
      final voices = ['Default Voice', 'Male Voice', 'Female Voice', 'Child Voice'];
      setState(() {
        _availableVoices = voices;
        if (_selectedVoice == null && voices.isNotEmpty) {
          _selectedVoice = voices.first;
        }
      });
      AppLogger.debug('[VOICE_PREVIEW] Loaded ${voices.length} available voices');
    } catch (e) {
      AppLogger.debug('[VOICE_PREVIEW] Error loading voices: $e');
      setState(() {
        _availableVoices = ['Default Voice'];
        _selectedVoice = 'Default Voice';
      });
    } finally {
      setState(() => _isLoading = false);
    }
  }

  /// Preview the voice with current settings
  Future<void> _previewVoice() async {
    if (_isPlaying) {
      await _narrationService.stop();
      setState(() => _isPlaying = false);
      return;
    }

    setState(() => _isPlaying = true);

    try {
      // Apply voice settings using the correct interface methods
      await _narrationService.setSpeechRate(widget.rate);
      await _narrationService.setSpeechPitch(widget.pitch);
      await _narrationService.setSpeechVolume(widget.volume);

      // Play sample text
      final sampleText = _sampleTexts[_currentSampleIndex];
      await _narrationService.narrateText(
        sampleText,
        emotionCue: 'neutral',
        storyId: 'voice_preview',
        sceneId: 'sample_$_currentSampleIndex',
      );

      AppLogger.debug('[VOICE_PREVIEW] Played sample: $_currentSampleIndex');
    } catch (e) {
      AppLogger.debug('[VOICE_PREVIEW] Error during preview: $e');
    } finally {
      if (mounted) {
        setState(() => _isPlaying = false);
      }
    }
  }

  /// Change to next sample text
  void _nextSample() {
    setState(() {
      _currentSampleIndex = (_currentSampleIndex + 1) % _sampleTexts.length;
    });
  }

  /// Change to previous sample text
  void _previousSample() {
    setState(() {
      _currentSampleIndex = (_currentSampleIndex - 1 + _sampleTexts.length) % _sampleTexts.length;
    });
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Voice Preview',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),

            // Voice selection dropdown
            if (_isLoading)
              const Center(child: CircularProgressIndicator())
            else
              _buildVoiceSelector(theme),

            const SizedBox(height: 16),

            // Sample text display
            _buildSampleTextDisplay(theme),

            const SizedBox(height: 16),

            // Sample navigation
            _buildSampleNavigation(theme),

            const SizedBox(height: 16),

            // Preview controls
            _buildPreviewControls(theme),
          ],
        ),
      ),
    );
  }

  /// Build voice selector dropdown
  Widget _buildVoiceSelector(ThemeData theme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Select Voice',
          style: theme.textTheme.bodyMedium?.copyWith(
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 8),
        DropdownButtonFormField<String>(
          value: _selectedVoice,
          decoration: InputDecoration(
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
            ),
            contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          ),
          items: _availableVoices.map((voice) {
            return DropdownMenuItem<String>(
              value: voice,
              child: Text(voice),
            );
          }).toList(),
          onChanged: (String? newVoice) {
            setState(() {
              _selectedVoice = newVoice;
            });
            widget.onVoiceChanged?.call();
          },
          hint: const Text('Select a voice'),
        ),
      ],
    );
  }

  /// Build sample text display
  Widget _buildSampleTextDisplay(ThemeData theme) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: theme.colorScheme.outline.withValues(alpha: 0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Sample Text ${_currentSampleIndex + 1} of ${_sampleTexts.length}',
            style: theme.textTheme.bodySmall?.copyWith(
              color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
            ),
          ),
          const SizedBox(height: 8),
          Text(
            _sampleTexts[_currentSampleIndex],
            style: theme.textTheme.bodyMedium?.copyWith(
              height: 1.5,
            ),
          ),
        ],
      ),
    );
  }

  /// Build sample navigation controls
  Widget _buildSampleNavigation(ThemeData theme) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        TextButton.icon(
          onPressed: _currentSampleIndex > 0 ? _previousSample : null,
          icon: const Icon(Icons.arrow_back),
          label: const Text('Previous'),
        ),
        Text(
          '${_currentSampleIndex + 1} / ${_sampleTexts.length}',
          style: theme.textTheme.bodySmall?.copyWith(
            color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
          ),
        ),
        TextButton.icon(
          onPressed: _currentSampleIndex < _sampleTexts.length - 1 ? _nextSample : null,
          icon: const Icon(Icons.arrow_forward),
          label: const Text('Next'),
        ),
      ],
    );
  }

  /// Build preview controls
  Widget _buildPreviewControls(ThemeData theme) {
    return Row(
      children: [
        Expanded(
          child: ElevatedButton.icon(
            onPressed: _isPlaying ? null : _previewVoice,
            icon: Icon(_isPlaying ? Icons.stop : Icons.play_arrow),
            label: Text(_isPlaying ? 'Playing...' : 'Preview Voice'),
            style: ElevatedButton.styleFrom(
              backgroundColor: theme.colorScheme.primary,
              foregroundColor: theme.colorScheme.onPrimary,
              padding: const EdgeInsets.symmetric(vertical: 12),
            ),
          ),
        ),
        const SizedBox(width: 12),
        if (_isPlaying)
          ElevatedButton.icon(
            onPressed: () async {
              await _narrationService.stop();
              setState(() => _isPlaying = false);
            },
            icon: const Icon(Icons.stop),
            label: const Text('Stop'),
            style: ElevatedButton.styleFrom(
              backgroundColor: theme.colorScheme.error,
              foregroundColor: theme.colorScheme.onError,
              padding: const EdgeInsets.symmetric(vertical: 12),
            ),
          ),
      ],
    );
  }

  /// Get the currently selected voice
  String? get selectedVoice => _selectedVoice;

  /// Get all available voices
  List<String> get availableVoices => _availableVoices;
}
