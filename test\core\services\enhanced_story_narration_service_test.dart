import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:choice_once_upon_a_time/core/services/enhanced_story_narration_service.dart';
import 'package:choice_once_upon_a_time/core/services/story_narration_service_interface.dart';
import 'package:choice_once_upon_a_time/core/audio/narration_tts_service_interface.dart';
import 'package:choice_once_upon_a_time/models/enhanced_story_model.dart';
import 'package:choice_once_upon_a_time/models/narration_models.dart';
import 'package:choice_once_upon_a_time/models/tts_models.dart';
import 'dart:async';

// Generate mocks
@GenerateMocks([IEnhancedNarrationTTSService])
import 'enhanced_story_narration_service_test.mocks.dart';

void main() {
  group('EnhancedStoryNarrationService', () {
    late EnhancedStoryNarrationService narrationService;
    late MockIEnhancedNarrationTTSService mockTtsService;

    setUp(() {
      mockTtsService = MockIEnhancedNarrationTTSService();
      narrationService = EnhancedStoryNarrationService();
      
      // Set up default mock behaviors
      when(mockTtsService.initialize()).thenAnswer((_) async => true);
      when(mockTtsService.isAvailable).thenReturn(true);
      when(mockTtsService.currentState).thenReturn(TTSState.ready);
      when(mockTtsService.eventStream).thenAnswer((_) => const Stream.empty());
      when(mockTtsService.wordBoundaryStream).thenAnswer((_) => const Stream.empty());
      when(mockTtsService.stateStream).thenAnswer((_) => const Stream.empty());
    });

    group('Initialization', () {
      test('should initialize successfully', () async {
        // Act
        await narrationService.initialize();

        // Assert
        expect(narrationService.isInitialized, true);
        expect(narrationService.currentState.status, NarrationStatus.idle);
      });

      test('should not initialize twice', () async {
        // Arrange
        await narrationService.initialize();

        // Act
        await narrationService.initialize();

        // Assert
        expect(narrationService.isInitialized, true);
      });

      test('should throw exception on TTS initialization failure', () async {
        // Arrange
        when(mockTtsService.initialize()).thenAnswer((_) async => false);

        // Act & Assert
        expect(
          () => narrationService.initialize(),
          throwsA(isA<StoryNarrationException>()),
        );
      });
    });

    group('Configuration', () {
      test('should configure narration settings', () async {
        // Arrange
        await narrationService.initialize();
        const config = NarrationConfig(
          speechRate: 0.8,
          speechPitch: 1.2,
          speechVolume: 0.9,
          language: 'es-ES',
        );

        // Act
        await narrationService.configure(config);

        // Assert
        expect(narrationService.currentConfig, config);
        verify(mockTtsService.setParameters(any)).called(1);
      });

      test('should update individual settings', () async {
        // Arrange
        await narrationService.initialize();

        // Act
        await narrationService.setSpeechRate(0.7);
        await narrationService.setSpeechPitch(1.1);
        await narrationService.setSpeechVolume(0.8);

        // Assert
        expect(narrationService.currentConfig.speechRate, 0.7);
        expect(narrationService.currentConfig.speechPitch, 1.1);
        expect(narrationService.currentConfig.speechVolume, 0.8);
      });
    });

    group('Scene Narration', () {
      test('should start scene narration successfully', () async {
        // Arrange
        await narrationService.initialize();
        const scene = EnhancedSceneModel(
          id: 'scene1',
          text: 'Once upon a time, there was a brave knight.',
          speaker: 'Narrator',
          emotion: 'calm',
          image: 'scene1.jpg',
          pauseDuration: 1000,
        );

        when(mockTtsService.speakWithEmotion(any, any))
            .thenAnswer((_) async => true);

        // Act
        await narrationService.narrateScene(scene, storyId: 'story1');

        // Assert
        expect(narrationService.currentState.status, NarrationStatus.loading);
        verify(mockTtsService.speakWithEmotion(any, 'calm')).called(1);
      });

      test('should handle scene narration failure', () async {
        // Arrange
        await narrationService.initialize();
        const scene = EnhancedSceneModel(
          id: 'scene1',
          text: 'Test text',
          speaker: 'Narrator',
          emotion: 'neutral',
          image: 'scene1.jpg',
          pauseDuration: 1000,
        );

        when(mockTtsService.speakWithEmotion(any, any))
            .thenThrow(Exception('TTS error'));

        // Act & Assert
        expect(
          () => narrationService.narrateScene(scene, storyId: 'story1'),
          throwsA(isA<StoryNarrationException>()),
        );
      });

      test('should throw exception when not initialized', () async {
        // Arrange
        const scene = EnhancedSceneModel(
          id: 'scene1',
          text: 'Test text',
          speaker: 'Narrator',
          emotion: 'neutral',
          image: 'scene1.jpg',
          pauseDuration: 1000,
        );

        // Act & Assert
        expect(
          () => narrationService.narrateScene(scene, storyId: 'story1'),
          throwsA(isA<StoryNarrationException>()),
        );
      });
    });

    group('Text Narration', () {
      test('should start text narration successfully', () async {
        // Arrange
        await narrationService.initialize();
        const text = 'Hello world';
        const emotionCue = 'happy';

        when(mockTtsService.speakWithEmotion(any, any))
            .thenAnswer((_) async => true);

        // Act
        await narrationService.narrateText(
          text,
          emotionCue: emotionCue,
          storyId: 'story1',
          sceneId: 'scene1',
        );

        // Assert
        expect(narrationService.currentState.status, NarrationStatus.loading);
        verify(mockTtsService.speakWithEmotion(any, emotionCue)).called(1);
      });
    });

    group('Playback Controls', () {
      test('should play narration', () async {
        // Arrange
        await narrationService.initialize();
        when(mockTtsService.resume()).thenAnswer((_) async => true);

        // Act
        await narrationService.play();

        // Assert
        // Verify that appropriate TTS method was called
        // (Implementation depends on current state)
      });

      test('should pause narration', () async {
        // Arrange
        await narrationService.initialize();
        when(mockTtsService.pause()).thenAnswer((_) async => true);

        // Act
        await narrationService.pause();

        // Assert
        verify(mockTtsService.pause()).called(1);
      });

      test('should stop narration', () async {
        // Arrange
        await narrationService.initialize();
        when(mockTtsService.stop()).thenAnswer((_) async => true);

        // Act
        await narrationService.stop();

        // Assert
        expect(narrationService.currentState.status, NarrationStatus.idle);
        verify(mockTtsService.stop()).called(1);
      });
    });

    group('Navigation Controls', () {
      test('should skip to next sentence', () async {
        // Arrange
        await narrationService.initialize();
        const scene = EnhancedSceneModel(
          id: 'scene1',
          text: 'First sentence. Second sentence. Third sentence.',
          speaker: 'Narrator',
          emotion: 'neutral',
          image: 'scene1.jpg',
          pauseDuration: 1000,
        );

        when(mockTtsService.speakWithEmotion(any, any))
            .thenAnswer((_) async => true);

        await narrationService.narrateScene(scene, storyId: 'story1');

        // Act
        await narrationService.skipToNextSentence();

        // Assert
        // Verify that the next sentence is being narrated
        verify(mockTtsService.speakWithEmotion(any, any)).called(greaterThan(1));
      });

      test('should skip to previous sentence', () async {
        // Arrange
        await narrationService.initialize();
        const scene = EnhancedSceneModel(
          id: 'scene1',
          text: 'First sentence. Second sentence. Third sentence.',
          speaker: 'Narrator',
          emotion: 'neutral',
          image: 'scene1.jpg',
          pauseDuration: 1000,
        );

        when(mockTtsService.speakWithEmotion(any, any))
            .thenAnswer((_) async => true);

        await narrationService.narrateScene(scene, storyId: 'story1');
        await narrationService.skipToNextSentence();

        // Act
        await narrationService.skipToPreviousSentence();

        // Assert
        // Verify that the previous sentence is being narrated
        verify(mockTtsService.speakWithEmotion(any, any)).called(greaterThan(2));
      });

      test('should seek to specific word', () async {
        // Arrange
        await narrationService.initialize();
        const scene = EnhancedSceneModel(
          id: 'scene1',
          text: 'The quick brown fox jumps over the lazy dog.',
          speaker: 'Narrator',
          emotion: 'neutral',
          image: 'scene1.jpg',
          pauseDuration: 1000,
        );

        when(mockTtsService.speakWithEmotion(any, any))
            .thenAnswer((_) async => true);

        await narrationService.narrateScene(scene, storyId: 'story1');

        // Act
        await narrationService.seekToWord(5);

        // Assert
        // Verify that narration started from the correct position
        verify(mockTtsService.speakWithEmotion(any, any)).called(greaterThan(1));
      });

      test('should replay current sentence', () async {
        // Arrange
        await narrationService.initialize();
        const scene = EnhancedSceneModel(
          id: 'scene1',
          text: 'This is a test sentence.',
          speaker: 'Narrator',
          emotion: 'neutral',
          image: 'scene1.jpg',
          pauseDuration: 1000,
        );

        when(mockTtsService.speakWithEmotion(any, any))
            .thenAnswer((_) async => true);

        await narrationService.narrateScene(scene, storyId: 'story1');

        // Act
        await narrationService.replayCurrentSentence();

        // Assert
        verify(mockTtsService.speakWithEmotion(any, any)).called(2);
      });
    });

    group('Character Voices', () {
      test('should set character voice', () async {
        // Arrange
        await narrationService.initialize();
        const characterId = 'hero';
        const voiceConfig = NarrationConfig(
          speechRate: 0.6,
          speechPitch: 1.1,
          emotionCue: 'brave',
        );

        // Act
        await narrationService.setCharacterVoice(characterId, voiceConfig);

        // Assert
        expect(narrationService.getCharacterVoice(characterId), voiceConfig);
      });

      test('should narrate scene with character voices', () async {
        // Arrange
        await narrationService.initialize();
        const scene = EnhancedSceneModel(
          id: 'scene1',
          text: 'Hello, I am the hero!',
          speaker: 'Hero',
          emotion: 'confident',
          image: 'scene1.jpg',
          pauseDuration: 1000,
        );

        const characterVoices = {
          'Hero': NarrationConfig(
            speechRate: 0.7,
            speechPitch: 1.2,
            emotionCue: 'heroic',
          ),
        };

        when(mockTtsService.speakWithEmotion(any, any))
            .thenAnswer((_) async => true);

        // Act
        await narrationService.narrateSceneWithCharacterVoices(
          scene,
          storyId: 'story1',
          characterVoices: characterVoices,
        );

        // Assert
        expect(narrationService.getCharacterVoice('Hero'), isNotNull);
        verify(mockTtsService.speakWithEmotion(any, any)).called(1);
      });
    });

    group('Progress Tracking', () {
      test('should track narration progress', () async {
        // Arrange
        await narrationService.initialize();
        const scene = EnhancedSceneModel(
          id: 'scene1',
          text: 'This is a test sentence for progress tracking.',
          speaker: 'Narrator',
          emotion: 'neutral',
          image: 'scene1.jpg',
          pauseDuration: 1000,
        );

        when(mockTtsService.speakWithEmotion(any, any))
            .thenAnswer((_) async => true);

        // Act
        await narrationService.narrateScene(scene, storyId: 'story1');

        // Assert
        final progress = narrationService.getCurrentProgress();
        expect(progress, isNotNull);
        expect(progress!.storyId, 'story1');
        expect(progress.sceneId, 'scene1');
        expect(progress.totalWords, greaterThan(0));
      });
    });

    group('Disposal', () {
      test('should dispose properly', () async {
        // Arrange
        await narrationService.initialize();
        when(mockTtsService.dispose()).thenAnswer((_) async {});

        // Act
        await narrationService.dispose();

        // Assert
        expect(narrationService.isInitialized, false);
        verify(mockTtsService.dispose()).called(1);
      });
    });
  });
}
