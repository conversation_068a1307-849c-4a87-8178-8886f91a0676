import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:choice_once_upon_a_time/shared_widgets/primary_button_widget.dart';
import 'package:choice_once_upon_a_time/app/providers/settings_provider.dart'; // Assuming ftueCompletedProvider is here
import 'package:choice_once_upon_a_time/core/audio/voice_guidance_manager.dart';
// import 'package:choice_once_upon_a_time/core/mixins/screen_narrator_mixin.dart'; // Import the new VoiceGuidanceManager
import 'package:choice_once_upon_a_time/l10n/app_localizations.dart'; // Required for AppLocalizations

/// First Time User Experience screen
class FTUEScreen extends ConsumerStatefulWidget { // Changed from ConsumerWidget to ConsumerStatefulWidget
  const FTUEScreen({super.key});

  @override
  ConsumerState<FTUEScreen> createState() => _FTUEScreenState();
}

class _FTUEScreenState extends ConsumerState<FTUEScreen> { // State class for FTUEScreen
  @override
  void initState() {
    super.initState();
    // Play the initial FTUE welcome guide after the widget has been built.
    WidgetsBinding.instance.addPostFrameCallback((_) {
      // Play screen introduction first, then the detailed FTUE guide
      // playScreenIntroduction(ref, 'screen_ftue_intro').then((_) {
        _playFtueWelcomeGuide();
      // });
    });
  }

  @override
  void dispose() {
    // Stop any active voice guidance when the screen is disposed
    // Check if widget is still mounted before accessing ref
    if (mounted) {
      try {
        ref.read(voiceGuidanceManagerProvider).stopGuide();
      } catch (e) {
        print("FTUE Dispose: Error stopping voice guide: $e");
      }
    }
    super.dispose();
  }

  Future<void> _playFtueWelcomeGuide() async {
    print("FTUE Guide: _playFtueWelcomeGuide started."); // <--- ADD THIS
  // Add a small delay here to ensure TTS service is fully ready
    await Future.delayed(const Duration(milliseconds: 500)); // Added delay

    if (!mounted) {
      print("FTUE Guide: Widget not mounted, exiting."); // <--- ADD THIS
      return;
    }
    final voiceGuidanceManager = ref.read(voiceGuidanceManagerProvider);
    final appLocalizations = AppLocalizations.of(context);

    // Check if localizations are available
    if (appLocalizations == null) {
      print("FTUE Guide: AppLocalizations not available, exiting.");
      return;
    }

    // Play the main welcome message
    await voiceGuidanceManager.playGuide(context, (l) => l.ftueScreenWelcome);

    // After a delay, sequentially introduce features
    await Future.delayed(const Duration(seconds: 4)); // Adjust delay as needed

    if (!mounted) return;
    await voiceGuidanceManager.playGuide(context, (l) => l.ftueFeatureInteractiveStories);
    await Future.delayed(const Duration(seconds: 3));

    if (!mounted) return;
    await voiceGuidanceManager.playGuide(context, (l) => l.ftueFeatureLifeValues);
    await Future.delayed(const Duration(seconds: 3));

    if (!mounted) return;
    await voiceGuidanceManager.playGuide(context, (l) => l.ftueFeatureNarration);
    await Future.delayed(const Duration(seconds: 3));

    if (!mounted) return;
    // Final prompt for user action
    await voiceGuidanceManager.playGuide(context, (l) => l.ftueCompletePrompt);
  }

  void _completeFTUE(WidgetRef ref, BuildContext context) {
    ref.read(voiceGuidanceManagerProvider).stopGuide(); // Stop guide on completion
    ref.read(settingsProvider.notifier).completeFTUE();
    context.go('/home');
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isPortrait = MediaQuery.of(context).orientation == Orientation.portrait;
    final isSmallScreen = MediaQuery.of(context).size.height < 600;

    return PopScope(
      canPop: false,
      onPopInvokedWithResult: (bool didPop, Object? result) async {
        if (didPop) return;

        // Store navigator before async operations
        final navigator = Navigator.of(context);

        // Show exit confirmation dialog for FTUE screen
        final shouldExit = await _showExitConfirmationDialog(context);
        if (shouldExit == true && mounted) {
          // Stop voice guidance and exit the app
          ref.read(voiceGuidanceManagerProvider).stopGuide();
          // Exit the app
          navigator.pop();
        }
      },
      child: Scaffold(
        backgroundColor: theme.scaffoldBackgroundColor,
        body: SafeArea(
          child: Padding(
            padding: EdgeInsets.symmetric(
              horizontal: 20,
              vertical: isSmallScreen ? 12 : 16
            ),
            child: Column(
              children: [
                // Skip button
                Align(
                  alignment: Alignment.topRight,
                  child: TextButton(
                    onPressed: () {
                      ref.read(voiceGuidanceManagerProvider).playGuide(context, (l) => l.ftueSkipPrompt); // Optional: speak on skip tap
                      _completeFTUE(ref, context);
                    },
                    child: const Text('Skip'),
                  ),
                ),

                // Main content
                Expanded(
                  child: isPortrait ? _buildPortraitLayout(theme, isSmallScreen) : _buildLandscapeLayout(theme),
                ),

                // Get Started button
                PrimaryButtonWidget(
                  text: 'Start Reading',
                  onPressed: () => _completeFTUE(ref, context),
                  icon: const Icon(Icons.arrow_forward),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// Show exit confirmation dialog for the FTUE screen
  Future<bool?> _showExitConfirmationDialog(BuildContext context) {
    return showDialog<bool>(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Exit App'),
          content: const Text('Are you sure you want to exit before completing the introduction?'),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: const Text('Cancel'),
            ),
            ElevatedButton(
              onPressed: () => Navigator.of(context).pop(true),
              child: const Text('Exit'),
            ),
          ],
        );
      },
    );
  }

  Widget _buildPortraitLayout(ThemeData theme, bool isSmallScreen) {
    final spacing = isSmallScreen ? 8.0 : 12.0;
    
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        // App icon
        Container(
          width: isSmallScreen ? 80 : 100,
          height: isSmallScreen ? 80 : 100,
          decoration: BoxDecoration(
            color: theme.colorScheme.primary.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(isSmallScreen ? 40 : 50),
          ),
          child: Icon(
            Icons.auto_stories,
            size: isSmallScreen ? 32 : 40,
            color: theme.colorScheme.primary,
          ),
        ),
        
        SizedBox(height: spacing),
        
        Text(
          'Welcome to Choice',
          style: theme.textTheme.headlineMedium?.copyWith(
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.primary,
          ),
        ),

        const SizedBox(height: 4),
        
        Text(
          'Start your interactive story adventure',
          style: theme.textTheme.bodyMedium?.copyWith(
            color: theme.colorScheme.onSurfaceVariant,
          ),
          textAlign: TextAlign.center,
        ),
        
        SizedBox(height: spacing * 2),
        
        // Core features
        _buildFeatureItem(
          theme, 
          Icons.book_outlined, 
          'Interactive Stories',
          'Choose your own path in every tale',
        ),
        SizedBox(height: spacing),
        _buildFeatureItem(
          theme, 
          Icons.favorite_outline, 
          'Life Values',
          'Stories that teach and inspire',
        ),
        SizedBox(height: spacing),
        _buildFeatureItem(
          theme, 
          Icons.volume_up_outlined, 
          'Narration',
          'Listen to your stories come alive',
        ),

        const Spacer(),
      ],
    );
  }

  Widget _buildLandscapeLayout(ThemeData theme) {
    return Row(
      children: [
        // Left side - App icon
        Expanded(
          flex: 2,
          child: Container(
            padding: const EdgeInsets.all(16),
            child: Container(
              decoration: BoxDecoration(
                color: theme.colorScheme.primary.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(40),
              ),
              child: Icon(
                Icons.auto_stories,
                size: 40,
                color: theme.colorScheme.primary,
              ),
            ),
          ),
        ),
        
        // Right side - Content
        Expanded(
          flex: 3,
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Welcome to Choice',
                style: theme.textTheme.headlineMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: theme.colorScheme.primary,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                'Start your interactive story adventure',
                style: theme.textTheme.bodyMedium?.copyWith(
                  color: theme.colorScheme.onSurfaceVariant,
                ),
              ),
              const SizedBox(height: 16),
              _buildFeatureItem(
                theme, 
                Icons.book_outlined, 
                'Interactive Stories',
                'Choose your own path in every tale',
              ),
              const SizedBox(height: 8),
              _buildFeatureItem(
                theme, 
                Icons.favorite_outline, 
                'Life Values',
                'Stories that teach and inspire',
              ),
              const SizedBox(height: 8),
              _buildFeatureItem(
                theme, 
                Icons.volume_up_outlined, 
                'Narration',
                'Listen to your stories come alive',
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildFeatureItem(ThemeData theme, IconData icon, String title, String subtitle) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          width: 32,
          height: 32,
          decoration: BoxDecoration(
            color: theme.colorScheme.secondary.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(16),
          ),
          child: Icon(
            icon,
            color: theme.colorScheme.secondary,
            size: 16,
          ),
        ),
        
        const SizedBox(width: 12),
        
        Flexible(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: theme.textTheme.titleSmall?.copyWith(
                  fontWeight: FontWeight.w500,
                ),
              ),
              Text(
                subtitle,
                style: theme.textTheme.bodySmall?.copyWith(
                  color: theme.colorScheme.onSurfaceVariant,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}