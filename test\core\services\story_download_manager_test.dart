import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:choice_once_upon_a_time/core/services/story_download_manager.dart';
import 'package:choice_once_upon_a_time/core/services/firebase_storage_service.dart';
import 'package:choice_once_upon_a_time/core/services/zip_extraction_service.dart';
import 'package:choice_once_upon_a_time/models/firebase_story_metadata.dart';

import 'story_download_manager_test.mocks.dart';

@GenerateMocks([
  FirebaseStorageService,
  ZipExtractionService,
])
void main() {
  group('StoryDownloadManager Tests', () {
    late StoryDownloadManager downloadManager;
    late MockFirebaseStorageService mockFirebaseStorage;
    late MockZipExtractionService mockZipExtraction;

    setUp(() {
      mockFirebaseStorage = MockFirebaseStorageService();
      mockZipExtraction = MockZipExtractionService();
      
      // Create a new instance for each test
      downloadManager = StoryDownloadManager();
      
      // Replace the internal services with mocks
      // Note: This would require making the services injectable in the actual implementation
    });

    tearDown(() {
      downloadManager.dispose();
    });

    group('downloadStory', () {
      test('should download story successfully and emit progress updates', () async {
        // Arrange
        const storyId = 'test_story';
        const zipPath = '/path/to/story.zip';
        const extractedPath = '/path/to/extracted/story';

        // Track progress updates
        final progressUpdates = <DownloadProgress>[];
        downloadManager.downloadProgress.listen((progress) {
          progressUpdates.add(progress);
        });

        // Mock the download process
        when(mockZipExtraction.isStoryDownloaded(storyId))
            .thenAnswer((_) async => false);
        when(mockFirebaseStorage.storyExists(storyId))
            .thenAnswer((_) async => true);
        when(mockFirebaseStorage.downloadStoryZip(
          storyId,
          onProgress: anyNamed('onProgress'),
        )).thenAnswer((invocation) async {
          final onProgress = invocation.namedArguments[#onProgress] as Function(double)?;
          onProgress?.call(0.5); // Simulate 50% download progress
          onProgress?.call(1.0); // Simulate 100% download progress
          return zipPath;
        });
        when(mockZipExtraction.extractStoryZip(
          zipPath,
          storyId,
          onProgress: anyNamed('onProgress'),
        )).thenAnswer((invocation) async {
          final onProgress = invocation.namedArguments[#onProgress] as Function(double)?;
          onProgress?.call(0.5); // Simulate 50% extraction progress
          onProgress?.call(1.0); // Simulate 100% extraction progress
          return extractedPath;
        });
        when(mockZipExtraction.validateStoryStructure(extractedPath))
            .thenAnswer((_) async => true);

        // Act
        final result = await downloadManager.downloadStory(storyId);

        // Assert
        expect(result, isTrue);
        
        // Wait a bit for progress updates to be emitted
        await Future.delayed(const Duration(milliseconds: 100));
        
        // Verify progress updates were emitted
        expect(progressUpdates.isNotEmpty, isTrue);
        expect(progressUpdates.last.status, equals(DownloadStatus.downloaded));
        expect(progressUpdates.last.progress, equals(1.0));
      });

      test('should return true if story already downloaded', () async {
        // Arrange
        const storyId = 'test_story';

        when(mockZipExtraction.isStoryDownloaded(storyId))
            .thenAnswer((_) async => true);

        // Act
        final result = await downloadManager.downloadStory(storyId);

        // Assert
        expect(result, isTrue);
        verifyNever(mockFirebaseStorage.downloadStoryZip(any, onProgress: anyNamed('onProgress')));
      });

      test('should return false and emit failed status on error', () async {
        // Arrange
        const storyId = 'test_story';

        // Track progress updates
        final progressUpdates = <DownloadProgress>[];
        downloadManager.downloadProgress.listen((progress) {
          progressUpdates.add(progress);
        });

        when(mockZipExtraction.isStoryDownloaded(storyId))
            .thenAnswer((_) async => false);
        when(mockFirebaseStorage.storyExists(storyId))
            .thenAnswer((_) async => false);

        // Act
        final result = await downloadManager.downloadStory(storyId);

        // Assert
        expect(result, isFalse);
        
        // Wait a bit for progress updates to be emitted
        await Future.delayed(const Duration(milliseconds: 100));
        
        // Verify failed status was emitted
        expect(progressUpdates.isNotEmpty, isTrue);
        expect(progressUpdates.last.status, equals(DownloadStatus.failed));
      });
    });

    group('download status tracking', () {
      test('should track download progress correctly', () {
        // Arrange
        const storyId = 'test_story';
        
        // Act
        downloadManager.downloadStory(storyId);
        
        // Assert
        expect(downloadManager.isDownloading(storyId), isTrue);
        expect(downloadManager.isDownloadFailed(storyId), isFalse);
      });

      test('should return correct download progress percentage', () {
        // Arrange
        const storyId = 'test_story';
        
        // Act - Initially no progress
        final initialProgress = downloadManager.getDownloadProgressPercentage(storyId);
        
        // Assert
        expect(initialProgress, equals(0.0));
      });

      test('should track active downloads', () {
        // Arrange
        const storyId1 = 'story1';
        const storyId2 = 'story2';
        
        // Act
        downloadManager.downloadStory(storyId1);
        downloadManager.downloadStory(storyId2);
        
        final activeDownloads = downloadManager.activeDownloads;
        
        // Assert
        expect(activeDownloads.length, greaterThanOrEqualTo(0)); // May be 0 if downloads complete quickly
      });
    });

    group('cancelDownload', () {
      test('should cancel download and emit cancelled status', () async {
        // Arrange
        const storyId = 'test_story';

        // Track progress updates
        final progressUpdates = <DownloadProgress>[];
        downloadManager.downloadProgress.listen((progress) {
          progressUpdates.add(progress);
        });

        // Act
        await downloadManager.cancelDownload(storyId);

        // Assert
        // Wait a bit for progress updates to be emitted
        await Future.delayed(const Duration(milliseconds: 100));
        
        // Verify cancelled status was emitted
        expect(progressUpdates.isNotEmpty, isTrue);
        expect(progressUpdates.last.status, equals(DownloadStatus.cancelled));
      });
    });

    group('deleteDownloadedStory', () {
      test('should delete story and return true on success', () async {
        // Arrange
        const storyId = 'test_story';
        const storyPath = '/path/to/story';

        when(mockZipExtraction.getStoryPath(storyId))
            .thenAnswer((_) async => storyPath);

        // Act
        final result = await downloadManager.deleteDownloadedStory(storyId);

        // Assert
        expect(result, isTrue);
      });

      test('should return false on error', () async {
        // Arrange
        const storyId = 'test_story';

        when(mockZipExtraction.getStoryPath(storyId))
            .thenThrow(Exception('Path not found'));

        // Act
        final result = await downloadManager.deleteDownloadedStory(storyId);

        // Assert
        expect(result, isFalse);
      });
    });

    group('progress stream', () {
      test('should emit progress updates through stream', () async {
        // Arrange
        const storyId = 'test_story';
        final progressUpdates = <DownloadProgress>[];

        // Listen to progress stream
        final subscription = downloadManager.downloadProgress.listen((progress) {
          progressUpdates.add(progress);
        });

        // Act
        await downloadManager.cancelDownload(storyId); // This should emit a progress update

        // Wait for stream updates
        await Future.delayed(const Duration(milliseconds: 100));

        // Assert
        expect(progressUpdates.isNotEmpty, isTrue);

        // Cleanup
        await subscription.cancel();
      });
    });

    group('getDownloadProgress', () {
      test('should return null for non-existent download', () {
        // Arrange
        const storyId = 'nonexistent_story';

        // Act
        final progress = downloadManager.getDownloadProgress(storyId);

        // Assert
        expect(progress, isNull);
      });
    });
  });
}
