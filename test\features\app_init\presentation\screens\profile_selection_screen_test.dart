import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:choice_once_upon_a_time/features/app_init/presentation/screens/profile_selection_screen.dart';
import 'package:choice_once_upon_a_time/features/parent_zone/data/user_profile_service.dart';

void main() {
  group('ProfileSelectionScreen', () {
    late ProviderContainer container;
    late GoRouter router;

    setUp(() {
      container = ProviderContainer();
      router = GoRouter(
        routes: [
          GoRoute(
            path: '/',
            builder: (context, state) => const ProfileSelectionScreen(),
          ),
          GoRoute(
            path: '/home',
            builder: (context, state) => const Scaffold(body: Text('Home')),
          ),
        ],
      );
    });

    tearDown(() {
      container.dispose();
    });

    Widget createWidget() {
      return UncontrolledProviderScope(
        container: container,
        child: MaterialApp.router(
          routerConfig: router,
        ),
      );
    }

    testWidgets('displays create first profile when no profiles exist', (tester) async {
      // Mock empty profiles
      container.read(userProfilesProvider.notifier).state = const AsyncValue.data([]);

      await tester.pumpWidget(createWidget());
      await tester.pumpAndSettle();

      expect(find.text('Welcome to Choice!'), findsOneWidget);
      expect(find.text('Create Child Profile'), findsOneWidget);
      expect(find.text('Continue without profile'), findsOneWidget);
    });

    testWidgets('displays profile selection when profiles exist', (tester) async {
      // Mock profiles
      final profiles = [
        UserProfile(
          id: '1',
          name: 'Alice',
          age: 5,
          avatarColor: Colors.blue,
          favoriteStories: [],
          totalReadingTime: 0,
          storiesCompleted: 0,
          createdAt: DateTime.now(),
        ),
        UserProfile(
          id: '2',
          name: 'Bob',
          age: 7,
          avatarColor: Colors.red,
          favoriteStories: [],
          totalReadingTime: 0,
          storiesCompleted: 0,
          createdAt: DateTime.now(),
        ),
      ];
      
      container.read(userProfilesProvider.notifier).state = AsyncValue.data(profiles);

      await tester.pumpWidget(createWidget());
      await tester.pumpAndSettle();

      expect(find.text('Choose Your Profile'), findsOneWidget);
      expect(find.text('Alice'), findsOneWidget);
      expect(find.text('Bob'), findsOneWidget);
      expect(find.text('5 years old'), findsOneWidget);
      expect(find.text('7 years old'), findsOneWidget);
      expect(find.text('Add New Child'), findsOneWidget);
    });

    testWidgets('shows loading indicator while profiles are loading', (tester) async {
      // Mock loading state
      container.read(userProfilesProvider.notifier).state = const AsyncValue.loading();

      await tester.pumpWidget(createWidget());
      await tester.pump();

      expect(find.byType(CircularProgressIndicator), findsOneWidget);
    });

    testWidgets('shows error state when profile loading fails', (tester) async {
      // Mock error state
      container.read(userProfilesProvider.notifier).state = 
          AsyncValue.error('Failed to load profiles', StackTrace.empty);

      await tester.pumpWidget(createWidget());
      await tester.pumpAndSettle();

      expect(find.text('Error loading profiles'), findsOneWidget);
      expect(find.text('Retry'), findsOneWidget);
    });

    testWidgets('can tap profile card to select profile', (tester) async {
      final profile = UserProfile(
        id: '1',
        name: 'Alice',
        age: 5,
        avatarColor: Colors.blue,
        favoriteStories: [],
        totalReadingTime: 0,
        storiesCompleted: 0,
        createdAt: DateTime.now(),
      );
      
      container.read(userProfilesProvider.notifier).state = AsyncValue.data([profile]);

      await tester.pumpWidget(createWidget());
      await tester.pumpAndSettle();

      // Tap on profile card
      await tester.tap(find.text('Alice'));
      await tester.pumpAndSettle();

      // Verify profile was selected
      expect(container.read(selectedProfileProvider), equals(profile));
    });

    testWidgets('can tap create new profile button', (tester) async {
      container.read(userProfilesProvider.notifier).state = const AsyncValue.data([]);

      await tester.pumpWidget(createWidget());
      await tester.pumpAndSettle();

      // Tap create profile button
      await tester.tap(find.text('Create Child Profile'));
      await tester.pumpAndSettle();

      // Verify dialog appears
      expect(find.text('Create New Profile'), findsOneWidget);
    });

    testWidgets('can continue without profile', (tester) async {
      container.read(userProfilesProvider.notifier).state = const AsyncValue.data([]);

      await tester.pumpWidget(createWidget());
      await tester.pumpAndSettle();

      // Tap continue without profile
      await tester.tap(find.text('Continue without profile'));
      await tester.pumpAndSettle();

      // Should navigate to home
      expect(find.text('Home'), findsOneWidget);
    });

    testWidgets('displays profile stats when available', (tester) async {
      final profile = UserProfile(
        id: '1',
        name: 'Alice',
        age: 5,
        avatarColor: Colors.blue,
        favoriteStories: ['story1', 'story2'],
        totalReadingTime: 120,
        storiesCompleted: 2,
        createdAt: DateTime.now(),
      );
      
      container.read(userProfilesProvider.notifier).state = AsyncValue.data([profile]);

      await tester.pumpWidget(createWidget());
      await tester.pumpAndSettle();

      expect(find.text('2 stories'), findsOneWidget);
    });

    testWidgets('responsive layout adjusts grid columns', (tester) async {
      final profiles = List.generate(4, (index) => UserProfile(
        id: '$index',
        name: 'Child $index',
        age: 5 + index,
        avatarColor: Colors.blue,
        favoriteStories: [],
        totalReadingTime: 0,
        storiesCompleted: 0,
        createdAt: DateTime.now(),
      ));
      
      container.read(userProfilesProvider.notifier).state = AsyncValue.data(profiles);

      // Test small screen (2 columns)
      await tester.binding.setSurfaceSize(const Size(400, 800));
      await tester.pumpWidget(createWidget());
      await tester.pumpAndSettle();

      // Verify grid layout
      final gridView = tester.widget<GridView>(find.byType(GridView));
      final delegate = gridView.gridDelegate as SliverGridDelegateWithFixedCrossAxisCount;
      expect(delegate.crossAxisCount, equals(2));

      // Test large screen (3 columns)
      await tester.binding.setSurfaceSize(const Size(800, 600));
      await tester.pumpWidget(createWidget());
      await tester.pumpAndSettle();

      final gridView2 = tester.widget<GridView>(find.byType(GridView));
      final delegate2 = gridView2.gridDelegate as SliverGridDelegateWithFixedCrossAxisCount;
      expect(delegate2.crossAxisCount, equals(3));

      // Reset surface size
      await tester.binding.setSurfaceSize(null);
    });

    testWidgets('handles profile creation flow', (tester) async {
      container.read(userProfilesProvider.notifier).state = const AsyncValue.data([]);

      await tester.pumpWidget(createWidget());
      await tester.pumpAndSettle();

      // Open create profile dialog
      await tester.tap(find.text('Create Child Profile'));
      await tester.pumpAndSettle();

      // Fill in profile details
      await tester.enterText(find.byType(TextField), 'New Child');
      await tester.pumpAndSettle();

      // Tap create button
      await tester.tap(find.text('Create Profile'));
      await tester.pumpAndSettle();

      // Verify profile creation was attempted
      // Note: In a real test, you'd mock the UserProfileService
    });
  });
}
