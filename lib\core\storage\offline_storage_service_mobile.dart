import 'dart:convert';
import 'dart:io';
import 'dart:math';
import 'dart:typed_data';
import 'package:path_provider/path_provider.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:choice_once_upon_a_time/models/story_model.dart';
import 'package:choice_once_upon_a_time/models/story_metadata_model.dart';

/// Exception thrown when storage operations fail
class StorageException implements Exception {
  final String message;
  final String? technicalDetails;

  StorageException(this.message, {this.technicalDetails});

  @override
  String toString() => 'StorageException: $message${technicalDetails != null ? '\nDetails: $technicalDetails' : ''}';
}

/// Service for managing offline story storage and downloads
class OfflineStorageService {
  static final OfflineStorageService _instance = OfflineStorageService._internal();
  factory OfflineStorageService() => _instance;
  OfflineStorageService._internal();

  Directory? _storageDirectory;
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseStorage _storage = FirebaseStorage.instance;
  bool _isCancelled = false;

  /// Initialize the storage service
  Future<void> initDatabase() async {
    try {
      _storageDirectory = await getApplicationSupportDirectory();
      final storiesDir = Directory('${_storageDirectory!.path}/stories');
      if (!await storiesDir.exists()) {
        await storiesDir.create(recursive: true);
      }
      print('OfflineStorageService: Initialized with storage at ${storiesDir.path}');
      // Add detailed path information
      print('Full storage path: ${_storageDirectory!.path}');
      print('Stories directory: ${storiesDir.path}');
      print('Parent directory: ${_storageDirectory!.parent.path}');
    } catch (e) {
      print('OfflineStorageService: Error initializing storage: $e');
      throw StorageException('Failed to initialize storage', technicalDetails: e.toString());
    }
  }

  /// Check if a story is downloaded and available offline
  Future<bool> isStoryDownloaded(String storyId, String version) async {
    try {
      if (_storageDirectory == null) await initDatabase();

      final storyDir = Directory('${_storageDirectory!.path}/stories/$storyId/$version');
      final storyFile = File('${storyDir.path}/story.json');
      final manifestFile = File('${storyDir.path}/manifest.json');

      if (!await storyFile.exists() || !await manifestFile.exists()) {
        return false;
      }

      // Check if all assets are downloaded
      final manifestContent = await manifestFile.readAsString();
      final manifest = jsonDecode(manifestContent) as Map<String, dynamic>;
      final assets = manifest['assets'] as List<dynamic>? ?? [];

      for (final asset in assets) {
        final assetPath = asset['localPath'] as String;
        final assetFile = File('${storyDir.path}/$assetPath');
        if (!await assetFile.exists()) {
          return false;
        }
      }

      return true;
    } catch (e) {
      print('OfflineStorageService: Error checking if story is downloaded: $e');
      return false;
    }
  }

  /// Get a locally stored story
  Future<StoryModel?> getLocalStory(String storyId, {String version = '1.0.0'}) async {
    try {
      if (_storageDirectory == null) await initDatabase();

      final storyFile = File('${_storageDirectory!.path}/stories/$storyId/$version/story.json');
      if (!await storyFile.exists()) {
        return null;
      }

      final jsonContent = await storyFile.readAsString();
      final jsonData = jsonDecode(jsonContent) as Map<String, dynamic>;
      return StoryModel.fromJson(jsonData);
    } catch (e) {
      print('OfflineStorageService: Error loading local story: $e');
      return null;
    }
  }

  /// Check if there's enough storage space for a download
  Future<bool> hasEnoughStorageSpace(int sizeInBytes) async {
    try {
      if (_storageDirectory == null) await initDatabase();

      // Get the application support directory
      final dir = await getApplicationSupportDirectory();
      
      // For now, we'll assume we have enough space
      // In a production environment, we would check actual free space
      // using platform-specific methods
      return true;
    } catch (e) {
      print('OfflineStorageService: Error checking storage space: $e');
      return false;
    }
  }

  /// Download a file from Firebase Storage with retries
  Future<Uint8List?> _fetchFromStorage(String url, {int maxRetries = 3}) async {
    final random = Random();
    
    for (var attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        final ref = _storage.refFromURL(url);
        final data = await ref.getData();
        if (data != null) {
          return data;
        }
      } catch (e) {
        print('OfflineStorageService: Download attempt $attempt failed: $e');
        if (attempt < maxRetries) {
          // Exponential backoff with jitter
          final delay = Duration(milliseconds: attempt * 1000 + random.nextInt(1000));
          await Future.delayed(delay);
        }
      }
    }
    return null;
  }

  /// Download a story for offline access
  Future<bool> downloadStory(StoryMetadataModel storyMeta, {
    Function(double progress)? onProgress,
    Function(String status)? onStatusUpdate,
  }) async {
    try {
      _isCancelled = false;
      if (_storageDirectory == null) await initDatabase();

      // Initial setup and validation
      onStatusUpdate?.call('Checking storage space...');
      if (_isCancelled) {
        onStatusUpdate?.call('Download cancelled');
        return false;
      }

      // Check if already downloading or downloaded
      final isDownloaded = await isStoryDownloaded(storyMeta.id, storyMeta.version);
      if (isDownloaded) {
        onStatusUpdate?.call('Story already downloaded');
        return true;
      }

      // Check available storage space (using estimated size)
      final hasSpace = await hasEnoughStorageSpace(storyMeta.estimatedSizeMb * 1024 * 1024);
      if (!hasSpace) {
        onStatusUpdate?.call('Insufficient storage space');
        throw StorageException('Not enough storage space available');
      }

      // Create story directory structure
      final storyDir = Directory('${_storageDirectory!.path}/stories/${storyMeta.id}/${storyMeta.version}');
      await storyDir.create(recursive: true);

      // Progress tracking variables
      double currentProgress = 0.0;
      updateProgress(double delta) {
        currentProgress += delta;
        onProgress?.call(currentProgress);
      }

      // Download story data
      updateProgress(0.1);
      onStatusUpdate?.call('Downloading story content...');
      
      // Fetch story data from Firestore
      final storyDoc = await _firestore.collection('stories').doc(storyMeta.id).get();
      if (!storyDoc.exists) {
        throw StorageException('Story not found in Firestore');
      }

      // Save story data
      final storyData = {
        'id': storyDoc.id,
        ...storyDoc.data()!,
      };
      final storyFile = File('${storyDir.path}/story.json');
      await storyFile.writeAsString(jsonEncode(storyData));

      // Download assets
      updateProgress(0.2);
      onStatusUpdate?.call('Downloading assets...');

      try {
        // Fetch asset manifest
        final manifestRef = _storage.ref('stories/${storyMeta.id}/${storyMeta.version}/manifest.json');
        final manifestBytes = await manifestRef.getData();
        final List<Map<String, dynamic>> assets;
        
        if (manifestBytes != null) {
          final manifestString = String.fromCharCodes(manifestBytes);
          final manifestJson = jsonDecode(manifestString) as Map<String, dynamic>;
          assets = List<Map<String, dynamic>>.from(manifestJson['assets'] ?? []);
        } else {
          throw StorageException('Asset manifest is empty');
        }

        // Download each asset
        final totalAssets = assets.length;
        final progressPerAsset = 0.6 / totalAssets; // 60% of progress bar for assets
        
        for (var i = 0; i < assets.length; i++) {
          if (_isCancelled) {
            onStatusUpdate?.call('Download cancelled');
            await _cleanupCancelledDownload(storyMeta);
            return false;
          }

          final asset = assets[i];
          onStatusUpdate?.call('Downloading asset ${i + 1} of $totalAssets');
          
          await _downloadAsset(asset, storyDir.path, storyMeta);
          updateProgress(progressPerAsset);
        }

        // Save final manifest
        onStatusUpdate?.call('Finalizing download...');
        updateProgress(0.1);

        final manifest = {
          'storyId': storyMeta.id,
          'version': storyMeta.version,
          'downloadedAt': DateTime.now().toIso8601String(),
          'assets': assets,
          'totalSize': await _calculateDirectorySize(storyDir),
        };

        final manifestFile = File('${storyDir.path}/manifest.json');
        await manifestFile.writeAsString(jsonEncode(manifest));

        // Final validation
        final isValid = await isStoryDownloaded(storyMeta.id, storyMeta.version);
        if (!isValid) {
          throw StorageException('Download validation failed');
        }

        onStatusUpdate?.call('Download complete!');
        updateProgress(1.0 - currentProgress); // Ensure we reach 100%

        print('OfflineStorageService: Successfully downloaded story ${storyMeta.id}');
        return true;
      } catch (e) {
        print('OfflineStorageService: Error downloading assets: $e');
        await _cleanupCancelledDownload(storyMeta);
        throw StorageException('Failed to download assets', technicalDetails: e.toString());
      }
    } catch (e) {
      print('OfflineStorageService: Error downloading story: $e');
      onStatusUpdate?.call('Download failed: ${e.toString()}');
      await _cleanupCancelledDownload(storyMeta);
      throw StorageException('Failed to download story', technicalDetails: e.toString());
    }
  }

  /// Calculate directory size in bytes
  Future<int> _calculateDirectorySize(Directory directory) async {
    int size = 0;
    try {
      await for (final file in directory.list(recursive: true)) {
        if (file is File) {
          size += await file.length();
        }
      }
    } catch (e) {
      print('Error calculating directory size: $e');
    }
    return size;
  }

  /// Cancel the current download
  void cancelDownload() {
    _isCancelled = true;
  }

  /// Clean up partially downloaded files when a download is cancelled
  Future<void> _cleanupCancelledDownload(StoryMetadataModel storyMeta) async {
    try {
      final storyDir = Directory('${_storageDirectory!.path}/stories/${storyMeta.id}/${storyMeta.version}');
      if (await storyDir.exists()) {
        await storyDir.delete(recursive: true);
        print('OfflineStorageService: Cleaned up cancelled download for ${storyMeta.id}');
      }
    } catch (e) {
      print('OfflineStorageService: Error cleaning up cancelled download: $e');
    }
  }

  /// Download an individual asset from Firebase Storage
  Future<void> _downloadAsset(Map<String, dynamic> asset, String storyDirPath, StoryMetadataModel storyMeta) async {
    final localPath = asset['localPath'] as String;
    final assetFile = File('$storyDirPath/$localPath');
    
    try {
      // Create parent directories if they don't exist
      await assetFile.parent.create(recursive: true);

      // Skip if asset is already downloaded and valid
      if (await _validateDownloadedFile(assetFile, asset['size'] as int?, asset['checksum'] as String?)) {
        print('OfflineStorageService: Asset already downloaded and valid: $localPath');
        return;
      }

      // Handle placeholder assets
      if (asset['url'] == 'placeholder' || asset['url'] == null) {
        await _createPlaceholderAsset(assetFile, asset['type'] as String, storyMeta.id);
        return;
      }

      // Try to download the asset with retries
      final data = await _fetchFromStorage(asset['url'] as String);
      if (data == null) {
        throw StorageException('Failed to download asset after retries');
      }

      // Write the downloaded data
      await assetFile.writeAsBytes(data);

      // Validate the downloaded file
      if (!await _validateDownloadedFile(assetFile, asset['size'] as int?, asset['checksum'] as String?)) {
        throw StorageException('Downloaded asset failed validation');
      }
    } catch (e) {
      print('OfflineStorageService: Error downloading asset $localPath: $e');
      // Create placeholder for failed downloads
      await _createPlaceholderAsset(assetFile, asset['type'] as String, storyMeta.id);
    }
  }

  /// Create appropriate placeholder content for different asset types
  Future<void> _createPlaceholderAsset(File file, String type, String storyId) async {
    final content = switch (type) {
      'image' => 'placeholder_image_$storyId',
      'audio' => 'placeholder_audio_$storyId',
      'json' => jsonEncode({
        'type': 'placeholder',
        'storyId': storyId,
        'createdAt': DateTime.now().toIso8601String(),
      }),
      _ => 'placeholder_${type}_$storyId',
    };
    
    await file.writeAsString(content);
  }

  /// Create a basic manifest for stories without one
  List<Map<String, dynamic>> _createPlaceholderAssetManifest(StoryMetadataModel storyMeta) {
    return [
      {
        'type': 'image',
        'url': 'placeholder',
        'localPath': 'images/cover.png',
        'size': 1024,
      },
      {
        'type': 'image',
        'url': 'placeholder',
        'localPath': 'images/scene_01.png',
        'size': 2048,
      },
      {
        'type': 'audio',
        'url': 'placeholder',
        'localPath': 'audio/background_01.mp3',
        'size': 512,
      },
    ];
  }

  /// Validate a downloaded file
  Future<bool> _validateDownloadedFile(File file, int? expectedSize, String? checksum) async {
    try {
      if (!await file.exists()) {
        return false;
      }

      if (expectedSize != null) {
        final size = await file.length();
        if (size != expectedSize) {
          print('OfflineStorageService: Size mismatch - Expected: $expectedSize, Got: $size');
          return false;
        }
      }

      if (checksum != null) {
        // TODO: Implement checksum validation when available
        print('OfflineStorageService: Checksum validation not implemented yet');
      }

      return true;
    } catch (e) {
      print('OfflineStorageService: Error validating file: $e');
      return false;
    }
  }

  /// Close the storage service
  Future<void> close() async {
    // Nothing to close for file-based storage
    print('OfflineStorageService: Service closed');
  }
}
