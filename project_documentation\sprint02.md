# AI Agent Instructions: Complete Sprint 2 - Core Story Player, Narrative Display & Branching Logic
## Project: "Choice: Once Upon A Time" Flutter App

**Objective:**
Autonomously develop and integrate the core story player functionalities for the "Choice: Once Upon A Time" Flutter app, building upon the completed Sprint 1 codebase. This includes implementing dynamic story loading and parsing, UI for in-story scenes (narrator text and background placeholders), functional branching narrative logic based on user choices from story JSON files, and placeholder UI for narrator controls. The generated Flutter code must be web-compatible for testing in Chrome, incorporate basic error handling, and adhere to all established project guidelines (TDD, Art Style, Modularity, Security).

**I. Prerequisites & Assumed Inputs:**
* The complete Flutter project structure and files generated from **Sprint 1** (Data Foundation & Story Library Shell) is the starting point for this sprint.
* **Detailed Technical Design Document (TDD - Task 2.7):** This remains the primary reference for all implementation details.
* **Art Style Definition & Asset Creation Plan (Task 2.3):** For styling UI elements and asset path conventions.
* **Screen Specifications Document ("Task 2.2 update"):** For UI details of screens developed or finalized in this sprint (Screens 3, 4, 5, 6).
* **Story JSON files (`story_01.json`, `story_02.json`, `story_03.json`):** These are essential for content, structure, and branching logic. The AI should be configured to use `story_01.json` as the default for testing the story player in this sprint.

**II. General Instructions for Code Generation:**
1.  **Continue from Sprint 1:** Modify and add to the existing codebase.
2.  **Flutter Version:** Continue using the latest stable Flutter version.
3.  **Modularity, Security & Testability:** Strictly adhere to the "Guidelines for AI Agent: Flutter Code Generation - Modularity & Security" provided previously. Ensure new code is structured for testability.
4.  **Web Compatibility:** Ensure generated Flutter code remains web-compatible for testing in Chrome.
5.  **Basic Error Handling:** Implement basic error handling mechanisms. For operations like data loading, parsing, or critical navigation:
    * If an error occurs (e.g., story file not found, JSON parsing error, invalid scene ID for navigation), the UI should gracefully handle this by displaying a user-friendly, generic error message (e.g., "Oops! Something went wrong while loading the story. Please try again." on the affected screen or as a dialog).
    * Log errors to the console for debugging (`print()` or `developer.log()`).
    * Avoid app crashes due to unhandled exceptions in the implemented features.

**III. Specific Task Instructions for Sprint 2:**

**1. Story Loading & Parsing into `StoryPlayerProvider` (TDD Section 3.3, 3.5):**
    * Enhance `StoryRepository` (from `features/story_library/data/`) to include a method: `Future<StoryModel> fetchStoryById(String storyId)`.
        * This method should load the specified story JSON file (e.g., `assets/stories/{storyId}.json`). For Sprint 2, focus on loading `story_01.json` when its `storyId` (e.g., "pip_pantry_puzzle_v1") is requested.
        * Implement JSON parsing into the `StoryModel` and its nested Dart objects.
        * Implement error handling for file not found or parsing errors.
    * Enhance `StoryPlayerProvider` (in `features/story_player/presentation/providers/`):
        * Add logic to call `StoryRepository.fetchStoryById()` when a story is selected (this will be triggered by navigation from Screen 3).
        * Store the loaded `StoryModel`.
        * Manage the current `SceneModel` being displayed, starting with the `initialSceneId` from the `StoryModel`.
        * Handle loading and error states, exposing them to the UI.

**2. Develop `StoryPlayerScreen` UI (Main In-Story Scene - Screen 5 from "Task 2.2 update"):**
    * This screen will be the primary interface for displaying story content.
    * It should listen to the `StoryPlayerProvider` to get the current `SceneModel`.
    * **Background Display:** Display the `backgroundImageUrl` from the `SceneModel`. Implement the placeholder logic: if the actual image asset (path from Task 2.3 asset list) is not found, display a **white background with the current `sceneId` as text** (centered, using body typography from theme).
    * **Narrator Text Display:** Use the `NarratorTextDisplayWidget` (TDD Section 3.2) to display the `narratorSegments[].text` for the current scene. For now, also display the `emotionCue` text next to or below the narrator text for debugging/reference.
    * Include the persistent "Home" Icon and "Pause" Icon, making them functional (Home leading to Screen 13 Exit Confirmation Popup; Pause leading to Screen 7 In-Story Pause Menu).

**3. Implement Branching Narrative Engine (within `StoryPlayerProvider` / `StoryEngineService` - TDD Section 3.5):**
    * When `StoryPlayerProvider.currentScene` indicates `sceneType == "choice_point"`:
        * The `StoryPlayerScreen` should display the `promptTextForTTS` (narrator frames the choice).
        * Render the `choices` list from the `SceneModel` using `ChoiceButtonWidget`s (TDD Section 3.2, created/styled in Sprint 0/1).
    * **Choice Selection Logic:**
        * When a `ChoiceButtonWidget` is tapped, it should call a method in `StoryPlayerProvider` (e.g., `void selectChoice(String choiceId)`).
        * This method should find the selected `ChoiceModel` in the current scene's `choices` list.
        * It must then update the `currentScene` in `StoryPlayerProvider` to the `SceneModel` corresponding to the `leadsToSceneId` of the chosen option.
        * The UI must rebuild to reflect the new scene.
        * Implement error handling if `leadsToSceneId` is invalid or not found in `StoryModel.scenes`.

**4. Implement Linear Scene Progression (within `StoryPlayerProvider` / `StoryEngineService` - TDD Section 3.5):**
    * For scenes with `sceneType == "narration_illustration"`:
        * After all `narratorSegments` of the current scene are notionally "played" (for this sprint, you can simulate this with a manual "Next Scene/Segment" button on the `StoryPlayerScreen` if full TTS timing isn't implemented yet, or after a fixed delay per segment), the `StoryPlayerProvider` should:
        * Check if the current `SceneModel` has a `nextSceneId`.
        * If yes, update `currentScene` to the `SceneModel` corresponding to `nextSceneId`.
        * If `nextSceneId` is null, it indicates the end of a branch; transition to the story's conclusion/end screen logic (Screen 8, to be fully implemented in a later sprint, but the navigation hook should be there).

**5. Implement Narration Controls UI (Placeholder Functionality - `NarrationControlsWidget` from TDD Section 3.2):**
    * Ensure the `NarrationControlsWidget` (with Pause/Play, Replay Segment buttons) is displayed on Screen 5 and Screen 6.
    * Connect these buttons to call the placeholder methods in `TTSService` via `StoryPlayerProvider`. For Sprint 2, the buttons update UI state (e.g., an `isPlaying` flag in `StoryPlayerProvider`) but don't need to control actual audio playback fully yet.

**6. Implement Story Intro/Splash Screen (Screen 3) & Loading Screen (Screen 4):**
    * When a story is tapped on `StoryLibraryScreen` (Screen 2), navigate to `StoryIntroScreen` (Screen 3), passing the `storyId`.
    * Screen 3 should display the story title and cover art placeholder based on the `storyId`.
    * Tapping "Play" on Screen 3 should:
        * Trigger the `StoryPlayerProvider` to load the full `StoryModel` for the given `storyId`.
        * Navigate to `LoadingScreen` (Screen 4) while data is being fetched/parsed. Screen 4 should display its calming animation/text.
        * Once the `StoryPlayerProvider` indicates data is loaded (or if an error occurs, display an error message), navigate from Screen 4 to the `StoryPlayerScreen` (Screen 5), starting with the `initialSceneId` of the loaded story.

**IV. Testing Requirements:**
1.  **Unit Tests:**
    * For `StoryRepository`: Test `fetchStoryById` logic (mocking file reads or Firestore calls). Test parsing and error handling.
    * For `StoryPlayerProvider`: Test story loading, scene progression logic (linear and branching), choice selection, and state updates. Mock dependencies.
2.  **Widget Tests:**
    * For `StoryPlayerScreen`: Verify it displays scene data (narrator text, `sceneId` for background placeholder) based on `StoryPlayerProvider` state.
    * For `ChoiceButtonWidget`: Verify it displays choice text/icon placeholder and correctly reports taps.
    * For `StoryIntroScreen` and `LoadingScreen`: Verify UI elements and basic navigation.
3.  **Manual Testing (in Chrome using `flutter run -d chrome`):**
    * Verify the entire flow: Library -> Story Intro -> Loading -> Story Player -> Making Choices -> Branching -> Next Scenes.
    * Verify placeholder for narrator text and emotion cues are displayed.
    * Verify background placeholder logic (white + sceneId).
    * Verify basic error messages appear for simulated errors (e.g., trying to load a non-existent story JSON, malformed JSON).

**V. Deliverables:**
1.  **Updated Flutter Project Source Code:** All new and modified Dart files for Sprint 2.
2.  **Completion Report (Markdown format - `sprint_02_completion_report.md`):**
    * Brief overview of tasks completed in Sprint 2.
    * Details on how error handling was implemented for story loading, parsing, and navigation (with examples or code snippets if helpful).
    * List of any significant assumptions made or challenges encountered.
    * List of new key files created and major files modified.
    * Confirmation that the prototype is runnable on Flutter Web.
3.  **Updated `README.md`:**
    * Add a "Sprint 2 Features" section detailing what's now functional (e.g., "Core story player implemented for `story_01.json` with text display and branching choices. Placeholder narrator controls.").
    * Ensure instructions for running on Flutter Web (`flutter run -d chrome`) are present and clear.
    * Any new setup steps required by the user (though ideally none for this sprint if building on S1 correctly).

**Final Instruction to AI Agent:** Generate the code, configuration content, and documentation updates according to these specifications for Sprint 2. Ensure all file paths and names are consistent with the TDD and previous sprint structures. The output should allow a developer to run the Flutter app in Chrome and test the core story player functionality for `story_01.json`.