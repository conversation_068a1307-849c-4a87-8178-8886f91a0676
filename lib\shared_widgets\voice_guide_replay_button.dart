import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:choice_once_upon_a_time/core/audio/enhanced_voice_guidance_manager.dart';
import 'package:choice_once_upon_a_time/l10n/app_localizations.dart';

/// A floating action button for replaying voice guidance
class VoiceGuideReplayButton extends ConsumerStatefulWidget {
  final String screenId;
  final String Function(AppLocalizations) textProvider;
  final String emotionCue;
  final VoidCallback? onReplayStarted;
  final VoidCallback? onReplayCompleted;

  const VoiceGuideReplayButton({
    super.key,
    required this.screenId,
    required this.textProvider,
    this.emotionCue = 'friendly',
    this.onReplayStarted,
    this.onReplayCompleted,
  });

  @override
  ConsumerState<VoiceGuideReplayButton> createState() => _VoiceGuideReplayButtonState();
}

class _VoiceGuideReplayButtonState extends ConsumerState<VoiceGuideReplayButton>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  bool _isReplaying = false;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.95,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  Future<void> _replayVoiceGuide() async {
    if (_isReplaying) return;

    setState(() {
      _isReplaying = true;
    });

    _animationController.forward().then((_) {
      _animationController.reverse();
    });

    widget.onReplayStarted?.call();

    try {
      final voiceGuideManager = ref.read(enhancedVoiceGuidanceManagerProvider);
      await voiceGuideManager.replayScreenGuide(
        screenId: widget.screenId,
        context: context,
        textProvider: widget.textProvider,
        emotionCue: widget.emotionCue,
      );
    } finally {
      if (mounted) {
        setState(() {
          _isReplaying = false;
        });
        widget.onReplayCompleted?.call();
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final voiceGuideManager = ref.watch(enhancedVoiceGuidanceManagerProvider);
    
    // Only show the button if the screen has been played at least once
    if (!voiceGuideManager.hasScreenBeenPlayed(widget.screenId)) {
      return const SizedBox.shrink();
    }

    return AnimatedBuilder(
      animation: _scaleAnimation,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: FloatingActionButton.small(
            onPressed: _isReplaying ? null : _replayVoiceGuide,
            backgroundColor: _isReplaying 
                ? theme.colorScheme.surfaceContainerHighest
                : theme.colorScheme.primaryContainer,
            foregroundColor: _isReplaying
                ? theme.colorScheme.onSurfaceVariant
                : theme.colorScheme.onPrimaryContainer,
            tooltip: 'Replay voice guide',
            child: _isReplaying
                ? SizedBox(
                    width: 16,
                    height: 16,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(
                        theme.colorScheme.onSurfaceVariant,
                      ),
                    ),
                  )
                : const Icon(Icons.replay, size: 20),
          ),
        );
      },
    );
  }
}

/// A compact replay button for use in app bars or toolbars
class CompactVoiceGuideReplayButton extends ConsumerStatefulWidget {
  final String screenId;
  final String Function(AppLocalizations) textProvider;
  final String emotionCue;
  final VoidCallback? onReplayStarted;
  final VoidCallback? onReplayCompleted;

  const CompactVoiceGuideReplayButton({
    super.key,
    required this.screenId,
    required this.textProvider,
    this.emotionCue = 'friendly',
    this.onReplayStarted,
    this.onReplayCompleted,
  });

  @override
  ConsumerState<CompactVoiceGuideReplayButton> createState() => _CompactVoiceGuideReplayButtonState();
}

class _CompactVoiceGuideReplayButtonState extends ConsumerState<CompactVoiceGuideReplayButton> {
  bool _isReplaying = false;

  Future<void> _replayVoiceGuide() async {
    if (_isReplaying) return;

    setState(() {
      _isReplaying = true;
    });

    widget.onReplayStarted?.call();

    try {
      final voiceGuideManager = ref.read(enhancedVoiceGuidanceManagerProvider);
      await voiceGuideManager.replayScreenGuide(
        screenId: widget.screenId,
        context: context,
        textProvider: widget.textProvider,
        emotionCue: widget.emotionCue,
      );
    } finally {
      if (mounted) {
        setState(() {
          _isReplaying = false;
        });
        widget.onReplayCompleted?.call();
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final voiceGuideManager = ref.watch(enhancedVoiceGuidanceManagerProvider);
    
    // Only show the button if the screen has been played at least once
    if (!voiceGuideManager.hasScreenBeenPlayed(widget.screenId)) {
      return const SizedBox.shrink();
    }

    return IconButton(
      onPressed: _isReplaying ? null : _replayVoiceGuide,
      icon: _isReplaying
          ? SizedBox(
              width: 16,
              height: 16,
              child: CircularProgressIndicator(
                strokeWidth: 2,
                valueColor: AlwaysStoppedAnimation<Color>(
                  theme.colorScheme.onSurfaceVariant,
                ),
              ),
            )
          : const Icon(Icons.replay, size: 20),
      tooltip: 'Replay voice guide',
      color: theme.colorScheme.onSurfaceVariant,
    );
  }
}
