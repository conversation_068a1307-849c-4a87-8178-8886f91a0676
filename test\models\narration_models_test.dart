import 'package:flutter_test/flutter_test.dart';
import 'package:choice_once_upon_a_time/models/narration_models.dart';

void main() {
  group('NarrationState', () {
    test('should create idle state correctly', () {
      const state = NarrationState.idle;
      
      expect(state.status, NarrationStatus.idle);
      expect(state.currentText, isNull);
      expect(state.currentWordIndex, 0);
      expect(state.progress, 0.0);
      expect(state.error, isNull);
      expect(state.autoProgress, true);
    });

    test('should create loading state correctly', () {
      const state = NarrationState.loading;
      
      expect(state.status, NarrationStatus.loading);
    });

    test('should create error state correctly', () {
      const errorMessage = 'Test error';
      final state = NarrationState.createError(errorMessage);
      
      expect(state.status, NarrationStatus.error);
      expect(state.error, errorMessage);
    });

    test('should create copyWith correctly', () {
      const originalState = NarrationState(
        status: NarrationStatus.playing,
        currentText: 'Original text',
        currentWordIndex: 5,
        progress: 0.5,
      );

      final newState = originalState.copyWith(
        status: NarrationStatus.paused,
        currentWordIndex: 10,
      );

      expect(newState.status, NarrationStatus.paused);
      expect(newState.currentText, 'Original text'); // Unchanged
      expect(newState.currentWordIndex, 10); // Changed
      expect(newState.progress, 0.5); // Unchanged
    });

    test('should support equality comparison', () {
      const state1 = NarrationState(
        status: NarrationStatus.playing,
        currentText: 'Test text',
        currentWordIndex: 3,
        progress: 0.3,
      );

      const state2 = NarrationState(
        status: NarrationStatus.playing,
        currentText: 'Test text',
        currentWordIndex: 3,
        progress: 0.3,
      );

      const state3 = NarrationState(
        status: NarrationStatus.paused,
        currentText: 'Test text',
        currentWordIndex: 3,
        progress: 0.3,
      );

      expect(state1, equals(state2));
      expect(state1, isNot(equals(state3)));
    });

    test('should serialize to/from JSON correctly', () {
      const originalState = NarrationState(
        status: NarrationStatus.playing,
        currentText: 'Test text',
        currentWordIndex: 5,
        progress: 0.75,
        durationMs: 10000,
        positionMs: 7500,
        autoProgress: false,
      );

      final json = originalState.toJson();
      final deserializedState = NarrationState.fromJson(json);

      expect(deserializedState, equals(originalState));
    });
  });

  group('NarrationConfig', () {
    test('should create default config correctly', () {
      const config = NarrationConfig.defaultConfig;
      
      expect(config.speechRate, 0.5);
      expect(config.speechPitch, 1.0);
      expect(config.speechVolume, 1.0);
      expect(config.language, 'en-US');
      expect(config.autoProgress, true);
      expect(config.highlightWords, true);
      expect(config.emotionCue, 'neutral');
      expect(config.sentencePauseMs, 500);
      expect(config.wordPauseMs, 100);
    });

    test('should create custom config correctly', () {
      const config = NarrationConfig(
        speechRate: 0.8,
        speechPitch: 1.2,
        speechVolume: 0.9,
        language: 'es-ES',
        autoProgress: false,
        highlightWords: false,
        emotionCue: 'happy',
        sentencePauseMs: 1000,
        wordPauseMs: 200,
      );

      expect(config.speechRate, 0.8);
      expect(config.speechPitch, 1.2);
      expect(config.speechVolume, 0.9);
      expect(config.language, 'es-ES');
      expect(config.autoProgress, false);
      expect(config.highlightWords, false);
      expect(config.emotionCue, 'happy');
      expect(config.sentencePauseMs, 1000);
      expect(config.wordPauseMs, 200);
    });

    test('should create copyWith correctly', () {
      const originalConfig = NarrationConfig(
        speechRate: 0.5,
        speechPitch: 1.0,
        emotionCue: 'neutral',
      );

      final newConfig = originalConfig.copyWith(
        speechRate: 0.8,
        emotionCue: 'happy',
      );

      expect(newConfig.speechRate, 0.8);
      expect(newConfig.speechPitch, 1.0); // Unchanged
      expect(newConfig.emotionCue, 'happy');
    });

    test('should serialize to/from JSON correctly', () {
      const originalConfig = NarrationConfig(
        speechRate: 0.7,
        speechPitch: 1.1,
        speechVolume: 0.8,
        language: 'fr-FR',
        autoProgress: false,
        highlightWords: true,
        emotionCue: 'excited',
        sentencePauseMs: 750,
        wordPauseMs: 150,
      );

      final json = originalConfig.toJson();
      final deserializedConfig = NarrationConfig.fromJson(json);

      expect(deserializedConfig, equals(originalConfig));
    });
  });

  group('WordHighlight', () {
    test('should create word highlight correctly', () {
      const wordHighlight = WordHighlight(
        startIndex: 10,
        endIndex: 15,
        word: 'hello',
        startTimeMs: 1000,
        endTimeMs: 1500,
        isActive: true,
        sentenceIndex: 2,
      );

      expect(wordHighlight.startIndex, 10);
      expect(wordHighlight.endIndex, 15);
      expect(wordHighlight.word, 'hello');
      expect(wordHighlight.startTimeMs, 1000);
      expect(wordHighlight.endTimeMs, 1500);
      expect(wordHighlight.isActive, true);
      expect(wordHighlight.sentenceIndex, 2);
      expect(wordHighlight.durationMs, 500);
    });

    test('should create copyWith correctly', () {
      const originalHighlight = WordHighlight(
        startIndex: 5,
        endIndex: 10,
        word: 'test',
        startTimeMs: 500,
        endTimeMs: 800,
        isActive: false,
        sentenceIndex: 1,
      );

      final newHighlight = originalHighlight.copyWith(
        isActive: true,
        endTimeMs: 900,
      );

      expect(newHighlight.startIndex, 5); // Unchanged
      expect(newHighlight.endIndex, 10); // Unchanged
      expect(newHighlight.word, 'test'); // Unchanged
      expect(newHighlight.startTimeMs, 500); // Unchanged
      expect(newHighlight.endTimeMs, 900); // Changed
      expect(newHighlight.isActive, true); // Changed
      expect(newHighlight.sentenceIndex, 1); // Unchanged
      expect(newHighlight.durationMs, 400); // Calculated from new endTimeMs
    });

    test('should serialize to/from JSON correctly', () {
      const originalHighlight = WordHighlight(
        startIndex: 20,
        endIndex: 25,
        word: 'world',
        startTimeMs: 2000,
        endTimeMs: 2300,
        isActive: false,
        sentenceIndex: 3,
      );

      final json = originalHighlight.toJson();
      final deserializedHighlight = WordHighlight.fromJson(json);

      expect(deserializedHighlight, equals(originalHighlight));
    });
  });

  group('NarrationProgress', () {
    test('should create progress correctly', () {
      final lastUpdated = DateTime.now();
      final progress = NarrationProgress(
        storyId: 'story123',
        sceneId: 'scene456',
        totalWords: 100,
        completedWords: 50,
        totalSentences: 10,
        completedSentences: 5,
        totalDurationMs: 60000,
        currentPositionMs: 30000,
        isCompleted: false,
        lastUpdated: lastUpdated,
      );

      expect(progress.storyId, 'story123');
      expect(progress.sceneId, 'scene456');
      expect(progress.totalWords, 100);
      expect(progress.completedWords, 50);
      expect(progress.totalSentences, 10);
      expect(progress.completedSentences, 5);
      expect(progress.totalDurationMs, 60000);
      expect(progress.currentPositionMs, 30000);
      expect(progress.isCompleted, false);
      expect(progress.lastUpdated, lastUpdated);
      expect(progress.progressPercentage, 0.5);
      expect(progress.timeProgressPercentage, 0.5);
    });

    test('should handle zero values correctly', () {
      final progress = NarrationProgress(
        storyId: 'story123',
        sceneId: 'scene456',
        totalWords: 0,
        totalDurationMs: 0,
        lastUpdated: DateTime.now(),
      );

      expect(progress.progressPercentage, 0.0);
      expect(progress.timeProgressPercentage, 0.0);
    });

    test('should create copyWith correctly', () {
      final originalProgress = NarrationProgress(
        storyId: 'story123',
        sceneId: 'scene456',
        totalWords: 100,
        completedWords: 25,
        isCompleted: false,
        lastUpdated: DateTime.now(),
      );

      final newProgress = originalProgress.copyWith(
        completedWords: 75,
        isCompleted: true,
      );

      expect(newProgress.storyId, 'story123'); // Unchanged
      expect(newProgress.sceneId, 'scene456'); // Unchanged
      expect(newProgress.totalWords, 100); // Unchanged
      expect(newProgress.completedWords, 75); // Changed
      expect(newProgress.isCompleted, true); // Changed
    });

    test('should serialize to/from JSON correctly', () {
      final lastUpdated = DateTime.now();
      final originalProgress = NarrationProgress(
        storyId: 'story789',
        sceneId: 'scene012',
        totalWords: 200,
        completedWords: 150,
        totalSentences: 20,
        completedSentences: 15,
        totalDurationMs: 120000,
        currentPositionMs: 90000,
        isCompleted: true,
        lastUpdated: lastUpdated,
      );

      final json = originalProgress.toJson();
      final deserializedProgress = NarrationProgress.fromJson(json);

      expect(deserializedProgress, equals(originalProgress));
    });
  });
}
