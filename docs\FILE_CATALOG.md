# Choice: Once Upon A Time - File Catalog

This document provides a comprehensive catalog of all files in the project, their purposes, and update schedules.

## 🗂️ Project Structure Overview

```
lib/
├── app/                    # App-wide configuration
│   ├── providers/         # Global Riverpod providers
│   ├── routing/          # GoRouter configuration
│   └── theme/            # Global theme definitions
├── core/                  # Core utilities and services
│   ├── audio/            # TTS and sound services
│   ├── network/          # Network connectivity
│   ├── storage/          # Offline storage service
│   └── utils/            # Utility functions
├── features/             # Feature modules
│   ├── app_init/        # App initialization & FTUE
│   ├── auth/            # Authentication
│   ├── ai_stories/      # AI Story Generation
│   ├── parent_zone/     # Parent dashboard and its widgets
│   ├── story_library/   # Home screen, story library, and related widgets
│   └── story_player/    # Story playback & interaction
├── models/              # Shared data models
├── shared_widgets/      # Reusable UI components
├── l10n/               # Localization files
└── main.dart           # App entry point
```

## 📁 Core Files

### Entry Point
- **`lib/main.dart`**
  - Purpose: Application entry point, initializes services and providers.
  - Updates: When adding new global providers or changing initialization logic.

### App Configuration
- **`lib/app/routing/app_router.dart`**
  - Purpose: GoRouter configuration and route definitions.
  - Updates: When adding new screens or changing navigation.

## 🔧 Core Services & Infrastructure

### Enhanced Story Loading
- **`lib/core/services/firebase_storage_service.dart`** ✨ **NEW**
  - Purpose: Downloads story ZIP files from Firebase Storage with progress tracking.
  - Features: Story existence checking, metadata download, asset URL generation.
  - Updates: When modifying Firebase Storage integration or download logic.

- **`lib/core/services/zip_extraction_service.dart`** ✨ **NEW**
  - Purpose: Extracts downloaded ZIP files and manages local story storage.
  - Features: ZIP extraction, story validation, local storage management, cleanup.
  - Updates: When changing local storage structure or extraction logic.

- **`lib/core/services/enhanced_story_service.dart`** ✨ **NEW**
  - Purpose: Enhanced story loading service with new JSON structure support.
  - Features: Asset scanning, story validation, metadata creation, caching, character/narrator profiles.
  - Updates: When modifying enhanced story structure or loading logic.

- **`lib/core/services/enhanced_story_narration_service.dart`** ✨ **NEW**
  - Purpose: Complete story narration service with scene-by-scene narration and word-level highlighting.
  - Features: Character-specific voices, emotion-based effects, progress tracking, sentence navigation, auto-progression.
  - Updates: When adding new narration features or story integration.

- **`lib/core/audio/enhanced_narration_tts_service.dart`** ✨ **NEW**
  - Purpose: Enhanced TTS service with advanced narration features and word boundary tracking.
  - Features: Word-level highlighting, emotion-based voice modulation, character voices, SSML support, preloading.
  - Updates: When adding new TTS features or voice modulation capabilities.

- **`lib/core/audio/narration_tts_service_interface.dart`** ✨ **NEW**
  - Purpose: Abstract interface for TTS services with comprehensive narration contracts.
  - Features: Service interfaces, emotion mapping, TTS configuration, factory patterns.
  - Updates: When adding new TTS service requirements or interface methods.

- **`lib/core/services/story_narration_service_interface.dart`** ✨ **NEW**
  - Purpose: Abstract interface for story narration services with advanced features.
  - Features: Scene narration, progress tracking, character voices, analytics, background music.
  - Updates: When adding new story narration requirements or service contracts.

- **`lib/core/services/accessibility_service.dart`** ✨ **NEW**
  - Purpose: Comprehensive accessibility features management.
  - Features: High contrast, large text, reduced motion, voice-over support, subtitle contrast.
  - Updates: When adding new accessibility features or system integration.

### Rewards System
- **`lib/core/services/rewards_service.dart`** ✨ **NEW**
  - Purpose: Tracks and manages user rewards for story completion and moral choices.
  - Features: Completion rewards, moral choice rewards, reward counting, persistence.
  - Updates: When adding new reward types or tracking features.

- **`lib/core/services/story_rewards_service.dart`** ✨ **NEW**
  - Purpose: Enhanced rewards service with visual feedback and progress tracking.
  - Features: Achievement badges, completion tracking, choice rewards, story collection progress.
  - Updates: When adding new reward types or achievement systems.

### Asset Management
- **`lib/core/services/asset_fallback_service.dart`** ✨ **NEW**
  - Purpose: Handles missing assets with graceful fallbacks and TTS narration.
  - Features: Placeholder images, TTS fallback for missing audio, asset validation.
  - Updates: When adding new asset types or fallback strategies.

- **`lib/core/services/asset_story_service.dart`** ✨ **NEW**
  - Purpose: Manages asset-based story loading and validation from bundled assets.
  - Features: Asset discovery, story transformation, caching, structure validation.
  - Updates: When changing asset structure or story format support.

### Utility Services
- **`lib/core/utils/responsive_utils.dart`** ✨ **NEW**
  - Purpose: Comprehensive responsive design utilities for cross-platform compatibility.
  - Features: Device type detection, responsive sizing, breakpoint management, layout constraints.
  - Updates: When adding new responsive features or device support.

## 🎯 Feature Modules

### App Initialization (app_init/)
- **`lib/features/app_init/presentation/screens/launch_screen.dart`**
  - Purpose: Initial app launch screen, decides where to route the user.
  - Updates: When modifying launch experience or routing logic.
- **`lib/features/app_init/presentation/screens/ftue_screen.dart`**
  - Purpose: First Time User Experience.
  - Updates: When changing the onboarding flow.

### Story Library & Home Screen (story_library/)
- **`lib/features/story_library/presentation/screens/home_screen.dart`**
  - Purpose: **Main Dashboard/Landing Screen.** Contains the animated scene and a grid of content sections. This is the first screen returning users see.
  - Updates: When changing the overall home screen layout.
- **`lib/features/story_library/presentation/screens/story_library_screen.dart`** ✨ **ENHANCED**
  - Purpose: **Full Story Catalog.** A dedicated screen to browse the *entire* story catalog with search functionality.
  - Features: Responsive search bar, text overflow handling, responsive grid (1-5 columns), theme-consistent error states.
  - Updates: When modifying the full story grid or library features.
- **`lib/features/story_library/presentation/widgets/animated_scene_widget.dart`**
  - Purpose: Displays the animated scene at the top of the `HomeScreen`.
  - Updates: When the animation or visual design is changed.
- **`lib/features/story_library/presentation/widgets/home_screen_grid_area.dart`**
  - Purpose: The main grid container on the `HomeScreen`.
  - Updates: When the grid's layout or responsive behavior is modified.
- **`lib/features/story_library/presentation/widgets/story_library_grid_section.dart`**
  - Purpose: A grid item on the `HomeScreen` that links to the full `StoryLibraryScreen`.
  - Updates: When its visual representation or navigation is changed.
- **`lib/features/story_library/presentation/widgets/continue_reading_grid_section.dart`** ✨ **ENHANCED**
  - Purpose: A grid item that appears to allow users to resume an unfinished story.
  - Features: Navigation to continue story screen, responsive design.
  - Updates: When its visual representation or data logic is changed.
- **`lib/features/story_library/presentation/widgets/featured_stories_grid_section.dart`**
  - Purpose: A grid item to display and link to featured stories.
  - Updates: When its visual representation or navigation is changed.
- **`lib/features/story_library/presentation/widgets/ai_stories_grid_section.dart`**
  - Purpose: A grid item on the `HomeScreen` that links to AI-generated stories.
  - Updates: When its visual representation or navigation is changed.
- **`lib/features/story_library/data/story_repository.dart`** ✨ **ENHANCED**
  - Purpose: Multi-source story data access with priority-based loading and Firebase integration.
  - Features: Local storage → Assets → Firebase Storage priority, ZIP download/extraction, offline support, Firebase story metadata, download management.
  - Updates: When changing story data structure or fetching logic.

### Firebase Story Integration
- **`lib/core/services/story_download_manager.dart`** ✨ **NEW**
  - Purpose: Manages story downloads from Firebase Storage with progress tracking and error handling.
  - Features: Download progress streams, concurrent download management, download cancellation, cleanup operations, status tracking.
  - Updates: When adding new download features or progress tracking capabilities.

- **`lib/models/firebase_story_metadata.dart`** ✨ **NEW**
  - Purpose: Data models for Firebase-based story metadata and download tracking.
  - Features: FirebaseStoryMetadata, DownloadProgress, StoryDownloadInfo models with JSON serialization.
  - Updates: When adding new Firebase story fields or download tracking features.

### Story Cleanup and Management
- **`lib/core/services/story_cleanup_service.dart`** ✨ **NEW**
  - Purpose: Manages automatic cleanup of downloaded stories after 30 days with configurable settings.
  - Features: Download tracking, automatic cleanup scheduling, manual cleanup, storage usage monitoring, cleanup preferences.
  - Updates: When modifying cleanup policies or adding new storage management features.

- **`lib/core/services/permission_service.dart`** ✨ **NEW**
  - Purpose: Handles device permissions for story downloads with cross-platform support.
  - Features: Storage permission checking, Android 13+ media permissions, iOS photo permissions, permission dialogs, settings navigation.
  - Updates: When adding new permission types or updating platform-specific permission handling.

- **`lib/core/services/download_notification_service.dart`** ✨ **NEW**
  - Purpose: Provides user feedback for download operations with progress tracking and notifications.
  - Features: Download progress dialogs, success/failure notifications, snackbar messages, progress streams.
  - Updates: When adding new notification types or progress tracking features.

- **`lib/features/settings/presentation/screens/downloaded_stories_screen.dart`** ✨ **NEW**
  - Purpose: User interface for managing downloaded stories and cleanup settings.
  - Features: Downloaded stories list, cleanup settings toggle, manual cleanup, storage usage display, story deletion.
  - Updates: When adding new storage management features or UI improvements.

### AI Stories (ai_stories/)
- **`lib/features/ai_stories/presentation/screens/ai_story_generation_screen.dart`**
  - Purpose: Screen for generating and managing AI-created stories.
  - Updates: When modifying AI story generation features or UI.

### Story Player (story_player/)
- **`lib/features/story_player/presentation/screens/continue_story_screen.dart`** ✨ **NEW**
  - Purpose: Screen for displaying stories that can be continued from where the user left off.
  - Features: Responsive grid layout, progress tracking, empty state handling.
  - Updates: When modifying continue reading functionality or progress tracking.
- **`lib/features/story_player/presentation/screens/story_player_screen.dart`** ✨ **ENHANCED**
  - Purpose: Main story playback interface with responsive controls and rewards integration.
  - Features: Portrait/landscape layouts, responsive narration controls, theme consistency, rewards display.
  - Updates: When changing story playback features or responsive design.
- **`lib/features/story_player/presentation/screens/enhanced_story_player_screen.dart`** ✨ **NEW**
  - Purpose: Advanced story player with welcome screen, character profiles, and enhanced features.
  - Features: Multi-phase playback, character introductions, emotion-based effects, completion summary.
  - Updates: When adding new story player features or phases.

### Refactored Story Player Architecture (Dedicated Screens)
- **`lib/features/story_player/presentation/screens/story_introduction_screen.dart`** ✨ **NEW**
  - Purpose: Dedicated screen for story introduction and welcome phase.
  - Features: Story loading, welcome content display, fade animations, voice guide integration.
  - Updates: When modifying story introduction experience or welcome content.

- **`lib/features/story_player/presentation/screens/character_introduction_screen.dart`** ✨ **NEW**
  - Purpose: Dedicated screen for character introduction phase.
  - Features: Character profiles display, animated transitions, voice narration, setup before story begins.
  - Updates: When modifying character introduction experience or profile content.

- **`lib/features/story_player/presentation/screens/story_playback_screen.dart`** ✨ **NEW**
  - Purpose: Dedicated screen for story playback and narration.
  - Features: Enhanced landscape story widget integration, scene navigation, choice handling, completion management, landscape orientation.
  - Updates: When modifying story playback experience or narration features.

### Enhanced Story Player Widgets
- **`lib/features/story_player/presentation/widgets/welcome_screen_widget.dart`** ✨ **NEW**
  - Purpose: Animated welcome screen introducing the story and characters.
  - Features: Story cover display, metadata chips, narrated introduction, responsive design.
  - Updates: When modifying welcome screen content or animations.

- **`lib/features/story_player/presentation/widgets/character_profiles_widget.dart`** ✨ **NEW**
  - Purpose: Interactive character introduction screen with profiles and voice samples.
  - Features: Character cards, voice narration, page indicators, responsive layout.
  - Updates: When adding character features or profile information.

- **`lib/features/story_player/presentation/widgets/story_scene_widget.dart`** ✨ **NEW**
  - Purpose: Enhanced scene display with emotion effects and choice interactions.
  - Features: Emotion-based visual effects, choice feedback, progress tracking, subtitle display.
  - Updates: When adding scene features or interaction types.

- **`lib/features/story_player/presentation/widgets/story_narration_widget.dart`** ✨ **NEW**
  - Purpose: Main narration widget with text display, word highlighting, and comprehensive controls.
  - Features: Real-time word highlighting, responsive design, progress tracking, play/pause/stop controls, auto-start capability.
  - Updates: When enhancing narration features or user interface controls.

- **`lib/features/story_player/presentation/widgets/narration_controls_widget.dart`** ✨ **NEW**
  - Purpose: Advanced narration controls with comprehensive playback management and settings.
  - Features: Play/pause/stop/replay controls, speed/volume adjustment, progress slider, sentence navigation, responsive design.
  - Updates: When adding new control features or user interaction capabilities.

- **`lib/features/story_player/presentation/widgets/word_highlight_widget.dart`** ✨ **NEW**
  - Purpose: Specialized widget for synchronized word-level highlighting during narration.
  - Features: Real-time word highlighting, smooth animations, glow effects, customizable styles, responsive text display.
  - Updates: When enhancing highlighting effects or animation features.

- **`lib/features/story_player/presentation/widgets/enhanced_story_narration_widget.dart`** ✨ **NEW**
  - Purpose: Integration widget combining narration with enhanced story player features.
  - Features: Scene-based narration, character voice integration, responsive design, auto-progression, completion handling.
  - Updates: When integrating new story features or narration capabilities.

- **`lib/features/story_player/presentation/widgets/scene_progression_controls.dart`** ✨ **NEW**
  - Purpose: Controls for managing scene progression and auto-mode functionality.
  - Features: Auto/manual mode toggle, continue/replay buttons, visual feedback, responsive design.
  - Updates: When adding progression features or user controls.

- **`lib/features/story_player/presentation/widgets/story_choice_overlay.dart`** ✨ **NEW**
  - Purpose: Enhanced overlay for displaying story choices with visual design.
  - Features: Animated choice buttons, emotion-based colors, icons, dismissible overlay.
  - Updates: When modifying choice presentation or interaction design.

- **`lib/features/story_player/presentation/widgets/story_completion_widget.dart`** ✨ **NEW**
  - Purpose: Story completion screen with rewards, progress summary, and replay options.
  - Features: Completion celebration, progress visualization, reward display, replay encouragement.
  - Updates: When modifying completion experience or reward display.

- **`lib/features/story_player/presentation/widgets/story_playback_widget.dart`** ✨ **NEW**
  - Purpose: Complete replacement for problematic EnhancedLandscapeStoryWidget - reliable story playback widget built from scratch.
  - Features: Full-screen scene image display with proper asset loading, TTS narration with play/pause/stop controls, word-level text highlighting synchronized with TTS, scene navigation controls with manual progression, choice selection overlay, landscape orientation handling, comprehensive error handling and loading states.
  - Updates: When modifying story playback experience, image display, or narration features.

- **`lib/features/story_player/presentation/screens/new_story_player_screen.dart`** ✨ **NEW**
  - Purpose: Next-generation story player with immersive landscape experience and advanced features.
  - Features: Immersive landscape orientation, smooth scene transitions with configurable delays, side-by-side choice layout with scene icons, vocabulary discussion integration, story settings controls (font size, transparency, narration speed), comprehensive progress tracking, fade/scale animations, responsive design.
  - Updates: When enhancing story player experience or adding new interactive features.

- **`lib/features/story_player/presentation/widgets/story_choice_popup_widget.dart`** ✨ **ENHANCED**
  - Purpose: Redesigned choice popup with responsive side-by-side layout and scene preview icons.
  - Features: Horizontal/vertical responsive layouts, scene icon previews, enhanced visual design, smooth animations, accessibility improvements.
  - Updates: When modifying choice presentation or adding new choice interaction features.

- **`lib/features/story_player/presentation/widgets/vocabulary_discussion_widget.dart`** ✨ **NEW**
  - Purpose: Interactive vocabulary learning widget with images and TTS narration.
  - Features: Vocabulary entry display with images, TTS narration for word explanations, responsive horizontal/vertical layouts, progress indicators, navigation controls, fallback vocabulary images.
  - Updates: When adding vocabulary features or educational content.

- **`lib/features/story_player/presentation/widgets/story_settings_widget.dart`** ✨ **NEW**
  - Purpose: Comprehensive story settings overlay with real-time preview and persistence.
  - Features: Font size control (12-32px), subtitle transparency (0-100%), narration speed (0.3x-2.0x), real-time preview, SharedPreferences persistence, responsive design, smooth animations.
  - Updates: When adding new settings options or customization features.

- **`lib/core/services/story_progress_service.dart`** ✨ **NEW**
  - Purpose: Comprehensive progress tracking service with scene-level granularity and choice tracking.
  - Features: Scene visit tracking, choice recording, play time tracking, progress percentage calculation, completion detection, persistent storage, progress queries, batch operations.
  - Updates: When adding new progress tracking features or analytics requirements.

### Parent Zone Narrator Voice System
- **`lib/features/parent_zone/presentation/screens/narrator_creation_screen.dart`** ✨ **ENHANCED**
  - Purpose: Complete narrator creation and editing interface with voice customization.
  - Features: Narrator profile creation, voice settings (pitch, rate, volume), personality traits selection, category and characteristics configuration, real-time voice preview, form validation, responsive design.
  - Updates: When adding new narrator customization options or voice features.

- **`lib/features/parent_zone/presentation/screens/narrator_selection_screen.dart`** ✨ **NEW**
  - Purpose: Narrator selection and management interface for parents.
  - Features: Current narrator display, available narrators list, narrator preview functionality, edit/delete operations, create new narrator access, responsive card layout.
  - Updates: When modifying narrator selection workflow or adding management features.

- **`lib/features/parent_zone/presentation/widgets/voice_preview_widget.dart`** ✨ **NEW**
  - Purpose: Interactive voice preview widget with TTS integration and sample text playback.
  - Features: Voice selection dropdown, sample text navigation, real-time voice preview, TTS settings application, multiple sample texts, play/stop controls.
  - Updates: When enhancing voice preview functionality or adding new sample texts.

- **`lib/features/parent_zone/presentation/widgets/narrator_form_widget.dart`** ✨ **NEW**
  - Purpose: Form widget for narrator basic information input with validation.
  - Features: Narrator name and description fields, form validation, character limits, input formatting, responsive design.
  - Updates: When adding new narrator profile fields or validation rules.

- **`lib/features/parent_zone/presentation/providers/narrator_creation_provider.dart`** ✨ **NEW**
  - Purpose: Riverpod provider for narrator creation and management state.
  - Features: Narrator CRUD operations, selected narrator management, SharedPreferences persistence, state management, error handling, default narrator provision.
  - Updates: When adding new narrator management features or storage requirements.

### Story Player Providers & Widgets
- **`lib/features/story_player/presentation/providers/rewards_provider.dart`** ✨ **NEW**
  - Purpose: Riverpod provider for managing rewards state and operations.
  - Features: Reward tracking, completion detection, moral choice rewards, state management.
  - Updates: When adding new reward types or tracking features.

- **`lib/features/story_player/presentation/widgets/rewards_display_widget.dart`** ✨ **NEW**
  - Purpose: Child-friendly UI for displaying earned rewards with animations.
  - Features: Celebration dialogs, reward badges, responsive design, notification widgets.
  - Updates: When modifying reward display or adding new reward types.

### Rewards Feature Module (rewards/)
- **`lib/features/rewards/presentation/screens/rewards_screen.dart`** ✨ **NEW**
  - Purpose: Comprehensive rewards and achievements screen with animated displays.
  - Features: Achievement badges, progress overview, story collection tracking, animated rewards.
  - Updates: When adding new achievement types or reward categories.

- **`lib/features/rewards/presentation/widgets/reward_card_widget.dart`** ✨ **NEW**
  - Purpose: Individual reward card with animations and celebration effects.
  - Features: Reward type icons, animated appearance, date formatting, responsive design.
  - Updates: When modifying reward card appearance or animation.

- **`lib/features/rewards/presentation/widgets/progress_overview_widget.dart`** ✨ **NEW**
  - Purpose: Visual progress tracking with animated charts and statistics.
  - Features: Circular progress indicators, progress bars, achievement summaries, responsive charts.
  - Updates: When adding new progress metrics or visualization types.

- **`lib/features/rewards/presentation/widgets/achievement_badge_widget.dart`** ✨ **NEW**
  - Purpose: Interactive achievement badges with unlock animations and details.
  - Features: Badge states, glow effects, achievement details dialog, responsive design.
  - Updates: When adding new achievement types or badge interactions.

## 🎨 Enhanced Components & Widgets

### Global Widgets
- **`lib/widgets/global_narrator_widget.dart`** ✨ **ENHANCED**
  - Purpose: Global narrator controls with play/pause/stop functionality.
  - Features: Responsive sizing, theme-consistent colors, proper error handling, tooltip-free design for Android compatibility.
  - Updates: When modifying global narration features or responsive behavior.

### Shared Widgets
- **`lib/shared_widgets/accessible_subtitle_widget.dart`** ✨ **NEW**
  - Purpose: Accessible subtitle display with high contrast and responsive design.
  - Features: High contrast mode, large text support, emotion-based styling, voice-over compatibility.
  - Updates: When adding accessibility features or subtitle customization.

### Story Library Widgets
- **`lib/features/story_library/presentation/widgets/story_cover_card_widget.dart`** ✨ **ENHANCED**
  - Purpose: Individual story card component with download functionality.
  - Features: Responsive sizing, standardized download buttons, progress indicators, theme-consistent snackbars, storage status checking.
  - Updates: When modifying story card appearance or download functionality.

- **`lib/features/story_library/presentation/widgets/new_story_card_widget.dart`** ✨ **ENHANCED**
  - Purpose: Enhanced story card widget with Firebase download support and progress tracking.
  - Features: Download/play status management, Firebase story downloading, progress indicators, retry functionality, responsive design.
  - Updates: When modifying story card interactions or download functionality.

- **`lib/features/story_library/presentation/providers/new_story_library_provider.dart`** ✨ **ENHANCED**
  - Purpose: Enhanced story library provider with Firebase integration and download management.
  - Features: Multi-source story loading (assets + Firebase), download progress tracking, story status management, search functionality.
  - Updates: When adding new story sources or download features.

- **`lib/features/story_library/presentation/widgets/home_screen_grid_area.dart`** ✨ **ENHANCED**
  - Purpose: Responsive grid container for home screen sections.
  - Features: 1-4 column layouts based on screen width, MediaQuery-based responsive design, LayoutBuilder optimization.
  - Updates: When modifying grid layout or responsive breakpoints.

### Enhanced Data Models
- **`lib/models/story_metadata_model.dart`** ✨ **ENHANCED**
  - Purpose: Story metadata data model with rewards support.
  - Features: Added `hasProgress` and `rewards` fields, comprehensive copyWith method.
  - Updates: When adding new story metadata fields or progress tracking features.

- **`lib/models/story_model.dart`** ✨ **ENHANCED**
  - Purpose: Complete story data model with rewards integration.
  - Features: Added `rewards` field for completion and moral choice rewards.
  - Updates: When adding new story structure fields or reward types.

- **`lib/models/rewards_model.dart`** ✨ **NEW**
  - Purpose: Rewards system data models for tracking user achievements.
  - Features: RewardsModel for story rewards, EarnedReward for tracking earned rewards.
  - Updates: When adding new reward types or tracking mechanisms.

- **`lib/models/enhanced_story_model.dart`** ✨ **NEW**
  - Purpose: Comprehensive enhanced story data models with new JSON structure.
  - Features: Character profiles, narrator settings, voice models, scene choices, post-story content.
  - Updates: When adding new story structure elements or enhanced features.

- **`lib/models/narration_models.dart`** ✨ **NEW**
  - Purpose: Core narration system data models with JSON serialization support.
  - Features: NarrationState, NarrationConfig, WordHighlight, NarrationProgress models with comprehensive state management.
  - Updates: When adding new narration features or state tracking requirements.

- **`lib/models/tts_models.dart`** ✨ **NEW**
  - Purpose: Text-to-Speech system data models with event handling and voice management.
  - Features: TTSEvent, TTSParameters, TTSVoice, TTSState models with emotion mapping and voice configuration.
  - Updates: When adding new TTS features or voice modulation capabilities.

### Enhanced Repository Layer
- **`lib/features/story_library/data/enhanced_story_repository.dart`** ✨ **NEW**
  - Purpose: Repository for managing enhanced stories with validation and caching.
  - Features: Story loading, character profiles, narrator settings, asset validation.
  - Updates: When modifying enhanced story data access or validation logic.

## 🧪 Test Suite

### Unit Tests
- **`test/features/story_library/providers/story_library_provider_test.dart`** ✨ **NEW**
  - Purpose: Unit tests for story library provider functionality.
  - Coverage: Story loading, progress tracking, search functionality, error handling.

- **`test/widgets/global_narrator_controller_test.dart`** ✨ **NEW**
  - Purpose: Unit tests for global narrator controller.
  - Coverage: Route-based narration, play/pause/stop controls, state management.

- **`test/models/enhanced_story_model_test.dart`** ✨ **NEW**
  - Purpose: Comprehensive unit tests for enhanced story data models.
  - Coverage: JSON serialization, model validation, path generation, scene navigation.

- **`test/core/services/enhanced_story_service_test.dart`** ✨ **NEW**
  - Purpose: Unit tests for enhanced story loading service.
  - Coverage: Story scanning, loading, validation, caching, metadata creation.

- **`test/core/services/accessibility_service_test.dart`** ✨ **NEW**
  - Purpose: Unit tests for accessibility service functionality.
  - Coverage: Settings management, stream behavior, persistence, contrast calculations.

### Widget Tests
- **`test/features/story_library/widgets/story_cover_card_widget_test.dart`** ✨ **NEW**
  - Purpose: Widget tests for story cover card component.
  - Coverage: Responsive design, download states, progress indicators, user interactions.

- **`test/features/home/<USER>/home_screen_grid_area_test.dart`** ✨ **NEW**
  - Purpose: Widget tests for responsive home screen grid.
  - Coverage: Column calculations, screen size adaptations, orientation changes, performance.

- **`test/features/story_player/enhanced_story_player_test.dart`** ✨ **NEW**
  - Purpose: Comprehensive widget tests for enhanced story player components.
  - Coverage: Welcome screen, character profiles, scene display, responsive design, animations.

### Integration Tests
- **`test/integration/user_journey_test.dart`** ✨ **NEW**
  - Purpose: End-to-end user journey testing.
  - Coverage: Navigation flows, responsive design across screen sizes, error handling, performance.

### Enhanced Testing Suite
- **`test/models/rewards_model_test.dart`** ✨ **NEW**
  - Purpose: Unit tests for rewards data models.
  - Coverage: JSON serialization, model validation, equality comparison, copyWith functionality.

- **`test/core/services/rewards_service_test.dart`** ✨ **NEW**
  - Purpose: Unit tests for rewards service functionality.
  - Coverage: Reward awarding, tracking, persistence, counting, story completion detection.

- **`test/core/services/asset_story_service_test.dart`** ✨ **NEW**
  - Purpose: Unit tests for asset-based story loading service.
  - Coverage: Asset discovery, story loading, caching, validation, format transformation.

- **`test/models/narration_models_test.dart`** ✨ **NEW**
  - Purpose: Comprehensive unit tests for narration system data models.
  - Coverage: NarrationState, NarrationConfig, WordHighlight, NarrationProgress models with JSON serialization and state management.

- **`test/models/tts_models_test.dart`** ✨ **NEW**
  - Purpose: Unit tests for TTS system data models and event handling.
  - Coverage: TTSEvent, TTSParameters, TTSVoice, TTSState models with emotion mapping and voice configuration.

- **`test/core/services/enhanced_story_narration_service_test.dart`** ✨ **NEW**
  - Purpose: Unit tests for enhanced story narration service functionality.
  - Coverage: Service initialization, scene narration, character voices, progress tracking, playback controls.

- **`test/features/story_player/widgets/story_narration_widget_test.dart`** ✨ **NEW**
  - Purpose: Widget tests for main story narration widget components.
  - Coverage: Text display, control interactions, progress tracking, responsive design, auto-start functionality.

- **`test/features/story_player/widgets/word_highlight_widget_test.dart`** ✨ **NEW**
  - Purpose: Widget tests for word-level highlighting functionality.
  - Coverage: Text highlighting, animation effects, style customization, responsive design, text parsing.

- **`test/features/story_library/data/enhanced_story_repository_test.dart`** ✨ **NEW**
  - Purpose: Unit tests for enhanced story repository with multi-source loading.
  - Coverage: Priority loading, Firebase integration, local storage, asset loading.

- **`test/integration/asset_loading_integration_test.dart`** ✨ **ENHANCED**
  - Purpose: Integration tests for complete asset loading workflow.
  - Coverage: Asset discovery, story loading, fallback handling, repository integration.

### New Story Player Testing Suite
- **`test/features/story_player/presentation/screens/new_story_player_screen_test.dart`** ✨ **NEW**
  - Purpose: Comprehensive widget tests for next-generation story player.
  - Coverage: Loading states, story content display, settings integration, narration controls, scene navigation, choice handling, story completion, transition animations, error handling, memory cleanup.

- **`test/features/story_player/presentation/widgets/story_settings_widget_test.dart`** ✨ **NEW**
  - Purpose: Widget tests for story settings overlay and controls.
  - Coverage: Settings panel display, slider interactions, real-time preview updates, reset functionality, responsive design, animation behavior, edge value handling, persistence integration.

- **`test/core/services/story_progress_service_test.dart`** ✨ **NEW**
  - Purpose: Comprehensive unit tests for story progress tracking service.
  - Coverage: Progress model serialization, persistence operations, progress queries, progress calculations, progress creation and updates, all progress management, error handling, edge cases.

### Parent Zone Testing Suite
- **`test/features/parent_zone/presentation/providers/narrator_creation_provider_test.dart`** ✨ **NEW**
  - Purpose: Comprehensive unit tests for narrator creation provider and state management.
  - Coverage: Narrator CRUD operations, state transitions, persistence operations, error handling, selected narrator management, default narrator handling, provider state updates.

- **`test/features/parent_zone/presentation/widgets/create_profile_dialog_test.dart`** ✨ **NEW**
  - Purpose: Widget tests for profile creation dialog functionality.
  - Coverage: Form validation, user input handling, age selection, avatar color selection, profile creation flow, responsive design, preview updates.

- **`test/features/parent_zone/data/profile_sync_service_test.dart`** ✨ **NEW**
  - Purpose: Unit tests for profile synchronization service.
  - Coverage: Online/offline sync modes, profile merging, error handling, connectivity checks, pending sync operations, timestamp management.

- **`test/features/app_init/presentation/screens/profile_selection_screen_test.dart`** ✨ **NEW**
  - Purpose: Widget tests for profile selection screen and user flows.
  - Coverage: Profile display, selection functionality, create new profile flow, responsive layout, loading states, error handling, navigation flows.

- **`test/features/parent_zone/presentation/widgets/voice_preview_widget_test.dart`** ✨ **NEW** (Recommended)
  - Purpose: Widget tests for voice preview functionality.
  - Coverage: Voice selection, sample text navigation, TTS integration, play/stop controls, responsive design.

- **`test/features/parent_zone/presentation/screens/narrator_selection_screen_test.dart`** ✨ **NEW** (Recommended)
  - Purpose: Widget tests for narrator selection and management interface.
  - Coverage: Narrator display, selection functionality, edit/delete operations, create new narrator navigation, responsive layout.

## 📱 Responsive Design Patterns

### Breakpoints Implemented
- **Very Small Screens** (< 400px): 1 column layout, compact spacing
- **Small Screens** (400-700px): 2 column layout, medium spacing
- **Medium Screens** (700-1000px): 3 column layout, standard spacing
- **Large Screens** (> 1000px): 4+ column layout, generous spacing

### Key Responsive Features
- **MediaQuery-based sizing**: Dynamic padding, font sizes, and icon sizes
- **LayoutBuilder optimization**: Efficient column calculations
- **Text overflow handling**: maxLines and ellipsis throughout
- **Theme consistency**: Proper colorScheme usage with withValues() API
- **Android compatibility**: Tooltip-free design, proper widget disposal

## 🔧 Android Compatibility Fixes

### Critical Issues Resolved
- **Tooltip Overlay Error**: Replaced IconButton tooltips with custom InkWell implementations
- **Widget Disposal Error**: Added proper mounted checks in dispose methods
- **Null Check Exceptions**: Added null safety checks for AppLocalizations
- **API Deprecation**: Updated withOpacity() to withValues() throughout enhanced components

### Testing Results
- **App Launch**: ✅ Successful on Android 15 (CPH2619)
- **TTS Functionality**: ✅ Working correctly
- **Firebase Integration**: ✅ Properly configured
- **Responsive UI**: ✅ Adapts correctly to 1080x2400 screen
- **Navigation**: ✅ Smooth transitions between screens
- **Download System**: ✅ Functional with progress tracking

## 📦 Assets & Resources

### Enhanced Story Assets Structure
- **`assets/stories/`** ✨ **ENHANCED**
  - Purpose: Contains all story content with standardized structure for multi-source loading.
  - Structure: Each story in `{story_id}/` folder with `story.json`, `images/`, `audio/` subfolders.
  - Features: Master story list, rewards metadata, backward compatibility.
  - Updates: When adding new stories or updating existing story content.

- **`assets/stories/stories.json`** ✨ **ENHANCED**
  - Purpose: Master list of available stories for discovery and metadata.
  - Features: Story metadata, availability flags, version information.
  - Updates: When adding or removing stories from the asset bundle.

- **`assets/stories/story001/story001.json`** ✨ **NEW**
  - Purpose: Enhanced story structure example with character profiles and narrator settings.
  - Features: Character voices, emotion cues, choice branching, post-story content.
  - Updates: Reference for implementing enhanced story features.

- **`assets/stories/story002/story002.json`** ✨ **NEW**
  - Purpose: Second enhanced story example demonstrating sharing moral values.
  - Features: Character interactions, moral choice tracking, reward integration.
  - Updates: Reference for moral value implementation in stories.

### Placeholder Assets
- **`assets/images/placeholder.jpg`** ✨ **NEW**
  - Purpose: Default fallback image for missing story assets.
  - Usage: Used by AssetFallbackService when story images are unavailable.

- **`assets/images/story_covers/placeholder_cover.jpg`** ✨ **NEW**
  - Purpose: Default fallback cover image for story library.
  - Usage: Used when story cover images are missing or failed to load.

- **`assets/images/placeholder_scene.jpg`** ✨ **NEW**
  - Purpose: Default fallback scene image for story player.
  - Usage: Used when scene-specific images are unavailable.

### Example Story with Rewards
- **`assets/stories/the_lantern_of_unity/story_with_rewards.json`** ✨ **NEW**
  - Purpose: Example story demonstrating the new rewards system structure.
  - Features: Completion rewards, moral choice rewards, enhanced metadata.
  - Usage: Reference for implementing rewards in other stories.

### Examples and Documentation
- **`example/asset_loading_example.dart`** ✨ **NEW**
  - Purpose: Comprehensive example demonstrating asset loading workflow.
  - Features: Story discovery, metadata loading, validation, fallback testing.
  - Usage: Reference for implementing asset loading in applications.

- **`docs/CODE_MANAGEMENT_GUIDELINES.md`** ✨ **NEW**
  - Purpose: Comprehensive code management, documentation, and quality assurance guidelines.
  - Features: Manual code reading protocols, index-based update system, documentation standards, quality checklists.
  - Usage: Reference for maintaining consistent development practices and code quality.

## 📊 Performance Metrics

### Before Enhancements
- Fixed layouts causing overflow on small screens
- Hard-coded colors inconsistent with theme
- No responsive design patterns
- Multiple compilation errors
- Deprecated API usage throughout

### After Enhancements
- ✅ Fully responsive layouts (320px to 1200px+ width)
- ✅ Consistent theme usage throughout UI components
- ✅ Proper overflow handling and text truncation
- ✅ Clean compilation with only test-related errors
- ✅ Modern API usage in all enhanced components
- ✅ Comprehensive test coverage for core functionality (>80%)
- ✅ Enhanced story system with character profiles and narrator settings
- ✅ Advanced narration with emotion-based effects and background music
- ✅ Comprehensive accessibility features and high contrast support
- ✅ Visual rewards system with animated achievements and progress tracking

## 🚀 Future Maintenance

### Regular Updates Needed
- **Theme consistency**: Ensure new components use theme.colorScheme
- **Responsive design**: Test new components across all breakpoints
- **Android compatibility**: Verify new features work on Android devices
- **Test coverage**: Add tests for new functionality
- **API updates**: Keep up with Flutter API changes and deprecations

### Monitoring Points
- **Performance**: Watch for layout overflow errors
- **Memory usage**: Monitor widget disposal and state management
- **User experience**: Test navigation flows on different screen sizes
- **Accessibility**: Ensure components remain accessible across devices

## 🗑️ Removed Files (Narration System Overhaul)

### Legacy Narration Components Removed
- **`lib/core/services/story_narration_service.dart`** ❌ **REMOVED**
  - Reason: Replaced with enhanced story narration service with better architecture and features.
  - Replacement: `lib/core/services/enhanced_story_narration_service.dart`

- **`lib/features/story_player/presentation/widgets/enhanced_narration_widget.dart`** ❌ **REMOVED**
  - Reason: Replaced with specialized narration widgets with better separation of concerns.
  - Replacement: `lib/features/story_player/presentation/widgets/story_narration_widget.dart`

- **`lib/features/story_player/presentation/widgets/narrator_text_display_widget.dart`** ❌ **REMOVED**
  - Reason: Functionality integrated into new word highlight widget with enhanced features.
  - Replacement: `lib/features/story_player/presentation/widgets/word_highlight_widget.dart`

- **`lib/features/story_player/presentation/widgets/narration_controls_widget.dart`** ❌ **REMOVED**
  - Reason: Completely rewritten with enhanced controls and responsive design.
  - Replacement: New `lib/features/story_player/presentation/widgets/narration_controls_widget.dart`

- **`lib/features/story_player/presentation/screens/narrated_story_player_screen.dart`** ❌ **REMOVED**
  - Reason: Legacy screen replaced with enhanced story player integration.
  - Replacement: Enhanced integration in `enhanced_story_player_screen.dart`

### Legacy Test Files Removed
- **`test/features/story_player/enhanced_story_player_test.dart`** ❌ **REMOVED**
- **`test/core/audio/unified_tts_service_test.dart`** ❌ **REMOVED**
- **`test/widgets/global_narrator_controller_test.dart`** ❌ **REMOVED**
- **`test/core/mixins/screen_narrator_mixin_test.dart`** ❌ **REMOVED**
- **`test/core/mixins/screen_narrator_mixin_test.mocks.dart`** ❌ **REMOVED**

## 🔄 Recent Updates (January 2025)

### New Files Added
- **`lib/core/services/progress_tracking_service.dart`** ✨ **NEW**
  - Purpose: Comprehensive progress tracking with local storage and optional Firebase sync.
  - Features: Reading sessions, streak tracking, achievements, weekly/monthly summaries.
  - Updates: When adding new progress metrics or sync features.

- **`lib/models/progress_tracking_models.dart`** ✨ **NEW**
  - Purpose: Data models for progress tracking system.
  - Features: ReadingProgress, ReadingSession, ReadingStreak, Achievement models with JSON serialization.
  - Updates: When adding new progress tracking fields or data structures.

- **`lib/features/parent_zone/presentation/providers/progress_tracking_provider.dart`** ✨ **NEW**
  - Purpose: Riverpod providers for progress tracking state management.
  - Features: Stream providers for progress data, summary calculations, statistics.
  - Updates: When adding new progress tracking features or calculations.

- **`lib/features/parent_zone/presentation/screens/language_settings_screen.dart`** ✨ **NEW**
  - Purpose: Language settings screen with "Coming Soon" placeholder.
  - Features: Current language display, planned languages showcase, responsive design.
  - Updates: When implementing actual language switching functionality.

### Enhanced Files
- **`lib/features/story_library/data/story_repository.dart`** ✨ **ENHANCED**
  - Added: Deduplication logic to prevent duplicate story entries from multiple sources.
  - Fixed: Story loading priority (enhanced stories override legacy versions).

- **`lib/core/services/asset_only_story_service.dart`** ✨ **ENHANCED**
  - Fixed: Lock symbol logic - stories marked as free no longer show lock symbols.
  - Added: Proper isLocked field mapping based on isFree status.

- **`lib/features/story_library/presentation/widgets/story_cover_card_widget.dart`** ✨ **ENHANCED**
  - Fixed: Lock symbol display logic using _shouldShowLockSymbol() method.
  - Improved: Lock symbol only shows for locked AND non-free stories.

- **`lib/features/rewards/presentation/screens/rewards_screen.dart`** ✨ **ENHANCED**
  - Fixed: All deprecated withOpacity() calls updated to withValues(alpha: ...).
  - Removed: Mock data replaced with real progress tracking integration.
  - Added: Proper empty state handling and real achievement data.

- **`lib/features/parent_zone/presentation/screens/parent_auth_screen.dart`** ✨ **ENHANCED**
  - Fixed: Bottom overflow issues by wrapping in SingleChildScrollView.
  - Improved: Better error handling for Firebase authentication.
  - Added: Proper keyboard handling and responsive layout.

- **`lib/features/parent_zone/presentation/screens/subscription_screen.dart`** ✨ **ENHANCED**
  - Fixed: All deprecated withOpacity() calls updated to withValues(alpha: ...).
  - Improved: Purchase flow error handling with user feedback.
  - Enhanced: Better responsive design and overflow protection.

- **`lib/features/parent_zone/presentation/screens/parent_zone_dashboard_screen.dart`** ✨ **ENHANCED**
  - Improved: Center-aligned headers and sections for better visual presentation.
  - Updated: Navigation to use new language settings screen.

- **`lib/features/parent_zone/presentation/screens/manage_downloads_screen.dart`** ✨ **ENHANCED**
  - Added: Search functionality with real-time filtering.
  - Added: Sorting options (date, name, size) with dropdown menu.
  - Fixed: Deprecated withOpacity() call updated to withValues(alpha: ...).
  - Enhanced: Better user experience with comprehensive filtering.

- **`lib/features/parent_zone/presentation/screens/progress_tracking_screen.dart`** ✨ **ENHANCED**
  - Replaced: Mock data with real progress tracking service integration.
  - Added: Proper loading states and error handling.
  - Enhanced: Real-time progress updates from local storage.

- **`lib/app/routing/app_router.dart`** ✨ **ENHANCED**
  - Added: Route for new language settings screen.
  - Updated: Navigation structure for parent zone features.

### API Modernization
- **Deprecated API Fixes**: Updated all withOpacity() calls to withValues(alpha: ...) across:
  - Rewards screen
  - Subscription screen
  - Download management screen
  - Progress tracking components

### Testing Improvements
- **Test File Fixes**: Updated broken test files to match current API:
  - Fixed screen narrator mixin test method signatures
  - Resolved compilation errors in test suite
  - Improved test coverage for new components

### Bug Fixes Completed
- ✅ **Story Lock Symbols**: Fixed logic so free stories don't show lock symbols
- ✅ **Duplicate Stories**: Added deduplication in story repository
- ✅ **Rewards Screen**: Fixed red flash and overflow issues, removed mock data
- ✅ **Parent Auth**: Fixed bottom overflow and improved error handling
- ✅ **Subscription Flow**: Enhanced purchase flow with better error handling
- ✅ **Download Management**: Added filtering and sorting capabilities
- ✅ **Progress Tracking**: Implemented comprehensive local storage with Firebase sync
- ✅ **Language Settings**: Added coming soon placeholder screen
- ✅ **Parent Zone UI**: Center-aligned widgets for better presentation

---

*Last Updated: Jin
 2025*
*Enhanced with comprehensive issue resolution, progress tracking system, improved UI/UX, and modernized API usage*