import 'package:choice_once_upon_a_time/models/story_metadata_model.dart';
import 'package:choice_once_upon_a_time/models/story_model.dart';
import 'package:choice_once_upon_a_time/core/services/asset_only_story_service.dart';
import 'package:choice_once_upon_a_time/core/services/enhanced_story_service.dart';
import 'package:choice_once_upon_a_time/core/services/firebase_storage_service.dart';
import 'package:choice_once_upon_a_time/core/services/zip_extraction_service.dart';
import 'package:choice_once_upon_a_time/core/utils/app_logger.dart';
import 'package:logger/logger.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'dart:io';
import 'dart:convert';

/// Exception thrown when story loading fails
class StoryLoadException implements Exception {
  final String message;
  final String storyId;
  
  StoryLoadException(this.message, this.storyId);
  
  @override
  String toString() => 'StoryLoadException: $message (Story ID: $storyId)';
}

/// Repository for managing stories from assets, Firebase, and local downloads
class StoryRepository {
  static final Logger _logger = Logger();
  final AssetOnlyStoryService _assetStoryService;
  final EnhancedStoryService _enhancedStoryService;
  final FirebaseStorageService _firebaseStorageService;
  final ZipExtractionService _zipExtractionService;
  final FirebaseFirestore? _firestore;

  StoryRepository({
    AssetOnlyStoryService? assetStoryService,
    EnhancedStoryService? enhancedStoryService,
    FirebaseStorageService? firebaseStorageService,
    ZipExtractionService? zipExtractionService,
    FirebaseFirestore? firestore,
  }) : _assetStoryService = assetStoryService ?? AssetOnlyStoryService(),
       _enhancedStoryService = enhancedStoryService ?? EnhancedStoryService(),
       _firebaseStorageService = firebaseStorageService ?? FirebaseStorageService(),
       _zipExtractionService = zipExtractionService ?? ZipExtractionService(),
       _firestore = firestore {
    AppLogger.debug('[COMPONENT_LOAD] Full path: lib/features/story_library/data/story_repository.dart - StoryRepository');
  }

  /// Fetches all story metadata from assets (both legacy and enhanced) and Firebase
  Future<List<StoryMetadataModel>> fetchStoryMetadataList() async {
    try {
      _logger.i('[StoryRepository] Fetching story metadata from all sources');

      // Get stories from both asset services
      final legacyStories = await _assetStoryService.getAllStoryMetadata();
      final enhancedStories = await _enhancedStoryService.getAllStoryMetadata();

      // Get Firebase stories list
      final firebaseStoryIds = await getFirebaseStoryList();

      // Deduplicate stories by ID, preferring enhanced > legacy > firebase
      final storyMap = <String, StoryMetadataModel>{};

      // Add legacy stories first
      for (final story in legacyStories) {
        storyMap[story.id] = story;
      }

      // Add enhanced stories, which will override legacy versions with same ID
      for (final story in enhancedStories) {
        storyMap[story.id] = story;
      }

      // Add Firebase stories as placeholders if not already present
      for (final storyId in firebaseStoryIds) {
        if (!storyMap.containsKey(storyId)) {
          // Create placeholder metadata for Firebase stories
          final firebaseMetadata = await _createFirebaseStoryMetadata(storyId);
          if (firebaseMetadata != null) {
            storyMap[storyId] = firebaseMetadata;
          }
        }
      }

      final allStories = storyMap.values.toList();

      _logger.i('[StoryRepository] Total stories available: ${allStories.length} (${legacyStories.length} legacy + ${enhancedStories.length} enhanced + ${firebaseStoryIds.length} firebase, deduplicated)');
      return allStories;

    } catch (e) {
      _logger.e('[StoryRepository] Failed to fetch story metadata: $e');
      return [];
    }
  }

  /// Creates metadata for Firebase stories
  Future<StoryMetadataModel?> _createFirebaseStoryMetadata(String storyId) async {
    try {
      // Try to get metadata from Firebase Storage
      final metadata = await _firebaseStorageService.downloadStoryMetadata(storyId);
      if (metadata != null) {
        return StoryMetadataModel(
          id: storyId,
          title: {'en-US': metadata['title'] ?? 'Firebase Story: $storyId'},
          coverImageUrl: metadata['coverImageUrl'] ?? 'assets/images/story_covers/default_cover.png',
          loglineShort: {'en-US': metadata['description'] ?? 'A story from Firebase'},
          targetMoralValue: metadata['moralValue'] ?? 'friendship',
          targetAgeSubSegment: metadata['ageGroup'] ?? '5-7',
          estimatedDurationMinutes: metadata['duration'] ?? 10,
          isFree: metadata['isFree'] ?? true,
          isLocked: false,
          published: true,
          dataSource: 'firebase',
          version: metadata['version'] ?? '1.0.0',
          supportedLanguages: metadata['languages'] ?? ['en-US'],
          defaultLanguage: 'en-US',
          initialSceneId: metadata['initialSceneId'] ?? 'scene_1',
        );
      }

      // Fallback: create basic metadata
      return StoryMetadataModel(
        id: storyId,
        title: {'en-US': 'Story: $storyId'},
        coverImageUrl: 'assets/images/story_covers/default_cover.png',
        loglineShort: {'en-US': 'A downloadable story'},
        targetMoralValue: 'friendship',
        targetAgeSubSegment: '5-7',
        estimatedDurationMinutes: 10,
        isFree: true,
        isLocked: false,
        published: true,
        dataSource: 'firebase',
        version: '1.0.0',
        supportedLanguages: ['en-US'],
        defaultLanguage: 'en-US',
        initialSceneId: 'scene_1',
      );

    } catch (e) {
      _logger.w('[StoryRepository] Failed to create Firebase metadata for $storyId: $e');
      return null;
    }
  }

  /// Fetches a specific story by ID with hybrid loading (assets, local downloads, Firebase)
  Future<StoryModel> fetchStoryById(String storyId, {String? dataSource}) async {
    try {
      _logger.i('[StoryRepository] Fetching story: $storyId (dataSource: $dataSource)');

      // Priority 1: Try local downloaded story first
      if (await _zipExtractionService.isStoryDownloaded(storyId)) {
        _logger.i('[StoryRepository] Loading story from local download: $storyId');
        final localPath = await _zipExtractionService.getStoryPath(storyId);
        final story = await _loadStoryFromLocalPath(localPath, storyId);
        if (story != null) {
          _logger.i('[StoryRepository] Successfully loaded story from local download: $storyId');
          return story;
        }
      }

      // Priority 2: Try asset story
      try {
        final story = await _assetStoryService.loadStory(storyId);
        if (story != null) {
          _logger.i('[StoryRepository] Successfully loaded story from assets: $storyId');
          return story;
        }
      } catch (e) {
        _logger.d('[StoryRepository] Story not found in assets: $storyId');
      }

      // Priority 3: Check if story exists in Firebase (but don't auto-download)
      if (await _firebaseStorageService.storyExists(storyId)) {
        throw StoryLoadException(
          'Story available in Firebase but not downloaded. Please download first.',
          storyId,
        );
      }

      throw StoryLoadException('Story not found in any source', storyId);

    } catch (e) {
      _logger.e('[StoryRepository] Failed to load story $storyId: $e');
      if (e is StoryLoadException) {
        rethrow;
      }
      throw StoryLoadException('Failed to load story: $e', storyId);
    }
  }

  /// Loads a story from a local file path
  Future<StoryModel?> _loadStoryFromLocalPath(String localPath, String storyId) async {
    try {
      final storyFile = File('$localPath/story.json');
      if (!await storyFile.exists()) {
        _logger.w('[StoryRepository] story.json not found in local path: $localPath');
        return null;
      }

      final jsonContent = await storyFile.readAsString();
      final storyData = jsonDecode(jsonContent) as Map<String, dynamic>;

      // Ensure the story has the correct ID
      storyData['id'] = storyId;

      return StoryModel.fromJson(storyData);
    } catch (e) {
      _logger.e('[StoryRepository] Failed to load story from local path $localPath: $e');
      return null;
    }
  }

  /// Checks if a story exists in assets
  Future<bool> storyExists(String storyId) async {
    try {
      final story = await _assetStoryService.loadStory(storyId);
      return story != null;
    } catch (e) {
      return false;
    }
  }

  /// Clears all caches
  void clearCache() {
    _assetStoryService.clearCache();
    _enhancedStoryService.clearCache();
    _logger.i('[StoryRepository] All caches cleared');
  }

  /// Gets cache statistics
  Map<String, dynamic> getCacheStats() {
    final enhancedStats = _enhancedStoryService.getCacheStats();
    return {
      'repository': 'StoryRepository',
      'sources': ['assets_only', 'enhanced_assets', 'firebase_storage'],
      'enhanced_service': enhancedStats,
    };
  }

  /// Downloads a story from Firebase Storage
  Future<bool> downloadStoryFromFirebase(String storyId, {
    Function(double)? onProgress,
  }) async {
    try {
      _logger.i('[StoryRepository] Starting Firebase download for story: $storyId');

      // Check if story already exists locally
      if (await _zipExtractionService.isStoryDownloaded(storyId)) {
        _logger.i('[StoryRepository] Story $storyId already downloaded locally');
        return true;
      }

      // Check if story exists in Firebase Storage
      if (!await _firebaseStorageService.storyExists(storyId)) {
        throw StoryLoadException('Story not found in Firebase Storage', storyId);
      }

      // Download the ZIP file
      final zipPath = await _firebaseStorageService.downloadStoryZip(
        storyId,
        onProgress: onProgress,
      );

      // Extract the ZIP file
      final extractedPath = await _zipExtractionService.extractStoryZip(
        zipPath,
        storyId,
        onProgress: onProgress,
      );

      // Validate the extracted story structure
      final isValid = await _zipExtractionService.validateStoryStructure(extractedPath);
      if (!isValid) {
        throw StoryLoadException('Downloaded story has invalid structure', storyId);
      }

      _logger.i('[StoryRepository] Successfully downloaded and extracted story: $storyId');
      return true;

    } catch (e) {
      _logger.e('[StoryRepository] Failed to download story $storyId from Firebase: $e');
      if (e is StoryLoadException) {
        rethrow;
      }
      throw StoryLoadException('Failed to download story from Firebase: $e', storyId);
    }
  }

  /// Checks if a story is available in Firebase Storage
  Future<bool> isStoryAvailableInFirebase(String storyId) async {
    try {
      return await _firebaseStorageService.storyExists(storyId);
    } catch (e) {
      _logger.w('[StoryRepository] Failed to check Firebase availability for $storyId: $e');
      return false;
    }
  }

  /// Checks if a story is downloaded locally
  Future<bool> isStoryDownloadedLocally(String storyId) async {
    try {
      return await _zipExtractionService.isStoryDownloaded(storyId);
    } catch (e) {
      _logger.w('[StoryRepository] Failed to check local download status for $storyId: $e');
      return false;
    }
  }

  /// Gets the local storage path for a downloaded story
  Future<String?> getLocalStoryPath(String storyId) async {
    try {
      if (await _zipExtractionService.isStoryDownloaded(storyId)) {
        return await _zipExtractionService.getStoryPath(storyId);
      }
      return null;
    } catch (e) {
      _logger.w('[StoryRepository] Failed to get local path for $storyId: $e');
      return null;
    }
  }

  /// Lists all available stories from Firebase Storage
  Future<List<String>> getFirebaseStoryList() async {
    try {
      return await _firebaseStorageService.listAvailableStories();
    } catch (e) {
      _logger.w('[StoryRepository] Failed to list Firebase stories: $e');
      return [];
    }
  }

  /// Gets comprehensive story availability information
  Future<Map<String, dynamic>> getStoryAvailabilityInfo(String storyId) async {
    final assetExists = await storyExists(storyId);
    final firebaseExists = await isStoryAvailableInFirebase(storyId);
    final locallyDownloaded = await isStoryDownloadedLocally(storyId);
    final localPath = await getLocalStoryPath(storyId);

    return {
      'storyId': storyId,
      'availableInAssets': assetExists,
      'availableInFirebase': firebaseExists,
      'downloadedLocally': locallyDownloaded,
      'localPath': localPath,
      'recommendedSource': _getRecommendedSource(assetExists, firebaseExists, locallyDownloaded),
    };
  }

  /// Determines the recommended source for loading a story
  String _getRecommendedSource(bool assetExists, bool firebaseExists, bool locallyDownloaded) {
    if (locallyDownloaded) return 'local_download';
    if (assetExists) return 'assets';
    if (firebaseExists) return 'firebase';
    return 'not_available';
  }

  /// Gets the status for a story (play/download) considering all sources
  Future<String> getStoryStatus(String storyId) async {
    try {
      // Check if story is downloaded locally
      if (await isStoryDownloadedLocally(storyId)) {
        return 'play';
      }

      // Check if story exists in assets
      if (await storyExists(storyId)) {
        return 'play';
      }

      // Check if story is available in Firebase for download
      if (await isStoryAvailableInFirebase(storyId)) {
        return 'download';
      }

      // Story not available anywhere
      return 'error';
    } catch (e) {
      _logger.e('[StoryRepository] Failed to get story status for $storyId: $e');
      return 'error';
    }
  }

  /// Downloads a story and returns success status
  Future<bool> downloadStory(String storyId, {
    Function(double)? onProgress,
  }) async {
    try {
      return await downloadStoryFromFirebase(storyId, onProgress: onProgress);
    } catch (e) {
      _logger.e('[StoryRepository] Failed to download story $storyId: $e');
      return false;
    }
  }
}
