import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

/// A widget representing the "Continue Reading" section on the Home Screen grid.
///
/// This widget should only be displayed if there is a story in progress.
/// It provides a direct way for the user to jump back into their last read story.
class ContinueReadingGridSection extends StatelessWidget {
  const ContinueReadingGridSection({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final screenSize = MediaQuery.of(context).size;

    // In a real implementation, you would check a provider to see if there's
    // a story to continue. For this placeholder, we assume there is one.
    const bool hasStoryToContinue = true;

    if (!hasStoryToContinue) {
      // If there is no story to continue, this widget should not be visible.
      // Returning an empty container is a common way to handle this.
      return const SizedBox.shrink();
    }

    // Responsive icon size
    final iconSize = screenSize.width < 600 ? 40.0 : 48.0;

    return GestureDetector(
      onTap: () {
        // Navigate to the continue story screen
        context.go('/continue_story');
      },
      child: Card(
        elevation: 4.0,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16.0),
        ),
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(16.0),
            gradient: LinearGradient(
              colors: [
                Colors.green.withValues(alpha: 0.7),
                Colors.green,
              ],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
          ),
          child: Padding(
            padding: EdgeInsets.symmetric(
              horizontal: screenSize.width * 0.02,
              vertical: 8.0,
            ),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.play_circle_fill,
                  size: iconSize,
                  color: theme.colorScheme.onPrimary,
                ),
                const SizedBox(height: 8.0),
                Text(
                  'Continue Reading',
                  style: theme.textTheme.titleMedium?.copyWith(
                    color: theme.colorScheme.onPrimary,
                    fontWeight: FontWeight.bold,
                  ),
                  textAlign: TextAlign.center,
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: 4.0),
                Text(
                  'The Magical Forest', // This would be the actual story title
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: theme.colorScheme.onPrimary.withValues(alpha: 0.8),
                  ),
                  textAlign: TextAlign.center,
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
