import 'package:json_annotation/json_annotation.dart';

part 'choice_model.g.dart';

/// Model for story choices that users can make
@JsonSerializable()
class ChoiceModel {
  /// Unique identifier for this choice
  final String choiceId;

  /// Display text for the choice button
  final String displayTextKey;

  /// Visual cue description (optional)
  final String? visualCueDescription;

  /// Narrator guidance (optional)
  final Map<String, dynamic>? narratorGuidance;

  /// ID of the scene this choice leads to
  final String leadsToSceneId;

  const ChoiceModel({
    required this.choiceId,
    required this.displayTextKey,
    this.visualCueDescription,
    this.narratorGuidance,
    required this.leadsToSceneId,
  });

  /// Creates a ChoiceModel from JSON
  factory ChoiceModel.fromJson(Map<String, dynamic> json) =>
      _$ChoiceModelFromJson(json);

  /// Converts the ChoiceModel to JSON
  Map<String, dynamic> toJson() => _$ChoiceModelToJson(this);

  /// Gets the display text (since it's now a String, return directly)
  String getLocalizedDisplayText(String languageCode) {
    return displayTextKey;
  }

  /// Gets the narrator guidance text if available
  String? getLocalizedGuidanceText(String languageCode) {
    if (narratorGuidance != null && narratorGuidance!['text'] != null) {
      final textMap = narratorGuidance!['text'] as Map<String, dynamic>?;
      return textMap?[languageCode] as String?;
    }
    return null;
  }

  /// Creates a copy of this model with updated fields
  ChoiceModel copyWith({
    String? choiceId,
    String? displayTextKey,
    String? visualCueDescription,
    Map<String, dynamic>? narratorGuidance,
    String? leadsToSceneId,
  }) {
    return ChoiceModel(
      choiceId: choiceId ?? this.choiceId,
      displayTextKey: displayTextKey ?? this.displayTextKey,
      visualCueDescription: visualCueDescription ?? this.visualCueDescription,
      narratorGuidance: narratorGuidance ?? this.narratorGuidance,
      leadsToSceneId: leadsToSceneId ?? this.leadsToSceneId,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ChoiceModel && other.choiceId == choiceId;
  }

  @override
  int get hashCode => choiceId.hashCode;

  @override
  String toString() {
    return 'ChoiceModel(choiceId: $choiceId, leadsToSceneId: $leadsToSceneId)';
  }
}
