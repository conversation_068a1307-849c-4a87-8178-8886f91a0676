import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:choice_once_upon_a_time/core/utils/app_logger.dart';
import 'package:choice_once_upon_a_time/features/parent_zone/data/user_profile_service.dart';

/// Service for managing user data in Firebase Firestore
class FirebaseUserService {
  static const String _logPrefix = 'FIREBASE';
  final FirebaseFirestore _firestore;
  final FirebaseAuth _auth;

  FirebaseUserService({
    FirebaseFirestore? firestore,
    FirebaseAuth? auth,
  }) : _firestore = firestore ?? FirebaseFirestore.instance,
       _auth = auth ?? FirebaseAuth.instance;

  /// Get current user's Firebase document reference
  DocumentReference? get _currentUserDoc {
    final user = _auth.currentUser;
    if (user == null) return null;
    return _firestore.collection('users').doc(user.uid);
  }

  /// Fetch user information from Firebase
  Future<Map<String, dynamic>?> fetchUserInfo() async {
    try {
      AppLogger.debug('$_logPrefix: Fetching user information from Firebase');
      
      final userDoc = _currentUserDoc;
      if (userDoc == null) {
        AppLogger.warning('$_logPrefix: No authenticated user found');
        return null;
      }

      final snapshot = await userDoc.get();
      if (!snapshot.exists) {
        AppLogger.warning('$_logPrefix: User document does not exist in Firestore');
        return null;
      }

      final userData = snapshot.data() as Map<String, dynamic>;
      AppLogger.info('$_logPrefix: Successfully fetched user information');
      return userData;
    } catch (e, stackTrace) {
      AppLogger.error('$_logPrefix: Error fetching user information', e, stackTrace);
      return null;
    }
  }

  /// Create or update user information in Firebase
  Future<bool> createOrUpdateUserInfo({
    required String firstName,
    required String lastName,
    required int age,
    String? email,
    Map<String, dynamic>? additionalData,
  }) async {
    try {
      AppLogger.debug('$_logPrefix: Creating/updating user information');
      
      final userDoc = _currentUserDoc;
      if (userDoc == null) {
        AppLogger.error('$_logPrefix: No authenticated user found');
        return false;
      }

      final userData = {
        'firstName': firstName,
        'lastName': lastName,
        'age': age,
        'email': email ?? _auth.currentUser?.email,
        'updatedAt': FieldValue.serverTimestamp(),
        'createdAt': FieldValue.serverTimestamp(),
        ...?additionalData,
      };

      await userDoc.set(userData, SetOptions(merge: true));
      AppLogger.info('$_logPrefix: Successfully created/updated user information');
      return true;
    } catch (e, stackTrace) {
      AppLogger.error('$_logPrefix: Error creating/updating user information', e, stackTrace);
      return false;
    }
  }

  /// Fetch child profiles from Firebase
  Future<List<UserProfile>> fetchChildProfiles() async {
    try {
      AppLogger.debug('$_logPrefix: Fetching child profiles from Firebase');
      
      final userDoc = _currentUserDoc;
      if (userDoc == null) {
        AppLogger.warning('$_logPrefix: No authenticated user found');
        return [];
      }

      final childProfilesSnapshot = await userDoc
          .collection('childProfiles')
          .orderBy('createdAt', descending: false)
          .get();

      final profiles = <UserProfile>[];
      for (final doc in childProfilesSnapshot.docs) {
        try {
          final data = doc.data();
          data['id'] = doc.id; // Ensure ID is set
          final profile = UserProfile.fromJson(data);
          profiles.add(profile);
        } catch (e) {
          AppLogger.warning('$_logPrefix: Failed to parse child profile ${doc.id}: $e');
        }
      }

      AppLogger.info('$_logPrefix: Successfully fetched ${profiles.length} child profiles');
      return profiles;
    } catch (e, stackTrace) {
      AppLogger.error('$_logPrefix: Error fetching child profiles', e, stackTrace);
      return [];
    }
  }

  /// Create child profile in Firebase
  Future<bool> createChildProfile(UserProfile profile) async {
    try {
      AppLogger.debug('$_logPrefix: Creating child profile: ${profile.name}');
      
      final userDoc = _currentUserDoc;
      if (userDoc == null) {
        AppLogger.error('$_logPrefix: No authenticated user found');
        return false;
      }

      final profileData = profile.toJson();
      profileData['createdAt'] = FieldValue.serverTimestamp();
      profileData['updatedAt'] = FieldValue.serverTimestamp();

      await userDoc.collection('childProfiles').doc(profile.id).set(profileData);
      AppLogger.info('$_logPrefix: Successfully created child profile: ${profile.name}');
      return true;
    } catch (e, stackTrace) {
      AppLogger.error('$_logPrefix: Error creating child profile', e, stackTrace);
      return false;
    }
  }

  /// Update child profile in Firebase
  Future<bool> updateChildProfile(UserProfile profile) async {
    try {
      AppLogger.debug('$_logPrefix: Updating child profile: ${profile.name}');
      
      final userDoc = _currentUserDoc;
      if (userDoc == null) {
        AppLogger.error('$_logPrefix: No authenticated user found');
        return false;
      }

      final profileData = profile.toJson();
      profileData['updatedAt'] = FieldValue.serverTimestamp();

      await userDoc.collection('childProfiles').doc(profile.id).update(profileData);
      AppLogger.info('$_logPrefix: Successfully updated child profile: ${profile.name}');
      return true;
    } catch (e, stackTrace) {
      AppLogger.error('$_logPrefix: Error updating child profile', e, stackTrace);
      return false;
    }
  }

  /// Delete child profile from Firebase
  Future<bool> deleteChildProfile(String profileId) async {
    try {
      AppLogger.debug('$_logPrefix: Deleting child profile: $profileId');
      
      final userDoc = _currentUserDoc;
      if (userDoc == null) {
        AppLogger.error('$_logPrefix: No authenticated user found');
        return false;
      }

      await userDoc.collection('childProfiles').doc(profileId).delete();
      AppLogger.info('$_logPrefix: Successfully deleted child profile: $profileId');
      return true;
    } catch (e, stackTrace) {
      AppLogger.error('$_logPrefix: Error deleting child profile', e, stackTrace);
      return false;
    }
  }

  /// Sync child progress to Firebase
  Future<bool> syncChildProgress(String profileId, String storyId, Map<String, dynamic> progressData) async {
    try {
      AppLogger.debug('$_logPrefix: Syncing progress for profile $profileId, story $storyId');
      
      final userDoc = _currentUserDoc;
      if (userDoc == null) {
        AppLogger.error('$_logPrefix: No authenticated user found');
        return false;
      }

      progressData['updatedAt'] = FieldValue.serverTimestamp();
      
      await userDoc
          .collection('childProfiles')
          .doc(profileId)
          .collection('progress')
          .doc(storyId)
          .set(progressData, SetOptions(merge: true));

      AppLogger.info('$_logPrefix: Successfully synced progress');
      return true;
    } catch (e, stackTrace) {
      AppLogger.error('$_logPrefix: Error syncing child progress', e, stackTrace);
      return false;
    }
  }

  /// Get user's display name for greeting
  String? getUserDisplayName() {
    final user = _auth.currentUser;
    return user?.displayName ?? user?.email?.split('@')[0];
  }

  /// Check if user is authenticated
  bool get isAuthenticated => _auth.currentUser != null;
}

/// Provider for Firebase user service
final firebaseUserServiceProvider = Provider<FirebaseUserService>((ref) {
  return FirebaseUserService();
});

/// Provider for user information from Firebase
final firebaseUserInfoProvider = FutureProvider<Map<String, dynamic>?>((ref) async {
  final firebaseUserService = ref.watch(firebaseUserServiceProvider);
  return await firebaseUserService.fetchUserInfo();
});

/// Provider for child profiles from Firebase
final firebaseChildProfilesProvider = FutureProvider<List<UserProfile>>((ref) async {
  final firebaseUserService = ref.watch(firebaseUserServiceProvider);
  return await firebaseUserService.fetchChildProfiles();
});
