# Story Instruction Guide

## Overview

This document provides comprehensive instructions for creating, managing, and implementing interactive stories in the Choice: Once Upon A Time Flutter application.

## Story Structure

### Story Metadata

Each story must include the following metadata:

```json
{
  "id": "unique_story_identifier",
  "title": {
    "en-US": "English Title",
    "es-ES": "Spanish Title"
  },
  "description": {
    "en-US": "English description",
    "es-ES": "Spanish description"
  },
  "targetMoralValue": "Kindness",
  "targetAgeSubSegment": "5-7",
  "estimatedDurationMinutes": 15,
  "coverImageUrl": "assets/images/story_covers/story_name.jpg",
  "isNew": false,
  "isLocked": false,
  "hasProgress": false,
  "version": "1.0.0"
}
```

### Story Content Structure

Stories are organized into chapters and scenes:

```
Story
├── Chapter 1
│   ├── Scene 1 (Introduction)
│   ├── Scene 2 (Choice Point)
│   └── Scene 3 (Consequence)
├── Chapter 2
│   ├── Scene 1
│   └── Scene 2
└── Ending Scenes
    ├── Happy Ending
    ├── Learning Ending
    └── Alternative Ending
```

## Creating New Stories

### Step 1: Story Planning

1. **Define the Moral Value**: Choose a clear moral lesson (e.g., honesty, kindness, courage)
2. **Set Age Range**: Determine appropriate age group (3-5, 5-7, 7-9, 9-12)
3. **Plan Duration**: Estimate reading time (5-30 minutes)
4. **Create Story Outline**: Map out key scenes and choice points

### Step 2: Content Creation

#### Text Guidelines
- Use age-appropriate vocabulary
- Keep sentences short and clear
- Include descriptive language for immersion
- Ensure consistent character voices

#### Choice Points
- Provide 2-3 meaningful options
- Each choice should lead to different consequences
- Choices should relate to the moral lesson
- Include both obvious and subtle moral decisions

#### Audio Integration
- Write natural-sounding dialogue
- Include emotion cues for TTS: `[happy]`, `[sad]`, `[excited]`
- Plan for voice guidance prompts
- Consider background sound effects

### Step 3: Technical Implementation

#### File Structure
```
assets/stories/story_name/
├── metadata.json
├── content/
│   ├── chapter_1.json
│   ├── chapter_2.json
│   └── endings.json
├── images/
│   ├── cover.jpg
│   ├── scene_1.jpg
│   └── character_portraits/
└── audio/
    ├── narration/
    ├── sound_effects/
    └── background_music/
```

#### Content JSON Format
```json
{
  "chapterId": "chapter_1",
  "title": "The Beginning",
  "scenes": [
    {
      "sceneId": "scene_1",
      "type": "narrative",
      "content": {
        "text": "Once upon a time...",
        "imageUrl": "assets/stories/story_name/images/scene_1.jpg",
        "audioUrl": "assets/stories/story_name/audio/narration/scene_1.mp3",
        "emotionCue": "gentle"
      }
    },
    {
      "sceneId": "scene_2",
      "type": "choice",
      "content": {
        "text": "What should the character do?",
        "choices": [
          {
            "id": "choice_1",
            "text": "Help the friend",
            "consequence": "positive",
            "nextScene": "scene_3a"
          },
          {
            "id": "choice_2",
            "text": "Walk away",
            "consequence": "negative",
            "nextScene": "scene_3b"
          }
        ]
      }
    }
  ]
}
```

## Story Integration

### Adding Stories to the App

1. **Add Metadata**: Update `lib/data/story_metadata.dart`
2. **Add Assets**: Place story files in appropriate asset directories
3. **Update Asset Manifest**: Add new assets to `pubspec.yaml`
4. **Test Story Flow**: Verify all scenes and choices work correctly

### Story Loading System

The app uses a multi-source loading system:

1. **Asset Stories**: Bundled with the app
2. **Downloaded Stories**: Cached locally for offline access
3. **Streaming Stories**: Loaded from remote servers

### Localization

Stories support multiple languages:

1. **Text Content**: All text must be provided in supported languages
2. **Audio Content**: Narration should be available in each language
3. **Images**: Consider cultural appropriateness for different regions

## Voice Guidance Integration

### Screen-Specific Guidance

Each story screen should include voice guidance:

```dart
// In story screens
@override
String Function(AppLocalizations)? getScreenIntroductionText() {
  return (l10n) => l10n.storyIntroPrompt;
}
```

### Choice Guidance

Provide audio cues for interactive elements:

```dart
// When presenting choices
await ref.read(enhancedVoiceGuidanceManagerProvider).playScreenGuide(
  screenId: 'story_choice_${widget.storyId}',
  context: context,
  textProvider: (l10n) => l10n.choicePrompt,
  emotionCue: 'encouraging',
);
```

## Testing Stories

### Manual Testing Checklist

- [ ] Story loads correctly
- [ ] All scenes display properly
- [ ] Choices lead to correct consequences
- [ ] Audio plays synchronously with text
- [ ] Images load and display correctly
- [ ] Voice guidance works on each screen
- [ ] Story can be completed successfully
- [ ] Progress tracking works
- [ ] Offline mode functions properly

### Automated Testing

```dart
// Example story test
testWidgets('Story loads and displays correctly', (WidgetTester tester) async {
  await tester.pumpWidget(
    ProviderScope(
      child: MaterialApp(
        home: StoryPlayerScreen(storyId: 'test_story'),
      ),
    ),
  );
  
  expect(find.text('Story Title'), findsOneWidget);
  expect(find.byType(Image), findsOneWidget);
});
```

## Performance Considerations

### Asset Optimization

1. **Images**: Use WebP format for better compression
2. **Audio**: Use AAC format for narration, OGG for effects
3. **Text**: Minimize JSON file sizes
4. **Caching**: Implement proper caching strategies

### Memory Management

1. **Lazy Loading**: Load scenes as needed
2. **Asset Disposal**: Properly dispose of unused assets
3. **Image Caching**: Use Flutter's image caching system
4. **Audio Streaming**: Stream long audio files

## Accessibility

### Visual Accessibility

1. **High Contrast**: Ensure sufficient color contrast
2. **Font Scaling**: Support dynamic font sizing
3. **Alternative Text**: Provide descriptions for images
4. **Focus Management**: Proper focus order for navigation

### Audio Accessibility

1. **Closed Captions**: Provide text alternatives for audio
2. **Audio Descriptions**: Describe visual elements
3. **Speed Control**: Allow playback speed adjustment
4. **Volume Control**: Independent volume controls

## Content Guidelines

### Age Appropriateness

- **3-5 years**: Simple concepts, basic vocabulary, short stories
- **5-7 years**: More complex plots, expanded vocabulary, moral lessons
- **7-9 years**: Character development, multiple perspectives, problem-solving
- **9-12 years**: Advanced themes, critical thinking, complex choices

### Moral Values

Focus on universal values:
- Kindness and empathy
- Honesty and integrity
- Courage and perseverance
- Responsibility and accountability
- Friendship and cooperation
- Respect and tolerance

### Cultural Sensitivity

- Avoid stereotypes and biases
- Include diverse characters and perspectives
- Respect different cultural traditions
- Use inclusive language
- Consider global audience

## Troubleshooting

### Common Issues

1. **Story Won't Load**
   - Check file paths in metadata
   - Verify asset declarations in pubspec.yaml
   - Ensure proper JSON formatting

2. **Audio Not Playing**
   - Verify audio file formats
   - Check audio permissions
   - Test on different devices

3. **Images Not Displaying**
   - Confirm image file paths
   - Check image file formats
   - Verify asset bundle inclusion

4. **Choices Not Working**
   - Validate choice JSON structure
   - Check scene ID references
   - Test navigation logic

### Debug Tools

Use the app's debug features:
- Story validation tools
- Asset loading diagnostics
- Performance monitoring
- Error logging and reporting

## Best Practices

1. **Version Control**: Use semantic versioning for stories
2. **Testing**: Test on multiple devices and screen sizes
3. **Performance**: Monitor loading times and memory usage
4. **User Feedback**: Collect and analyze user engagement data
5. **Iteration**: Continuously improve based on user feedback
6. **Documentation**: Keep story documentation up to date
7. **Collaboration**: Use clear naming conventions and file organization

## Resources

- [Flutter Asset Management](https://flutter.dev/docs/development/ui/assets-and-images)
- [Audio Plugin Documentation](https://pub.dev/packages/audioplayers)
- [Localization Guide](https://flutter.dev/docs/development/accessibility-and-localization/internationalization)
- [Accessibility Guidelines](https://flutter.dev/docs/development/accessibility-and-localization/accessibility)
