import 'package:flutter/material.dart';

class LoadingIndicatorWidget extends StatelessWidget {
  final Color? color;
  final double? size; // Not used by default CircularProgressIndicator size but useful for custom

  const LoadingIndicatorWidget({super.key, this.color, this.size});

  @override
  Widget build(BuildContext context) {
    // Placeholder for "pulsing star/leaf" if feasible for AI:
    // return Center(child: YourCustomPulsingAnimationWidget(color: color ?? Theme.of(context).colorScheme.secondary));

    // Default: CircularProgressIndicator
    return Center(
      child: CircularProgressIndicator(
        valueColor: AlwaysStoppedAnimation<Color>(color ?? Theme.of(context).colorScheme.secondary),
      ),
    );
  }
}
