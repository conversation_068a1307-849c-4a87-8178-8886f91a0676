import 'dart:convert';
import 'package:choice_once_upon_a_time/core/services/story_timing_analyzer.dart';
import 'package:choice_once_upon_a_time/core/testing/story_timing_test_runner.dart';
import 'package:choice_once_upon_a_time/core/utils/app_logger.dart';

/// Generator for comprehensive timing documentation and reports
/// Creates detailed markdown reports with performance metrics and optimization recommendations
class TimingDocumentationGenerator {
  static const String _logPrefix = 'TIMING_DOCS';
  
  /// Generate comprehensive timing documentation
  static String generateComprehensiveTimingReport({
    required Map<String, StoryTimingTestResult> testResults,
    required Map<String, dynamic> timingData,
    String? additionalNotes,
  }) {
    final buffer = StringBuffer();
    
    // Header
    buffer.writeln('# Story Timing Analysis Report');
    buffer.writeln();
    buffer.writeln('**Generated:** ${DateTime.now().toIso8601String()}');
    buffer.writeln('**Analysis Type:** Comprehensive Story Playback Timing');
    buffer.writeln('**Child-Friendly Requirements:** Enforced');
    buffer.writeln();
    
    // Executive Summary
    _addExecutiveSummary(buffer, testResults);
    
    // Configuration Details
    _addConfigurationDetails(buffer);
    
    // Test Results Overview
    _addTestResultsOverview(buffer, testResults);
    
    // Detailed Scene Analysis
    _addDetailedSceneAnalysis(buffer, testResults);
    
    // Performance Metrics
    _addPerformanceMetrics(buffer, testResults);
    
    // Child-Friendly Compliance Analysis
    _addChildFriendlyCompliance(buffer, testResults);
    
    // Optimization Recommendations
    _addOptimizationRecommendations(buffer, testResults);
    
    // Technical Details
    _addTechnicalDetails(buffer, timingData);
    
    // Additional Notes
    if (additionalNotes != null) {
      buffer.writeln('## Additional Notes');
      buffer.writeln();
      buffer.writeln(additionalNotes);
      buffer.writeln();
    }
    
    // Footer
    _addFooter(buffer);
    
    return buffer.toString();
  }

  /// Add executive summary section
  static void _addExecutiveSummary(StringBuffer buffer, Map<String, StoryTimingTestResult> testResults) {
    buffer.writeln('## Executive Summary');
    buffer.writeln();
    
    final successfulTests = testResults.values.where((r) => r.success).toList();
    final totalScenes = successfulTests.fold(0, (sum, test) => sum + test.sceneCount);
    final averageCompliance = successfulTests.isNotEmpty 
        ? successfulTests.fold(0.0, (sum, test) => sum + test.timingReport.childFriendlyCompliance) / successfulTests.length
        : 0.0;
    
    buffer.writeln('### Key Findings');
    buffer.writeln();
    buffer.writeln('- **Stories Tested:** ${testResults.length}');
    buffer.writeln('- **Successful Tests:** ${successfulTests.length}');
    buffer.writeln('- **Total Scenes Analyzed:** $totalScenes');
    buffer.writeln('- **Average Child-Friendly Compliance:** ${(averageCompliance * 100).toStringAsFixed(1)}%');
    
    if (averageCompliance >= 0.9) {
      buffer.writeln('- **Overall Assessment:** ✅ **EXCELLENT** - Timing meets all child-friendly requirements');
    } else if (averageCompliance >= 0.8) {
      buffer.writeln('- **Overall Assessment:** ✅ **GOOD** - Timing mostly meets child-friendly requirements');
    } else if (averageCompliance >= 0.6) {
      buffer.writeln('- **Overall Assessment:** ⚠️ **NEEDS IMPROVEMENT** - Some timing issues identified');
    } else {
      buffer.writeln('- **Overall Assessment:** ❌ **POOR** - Significant timing issues require attention');
    }
    
    buffer.writeln();
  }

  /// Add configuration details section
  static void _addConfigurationDetails(StringBuffer buffer) {
    buffer.writeln('## Configuration Details');
    buffer.writeln();
    buffer.writeln('### Child-Friendly Timing Requirements');
    buffer.writeln();
    buffer.writeln('| Parameter | Value | Purpose |');
    buffer.writeln('|-----------|-------|---------|');
    buffer.writeln('| TTS Speed | ${StoryTimingAnalyzer.childFriendlyTTSSpeed} | Optimal speech rate for children |');
    buffer.writeln('| Minimum Scene Display | ${StoryTimingAnalyzer.minimumSceneDisplayMs}ms (${StoryTimingAnalyzer.minimumSceneDisplayMs / 1000}s) | Ensure adequate reading time |');
    buffer.writeln('| Sentence Pause | ${StoryTimingAnalyzer.sentencePauseMs}ms (${StoryTimingAnalyzer.sentencePauseMs / 1000}s) | Allow comprehension between sentences |');
    buffer.writeln('| Scene Transition | ${StoryTimingAnalyzer.sceneTransitionMs}ms | Smooth visual transitions |');
    buffer.writeln();
    
    buffer.writeln('### Test Configuration');
    buffer.writeln();
    buffer.writeln('- **Test Stories:** ${StoryTimingTestRunner.testStoryIds.join(", ")}');
    buffer.writeln('- **Autoplay Enabled:** ${StoryTimingTestRunner.enableAutoplay}');
    buffer.writeln('- **Max Test Duration:** ${StoryTimingTestRunner.maxTestDurationMinutes} minutes');
    buffer.writeln();
  }

  /// Add test results overview section
  static void _addTestResultsOverview(StringBuffer buffer, Map<String, StoryTimingTestResult> testResults) {
    buffer.writeln('## Test Results Overview');
    buffer.writeln();
    
    buffer.writeln('| Story ID | Status | Scenes | Total Time | Avg Scene Time | Compliance |');
    buffer.writeln('|----------|--------|--------|------------|----------------|------------|');
    
    for (final entry in testResults.entries) {
      final storyId = entry.key;
      final result = entry.value;
      
      if (result.success) {
        final report = result.timingReport;
        final totalTimeS = (report.totalStoryTime / 1000).toStringAsFixed(1);
        final avgSceneTimeS = (report.averageSceneTime / 1000).toStringAsFixed(1);
        final compliance = (report.childFriendlyCompliance * 100).toStringAsFixed(1);
        final status = report.childFriendlyCompliance >= 0.8 ? '✅ Pass' : '⚠️ Issues';
        
        buffer.writeln('| $storyId | $status | ${report.totalScenes} | ${totalTimeS}s | ${avgSceneTimeS}s | $compliance% |');
      } else {
        buffer.writeln('| $storyId | ❌ Failed | - | - | - | - |');
      }
    }
    
    buffer.writeln();
  }

  /// Add detailed scene analysis section
  static void _addDetailedSceneAnalysis(StringBuffer buffer, Map<String, StoryTimingTestResult> testResults) {
    buffer.writeln('## Detailed Scene Analysis');
    buffer.writeln();
    
    for (final entry in testResults.entries) {
      final storyId = entry.key;
      final result = entry.value;
      
      if (!result.success) continue;
      
      buffer.writeln('### $storyId');
      buffer.writeln();
      
      final report = result.timingReport;
      
      buffer.writeln('| Scene ID | Total Time | Load Time | Narration | Pauses | Transition | Compliance |');
      buffer.writeln('|----------|------------|-----------|-----------|--------|------------|------------|');
      
      for (final sceneData in report.sceneTimings) {
        final totalTimeS = (sceneData.totalTime / 1000).toStringAsFixed(1);
        final loadTimeMs = sceneData.loadTime;
        final narrationTimeS = (sceneData.narrationTime / 1000).toStringAsFixed(1);
        final pauseTimeS = (sceneData.pauseTime / 1000).toStringAsFixed(1);
        final transitionTimeMs = sceneData.transitionTime;
        
        // Check compliance
        final meetsMinTime = sceneData.totalTime >= StoryTimingAnalyzer.minimumSceneDisplayMs;
        final hasGoodPacing = sceneData.narrationTime == 0 || 
                             (sceneData.narrationTime / (sceneData.narrationTime + sceneData.pauseTime)) <= 0.7;
        final compliance = meetsMinTime && hasGoodPacing ? '✅' : '⚠️';
        
        buffer.writeln('| ${sceneData.sceneId} | ${totalTimeS}s | ${loadTimeMs}ms | ${narrationTimeS}s | ${pauseTimeS}s | ${transitionTimeMs}ms | $compliance |');
      }
      
      buffer.writeln();
    }
  }

  /// Add performance metrics section
  static void _addPerformanceMetrics(StringBuffer buffer, Map<String, StoryTimingTestResult> testResults) {
    buffer.writeln('## Performance Metrics');
    buffer.writeln();
    
    final successfulTests = testResults.values.where((r) => r.success).toList();
    if (successfulTests.isEmpty) {
      buffer.writeln('No successful tests to analyze.');
      buffer.writeln();
      return;
    }
    
    // Calculate aggregate metrics
    final allSceneTimings = successfulTests
        .expand((test) => test.timingReport.sceneTimings)
        .toList();
    
    final avgLoadTime = allSceneTimings.fold(0, (sum, scene) => sum + scene.loadTime) / allSceneTimings.length;
    final avgNarrationTime = allSceneTimings.fold(0, (sum, scene) => sum + scene.narrationTime) / allSceneTimings.length;
    final avgPauseTime = allSceneTimings.fold(0, (sum, scene) => sum + scene.pauseTime) / allSceneTimings.length;
    final avgTransitionTime = allSceneTimings.fold(0, (sum, scene) => sum + scene.transitionTime) / allSceneTimings.length;
    
    buffer.writeln('### Aggregate Performance Metrics');
    buffer.writeln();
    buffer.writeln('| Metric | Average | Target | Status |');
    buffer.writeln('|--------|---------|--------|--------|');
    buffer.writeln('| Scene Load Time | ${avgLoadTime.toStringAsFixed(0)}ms | <500ms | ${avgLoadTime < 500 ? "✅" : "⚠️"} |');
    buffer.writeln('| Narration Time | ${(avgNarrationTime / 1000).toStringAsFixed(1)}s | Variable | ℹ️ |');
    buffer.writeln('| Pause Time | ${(avgPauseTime / 1000).toStringAsFixed(1)}s | ≥1.5s | ${avgPauseTime >= 1500 ? "✅" : "⚠️"} |');
    buffer.writeln('| Transition Time | ${avgTransitionTime.toStringAsFixed(0)}ms | ~800ms | ${(avgTransitionTime >= 600 && avgTransitionTime <= 1000) ? "✅" : "⚠️"} |');
    buffer.writeln();
    
    // Performance bottlenecks
    buffer.writeln('### Performance Bottlenecks');
    buffer.writeln();
    
    final slowLoadingScenes = allSceneTimings.where((s) => s.loadTime > 1000).toList();
    final veryLongScenes = allSceneTimings.where((s) => s.totalTime > 15000).toList();
    
    if (slowLoadingScenes.isNotEmpty) {
      buffer.writeln('**Slow Loading Scenes (>1000ms):**');
      for (final scene in slowLoadingScenes) {
        buffer.writeln('- ${scene.sceneId}: ${scene.loadTime}ms');
      }
      buffer.writeln();
    }
    
    if (veryLongScenes.isNotEmpty) {
      buffer.writeln('**Very Long Scenes (>15s):**');
      for (final scene in veryLongScenes) {
        buffer.writeln('- ${scene.sceneId}: ${(scene.totalTime / 1000).toStringAsFixed(1)}s');
      }
      buffer.writeln();
    }
    
    if (slowLoadingScenes.isEmpty && veryLongScenes.isEmpty) {
      buffer.writeln('✅ No significant performance bottlenecks detected.');
      buffer.writeln();
    }
  }

  /// Add child-friendly compliance analysis
  static void _addChildFriendlyCompliance(StringBuffer buffer, Map<String, StoryTimingTestResult> testResults) {
    buffer.writeln('## Child-Friendly Compliance Analysis');
    buffer.writeln();
    
    final successfulTests = testResults.values.where((r) => r.success).toList();
    if (successfulTests.isEmpty) {
      buffer.writeln('No successful tests to analyze.');
      buffer.writeln();
      return;
    }
    
    final allSceneTimings = successfulTests
        .expand((test) => test.timingReport.sceneTimings)
        .toList();
    
    // Analyze compliance issues
    final shortScenes = allSceneTimings.where((s) => s.totalTime < StoryTimingAnalyzer.minimumSceneDisplayMs).toList();
    final fastPacedScenes = allSceneTimings.where((s) {
      if (s.narrationTime == 0) return false;
      final paceRatio = s.narrationTime / (s.narrationTime + s.pauseTime);
      return paceRatio > 0.7;
    }).toList();
    
    buffer.writeln('### Compliance Issues');
    buffer.writeln();
    
    if (shortScenes.isNotEmpty) {
      buffer.writeln('**Scenes Below Minimum Display Time (${StoryTimingAnalyzer.minimumSceneDisplayMs}ms):**');
      for (final scene in shortScenes) {
        buffer.writeln('- ${scene.sceneId}: ${scene.totalTime}ms (${(scene.totalTime / 1000).toStringAsFixed(1)}s)');
      }
      buffer.writeln();
    }
    
    if (fastPacedScenes.isNotEmpty) {
      buffer.writeln('**Scenes with Fast Narration Pacing (>70% narration vs pause time):**');
      for (final scene in fastPacedScenes) {
        final paceRatio = scene.narrationTime / (scene.narrationTime + scene.pauseTime);
        buffer.writeln('- ${scene.sceneId}: ${(paceRatio * 100).toStringAsFixed(1)}% narration ratio');
      }
      buffer.writeln();
    }
    
    if (shortScenes.isEmpty && fastPacedScenes.isEmpty) {
      buffer.writeln('✅ All scenes meet child-friendly timing requirements.');
      buffer.writeln();
    }
    
    // Compliance summary
    final totalScenes = allSceneTimings.length;
    final compliantScenes = totalScenes - shortScenes.length - fastPacedScenes.length;
    final complianceRate = (compliantScenes / totalScenes * 100);
    
    buffer.writeln('### Compliance Summary');
    buffer.writeln();
    buffer.writeln('- **Total Scenes:** $totalScenes');
    buffer.writeln('- **Compliant Scenes:** $compliantScenes');
    buffer.writeln('- **Compliance Rate:** ${complianceRate.toStringAsFixed(1)}%');
    buffer.writeln();
  }

  /// Add optimization recommendations
  static void _addOptimizationRecommendations(StringBuffer buffer, Map<String, StoryTimingTestResult> testResults) {
    buffer.writeln('## Optimization Recommendations');
    buffer.writeln();
    
    final successfulTests = testResults.values.where((r) => r.success).toList();
    if (successfulTests.isEmpty) {
      buffer.writeln('No successful tests to analyze for recommendations.');
      buffer.writeln();
      return;
    }
    
    final recommendations = <String>[];
    
    // Analyze for recommendations
    final allSceneTimings = successfulTests
        .expand((test) => test.timingReport.sceneTimings)
        .toList();
    
    final avgLoadTime = allSceneTimings.fold(0, (sum, scene) => sum + scene.loadTime) / allSceneTimings.length;
    final shortScenes = allSceneTimings.where((s) => s.totalTime < StoryTimingAnalyzer.minimumSceneDisplayMs).length;
    final fastPacedScenes = allSceneTimings.where((s) {
      if (s.narrationTime == 0) return false;
      final paceRatio = s.narrationTime / (s.narrationTime + s.pauseTime);
      return paceRatio > 0.7;
    }).length;
    
    // Generate recommendations
    if (avgLoadTime > 500) {
      recommendations.add('**Optimize Asset Loading:** Average scene load time (${avgLoadTime.toStringAsFixed(0)}ms) exceeds target. Consider image compression, preloading, or caching strategies.');
    }
    
    if (shortScenes > 0) {
      recommendations.add('**Extend Scene Display Time:** $shortScenes scenes are below the minimum ${StoryTimingAnalyzer.minimumSceneDisplayMs}ms display time. Add padding or extend narration to ensure adequate reading time.');
    }
    
    if (fastPacedScenes > 0) {
      recommendations.add('**Improve Narration Pacing:** $fastPacedScenes scenes have fast narration pacing. Increase pause duration between sentences or reduce TTS speed for better comprehension.');
    }
    
    final avgCompliance = successfulTests.fold(0.0, (sum, test) => sum + test.timingReport.childFriendlyCompliance) / successfulTests.length;
    if (avgCompliance < 0.8) {
      recommendations.add('**Overall Timing Review:** Child-friendly compliance (${(avgCompliance * 100).toStringAsFixed(1)}%) is below target (80%). Review timing configuration and scene content.');
    }
    
    if (recommendations.isEmpty) {
      buffer.writeln('✅ **No optimization recommendations needed.** The story timing meets all child-friendly requirements and performance targets.');
    } else {
      buffer.writeln('### Priority Recommendations');
      buffer.writeln();
      for (int i = 0; i < recommendations.length; i++) {
        buffer.writeln('${i + 1}. ${recommendations[i]}');
        buffer.writeln();
      }
    }
    
    // General best practices
    buffer.writeln('### General Best Practices');
    buffer.writeln();
    buffer.writeln('- **Regular Testing:** Run timing analysis after content changes');
    buffer.writeln('- **User Testing:** Validate timing with actual children in target age group');
    buffer.writeln('- **Progressive Enhancement:** Start with conservative timing and adjust based on feedback');
    buffer.writeln('- **Accessibility:** Consider users with different reading speeds and attention spans');
    buffer.writeln();
  }

  /// Add technical details section
  static void _addTechnicalDetails(StringBuffer buffer, Map<String, dynamic> timingData) {
    buffer.writeln('## Technical Details');
    buffer.writeln();
    
    buffer.writeln('### Raw Timing Data');
    buffer.writeln();
    buffer.writeln('```json');
    buffer.writeln(const JsonEncoder.withIndent('  ').convert(timingData));
    buffer.writeln('```');
    buffer.writeln();
  }

  /// Add footer section
  static void _addFooter(StringBuffer buffer) {
    buffer.writeln('---');
    buffer.writeln();
    buffer.writeln('**Report Generated By:** Story Timing Analysis System');
    buffer.writeln('**Framework:** Flutter with Enhanced Story Player');
    buffer.writeln('**Analysis Engine:** StoryTimingAnalyzer v1.0');
    buffer.writeln('**Child-Friendly Standards:** Based on educational research for optimal reading comprehension');
    buffer.writeln();
    buffer.writeln('*This report provides comprehensive analysis of story playback timing to ensure optimal experience for children. All recommendations are based on established best practices for educational content delivery.*');
  }
}
