import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:choice_once_upon_a_time/features/home/<USER>/widgets/home_screen_grid_area.dart';

void main() {
  group('HomeScreenGridArea Tests', () {
    Widget createTestWidget({Size? screenSize}) {
      return ProviderScope(
        child: MaterialApp(
          home: Scaffold(
            body: screenSize != null
                ? SizedBox(
                    width: screenSize.width,
                    height: screenSize.height,
                    child: const HomeScreenGridArea(),
                  )
                : const HomeScreenGridArea(),
          ),
        ),
      );
    }

    group('Responsive Layout', () {
      testWidgets('should show 1 column on very small screens (< 400px)', (WidgetTester tester) async {
        // Arrange
        const smallScreenSize = Size(350, 600); // Very small screen
        await tester.binding.setSurfaceSize(smallScreenSize);

        // Act
        await tester.pumpWidget(createTestWidget(screenSize: smallScreenSize));
        await tester.pumpAndSettle();

        // Assert
        expect(find.byType(HomeScreenGridArea), findsOneWidget);
        
        // Verify no overflow errors
        expect(tester.takeException(), isNull);
        
        // The grid should be using LayoutBuilder to determine columns
        expect(find.byType(LayoutBuilder), findsOneWidget);
      });

      testWidgets('should show 2 columns on small screens (400-700px)', (WidgetTester tester) async {
        // Arrange
        const smallScreenSize = Size(500, 800); // Small screen
        await tester.binding.setSurfaceSize(smallScreenSize);

        // Act
        await tester.pumpWidget(createTestWidget(screenSize: smallScreenSize));
        await tester.pumpAndSettle();

        // Assert
        expect(find.byType(HomeScreenGridArea), findsOneWidget);
        expect(tester.takeException(), isNull);
      });

      testWidgets('should show 3 columns on medium screens (700-1000px)', (WidgetTester tester) async {
        // Arrange
        const mediumScreenSize = Size(800, 1000); // Medium screen
        await tester.binding.setSurfaceSize(mediumScreenSize);

        // Act
        await tester.pumpWidget(createTestWidget(screenSize: mediumScreenSize));
        await tester.pumpAndSettle();

        // Assert
        expect(find.byType(HomeScreenGridArea), findsOneWidget);
        expect(tester.takeException(), isNull);
      });

      testWidgets('should show 4 columns on large screens (> 1000px)', (WidgetTester tester) async {
        // Arrange
        const largeScreenSize = Size(1200, 800); // Large screen
        await tester.binding.setSurfaceSize(largeScreenSize);

        // Act
        await tester.pumpWidget(createTestWidget(screenSize: largeScreenSize));
        await tester.pumpAndSettle();

        // Assert
        expect(find.byType(HomeScreenGridArea), findsOneWidget);
        expect(tester.takeException(), isNull);
      });
    });

    group('Grid Sections', () {
      testWidgets('should display all grid sections', (WidgetTester tester) async {
        // Act
        await tester.pumpWidget(createTestWidget());
        await tester.pumpAndSettle();

        // Assert - Check for key grid section widgets
        expect(find.byType(HomeScreenGridArea), findsOneWidget);
        
        // The grid should contain multiple sections
        // We can't easily test for specific sections without importing them,
        // but we can verify the overall structure
        expect(find.byType(LayoutBuilder), findsOneWidget);
      });
    });

    group('Responsive Spacing', () {
      testWidgets('should use appropriate spacing for small screens', (WidgetTester tester) async {
        // Arrange
        const smallScreenSize = Size(375, 667); // iPhone SE
        await tester.binding.setSurfaceSize(smallScreenSize);

        // Act
        await tester.pumpWidget(createTestWidget(screenSize: smallScreenSize));
        await tester.pumpAndSettle();

        // Assert
        expect(find.byType(HomeScreenGridArea), findsOneWidget);
        expect(tester.takeException(), isNull);
        
        // Verify that the widget adapts to small screen constraints
        final gridWidget = tester.widget<HomeScreenGridArea>(find.byType(HomeScreenGridArea));
        expect(gridWidget, isNotNull);
      });

      testWidgets('should use appropriate spacing for large screens', (WidgetTester tester) async {
        // Arrange
        const largeScreenSize = Size(1024, 768); // iPad
        await tester.binding.setSurfaceSize(largeScreenSize);

        // Act
        await tester.pumpWidget(createTestWidget(screenSize: largeScreenSize));
        await tester.pumpAndSettle();

        // Assert
        expect(find.byType(HomeScreenGridArea), findsOneWidget);
        expect(tester.takeException(), isNull);
      });
    });

    group('Orientation Changes', () {
      testWidgets('should handle portrait orientation', (WidgetTester tester) async {
        // Arrange
        const portraitSize = Size(375, 812); // iPhone X portrait
        await tester.binding.setSurfaceSize(portraitSize);

        // Act
        await tester.pumpWidget(createTestWidget(screenSize: portraitSize));
        await tester.pumpAndSettle();

        // Assert
        expect(find.byType(HomeScreenGridArea), findsOneWidget);
        expect(tester.takeException(), isNull);
      });

      testWidgets('should handle landscape orientation', (WidgetTester tester) async {
        // Arrange
        const landscapeSize = Size(812, 375); // iPhone X landscape
        await tester.binding.setSurfaceSize(landscapeSize);

        // Act
        await tester.pumpWidget(createTestWidget(screenSize: landscapeSize));
        await tester.pumpAndSettle();

        // Assert
        expect(find.byType(HomeScreenGridArea), findsOneWidget);
        expect(tester.takeException(), isNull);
      });
    });

    group('Performance', () {
      testWidgets('should not cause overflow errors', (WidgetTester tester) async {
        // Test various screen sizes to ensure no overflow
        final testSizes = [
          const Size(320, 568), // iPhone 5
          const Size(375, 667), // iPhone SE
          const Size(414, 896), // iPhone 11 Pro Max
          const Size(768, 1024), // iPad
          const Size(1024, 768), // iPad landscape
        ];

        for (final size in testSizes) {
          await tester.binding.setSurfaceSize(size);
          
          await tester.pumpWidget(createTestWidget(screenSize: size));
          await tester.pumpAndSettle();

          // Assert no overflow errors
          expect(tester.takeException(), isNull, 
              reason: 'Overflow error at size ${size.width}x${size.height}');
        }
      });

      testWidgets('should rebuild efficiently on size changes', (WidgetTester tester) async {
        // Arrange
        await tester.binding.setSurfaceSize(const Size(375, 667));
        await tester.pumpWidget(createTestWidget());
        await tester.pumpAndSettle();

        // Act - Change screen size
        await tester.binding.setSurfaceSize(const Size(1024, 768));
        await tester.pumpWidget(createTestWidget());
        await tester.pumpAndSettle();

        // Assert
        expect(find.byType(HomeScreenGridArea), findsOneWidget);
        expect(tester.takeException(), isNull);
      });
    });

    group('Accessibility', () {
      testWidgets('should be accessible on different screen sizes', (WidgetTester tester) async {
        // Arrange
        const accessibilitySize = Size(375, 667);
        await tester.binding.setSurfaceSize(accessibilitySize);

        // Act
        await tester.pumpWidget(createTestWidget(screenSize: accessibilitySize));
        await tester.pumpAndSettle();

        // Assert
        expect(find.byType(HomeScreenGridArea), findsOneWidget);
        
        // Verify the widget is accessible (no semantic issues)
        final semantics = tester.getSemantics(find.byType(HomeScreenGridArea));
        expect(semantics, isNotNull);
      });
    });
  });
}
