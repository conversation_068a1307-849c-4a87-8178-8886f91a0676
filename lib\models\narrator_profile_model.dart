import 'package:json_annotation/json_annotation.dart';
import 'package:choice_once_upon_a_time/models/enhanced_story_model.dart';
import 'package:choice_once_upon_a_time/core/utils/app_logger.dart';

part 'narrator_profile_model.g.dart';

/// Custom narrator profile model for parent-created narrators
@JsonSerializable()
class NarratorProfileModel {
  final String id;
  final String name;
  final String description;
  final VoiceModel voice;
  final NarratorCategory category;
  final NarratorGender gender;
  final NarratorAgeRange ageRange;
  final List<NarratorPersonality> personalities;
  final String? accentLanguage;
  final DateTime createdAt;
  final DateTime updatedAt;
  final bool isDefault;

  const NarratorProfileModel({
    required this.id,
    required this.name,
    required this.description,
    required this.voice,
    required this.category,
    required this.gender,
    required this.ageRange,
    required this.personalities,
    this.accentLanguage,
    required this.createdAt,
    required this.updatedAt,
    this.isDefault = false,
  });

  factory NarratorProfileModel.fromJson(Map<String, dynamic> json) =>
      _$NarratorProfileModelFromJson(json);

  Map<String, dynamic> toJson() => _$NarratorProfileModelToJson(this);

  /// Create a copy with updated fields
  NarratorProfileModel copyWith({
    String? id,
    String? name,
    String? description,
    VoiceModel? voice,
    NarratorCategory? category,
    NarratorGender? gender,
    NarratorAgeRange? ageRange,
    List<NarratorPersonality>? personalities,
    String? accentLanguage,
    DateTime? createdAt,
    DateTime? updatedAt,
    bool? isDefault,
  }) {
    return NarratorProfileModel(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      voice: voice ?? this.voice,
      category: category ?? this.category,
      gender: gender ?? this.gender,
      ageRange: ageRange ?? this.ageRange,
      personalities: personalities ?? this.personalities,
      accentLanguage: accentLanguage ?? this.accentLanguage,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      isDefault: isDefault ?? this.isDefault,
    );
  }

  /// Get display name for the narrator
  String get displayName => name.isNotEmpty ? name : 'Custom Narrator';

  /// Get personality description
  String get personalityDescription {
    if (personalities.isEmpty) return 'Neutral';
    return personalities.map((p) => p.displayName).join(', ');
  }

  /// Get voice description
  String get voiceDescription {
    return '${gender.displayName} ${ageRange.displayName} voice';
  }

  /// Validate the narrator profile
  bool get isValid {
    return name.isNotEmpty &&
           description.isNotEmpty &&
           voice.pitch >= 0.5 && voice.pitch <= 2.0 &&
           voice.rate >= 0.5 && voice.rate <= 2.0 &&
           voice.volume >= 0.5 && voice.volume <= 1.0;
  }
}

/// Narrator category enumeration
enum NarratorCategory {
  grandparent,
  teacher,
  friend,
  fantasyCharacter,
  custom;

  String get displayName {
    switch (this) {
      case NarratorCategory.grandparent:
        return 'Grandparent';
      case NarratorCategory.teacher:
        return 'Teacher';
      case NarratorCategory.friend:
        return 'Friend';
      case NarratorCategory.fantasyCharacter:
        return 'Fantasy Character';
      case NarratorCategory.custom:
        return 'Custom';
    }
  }

  String get description {
    switch (this) {
      case NarratorCategory.grandparent:
        return 'Warm, wise, and comforting storytelling voice';
      case NarratorCategory.teacher:
        return 'Clear, educational, and encouraging voice';
      case NarratorCategory.friend:
        return 'Friendly, casual, and relatable voice';
      case NarratorCategory.fantasyCharacter:
        return 'Magical, whimsical, and imaginative voice';
      case NarratorCategory.custom:
        return 'Personalized voice with custom characteristics';
    }
  }
}

/// Narrator gender enumeration
enum NarratorGender {
  male,
  female,
  neutral;

  String get displayName {
    switch (this) {
      case NarratorGender.male:
        return 'Male';
      case NarratorGender.female:
        return 'Female';
      case NarratorGender.neutral:
        return 'Neutral';
    }
  }
}

/// Narrator age range enumeration
enum NarratorAgeRange {
  child,
  adult,
  elderly;

  String get displayName {
    switch (this) {
      case NarratorAgeRange.child:
        return 'Child';
      case NarratorAgeRange.adult:
        return 'Adult';
      case NarratorAgeRange.elderly:
        return 'Elderly';
    }
  }

  String get description {
    switch (this) {
      case NarratorAgeRange.child:
        return 'Young, energetic voice';
      case NarratorAgeRange.adult:
        return 'Mature, confident voice';
      case NarratorAgeRange.elderly:
        return 'Wise, gentle voice';
    }
  }
}

/// Narrator personality traits enumeration
enum NarratorPersonality {
  gentle,
  energetic,
  calm,
  playful,
  wise,
  cheerful,
  mysterious,
  dramatic;

  String get displayName {
    switch (this) {
      case NarratorPersonality.gentle:
        return 'Gentle';
      case NarratorPersonality.energetic:
        return 'Energetic';
      case NarratorPersonality.calm:
        return 'Calm';
      case NarratorPersonality.playful:
        return 'Playful';
      case NarratorPersonality.wise:
        return 'Wise';
      case NarratorPersonality.cheerful:
        return 'Cheerful';
      case NarratorPersonality.mysterious:
        return 'Mysterious';
      case NarratorPersonality.dramatic:
        return 'Dramatic';
    }
  }

  String get description {
    switch (this) {
      case NarratorPersonality.gentle:
        return 'Soft, soothing storytelling approach';
      case NarratorPersonality.energetic:
        return 'Lively, animated storytelling style';
      case NarratorPersonality.calm:
        return 'Peaceful, relaxed narration';
      case NarratorPersonality.playful:
        return 'Fun, interactive storytelling';
      case NarratorPersonality.wise:
        return 'Thoughtful, insightful narration';
      case NarratorPersonality.cheerful:
        return 'Happy, upbeat storytelling';
      case NarratorPersonality.mysterious:
        return 'Intriguing, suspenseful narration';
      case NarratorPersonality.dramatic:
        return 'Expressive, theatrical storytelling';
    }
  }

  /// Get voice modulation parameters for this personality
  VoiceModulation get voiceModulation {
    switch (this) {
      case NarratorPersonality.gentle:
        return const VoiceModulation(pitchMultiplier: 0.9, rateMultiplier: 0.8, volumeMultiplier: 0.9);
      case NarratorPersonality.energetic:
        return const VoiceModulation(pitchMultiplier: 1.2, rateMultiplier: 1.3, volumeMultiplier: 1.0);
      case NarratorPersonality.calm:
        return const VoiceModulation(pitchMultiplier: 0.95, rateMultiplier: 0.7, volumeMultiplier: 0.8);
      case NarratorPersonality.playful:
        return const VoiceModulation(pitchMultiplier: 1.1, rateMultiplier: 1.1, volumeMultiplier: 0.95);
      case NarratorPersonality.wise:
        return const VoiceModulation(pitchMultiplier: 0.85, rateMultiplier: 0.9, volumeMultiplier: 0.9);
      case NarratorPersonality.cheerful:
        return const VoiceModulation(pitchMultiplier: 1.15, rateMultiplier: 1.0, volumeMultiplier: 0.95);
      case NarratorPersonality.mysterious:
        return const VoiceModulation(pitchMultiplier: 0.8, rateMultiplier: 0.6, volumeMultiplier: 0.7);
      case NarratorPersonality.dramatic:
        return const VoiceModulation(pitchMultiplier: 1.3, rateMultiplier: 1.2, volumeMultiplier: 1.0);
    }
  }
}

/// Voice modulation parameters for personality traits
class VoiceModulation {
  final double pitchMultiplier;
  final double rateMultiplier;
  final double volumeMultiplier;

  const VoiceModulation({
    required this.pitchMultiplier,
    required this.rateMultiplier,
    required this.volumeMultiplier,
  });

  /// Apply modulation to a voice model
  VoiceModel applyTo(VoiceModel baseVoice) {
    AppLogger.debug('[VOICE_MODULATION] Applying personality modulation: pitch=$pitchMultiplier, rate=$rateMultiplier, volume=$volumeMultiplier');
    
    return VoiceModel(
      name: baseVoice.name,
      pitch: (baseVoice.pitch * pitchMultiplier).clamp(0.5, 2.0),
      rate: (baseVoice.rate * rateMultiplier).clamp(0.5, 2.0),
      volume: (baseVoice.volume * volumeMultiplier).clamp(0.5, 1.0),
    );
  }
}
