import 'package:json_annotation/json_annotation.dart';

part 'child_progress_model.g.dart';

/// Comprehensive model for tracking child's learning progress
@JsonSerializable()
class ChildProgressModel {
  final String profileId;
  final String storyId;
  final DateTime lastPlayedAt;
  final DateTime createdAt;
  final DateTime updatedAt;
  
  // Story completion tracking
  final bool isCompleted;
  final String? completedOutcome;
  final int totalPlayTime; // in seconds
  final int completionCount;
  
  // Scene and choice tracking
  final List<String> visitedScenes;
  final Map<String, String> choicesMade; // sceneId -> choiceId
  final Map<String, int> scenePlayTime; // sceneId -> seconds
  
  // Moral values progress
  final List<MoralValueProgress> moralValues;
  
  // Vocabulary progress
  final List<VocabularyProgress> vocabularyLearned;
  
  // Reading skills
  final ReadingSkillsProgress readingSkills;
  
  // Engagement metrics
  final EngagementMetrics engagement;

  const ChildProgressModel({
    required this.profileId,
    required this.storyId,
    required this.lastPlayedAt,
    required this.createdAt,
    required this.updatedAt,
    this.isCompleted = false,
    this.completedOutcome,
    this.totalPlayTime = 0,
    this.completionCount = 0,
    this.visitedScenes = const [],
    this.choicesMade = const {},
    this.scenePlayTime = const {},
    this.moralValues = const [],
    this.vocabularyLearned = const [],
    required this.readingSkills,
    required this.engagement,
  });

  factory ChildProgressModel.fromJson(Map<String, dynamic> json) =>
      _$ChildProgressModelFromJson(json);

  Map<String, dynamic> toJson() => _$ChildProgressModelToJson(this);

  ChildProgressModel copyWith({
    String? profileId,
    String? storyId,
    DateTime? lastPlayedAt,
    DateTime? createdAt,
    DateTime? updatedAt,
    bool? isCompleted,
    String? completedOutcome,
    int? totalPlayTime,
    int? completionCount,
    List<String>? visitedScenes,
    Map<String, String>? choicesMade,
    Map<String, int>? scenePlayTime,
    List<MoralValueProgress>? moralValues,
    List<VocabularyProgress>? vocabularyLearned,
    ReadingSkillsProgress? readingSkills,
    EngagementMetrics? engagement,
  }) {
    return ChildProgressModel(
      profileId: profileId ?? this.profileId,
      storyId: storyId ?? this.storyId,
      lastPlayedAt: lastPlayedAt ?? this.lastPlayedAt,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      isCompleted: isCompleted ?? this.isCompleted,
      completedOutcome: completedOutcome ?? this.completedOutcome,
      totalPlayTime: totalPlayTime ?? this.totalPlayTime,
      completionCount: completionCount ?? this.completionCount,
      visitedScenes: visitedScenes ?? this.visitedScenes,
      choicesMade: choicesMade ?? this.choicesMade,
      scenePlayTime: scenePlayTime ?? this.scenePlayTime,
      moralValues: moralValues ?? this.moralValues,
      vocabularyLearned: vocabularyLearned ?? this.vocabularyLearned,
      readingSkills: readingSkills ?? this.readingSkills,
      engagement: engagement ?? this.engagement,
    );
  }

  /// Calculate overall progress percentage
  double get progressPercentage {
    if (visitedScenes.isEmpty) return 0.0;
    // This would be calculated based on total scenes in story
    // For now, return a simple calculation
    return isCompleted ? 100.0 : (visitedScenes.length * 10.0).clamp(0.0, 90.0);
  }

  /// Get total vocabulary words learned
  int get totalVocabularyLearned => vocabularyLearned.length;

  /// Get average moral value score
  double get averageMoralScore {
    if (moralValues.isEmpty) return 0.0;
    final total = moralValues.fold(0.0, (sum, mv) => sum + mv.score);
    return total / moralValues.length;
  }
}

/// Model for tracking moral value learning progress
@JsonSerializable()
class MoralValueProgress {
  final String moralValue;
  final String storyContext;
  final double score; // 0.0 to 1.0
  final DateTime learnedAt;
  final List<String> relatedChoices;
  final String? reflection; // Child's reflection or understanding

  const MoralValueProgress({
    required this.moralValue,
    required this.storyContext,
    required this.score,
    required this.learnedAt,
    this.relatedChoices = const [],
    this.reflection,
  });

  factory MoralValueProgress.fromJson(Map<String, dynamic> json) =>
      _$MoralValueProgressFromJson(json);

  Map<String, dynamic> toJson() => _$MoralValueProgressToJson(this);
}

/// Model for tracking vocabulary learning progress
@JsonSerializable()
class VocabularyProgress {
  final String word;
  final String definition;
  final String context; // Sentence where it appeared
  final String? imageUrl;
  final DateTime learnedAt;
  final int exposureCount;
  final bool isUnderstood;
  final List<String> relatedWords;

  const VocabularyProgress({
    required this.word,
    required this.definition,
    required this.context,
    this.imageUrl,
    required this.learnedAt,
    this.exposureCount = 1,
    this.isUnderstood = false,
    this.relatedWords = const [],
  });

  factory VocabularyProgress.fromJson(Map<String, dynamic> json) =>
      _$VocabularyProgressFromJson(json);

  Map<String, dynamic> toJson() => _$VocabularyProgressToJson(this);

  VocabularyProgress copyWith({
    String? word,
    String? definition,
    String? context,
    String? imageUrl,
    DateTime? learnedAt,
    int? exposureCount,
    bool? isUnderstood,
    List<String>? relatedWords,
  }) {
    return VocabularyProgress(
      word: word ?? this.word,
      definition: definition ?? this.definition,
      context: context ?? this.context,
      imageUrl: imageUrl ?? this.imageUrl,
      learnedAt: learnedAt ?? this.learnedAt,
      exposureCount: exposureCount ?? this.exposureCount,
      isUnderstood: isUnderstood ?? this.isUnderstood,
      relatedWords: relatedWords ?? this.relatedWords,
    );
  }
}

/// Model for tracking reading skills development
@JsonSerializable()
class ReadingSkillsProgress {
  final int wordsPerMinute;
  final double comprehensionScore; // 0.0 to 1.0
  final int totalWordsRead;
  final int totalSentencesRead;
  final double averageReadingTime; // seconds per sentence
  final List<String> strugglingWords;
  final List<String> masteredWords;
  final DateTime lastAssessment;

  const ReadingSkillsProgress({
    this.wordsPerMinute = 0,
    this.comprehensionScore = 0.0,
    this.totalWordsRead = 0,
    this.totalSentencesRead = 0,
    this.averageReadingTime = 0.0,
    this.strugglingWords = const [],
    this.masteredWords = const [],
    required this.lastAssessment,
  });

  factory ReadingSkillsProgress.fromJson(Map<String, dynamic> json) =>
      _$ReadingSkillsProgressFromJson(json);

  Map<String, dynamic> toJson() => _$ReadingSkillsProgressToJson(this);

  ReadingSkillsProgress copyWith({
    int? wordsPerMinute,
    double? comprehensionScore,
    int? totalWordsRead,
    int? totalSentencesRead,
    double? averageReadingTime,
    List<String>? strugglingWords,
    List<String>? masteredWords,
    DateTime? lastAssessment,
  }) {
    return ReadingSkillsProgress(
      wordsPerMinute: wordsPerMinute ?? this.wordsPerMinute,
      comprehensionScore: comprehensionScore ?? this.comprehensionScore,
      totalWordsRead: totalWordsRead ?? this.totalWordsRead,
      totalSentencesRead: totalSentencesRead ?? this.totalSentencesRead,
      averageReadingTime: averageReadingTime ?? this.averageReadingTime,
      strugglingWords: strugglingWords ?? this.strugglingWords,
      masteredWords: masteredWords ?? this.masteredWords,
      lastAssessment: lastAssessment ?? this.lastAssessment,
    );
  }

  /// Calculate reading level based on words per minute and age
  String getReadingLevel(int childAge) {
    if (wordsPerMinute == 0) return 'Not assessed';
    
    // Age-appropriate reading speed benchmarks
    final expectedWPM = {
      5: 30,
      6: 60,
      7: 90,
      8: 120,
      9: 150,
      10: 180,
    };

    final expected = expectedWPM[childAge] ?? 150;
    final ratio = wordsPerMinute / expected;

    if (ratio >= 1.2) return 'Advanced';
    if (ratio >= 0.8) return 'Grade Level';
    if (ratio >= 0.6) return 'Developing';
    return 'Needs Support';
  }
}

/// Model for tracking engagement and interaction metrics
@JsonSerializable()
class EngagementMetrics {
  final int totalInteractions;
  final int choicesChanged; // How many times child changed their mind
  final int pauseCount;
  final int replayCount;
  final double attentionSpan; // Average time before interaction
  final List<String> favoriteScenes;
  final Map<String, int> emotionalResponses; // emotion -> count
  final double overallEngagement; // 0.0 to 1.0

  const EngagementMetrics({
    this.totalInteractions = 0,
    this.choicesChanged = 0,
    this.pauseCount = 0,
    this.replayCount = 0,
    this.attentionSpan = 0.0,
    this.favoriteScenes = const [],
    this.emotionalResponses = const {},
    this.overallEngagement = 0.0,
  });

  factory EngagementMetrics.fromJson(Map<String, dynamic> json) =>
      _$EngagementMetricsFromJson(json);

  Map<String, dynamic> toJson() => _$EngagementMetricsToJson(this);

  EngagementMetrics copyWith({
    int? totalInteractions,
    int? choicesChanged,
    int? pauseCount,
    int? replayCount,
    double? attentionSpan,
    List<String>? favoriteScenes,
    Map<String, int>? emotionalResponses,
    double? overallEngagement,
  }) {
    return EngagementMetrics(
      totalInteractions: totalInteractions ?? this.totalInteractions,
      choicesChanged: choicesChanged ?? this.choicesChanged,
      pauseCount: pauseCount ?? this.pauseCount,
      replayCount: replayCount ?? this.replayCount,
      attentionSpan: attentionSpan ?? this.attentionSpan,
      favoriteScenes: favoriteScenes ?? this.favoriteScenes,
      emotionalResponses: emotionalResponses ?? this.emotionalResponses,
      overallEngagement: overallEngagement ?? this.overallEngagement,
    );
  }
}
