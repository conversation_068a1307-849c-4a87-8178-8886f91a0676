import 'dart:async';
import 'package:choice_once_upon_a_time/models/tts_models.dart';

/// Abstract interface for TTS services used in narration
/// Provides a clean contract for text-to-speech functionality with enhanced features
abstract class INarrationTTSService {
  /// Stream of TTS events for real-time feedback
  Stream<TTSEvent> get eventStream;
  
  /// Stream of TTS state changes
  Stream<TTSState> get stateStream;
  
  /// Current TTS state
  TTSState get currentState;
  
  /// Whether the service is available and ready
  bool get isAvailable;
  
  /// Whether T<PERSON> is currently speaking
  bool get isSpeaking;
  
  /// Whether TTS is currently paused
  bool get isPaused;

  /// Initialize the TTS service
  /// Returns true if initialization was successful
  Future<bool> initialize();

  /// Speak the given text with optional parameters
  /// Returns true if speech started successfully
  Future<bool> speak(String text, {TTSParameters? parameters});

  /// Pause the current speech
  /// Returns true if pause was successful
  Future<bool> pause();

  /// Resume paused speech
  /// Returns true if resume was successful
  Future<bool> resume();

  /// Stop the current speech
  /// Returns true if stop was successful
  Future<bool> stop();

  /// Set TTS parameters (rate, pitch, volume, etc.)
  Future<void> setParameters(TTSParameters parameters);

  /// Get list of available voices
  Future<List<TTSVoice>> getAvailableVoices();

  /// Set the voice to use for speech
  Future<bool> setVoice(TTSVoice voice);

  /// Set the language for speech
  Future<bool> setLanguage(String languageCode);

  /// Speak text with emotion-based voice modulation
  /// Automatically adjusts voice parameters based on emotion cue
  Future<bool> speakWithEmotion(String text, String emotionCue);

  /// Check if a specific language is supported
  Future<bool> isLanguageSupported(String languageCode);

  /// Get the estimated duration for speaking the given text
  /// Returns duration in milliseconds, or null if estimation is not available
  Future<int?> estimateSpeechDuration(String text);

  /// Dispose of the service and clean up resources
  Future<void> dispose();
}

/// Enhanced TTS service interface with advanced narration features
abstract class IEnhancedNarrationTTSService extends INarrationTTSService {
  /// Stream of word boundary events for word-level highlighting
  Stream<TTSEvent> get wordBoundaryStream;
  
  /// Stream of sentence boundary events for sentence-level tracking
  Stream<TTSEvent> get sentenceBoundaryStream;

  /// Speak text with word-level boundary tracking
  /// Enables real-time word highlighting during speech
  Future<bool> speakWithWordTracking(String text, {TTSParameters? parameters});

  /// Speak text with sentence-level boundary tracking
  /// Enables sentence-by-sentence progress tracking
  Future<bool> speakWithSentenceTracking(String text, {TTSParameters? parameters});

  /// Speak text with both word and sentence boundary tracking
  /// Provides the most detailed tracking for advanced narration features
  Future<bool> speakWithFullTracking(String text, {TTSParameters? parameters});

  /// Set emotion-based voice parameters
  /// Maps emotion cues to specific voice characteristics
  Future<void> setEmotionParameters(String emotionCue);

  /// Get voice parameters for a specific emotion
  TTSParameters getEmotionParameters(String emotionCue);

  /// Preload text for faster speech initiation
  /// Useful for reducing latency when starting narration
  Future<bool> preloadText(String text);

  /// Clear preloaded text
  Future<void> clearPreloadedText();

  /// Set custom voice parameters for character-specific voices
  Future<void> setCharacterVoice(String characterId, TTSParameters parameters);

  /// Speak text using a specific character's voice
  Future<bool> speakAsCharacter(String text, String characterId);

  /// Enable or disable SSML (Speech Synthesis Markup Language) support
  Future<void> setSSMLEnabled(bool enabled);

  /// Speak SSML-formatted text for advanced voice control
  Future<bool> speakSSML(String ssmlText);
}

/// Emotion cue mapper for voice modulation
class EmotionVoiceMapper {
  /// Map of emotion cues to TTS parameters
  static const Map<String, TTSParameters> _emotionMap = {
    'neutral': TTSParameters(rate: 0.5, pitch: 1.0, volume: 1.0),
    'happy': TTSParameters(rate: 0.6, pitch: 1.2, volume: 1.0),
    'sad': TTSParameters(rate: 0.4, pitch: 0.8, volume: 0.9),
    'excited': TTSParameters(rate: 0.7, pitch: 1.3, volume: 1.0),
    'calm': TTSParameters(rate: 0.4, pitch: 0.9, volume: 0.8),
    'angry': TTSParameters(rate: 0.6, pitch: 1.1, volume: 1.0),
    'surprised': TTSParameters(rate: 0.8, pitch: 1.4, volume: 1.0),
    'mysterious': TTSParameters(rate: 0.3, pitch: 0.7, volume: 0.7),
    'cheerful': TTSParameters(rate: 0.6, pitch: 1.1, volume: 1.0),
    'gentle': TTSParameters(rate: 0.4, pitch: 0.9, volume: 0.8),
    'dramatic': TTSParameters(rate: 0.5, pitch: 1.0, volume: 1.0),
    'whisper': TTSParameters(rate: 0.3, pitch: 0.8, volume: 0.5),
  };

  /// Get TTS parameters for a specific emotion
  static TTSParameters getParametersForEmotion(String emotionCue) {
    return _emotionMap[emotionCue.toLowerCase()] ?? _emotionMap['neutral']!;
  }

  /// Get all available emotion cues
  static List<String> get availableEmotions => _emotionMap.keys.toList();

  /// Check if an emotion cue is supported
  static bool isEmotionSupported(String emotionCue) {
    return _emotionMap.containsKey(emotionCue.toLowerCase());
  }
}

/// TTS service factory for creating appropriate implementations
class NarrationTTSServiceFactory {
  /// Create a TTS service instance based on platform and requirements
  static INarrationTTSService createService({
    bool enableWordTracking = true,
    bool enableEmotionMapping = true,
    bool enableSSML = false,
    TTSServiceConfig? config,
  }) {
    // Import will be added when concrete implementations are created
    // For now, we'll return the enhanced service if advanced features are needed
    if (enableWordTracking || enableSSML) {
      return _createEnhancedService(config);
    } else {
      return _createBasicService(config);
    }
  }

  static IEnhancedNarrationTTSService _createEnhancedService(TTSServiceConfig? config) {
    // Will be implemented with the concrete enhanced service
    throw UnimplementedError('Enhanced TTS service not yet implemented');
  }

  static INarrationTTSService _createBasicService(TTSServiceConfig? config) {
    // Will be implemented with the concrete basic service
    throw UnimplementedError('Basic TTS service not yet implemented');
  }
}

/// Exception thrown by TTS services
class TTSException implements Exception {
  final String message;
  final String? code;
  final dynamic originalError;

  const TTSException(this.message, {this.code, this.originalError});

  @override
  String toString() {
    if (code != null) {
      return 'TTSException [$code]: $message';
    }
    return 'TTSException: $message';
  }
}

/// TTS service configuration
class TTSServiceConfig {
  /// Whether to enable word boundary tracking
  final bool enableWordTracking;
  
  /// Whether to enable sentence boundary tracking
  final bool enableSentenceTracking;
  
  /// Whether to enable emotion-based voice modulation
  final bool enableEmotionMapping;
  
  /// Whether to enable SSML support
  final bool enableSSML;
  
  /// Default language for TTS
  final String defaultLanguage;
  
  /// Default voice parameters
  final TTSParameters defaultParameters;
  
  /// Maximum retry attempts for TTS operations
  final int maxRetryAttempts;
  
  /// Timeout for TTS operations in milliseconds
  final int operationTimeoutMs;

  const TTSServiceConfig({
    this.enableWordTracking = true,
    this.enableSentenceTracking = true,
    this.enableEmotionMapping = true,
    this.enableSSML = false,
    this.defaultLanguage = 'en-US',
    this.defaultParameters = TTSParameters.defaultParams,
    this.maxRetryAttempts = 3,
    this.operationTimeoutMs = 30000,
  });
}
