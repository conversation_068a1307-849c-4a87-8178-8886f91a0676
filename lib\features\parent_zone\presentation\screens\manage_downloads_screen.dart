import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:choice_once_upon_a_time/features/parent_zone/presentation/widgets/delete_confirmation_popup.dart';

/// Manage Downloads / Storage screen (Screen 12.2)
class ManageDownloadsScreen extends ConsumerStatefulWidget {
  const ManageDownloadsScreen({super.key});

  @override
  ConsumerState<ManageDownloadsScreen> createState() => _ManageDownloadsScreenState();
}

class _ManageDownloadsScreenState extends ConsumerState<ManageDownloadsScreen> {
  // Mock downloaded stories data - in real implementation, this would come from OfflineStorageService
  final List<DownloadedStoryEntry> _allDownloadedStories = [
    DownloadedStoryEntry(
      storyId: 'pip_pantry_puzzle',
      title: 'Pip and the Pantry Puzzle',
      sizeInMB: 15.2,
      downloadDate: DateTime.now().subtract(const Duration(days: 3)),
    ),
    DownloadedStoryEntry(
      storyId: 'lila_lost_locket',
      title: '<PERSON> and the Lost Locket',
      sizeInMB: 18.7,
      downloadDate: DateTime.now().subtract(const Duration(days: 1)),
    ),
    DownloadedStoryEntry(
      storyId: 'finley_forest_friends',
      title: 'Finley and the Forest Friends',
      sizeInMB: 22.1,
      downloadDate: DateTime.now().subtract(const Duration(hours: 6)),
    ),
  ];

  // Filtering and sorting state
  String _searchQuery = '';
  SortOption _sortOption = SortOption.dateNewest;

  List<DownloadedStoryEntry> get _filteredStories {
    var filtered = _allDownloadedStories.where((story) {
      return story.title.toLowerCase().contains(_searchQuery.toLowerCase());
    }).toList();

    // Sort the filtered list
    switch (_sortOption) {
      case SortOption.dateNewest:
        filtered.sort((a, b) => b.downloadDate.compareTo(a.downloadDate));
        break;
      case SortOption.dateOldest:
        filtered.sort((a, b) => a.downloadDate.compareTo(b.downloadDate));
        break;
      case SortOption.sizeSmallest:
        filtered.sort((a, b) => a.sizeInMB.compareTo(b.sizeInMB));
        break;
      case SortOption.sizeLargest:
        filtered.sort((a, b) => b.sizeInMB.compareTo(a.sizeInMB));
        break;
      case SortOption.nameAZ:
        filtered.sort((a, b) => a.title.compareTo(b.title));
        break;
      case SortOption.nameZA:
        filtered.sort((a, b) => b.title.compareTo(a.title));
        break;
    }

    return filtered;
  }

  double get _totalSizeInMB => _allDownloadedStories.fold(0.0, (sum, story) => sum + story.sizeInMB);

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Manage Downloads'),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => context.go('/parent_zone'),
        ),
      ),
      body: Column(
        children: [
          // Storage summary
          Container(
            margin: const EdgeInsets.all(16),
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: theme.colorScheme.primary.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Column(
              children: [
                Row(
                  children: [
                    Icon(
                      Icons.storage,
                      color: theme.colorScheme.primary,
                      size: 28,
                    ),
                    const SizedBox(width: 12),
                    Text(
                      'Storage Summary',
                      style: theme.textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: theme.colorScheme.primary,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Downloaded Stories',
                          style: theme.textTheme.bodyMedium?.copyWith(
                            color: Colors.grey[700],
                          ),
                        ),
                        Text(
                          '${_allDownloadedStories.length} stories',
                          style: theme.textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.end,
                      children: [
                        Text(
                          'Total Size',
                          style: theme.textTheme.bodyMedium?.copyWith(
                            color: Colors.grey[700],
                          ),
                        ),
                        Text(
                          '${_totalSizeInMB.toStringAsFixed(1)} MB',
                          style: theme.textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ],
            ),
          ),

          // Search and filter controls
          if (_allDownloadedStories.isNotEmpty) ...[
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: Row(
                children: [
                  Expanded(
                    child: TextField(
                      decoration: InputDecoration(
                        hintText: 'Search stories...',
                        prefixIcon: const Icon(Icons.search),
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                        contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                      ),
                      onChanged: (value) {
                        setState(() {
                          _searchQuery = value;
                        });
                      },
                    ),
                  ),
                  const SizedBox(width: 12),
                  PopupMenuButton<SortOption>(
                    icon: const Icon(Icons.sort),
                    tooltip: 'Sort options',
                    onSelected: (SortOption option) {
                      setState(() {
                        _sortOption = option;
                      });
                    },
                    itemBuilder: (context) => [
                      const PopupMenuItem(
                        value: SortOption.dateNewest,
                        child: Text('Date (Newest)'),
                      ),
                      const PopupMenuItem(
                        value: SortOption.dateOldest,
                        child: Text('Date (Oldest)'),
                      ),
                      const PopupMenuItem(
                        value: SortOption.nameAZ,
                        child: Text('Name (A-Z)'),
                      ),
                      const PopupMenuItem(
                        value: SortOption.nameZA,
                        child: Text('Name (Z-A)'),
                      ),
                      const PopupMenuItem(
                        value: SortOption.sizeSmallest,
                        child: Text('Size (Smallest)'),
                      ),
                      const PopupMenuItem(
                        value: SortOption.sizeLargest,
                        child: Text('Size (Largest)'),
                      ),
                    ],
                  ),
                ],
              ),
            ),
            const SizedBox(height: 16),
          ],

          // Delete all button
          if (_allDownloadedStories.isNotEmpty)
            Padding(
              padding: EdgeInsets.symmetric(
                horizontal: MediaQuery.of(context).size.width * 0.04,
              ),
              child: SizedBox(
                width: double.infinity,
                child: OutlinedButton.icon(
                  onPressed: () => _showDeleteAllConfirmation(context),
                  icon: Icon(
                    Icons.delete_sweep,
                    color: Theme.of(context).colorScheme.error,
                  ),
                  label: Text(
                    'Delete All Stories',
                    style: TextStyle(
                      color: Theme.of(context).colorScheme.error,
                    ),
                  ),
                  style: OutlinedButton.styleFrom(
                    side: BorderSide(color: Theme.of(context).colorScheme.error),
                    padding: const EdgeInsets.symmetric(vertical: 12),
                  ),
                ),
              ),
            ),

          const SizedBox(height: 16),

          // Downloaded stories list
          Expanded(
            child: _filteredStories.isEmpty
                ? _buildEmptyState(theme)
                : ListView.builder(
                    padding: const EdgeInsets.symmetric(horizontal: 16),
                    itemCount: _filteredStories.length,
                    itemBuilder: (context, index) {
                      final story = _filteredStories[index];
                      final originalIndex = _allDownloadedStories.indexOf(story);
                      return _buildStoryCard(theme, story, originalIndex);
                    },
                  ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState(ThemeData theme) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.download_outlined,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            'No Downloaded Stories',
            style: theme.textTheme.titleLarge?.copyWith(
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Stories you download for offline reading will appear here.',
            style: theme.textTheme.bodyMedium?.copyWith(
              color: Colors.grey[500],
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildStoryCard(ThemeData theme, DownloadedStoryEntry story, int index) {
    final screenSize = MediaQuery.of(context).size;
    final isSmallScreen = screenSize.width < 600;

    return Container(
      margin: EdgeInsets.only(
        bottom: 12,
        left: screenSize.width * 0.04,
        right: screenSize.width * 0.04,
      ),
      padding: EdgeInsets.all(isSmallScreen ? 12 : 16),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: theme.colorScheme.outline.withValues(alpha: 0.2)),
        boxShadow: [
          BoxShadow(
            color: theme.colorScheme.shadow.withValues(alpha: 0.1),
            spreadRadius: 1,
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          // Story thumbnail placeholder
          Container(
            width: isSmallScreen ? 50 : 60,
            height: isSmallScreen ? 50 : 60,
            decoration: BoxDecoration(
              color: theme.colorScheme.surfaceContainerHighest,
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              Icons.auto_stories,
              color: theme.colorScheme.onSurfaceVariant,
              size: isSmallScreen ? 24 : 30,
            ),
          ),
          SizedBox(width: isSmallScreen ? 12 : 16),

          // Story details
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  story.title,
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    fontSize: isSmallScreen ? 14 : 16,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: 4),
                Text(
                  '${story.sizeInMB.toStringAsFixed(1)} MB',
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: theme.colorScheme.onSurfaceVariant,
                    fontSize: isSmallScreen ? 11 : 12,
                  ),
                ),
                Text(
                  'Downloaded ${_formatDate(story.downloadDate)}',
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: theme.colorScheme.onSurfaceVariant.withValues(alpha: 0.8),
                    fontSize: isSmallScreen ? 10 : 11,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ),
          ),

          // Delete button
          IconButton(
            onPressed: () => _showDeleteStoryConfirmation(context, story, index),
            icon: const Icon(Icons.delete_outline),
            color: theme.colorScheme.error,
            tooltip: 'Delete story',
            iconSize: isSmallScreen ? 20 : 24,
          ),
        ],
      ),
    );
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays > 0) {
      return '${difference.inDays} day${difference.inDays == 1 ? '' : 's'} ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours} hour${difference.inHours == 1 ? '' : 's'} ago';
    } else {
      return 'Recently';
    }
  }

  void _showDeleteStoryConfirmation(BuildContext context, DownloadedStoryEntry story, int index) {
    showDialog(
      context: context,
      builder: (context) => DeleteConfirmationPopup(
        title: 'Delete Story?',
        message: 'Are you sure you want to delete "${story.title}"? You can re-download it later from the story library.',
        onConfirm: () {
          setState(() {
            _allDownloadedStories.removeAt(index);
          });
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('${story.title} deleted'),
              duration: const Duration(seconds: 2),
            ),
          );
        },
      ),
    );
  }

  void _showDeleteAllConfirmation(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => DeleteConfirmationPopup(
        title: 'Delete All Stories?',
        message: 'Are you sure you want to delete all ${_allDownloadedStories.length} downloaded stories? You can re-download them later from the story library.',
        onConfirm: () {
          setState(() {
            _allDownloadedStories.clear();
          });
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('All stories deleted'),
              duration: Duration(seconds: 2),
            ),
          );
        },
      ),
    );
  }
}

/// Model for downloaded story entries
class DownloadedStoryEntry {
  final String storyId;
  final String title;
  final double sizeInMB;
  final DateTime downloadDate;

  DownloadedStoryEntry({
    required this.storyId,
    required this.title,
    required this.sizeInMB,
    required this.downloadDate,
  });
}

/// Enum for sorting options
enum SortOption {
  dateNewest,
  dateOldest,
  nameAZ,
  nameZA,
  sizeSmallest,
  sizeLargest,
}
