import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:choice_once_upon_a_time/models/narrator_profile_model.dart';
import 'package:choice_once_upon_a_time/models/enhanced_story_model.dart' show VoiceModel;
import 'package:choice_once_upon_a_time/features/parent_zone/presentation/providers/narrator_creation_provider.dart';
import 'package:choice_once_upon_a_time/features/parent_zone/presentation/widgets/voice_preview_widget.dart';
import 'package:choice_once_upon_a_time/features/parent_zone/presentation/widgets/narrator_form_widget.dart';
import 'package:choice_once_upon_a_time/shared_widgets/loading_indicator_widget.dart';
import 'package:choice_once_upon_a_time/core/utils/app_logger.dart';

/// Screen for creating and editing custom narrator characters
class NarratorCreationScreen extends ConsumerStatefulWidget {
  final String? narratorId; // null for new narrator, ID for editing

  const NarratorCreationScreen({
    super.key,
    this.narratorId,
  });

  @override
  ConsumerState<NarratorCreationScreen> createState() => _NarratorCreationScreenState();
}

class _NarratorCreationScreenState extends ConsumerState<NarratorCreationScreen> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _descriptionController = TextEditingController();
  
  bool _isLoading = false;
  bool _hasUnsavedChanges = false;
  
  // Voice settings
  double _pitch = 1.0;
  double _rate = 1.0;
  double _volume = 1.0;
  String? _selectedVoiceName;
  
  // Character settings
  NarratorCategory _selectedCategory = NarratorCategory.custom;
  NarratorGender _selectedGender = NarratorGender.neutral;
  NarratorAgeRange _selectedAgeRange = NarratorAgeRange.adult;
  List<NarratorPersonality> _selectedPersonalities = [];

  @override
  void initState() {
    super.initState();
    AppLogger.debug('[COMPONENT_LOAD] Full path: lib/features/parent_zone/presentation/screens/narrator_creation_screen.dart - NarratorCreationScreen');
    
    _nameController.addListener(_onFormChanged);
    _descriptionController.addListener(_onFormChanged);
    
    if (widget.narratorId != null) {
      _loadExistingNarrator();
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  void _onFormChanged() {
    if (!_hasUnsavedChanges) {
      setState(() {
        _hasUnsavedChanges = true;
      });
    }
  }

  Future<void> _loadExistingNarrator() async {
    if (widget.narratorId == null) return;
    
    setState(() => _isLoading = true);
    
    try {
      final narrator = await ref.read(narratorCreationProvider.notifier)
          .loadNarrator(widget.narratorId!);
      
      if (narrator != null && mounted) {
        _populateFormWithNarrator(narrator);
      }
    } catch (e) {
      AppLogger.error('[NARRATOR_CREATION] Failed to load narrator: ${widget.narratorId}', e);
      if (mounted) {
        _showErrorSnackBar('Failed to load narrator: $e');
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  void _populateFormWithNarrator(NarratorProfileModel narrator) {
    _nameController.text = narrator.name;
    _descriptionController.text = narrator.description;
    _pitch = narrator.voice.pitch;
    _rate = narrator.voice.rate;
    _volume = narrator.voice.volume;
    _selectedVoiceName = narrator.voice.name;
    _selectedCategory = narrator.category;
    _selectedGender = narrator.gender;
    _selectedAgeRange = narrator.ageRange;
    _selectedPersonalities = List.from(narrator.personalities);
    
    setState(() {
      _hasUnsavedChanges = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final screenSize = MediaQuery.of(context).size;
    final isLandscape = screenSize.width > screenSize.height;
    
    return PopScope(
      canPop: !_hasUnsavedChanges,
      onPopInvokedWithResult: (bool didPop, dynamic result) async {
        if (didPop) return;
        
        AppLogger.debug('[NARRATOR_CREATION] Back button pressed with unsaved changes');
        final shouldDiscard = await _showDiscardChangesDialog();
        if (shouldDiscard && mounted) {
          context.pop();
        }
      },
      child: Scaffold(
        appBar: AppBar(
          title: Text(
            widget.narratorId == null ? 'Create Narrator' : 'Edit Narrator',
            style: theme.textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          backgroundColor: theme.colorScheme.surface,
          foregroundColor: theme.colorScheme.onSurface,
          elevation: 0,
          leading: IconButton(
            icon: const Icon(Icons.arrow_back),
            onPressed: () => _handleBackButton(),
          ),
          actions: [
            if (_hasUnsavedChanges)
              TextButton(
                onPressed: _isLoading ? null : _saveNarrator,
                child: Text(
                  'Save',
                  style: TextStyle(
                    color: theme.colorScheme.primary,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
          ],
        ),
        body: _isLoading
            ? const Center(child: LoadingIndicatorWidget())
            : SafeArea(
                child: isLandscape
                    ? _buildLandscapeLayout(theme, screenSize)
                    : _buildPortraitLayout(theme, screenSize),
              ),
        floatingActionButton: _hasUnsavedChanges
            ? FloatingActionButton.extended(
                onPressed: _isLoading ? null : _saveNarrator,
                icon: const Icon(Icons.save),
                label: const Text('Save Narrator'),
                backgroundColor: theme.colorScheme.primary,
                foregroundColor: theme.colorScheme.onPrimary,
              )
            : null,
      ),
    );
  }

  Widget _buildPortraitLayout(ThemeData theme, Size screenSize) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Form(
        key: _formKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            _buildHeaderSection(theme),
            const SizedBox(height: 24),
            _buildBasicInfoSection(theme),
            const SizedBox(height: 24),
            _buildVoiceSettingsSection(theme),
            const SizedBox(height: 24),
            _buildCharacteristicsSection(theme),
            const SizedBox(height: 24),
            _buildPersonalitySection(theme),
            const SizedBox(height: 24),
            _buildPreviewSection(theme),
            const SizedBox(height: 100), // Space for FAB
          ],
        ),
      ),
    );
  }

  Widget _buildLandscapeLayout(ThemeData theme, Size screenSize) {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Form(
        key: _formKey,
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Left column - Form fields
            Expanded(
              flex: 3,
              child: SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    _buildHeaderSection(theme),
                    const SizedBox(height: 20),
                    _buildBasicInfoSection(theme),
                    const SizedBox(height: 20),
                    _buildCharacteristicsSection(theme),
                    const SizedBox(height: 20),
                    _buildPersonalitySection(theme),
                  ],
                ),
              ),
            ),
            const SizedBox(width: 24),
            // Right column - Voice settings and preview
            Expanded(
              flex: 2,
              child: SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    _buildVoiceSettingsSection(theme),
                    const SizedBox(height: 20),
                    _buildPreviewSection(theme),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildHeaderSection(ThemeData theme) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: theme.colorScheme.primary.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        children: [
          Icon(
            Icons.record_voice_over,
            size: 48,
            color: theme.colorScheme.primary,
          ),
          const SizedBox(height: 12),
          Text(
            widget.narratorId == null
                ? 'Create Your Custom Narrator'
                : 'Edit Your Narrator',
            style: theme.textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
              color: theme.colorScheme.primary,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 8),
          Text(
            'Design a unique storytelling voice that your child will love',
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildBasicInfoSection(ThemeData theme) {
    return NarratorFormWidget(
      nameController: _nameController,
      descriptionController: _descriptionController,
      onChanged: _onFormChanged,
    );
  }

  Widget _buildVoiceSettingsSection(ThemeData theme) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Voice Settings',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            _buildVoiceSlider(
              'Pitch',
              _pitch,
              0.5,
              2.0,
              (value) => setState(() {
                _pitch = value;
                _onFormChanged();
              }),
              theme,
            ),
            _buildVoiceSlider(
              'Speed',
              _rate,
              0.5,
              2.0,
              (value) => setState(() {
                _rate = value;
                _onFormChanged();
              }),
              theme,
            ),
            _buildVoiceSlider(
              'Volume',
              _volume,
              0.5,
              1.0,
              (value) => setState(() {
                _volume = value;
                _onFormChanged();
              }),
              theme,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildVoiceSlider(
    String label,
    double value,
    double min,
    double max,
    ValueChanged<double> onChanged,
    ThemeData theme,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              label,
              style: theme.textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.w500,
              ),
            ),
            Text(
              value.toStringAsFixed(1),
              style: theme.textTheme.bodySmall?.copyWith(
                color: theme.colorScheme.primary,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
        Slider(
          value: value,
          min: min,
          max: max,
          divisions: ((max - min) * 10).round(),
          onChanged: onChanged,
        ),
        const SizedBox(height: 8),
      ],
    );
  }

  /// Build characteristics section
  Widget _buildCharacteristicsSection(ThemeData theme) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Narrator Characteristics',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),

            // Category selection
            _buildDropdownField(
              'Category',
              _selectedCategory.displayName,
              NarratorCategory.values.map((category) => DropdownMenuItem(
                value: category,
                child: Text(category.displayName),
              )).toList(),
              (NarratorCategory? value) {
                if (value != null) {
                  setState(() {
                    _selectedCategory = value;
                    _onFormChanged();
                  });
                }
              },
              theme,
            ),

            const SizedBox(height: 16),

            // Gender selection
            _buildDropdownField(
              'Gender',
              _selectedGender.displayName,
              NarratorGender.values.map((gender) => DropdownMenuItem(
                value: gender,
                child: Text(gender.displayName),
              )).toList(),
              (NarratorGender? value) {
                if (value != null) {
                  setState(() {
                    _selectedGender = value;
                    _onFormChanged();
                  });
                }
              },
              theme,
            ),

            const SizedBox(height: 16),

            // Age range selection
            _buildDropdownField(
              'Age Range',
              _selectedAgeRange.displayName,
              NarratorAgeRange.values.map((age) => DropdownMenuItem(
                value: age,
                child: Text(age.displayName),
              )).toList(),
              (NarratorAgeRange? value) {
                if (value != null) {
                  setState(() {
                    _selectedAgeRange = value;
                    _onFormChanged();
                  });
                }
              },
              theme,
            ),
          ],
        ),
      ),
    );
  }

  /// Build personality section
  Widget _buildPersonalitySection(ThemeData theme) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Personality Traits',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Select traits that describe your narrator\'s personality',
              style: theme.textTheme.bodySmall?.copyWith(
                color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
              ),
            ),
            const SizedBox(height: 16),

            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: NarratorPersonality.values.map((personality) {
                final isSelected = _selectedPersonalities.contains(personality);
                return FilterChip(
                  label: Text(personality.displayName),
                  selected: isSelected,
                  onSelected: (selected) {
                    setState(() {
                      if (selected) {
                        _selectedPersonalities.add(personality);
                      } else {
                        _selectedPersonalities.remove(personality);
                      }
                      _onFormChanged();
                    });
                  },
                  selectedColor: theme.colorScheme.primary.withValues(alpha: 0.2),
                  checkmarkColor: theme.colorScheme.primary,
                );
              }).toList(),
            ),
          ],
        ),
      ),
    );
  }

  /// Build preview section
  Widget _buildPreviewSection(ThemeData theme) {
    return VoicePreviewWidget(
      pitch: _pitch,
      rate: _rate,
      volume: _volume,
      selectedVoiceName: _selectedVoiceName,
      onVoiceChanged: () {
        _onFormChanged();
      },
    );
  }

  /// Build dropdown field
  Widget _buildDropdownField<T>(
    String label,
    String currentValue,
    List<DropdownMenuItem<T>> items,
    ValueChanged<T?> onChanged,
    ThemeData theme,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: theme.textTheme.bodyMedium?.copyWith(
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 8),
        DropdownButtonFormField<T>(
          decoration: InputDecoration(
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
            ),
            contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          ),
          items: items,
          onChanged: onChanged,
          hint: Text('Select $label'),
        ),
      ],
    );
  }

  /// Handle back button
  Future<void> _handleBackButton() async {
    if (_hasUnsavedChanges) {
      final shouldDiscard = await _showDiscardChangesDialog();
      if (shouldDiscard && mounted) {
        context.pop();
      }
    } else {
      context.pop();
    }
  }

  /// Show discard changes dialog
  Future<bool> _showDiscardChangesDialog() async {
    return await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Discard Changes?'),
        content: const Text('You have unsaved changes. Are you sure you want to discard them?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text('Discard'),
          ),
        ],
      ),
    ) ?? false;
  }

  /// Save narrator
  Future<void> _saveNarrator() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() => _isLoading = true);

    try {
      final narrator = NarratorProfileModel(
        id: widget.narratorId ?? 'narrator_${DateTime.now().millisecondsSinceEpoch}',
        name: _nameController.text.trim(),
        description: _descriptionController.text.trim(),
        voice: VoiceModel(
          name: _selectedVoiceName ?? 'Default',
          pitch: _pitch,
          rate: _rate,
          volume: _volume,
        ),
        category: _selectedCategory,
        gender: _selectedGender,
        ageRange: _selectedAgeRange,
        personalities: _selectedPersonalities,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      await ref.read(narratorCreationProvider.notifier).saveNarrator(narrator);

      if (mounted) {
        _showSuccessSnackBar('Narrator saved successfully!');
        setState(() {
          _hasUnsavedChanges = false;
        });
        context.pop();
      }
    } catch (e) {
      AppLogger.error('[NARRATOR_CREATION] Failed to save narrator', e);
      if (mounted) {
        _showErrorSnackBar('Failed to save narrator: $e');
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  /// Show success snackbar
  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.green,
      ),
    );
  }

  /// Show error snackbar
  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
      ),
    );
  }
}
