import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:choice_once_upon_a_time/models/enhanced_story_model.dart';
import 'package:choice_once_upon_a_time/features/story_library/data/enhanced_story_repository.dart';
import 'package:choice_once_upon_a_time/core/services/story_narration_service_interface.dart';
import 'package:choice_once_upon_a_time/core/services/enhanced_story_narration_service.dart';
import 'package:choice_once_upon_a_time/core/services/story_settings_service.dart';
import 'package:choice_once_upon_a_time/core/services/story_rewards_service.dart';
import 'package:choice_once_upon_a_time/shared_widgets/loading_indicator.dart';
import 'package:choice_once_upon_a_time/features/story_player/presentation/widgets/welcome_screen_widget.dart';
import 'package:choice_once_upon_a_time/features/story_player/presentation/widgets/character_profiles_widget.dart';
import 'package:choice_once_upon_a_time/features/story_player/presentation/widgets/story_completion_widget.dart';
import 'package:choice_once_upon_a_time/features/story_player/presentation/widgets/story_playback_widget.dart';
import 'package:choice_once_upon_a_time/features/story_player/presentation/widgets/scene_transition_loading_widget.dart';
import 'package:choice_once_upon_a_time/features/story_player/presentation/widgets/choice_transition_loading_widget.dart';

import 'package:choice_once_upon_a_time/core/utils/app_logger.dart';
import 'package:choice_once_upon_a_time/features/parent_zone/data/user_profile_service.dart';
import 'package:choice_once_upon_a_time/core/services/child_progress_service.dart';
import 'package:choice_once_upon_a_time/models/child_progress_model.dart';
import 'package:choice_once_upon_a_time/core/services/story_timing_analyzer.dart';

/// Enhanced story player with welcome screen, character profiles, and advanced features
class EnhancedStoryPlayerScreen extends StatefulWidget {
  final String storyId;

  const EnhancedStoryPlayerScreen({
    super.key,
    required this.storyId,
  });

  @override
  State<EnhancedStoryPlayerScreen> createState() => _EnhancedStoryPlayerScreenState();
}

class _EnhancedStoryPlayerScreenState extends State<EnhancedStoryPlayerScreen>
    with TickerProviderStateMixin {
  // Services
  late final EnhancedStoryRepository _repository;
  late final IStoryNarrationService _narrationService;
  late final StorySettingsService _settingsService;
  late final StoryRewardsService _rewardsService;
  late final ChildProgressService _progressService;
  late final StoryTimingAnalyzer _timingAnalyzer;

  // Animation controllers
  late final AnimationController _fadeController;
  late final AnimationController _slideController;
  late final Animation<double> _fadeAnimation;
  late final Animation<Offset> _slideAnimation;

  // State
  EnhancedStoryModel? _story;
  EnhancedSceneModel? _currentScene;
  StoryPlayerPhase _currentPhase = StoryPlayerPhase.loading;
  String? _error;
  final List<String> _visitedScenes = [];
  final Map<String, dynamic> _storyProgress = {};
  bool _isStoryActive = false;

  @override
  void initState() {
    super.initState();
    _initializeServices();
    _initializeAnimations();
    _loadStory();
  }

  /// Initialize all services
  void _initializeServices() {
    _repository = EnhancedStoryRepository();
    // Use singleton instance to avoid TTS conflicts
    _narrationService = EnhancedStoryNarrationService();
    _settingsService = StorySettingsService.instance;
    _rewardsService = StoryRewardsService.instance;
    _progressService = ChildProgressService();
    _timingAnalyzer = StoryTimingAnalyzer();
  }

  /// Initialize animations
  void _initializeAnimations() {
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    
    _slideController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _fadeController,
      curve: Curves.easeInOut,
    ));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(1.0, 0.0),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _slideController,
      curve: Curves.easeOutCubic,
    ));
  }

  /// Load the story and initialize services
  Future<void> _loadStory() async {
    AppLogger.debug('[STORY_PLAYER ESPS] Function: _loadStory called with parameters: storyId=${widget.storyId}');

    try {
      setState(() {
        _currentPhase = StoryPlayerPhase.loading;
        _error = null;
      });

      // Initialize services
      await Future.wait([
        _settingsService.initialize(),
        _rewardsService.initialize(),
        _narrationService.initialize(),
      ]);

      // Load the story
      final story = await _repository.fetchEnhancedStoryById(widget.storyId);

      // Log the story JSON asset path
      final storyJsonPath = 'assets/stories/${widget.storyId}/story.json';
      AppLogger.debug('[STORY_PLAYER] Asset loaded: $storyJsonPath');

      setState(() {
        _story = story;
        _currentScene = story.initialScene;
        _currentPhase = StoryPlayerPhase.welcome;
      });

      // Start fade-in animation
      _fadeController.forward();

      AppLogger.debug('[STORY_PLAYER] Function: _loadStory completed successfully');
    } catch (e) {
      AppLogger.error('[STORY_PLAYER] Function: _loadStory failed', e);
      setState(() {
        _error = 'Failed to load story: $e';
        _currentPhase = StoryPlayerPhase.error;
      });
    }
  }

  /// Transition to character profiles phase
  Future<void> _showCharacterProfiles() async {
    await _transitionToPhase(StoryPlayerPhase.characterProfiles);
  }

  /// Start the main story
  Future<void> _startStory() async {
    // Set landscape orientation when starting story narration
    //await _setLandscapeOrientation();
    await _transitionToPhase(StoryPlayerPhase.story);
  }

  /// Navigate to a specific scene with proper transition control and fade effects
  Future<void> _navigateToScene(String sceneId) async {
    AppLogger.debug('[STORY_PLAYER] Function: _navigateToScene called with parameters: sceneId=$sceneId, currentSceneId=${_currentScene?.id}');

    final scene = _story?.getSceneById(sceneId);
    if (scene != null) {
      // Complete timing for previous scene if any
      if (_currentScene != null) {
        _timingAnalyzer.completeSceneTiming(_currentScene!.id);
      }

      // Start timing analysis for new scene
      _timingAnalyzer.startSceneTiming(sceneId);

      AppLogger.debug('[SCENE_DEBUG] Scene transition: ${_currentScene?.id} → $sceneId');

      // Start scene transition timing
      if (_currentScene != null) {
        _timingAnalyzer.startSceneTransition(_currentScene!.id, sceneId);
      }

      // Log the scene image asset path
      final imagePath = scene.getImagePath(widget.storyId);
      AppLogger.debug('[STORY_PLAYER] Asset loaded: $imagePath');

      // Note: Loading animation will be shown only when user triggers scene transition
      // after narration completion, not immediately when navigating to scene

      setState(() {
        _currentScene = scene;
        if (!_visitedScenes.contains(sceneId)) {
          _visitedScenes.add(sceneId);
        }
      });

      // Track scene visit progress
      await _trackSceneVisit(sceneId);

      // Trigger scene transition animation with slide effect
      await _slideController.forward();
      _slideController.reset();

      // Mark scene transition complete
      if (_currentScene != null) {
        _timingAnalyzer.markSceneTransitionComplete(_currentScene!.id, sceneId);
      }

      // Fade in new scene
      await _fadeController.forward();

      // Mark scene as loaded
      _timingAnalyzer.markSceneLoaded(sceneId);

      AppLogger.debug('[SCENE_DEBUG] Scene $sceneId loaded with fade transition - Image: $imagePath');
      AppLogger.debug('[STORY_PLAYER] Function: _navigateToScene completed');
    } else {
      AppLogger.error('[STORY_PLAYER] Function: _navigateToScene failed - scene not found: $sceneId');
    }
  }

  /// Show scene transition loading animation
  Future<void> _showSceneTransitionLoading(String? currentSceneId, String nextSceneId) async {
    AppLogger.debug('[SCENE_TRANSITION] Showing transition loading: $currentSceneId → $nextSceneId');

    // Get transition duration from settings
    final duration = Duration(milliseconds: _settingsService.sceneTransitionDuration);

    // Show loading overlay
    if (mounted) {
      await showDialog(
        context: context,
        barrierDismissible: false,
        barrierColor: Colors.transparent,
        builder: (context) => SceneTransitionLoadingWidget(
          currentSceneId: currentSceneId,
          nextSceneId: nextSceneId,
          duration: duration,
          loadingText: 'Loading next scene...',
        ),
      );
    }
  }

  /// Show choice transition loading animation
  Future<void> _showChoiceTransitionLoading(String selectedChoice, String nextSceneId) async {
    AppLogger.debug('[CHOICE_TRANSITION] Showing choice loading: $selectedChoice → $nextSceneId');

    // Get transition duration from settings
    final duration = Duration(milliseconds: _settingsService.choiceTransitionDuration);

    // Show loading overlay
    if (mounted) {
      await showDialog(
        context: context,
        barrierDismissible: false,
        barrierColor: Colors.transparent,
        builder: (context) => ChoiceTransitionLoadingWidget(
          selectedChoice: selectedChoice,
          nextSceneId: nextSceneId,
          duration: duration,
        ),
      );
    }
  }

  /// Handle choice selection
  Future<void> _onChoiceSelected(ChoiceOptionModel choice) async {
    // Track choice selection
    if (_story != null && _currentScene != null) {
      await _trackChoiceSelection(_currentScene!.id, choice.option);

      // Award choice reward
      await _rewardsService.awardChoiceReward(
        _story!.storyId,
        choice.option,
        choice.option,
        _story!.moral,
      );
    }

    // Navigate to next scene
    await _navigateToScene(choice.next);

    // Check if story is complete
    if (_currentScene?.isEnding == true) {
      await _completeStory();
    }
  }

  /// Complete the story
  Future<void> _completeStory() async {
    if (_story != null) {
      // Complete timing for final scene
      if (_currentScene != null) {
        _timingAnalyzer.completeSceneTiming(_currentScene!.id);
      }

      // Generate and log timing report
      _generateTimingReport();

      // Track story completion
      await _trackStoryCompletion(_currentScene?.outcome ?? 'completed');

      await _rewardsService.completeStory(
        _story!.storyId,
        _story!.title,
        _story!.moral,
      );
    }

    await _transitionToPhase(StoryPlayerPhase.completion);
  }

  /// Transition between phases with animation
  Future<void> _transitionToPhase(StoryPlayerPhase newPhase) async {
    AppLogger.debug('[STORY_PLAYER TTP] Function: _transitionToPhase called with parameters: newPhase=$newPhase');
    await _fadeController.reverse();
    
    setState(() {
      _currentPhase = newPhase;
    });
    
    await _fadeController.forward();
  }

  /// Restart the story
  Future<void> _restartStory() async {
    setState(() {
      _currentScene = _story?.initialScene;
      _visitedScenes.clear();
      _storyProgress.clear();
    });
    
    await _transitionToPhase(StoryPlayerPhase.welcome);
  }

  /// Set landscape orientation for story playback
  Future<void> _setLandscapeOrientation() async {
    if (!_isStoryActive) {
      _isStoryActive = true;
      AppLogger.debug('[SCENE_DEBUG landscape] Setting landscape orientation for story playback');
      await SystemChrome.setPreferredOrientations([
        DeviceOrientation.landscapeLeft,
        DeviceOrientation.landscapeRight,
      ]);
    }
  }

  /// Reset orientation to allow all orientations
  Future<void> _resetOrientation() async {
    if (_isStoryActive) {
      _isStoryActive = false;
      AppLogger.debug('[SCENE_DEBUG] Resetting orientation to allow all orientations');
      await SystemChrome.setPreferredOrientations([
        DeviceOrientation.portraitUp,
        DeviceOrientation.portraitDown,
        DeviceOrientation.landscapeLeft,
        DeviceOrientation.landscapeRight,
      ]);
    }
  }

  /// Track scene visit for progress monitoring
  Future<void> _trackSceneVisit(String sceneId) async {
    try {
      // Get current profile (if any)
      // Note: In a real implementation, you'd get this from a provider or service
      // For now, we'll skip tracking if no profile is selected

      if (_story != null) {
        // Estimate scene play time (this would be more accurate with actual timing)
        const estimatedPlayTime = 30; // seconds

        await _progressService.updateSceneVisit(
          'current_profile_id', // This should come from selected profile
          _story!.storyId,
          sceneId,
          estimatedPlayTime,
        );

        AppLogger.debug('[PROGRESS_TRACKING] Scene visit tracked: $sceneId');
      }
    } catch (e) {
      AppLogger.error('[PROGRESS_TRACKING] Failed to track scene visit', e);
    }
  }

  /// Track choice selection for progress monitoring
  Future<void> _trackChoiceSelection(String sceneId, String choiceId) async {
    try {
      if (_story != null) {
        await _progressService.recordChoice(
          'current_profile_id', // This should come from selected profile
          _story!.storyId,
          sceneId,
          choiceId,
        );

        AppLogger.debug('[PROGRESS_TRACKING] Choice tracked: $sceneId -> $choiceId');
      }
    } catch (e) {
      AppLogger.error('[PROGRESS_TRACKING] Failed to track choice selection', e);
    }
  }

  /// Track story completion for progress monitoring
  Future<void> _trackStoryCompletion(String outcome) async {
    try {
      if (_story != null) {
        await _progressService.markStoryCompleted(
          'current_profile_id', // This should come from selected profile
          _story!.storyId,
          outcome,
        );

        AppLogger.debug('[PROGRESS_TRACKING] Story completion tracked: ${_story!.storyId} -> $outcome');
      }
    } catch (e) {
      AppLogger.error('[PROGRESS_TRACKING] Failed to track story completion', e);
    }
  }

  /// Generate comprehensive timing report for the story
  void _generateTimingReport() {
    try {
      final report = _timingAnalyzer.generateTimingReport();

      AppLogger.info('[TIMING_REPORT] === COMPREHENSIVE STORY TIMING REPORT ===');
      AppLogger.info('[TIMING_REPORT] Story ID: ${widget.storyId}');
      AppLogger.info('[TIMING_REPORT] Total Scenes: ${report.totalScenes}');
      AppLogger.info('[TIMING_REPORT] Total Story Time: ${report.totalStoryTime}ms (${(report.totalStoryTime / 1000).toStringAsFixed(1)}s)');
      AppLogger.info('[TIMING_REPORT] Average Scene Time: ${report.averageSceneTime}ms');
      AppLogger.info('[TIMING_REPORT] Average Load Time: ${report.averageLoadTime}ms');
      AppLogger.info('[TIMING_REPORT] Average Narration Time: ${report.averageNarrationTime}ms');
      AppLogger.info('[TIMING_REPORT] Child-Friendly Compliance: ${(report.childFriendlyCompliance * 100).toStringAsFixed(1)}%');

      if (report.slowestScene != null) {
        AppLogger.info('[TIMING_REPORT] Slowest Scene: ${report.slowestScene!.sceneId} (${report.slowestScene!.totalTime}ms)');
      }

      if (report.fastestScene != null) {
        AppLogger.info('[TIMING_REPORT] Fastest Scene: ${report.fastestScene!.sceneId} (${report.fastestScene!.totalTime}ms)');
      }

      // Log individual scene timings
      AppLogger.info('[TIMING_REPORT] === INDIVIDUAL SCENE TIMINGS ===');
      for (final sceneData in report.sceneTimings) {
        AppLogger.info('[TIMING_REPORT] Scene ${sceneData.sceneId}:');
        AppLogger.info('[TIMING_REPORT]   Total: ${sceneData.totalTime}ms');
        AppLogger.info('[TIMING_REPORT]   Load: ${sceneData.loadTime}ms');
        AppLogger.info('[TIMING_REPORT]   Narration: ${sceneData.narrationTime}ms');
        AppLogger.info('[TIMING_REPORT]   Pauses: ${sceneData.pauseTime}ms');
        AppLogger.info('[TIMING_REPORT]   Transition: ${sceneData.transitionTime}ms');
      }

      // Export timing data for further analysis
      final timingData = _timingAnalyzer.exportTimingData();
      AppLogger.debug('[TIMING_EXPORT] Timing data exported: ${timingData.toString()}');

    } catch (e, stackTrace) {
      AppLogger.error('[TIMING_REPORT] Error generating timing report', e, stackTrace);
    }
  }

  @override
  void dispose() {
    // Reset orientation when leaving story player
    _resetOrientation();
    _fadeController.dispose();
    _slideController.dispose();
    _narrationService.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // Remove AppBar for immersive cinema-like experience
    return Scaffold(
      body: _buildBody(),
    );
  }

  /// Build the main body
  Widget _buildBody() {
    if (_currentPhase == StoryPlayerPhase.loading) {
      return const Center(child: LoadingIndicator());
    }

    if (_currentPhase == StoryPlayerPhase.error) {
      return _buildErrorWidget();
    }

    return FadeTransition(
      opacity: _fadeAnimation,
      child: _buildPhaseContent(),
    );
  }

  /// Build error widget
  Widget _buildErrorWidget() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(Icons.error_outline, size: 64, color: Colors.red),
          const SizedBox(height: 16),
          Text(
            _error ?? 'An error occurred',
            style: const TextStyle(color: Colors.red),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: _loadStory,
            child: const Text('Retry'),
          ),
        ],
      ),
    );
  }

  /// Build content for current phase
  Widget _buildPhaseContent() {
    switch (_currentPhase) {
      case StoryPlayerPhase.welcome:
        AppLogger.debug('[STORY_PLAYER parent] Building welcome screen');
        return WelcomeScreenWidget(
          story: _story!,
          onContinue: _showCharacterProfiles,
          narrationService: _narrationService,
        );
      
      case StoryPlayerPhase.characterProfiles:
        AppLogger.debug('[STORY_PLAYER parent] Building character profiles screen');
        return CharacterProfilesWidget(
          story: _story!,
          onContinue: _startStory,
          narrationService: _narrationService,
        );
      
      case StoryPlayerPhase.story:
        AppLogger.debug('[STORY_PLAYER parent] Building story playback screen');
        return SlideTransition(
          position: _slideAnimation,
          child: StoryPlaybackWidget(
            story: _story!,
            scene: _currentScene!,
            onChoiceSelected: _onChoiceSelected,
            onSceneComplete: () async {
              AppLogger.debug('[STORY_PLAYER] Manual scene completion triggered by user');
              if (_currentScene?.next != null) {
                await _navigateToScene(_currentScene!.next!);
              } else {
                await _completeStory();
              }
            },
            narrationService: _narrationService,
            settingsService: _settingsService,
          ),
        );
      
      case StoryPlayerPhase.completion:
        AppLogger.debug('[STORY_PLAYER parent] Building completion screen');
        return StoryCompletionWidget(
          story: _story!,
          visitedScenes: _visitedScenes,
          onRestart: _restartStory,
          onExit: () => Navigator.of(context).pop(),
          rewardsService: _rewardsService,
        );
      
      default:
        return const SizedBox.shrink();
    }
  }
}

/// Enum for different phases of story playback
enum StoryPlayerPhase {
  loading,
  error,
  welcome,
  characterProfiles,
  story,
  completion,
}