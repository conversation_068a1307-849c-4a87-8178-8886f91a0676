import 'package:flutter/material.dart';
import 'package:choice_once_upon_a_time/core/utils/app_logger.dart';

/// Form widget for narrator basic information
class NarratorFormWidget extends StatelessWidget {
  final TextEditingController nameController;
  final TextEditingController descriptionController;
  final VoidCallback? onChanged;

  const NarratorFormWidget({
    super.key,
    required this.nameController,
    required this.descriptionController,
    this.onChanged,
  });

  @override
  Widget build(BuildContext context) {
    AppLogger.debug('[COMPONENT_LOAD] Full path: lib/features/parent_zone/presentation/widgets/narrator_form_widget.dart - NarratorFormWidget');
    
    final theme = Theme.of(context);

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Basic Information',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),

            // Narrator name field
            TextFormField(
              controller: nameController,
              decoration: InputDecoration(
                labelText: 'Narrator Name',
                hintText: 'e.g., Magical Morgan, Wise William',
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                prefixIcon: const Icon(Icons.person),
              ),
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'Please enter a narrator name';
                }
                if (value.trim().length < 2) {
                  return 'Name must be at least 2 characters';
                }
                if (value.trim().length > 50) {
                  return 'Name must be less than 50 characters';
                }
                return null;
              },
              onChanged: (value) => onChanged?.call(),
              textCapitalization: TextCapitalization.words,
              maxLength: 50,
            ),

            const SizedBox(height: 16),

            // Narrator description field
            TextFormField(
              controller: descriptionController,
              decoration: InputDecoration(
                labelText: 'Description',
                hintText: 'Describe your narrator\'s personality and style...',
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                prefixIcon: const Icon(Icons.description),
                alignLabelWithHint: true,
              ),
              maxLines: 4,
              maxLength: 200,
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'Please enter a description';
                }
                if (value.trim().length < 10) {
                  return 'Description must be at least 10 characters';
                }
                return null;
              },
              onChanged: (value) => onChanged?.call(),
              textCapitalization: TextCapitalization.sentences,
            ),

            const SizedBox(height: 8),

            // Helper text
            Text(
              'Tip: Describe how this narrator should sound and what makes them special for your child.',
              style: theme.textTheme.bodySmall?.copyWith(
                color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
                fontStyle: FontStyle.italic,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
