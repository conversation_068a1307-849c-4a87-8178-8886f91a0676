// Mocks generated by Mockito 5.4.4 from annotations
// in choice_once_upon_a_time/test/features/parent_zone/subscription_service_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i4;

import 'package:in_app_purchase/in_app_purchase.dart' as _i3;
import 'package:in_app_purchase_platform_interface/in_app_purchase_platform_interface.dart'
    as _i2;
import 'package:mockito/mockito.dart' as _i1;
import 'package:mockito/src/dummies.dart' as _i5;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeProductDetailsResponse_0 extends _i1.SmartFake
    implements _i2.ProductDetailsResponse {
  _FakeProductDetailsResponse_0(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

/// A class which mocks [InAppPurchase].
///
/// See the documentation for Mockito's code generation for more information.
class MockInAppPurchase extends _i1.Mock implements _i3.InAppPurchase {
  MockInAppPurchase() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i4.Stream<List<_i2.PurchaseDetails>> get purchaseStream =>
      (super.noSuchMethod(
        Invocation.getter(#purchaseStream),
        returnValue: _i4.Stream<List<_i2.PurchaseDetails>>.empty(),
      ) as _i4.Stream<List<_i2.PurchaseDetails>>);

  @override
  T getPlatformAddition<T extends _i2.InAppPurchasePlatformAddition?>() =>
      (super.noSuchMethod(
        Invocation.method(
          #getPlatformAddition,
          [],
        ),
        returnValue: _i5.dummyValue<T>(
          this,
          Invocation.method(
            #getPlatformAddition,
            [],
          ),
        ),
      ) as T);

  @override
  _i4.Future<bool> isAvailable() => (super.noSuchMethod(
        Invocation.method(
          #isAvailable,
          [],
        ),
        returnValue: _i4.Future<bool>.value(false),
      ) as _i4.Future<bool>);

  @override
  _i4.Future<_i2.ProductDetailsResponse> queryProductDetails(
          Set<String>? identifiers) =>
      (super.noSuchMethod(
        Invocation.method(
          #queryProductDetails,
          [identifiers],
        ),
        returnValue: _i4.Future<_i2.ProductDetailsResponse>.value(
            _FakeProductDetailsResponse_0(
          this,
          Invocation.method(
            #queryProductDetails,
            [identifiers],
          ),
        )),
      ) as _i4.Future<_i2.ProductDetailsResponse>);

  @override
  _i4.Future<bool> buyNonConsumable(
          {required _i2.PurchaseParam? purchaseParam}) =>
      (super.noSuchMethod(
        Invocation.method(
          #buyNonConsumable,
          [],
          {#purchaseParam: purchaseParam},
        ),
        returnValue: _i4.Future<bool>.value(false),
      ) as _i4.Future<bool>);

  @override
  _i4.Future<bool> buyConsumable({
    required _i2.PurchaseParam? purchaseParam,
    bool? autoConsume = true,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #buyConsumable,
          [],
          {
            #purchaseParam: purchaseParam,
            #autoConsume: autoConsume,
          },
        ),
        returnValue: _i4.Future<bool>.value(false),
      ) as _i4.Future<bool>);

  @override
  _i4.Future<void> completePurchase(_i2.PurchaseDetails? purchase) =>
      (super.noSuchMethod(
        Invocation.method(
          #completePurchase,
          [purchase],
        ),
        returnValue: _i4.Future<void>.value(),
        returnValueForMissingStub: _i4.Future<void>.value(),
      ) as _i4.Future<void>);

  @override
  _i4.Future<void> restorePurchases({String? applicationUserName}) =>
      (super.noSuchMethod(
        Invocation.method(
          #restorePurchases,
          [],
          {#applicationUserName: applicationUserName},
        ),
        returnValue: _i4.Future<void>.value(),
        returnValueForMissingStub: _i4.Future<void>.value(),
      ) as _i4.Future<void>);

  @override
  _i4.Future<String> countryCode() => (super.noSuchMethod(
        Invocation.method(
          #countryCode,
          [],
        ),
        returnValue: _i4.Future<String>.value(_i5.dummyValue<String>(
          this,
          Invocation.method(
            #countryCode,
            [],
          ),
        )),
      ) as _i4.Future<String>);
}

/// A class which mocks [ProductDetails].
///
/// See the documentation for Mockito's code generation for more information.
class MockProductDetails extends _i1.Mock implements _i2.ProductDetails {
  MockProductDetails() {
    _i1.throwOnMissingStub(this);
  }

  @override
  String get id => (super.noSuchMethod(
        Invocation.getter(#id),
        returnValue: _i5.dummyValue<String>(
          this,
          Invocation.getter(#id),
        ),
      ) as String);

  @override
  String get title => (super.noSuchMethod(
        Invocation.getter(#title),
        returnValue: _i5.dummyValue<String>(
          this,
          Invocation.getter(#title),
        ),
      ) as String);

  @override
  String get description => (super.noSuchMethod(
        Invocation.getter(#description),
        returnValue: _i5.dummyValue<String>(
          this,
          Invocation.getter(#description),
        ),
      ) as String);

  @override
  String get price => (super.noSuchMethod(
        Invocation.getter(#price),
        returnValue: _i5.dummyValue<String>(
          this,
          Invocation.getter(#price),
        ),
      ) as String);

  @override
  double get rawPrice => (super.noSuchMethod(
        Invocation.getter(#rawPrice),
        returnValue: 0.0,
      ) as double);

  @override
  String get currencyCode => (super.noSuchMethod(
        Invocation.getter(#currencyCode),
        returnValue: _i5.dummyValue<String>(
          this,
          Invocation.getter(#currencyCode),
        ),
      ) as String);

  @override
  String get currencySymbol => (super.noSuchMethod(
        Invocation.getter(#currencySymbol),
        returnValue: _i5.dummyValue<String>(
          this,
          Invocation.getter(#currencySymbol),
        ),
      ) as String);
}
