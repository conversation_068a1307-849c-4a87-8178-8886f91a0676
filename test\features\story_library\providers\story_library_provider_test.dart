import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:choice_once_upon_a_time/features/story_library/presentation/providers/story_library_provider.dart';
import 'package:choice_once_upon_a_time/features/story_library/data/story_repository.dart';
import 'package:choice_once_upon_a_time/models/story_metadata_model.dart';
import 'package:choice_once_upon_a_time/app/providers/service_providers.dart';

// Generate mocks
@GenerateMocks([StoryRepository])
import 'story_library_provider_test.mocks.dart';

void main() {
  group('StoryLibraryProvider Tests', () {
    late MockStoryRepository mockStoryRepository;
    late ProviderContainer container;

    setUp(() {
      mockStoryRepository = MockStoryRepository();
      container = ProviderContainer(
        overrides: [
          storyRepositoryProvider.overrideWithValue(mockStoryRepository),
        ],
      );
    });

    tearDown(() {
      container.dispose();
    });

    group('loadStories', () {
      test('should load stories successfully', () async {
        // Arrange
        final testStories = [
          const StoryMetadataModel(
            id: 'test_story_1',
            title: {'en-US': 'Test Story 1'},
            coverImageUrl: 'test_url_1',
            loglineShort: {'en-US': 'Test logline 1'},
            targetMoralValue: 'kindness',
            version: '1.0',
            supportedLanguages: ['en-US'],
            defaultLanguage: 'en-US',
            initialSceneId: 'scene_1',
            targetAgeSubSegment: '6-8',
          ),
          const StoryMetadataModel(
            id: 'test_story_2',
            title: {'en-US': 'Test Story 2'},
            coverImageUrl: 'test_url_2',
            loglineShort: {'en-US': 'Test logline 2'},
            targetMoralValue: 'courage',
            version: '1.0',
            supportedLanguages: ['en-US'],
            defaultLanguage: 'en-US',
            initialSceneId: 'scene_2',
            targetAgeSubSegment: '6-8',
          ),
        ];

        when(mockStoryRepository.fetchStoryMetadataList())
            .thenAnswer((_) async => testStories);

        // Act
        final provider = container.read(storyLibraryProvider.notifier);
        await provider.loadStories();

        // Assert
        final state = container.read(storyLibraryProvider);
        expect(state.stories, equals(testStories));
        expect(state.isLoading, isFalse);
        expect(state.error, isNull);
        verify(mockStoryRepository.fetchStoryMetadataList()).called(1);
      });

      test('should handle loading error', () async {
        // Arrange
        const errorMessage = 'Failed to load stories';
        when(mockStoryRepository.fetchStoryMetadataList())
            .thenThrow(Exception(errorMessage));

        // Act
        final provider = container.read(storyLibraryProvider.notifier);
        await provider.loadStories();

        // Assert
        final state = container.read(storyLibraryProvider);
        expect(state.stories, isEmpty);
        expect(state.isLoading, isFalse);
        expect(state.error, contains(errorMessage));
      });
    });

    group('loadStoriesWithProgress', () {
      test('should load stories with progress tracking', () async {
        // Arrange
        final testStories = [
          const StoryMetadataModel(
            id: 'simple_test_story',
            title: {'en-US': 'Simple Test Story'},
            coverImageUrl: 'test_url',
            loglineShort: {'en-US': 'Test logline'},
            targetMoralValue: 'kindness',
            version: '1.0',
            supportedLanguages: ['en-US'],
            defaultLanguage: 'en-US',
            initialSceneId: 'scene_1',
            targetAgeSubSegment: '6-8',
          ),
          const StoryMetadataModel(
            id: 'other_story',
            title: {'en-US': 'Other Story'},
            coverImageUrl: 'test_url_2',
            loglineShort: {'en-US': 'Test logline 2'},
            targetMoralValue: 'courage',
            version: '1.0',
            supportedLanguages: ['en-US'],
            defaultLanguage: 'en-US',
            initialSceneId: 'scene_2',
            targetAgeSubSegment: '6-8',
          ),
        ];

        when(mockStoryRepository.fetchStoryMetadataList())
            .thenAnswer((_) async => testStories);

        // Act
        final provider = container.read(storyLibraryProvider.notifier);
        await provider.loadStoriesWithProgress();

        // Assert
        final state = container.read(storyLibraryProvider);
        expect(state.stories.length, equals(2));
        expect(state.isLoading, isFalse);
        expect(state.error, isNull);
        
        // Check that simple_test_story has progress
        final simpleTestStory = state.stories.firstWhere((s) => s.id == 'simple_test_story');
        expect(simpleTestStory.hasProgress, isTrue);
        
        // Check that other_story doesn't have progress
        final otherStory = state.stories.firstWhere((s) => s.id == 'other_story');
        expect(otherStory.hasProgress, isFalse);
      });
    });

    group('setSearchQuery', () {
      test('should update search query', () {
        // Act
        final provider = container.read(storyLibraryProvider.notifier);
        provider.search('test query');

        // Assert
        final state = container.read(storyLibraryProvider);
        expect(state.searchQuery, equals('test query'));
      });
    });

    group('refresh', () {
      test('should force refresh stories', () async {
        // Arrange
        final testStories = [
          const StoryMetadataModel(
            id: 'refreshed_story',
            title: {'en-US': 'Refreshed Story'},
            coverImageUrl: 'test_url',
            loglineShort: {'en-US': 'Test logline'},
            targetMoralValue: 'kindness',
            version: '1.0',
            supportedLanguages: ['en-US'],
            defaultLanguage: 'en-US',
            initialSceneId: 'scene_1',
            targetAgeSubSegment: '6-8',
          ),
        ];

        when(mockStoryRepository.fetchStoryMetadataList())
            .thenAnswer((_) async => testStories);

        // Act
        final provider = container.read(storyLibraryProvider.notifier);
        await provider.refresh();

        // Assert
        final state = container.read(storyLibraryProvider);
        expect(state.stories, equals(testStories));
        expect(state.isLoading, isFalse);
        expect(state.error, isNull);
        verify(mockStoryRepository.fetchStoryMetadataList()).called(1);
      });
    });
  });
}
