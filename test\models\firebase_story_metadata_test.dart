import 'package:flutter_test/flutter_test.dart';
import 'package:choice_once_upon_a_time/models/firebase_story_metadata.dart';

void main() {
  group('FirebaseStoryMetadata Tests', () {
    late FirebaseStoryMetadata testMetadata;
    late DateTime testDate;

    setUp(() {
      testDate = DateTime(2024, 1, 1);
      testMetadata = FirebaseStoryMetadata(
        storyId: 'test_story',
        title: 'Test Story',
        description: 'A test story description',
        coverImageUrl: 'https://example.com/cover.jpg',
        categories: ['adventure', 'friendship'],
        ageRangeMin: 5,
        ageRangeMax: 8,
        estimatedDuration: 15.5,
        publishedAt: testDate,
        updatedAt: testDate,
        version: '1.0.0',
        downloadSizeMB: 25,
        isPremium: false,
        languages: ['en-US', 'es-ES'],
        assets: {
          'images': ['scene1.jpg', 'scene2.jpg'],
          'audio': ['narration.mp3'],
        },
        isDownloadable: true,
        downloadUrl: 'https://example.com/story.zip',
        checksum: 'abc123def456',
      );
    });

    group('JSON Serialization', () {
      test('should serialize to JSON correctly', () {
        // Act
        final json = testMetadata.toJson();

        // Assert
        expect(json['storyId'], equals('test_story'));
        expect(json['title'], equals('Test Story'));
        expect(json['description'], equals('A test story description'));
        expect(json['coverImageUrl'], equals('https://example.com/cover.jpg'));
        expect(json['categories'], equals(['adventure', 'friendship']));
        expect(json['ageRangeMin'], equals(5));
        expect(json['ageRangeMax'], equals(8));
        expect(json['estimatedDuration'], equals(15.5));
        expect(json['publishedAt'], equals(testDate.toIso8601String()));
        expect(json['updatedAt'], equals(testDate.toIso8601String()));
        expect(json['version'], equals('1.0.0'));
        expect(json['downloadSizeMB'], equals(25));
        expect(json['isPremium'], equals(false));
        expect(json['languages'], equals(['en-US', 'es-ES']));
        expect(json['assets'], equals({
          'images': ['scene1.jpg', 'scene2.jpg'],
          'audio': ['narration.mp3'],
        }));
        expect(json['isDownloadable'], equals(true));
        expect(json['downloadUrl'], equals('https://example.com/story.zip'));
        expect(json['checksum'], equals('abc123def456'));
      });

      test('should deserialize from JSON correctly', () {
        // Arrange
        final json = {
          'storyId': 'test_story',
          'title': 'Test Story',
          'description': 'A test story description',
          'coverImageUrl': 'https://example.com/cover.jpg',
          'categories': ['adventure', 'friendship'],
          'ageRangeMin': 5,
          'ageRangeMax': 8,
          'estimatedDuration': 15.5,
          'publishedAt': testDate.toIso8601String(),
          'updatedAt': testDate.toIso8601String(),
          'version': '1.0.0',
          'downloadSizeMB': 25,
          'isPremium': false,
          'languages': ['en-US', 'es-ES'],
          'assets': {
            'images': ['scene1.jpg', 'scene2.jpg'],
            'audio': ['narration.mp3'],
          },
          'isDownloadable': true,
          'downloadUrl': 'https://example.com/story.zip',
          'checksum': 'abc123def456',
        };

        // Act
        final metadata = FirebaseStoryMetadata.fromJson(json);

        // Assert
        expect(metadata.storyId, equals('test_story'));
        expect(metadata.title, equals('Test Story'));
        expect(metadata.description, equals('A test story description'));
        expect(metadata.coverImageUrl, equals('https://example.com/cover.jpg'));
        expect(metadata.categories, equals(['adventure', 'friendship']));
        expect(metadata.ageRangeMin, equals(5));
        expect(metadata.ageRangeMax, equals(8));
        expect(metadata.estimatedDuration, equals(15.5));
        expect(metadata.publishedAt, equals(testDate));
        expect(metadata.updatedAt, equals(testDate));
        expect(metadata.version, equals('1.0.0'));
        expect(metadata.downloadSizeMB, equals(25));
        expect(metadata.isPremium, equals(false));
        expect(metadata.languages, equals(['en-US', 'es-ES']));
        expect(metadata.assets, equals({
          'images': ['scene1.jpg', 'scene2.jpg'],
          'audio': ['narration.mp3'],
        }));
        expect(metadata.isDownloadable, equals(true));
        expect(metadata.downloadUrl, equals('https://example.com/story.zip'));
        expect(metadata.checksum, equals('abc123def456'));
      });
    });

    group('Equality and HashCode', () {
      test('should be equal when storyId and version are the same', () {
        // Arrange
        final metadata1 = FirebaseStoryMetadata(
          storyId: 'story1',
          title: 'Title 1',
          description: 'Description 1',
          coverImageUrl: 'cover1.jpg',
          categories: ['cat1'],
          ageRangeMin: 5,
          ageRangeMax: 7,
          estimatedDuration: 10.0,
          publishedAt: testDate,
          updatedAt: testDate,
          version: '1.0.0',
          downloadSizeMB: 20,
          isPremium: false,
          languages: ['en-US'],
          assets: {},
          isDownloadable: true,
          downloadUrl: 'url1',
          checksum: 'checksum1',
        );

        final metadata2 = FirebaseStoryMetadata(
          storyId: 'story1',
          title: 'Title 2', // Different title
          description: 'Description 2', // Different description
          coverImageUrl: 'cover2.jpg', // Different cover
          categories: ['cat2'], // Different categories
          ageRangeMin: 6, // Different age range
          ageRangeMax: 8,
          estimatedDuration: 15.0, // Different duration
          publishedAt: testDate.add(const Duration(days: 1)), // Different date
          updatedAt: testDate.add(const Duration(days: 1)),
          version: '1.0.0', // Same version
          downloadSizeMB: 30, // Different size
          isPremium: true, // Different premium status
          languages: ['es-ES'], // Different languages
          assets: {'test': 'value'}, // Different assets
          isDownloadable: false, // Different downloadable status
          downloadUrl: 'url2', // Different URL
          checksum: 'checksum2', // Different checksum
        );

        // Act & Assert
        expect(metadata1, equals(metadata2));
        expect(metadata1.hashCode, equals(metadata2.hashCode));
      });

      test('should not be equal when storyId is different', () {
        // Arrange
        final metadata1 = testMetadata;
        final metadata2 = FirebaseStoryMetadata(
          storyId: 'different_story', // Different storyId
          title: testMetadata.title,
          description: testMetadata.description,
          coverImageUrl: testMetadata.coverImageUrl,
          categories: testMetadata.categories,
          ageRangeMin: testMetadata.ageRangeMin,
          ageRangeMax: testMetadata.ageRangeMax,
          estimatedDuration: testMetadata.estimatedDuration,
          publishedAt: testMetadata.publishedAt,
          updatedAt: testMetadata.updatedAt,
          version: testMetadata.version,
          downloadSizeMB: testMetadata.downloadSizeMB,
          isPremium: testMetadata.isPremium,
          languages: testMetadata.languages,
          assets: testMetadata.assets,
          isDownloadable: testMetadata.isDownloadable,
          downloadUrl: testMetadata.downloadUrl,
          checksum: testMetadata.checksum,
        );

        // Act & Assert
        expect(metadata1, isNot(equals(metadata2)));
        expect(metadata1.hashCode, isNot(equals(metadata2.hashCode)));
      });

      test('should not be equal when version is different', () {
        // Arrange
        final metadata1 = testMetadata;
        final metadata2 = FirebaseStoryMetadata(
          storyId: testMetadata.storyId,
          title: testMetadata.title,
          description: testMetadata.description,
          coverImageUrl: testMetadata.coverImageUrl,
          categories: testMetadata.categories,
          ageRangeMin: testMetadata.ageRangeMin,
          ageRangeMax: testMetadata.ageRangeMax,
          estimatedDuration: testMetadata.estimatedDuration,
          publishedAt: testMetadata.publishedAt,
          updatedAt: testMetadata.updatedAt,
          version: '2.0.0', // Different version
          downloadSizeMB: testMetadata.downloadSizeMB,
          isPremium: testMetadata.isPremium,
          languages: testMetadata.languages,
          assets: testMetadata.assets,
          isDownloadable: testMetadata.isDownloadable,
          downloadUrl: testMetadata.downloadUrl,
          checksum: testMetadata.checksum,
        );

        // Act & Assert
        expect(metadata1, isNot(equals(metadata2)));
        expect(metadata1.hashCode, isNot(equals(metadata2.hashCode)));
      });
    });

    group('toString', () {
      test('should return formatted string representation', () {
        // Act
        final stringRepresentation = testMetadata.toString();

        // Assert
        expect(stringRepresentation, contains('FirebaseStoryMetadata'));
        expect(stringRepresentation, contains('storyId: test_story'));
        expect(stringRepresentation, contains('title: Test Story'));
        expect(stringRepresentation, contains('version: 1.0.0'));
      });
    });
  });

  group('DownloadProgress Tests', () {
    late DownloadProgress testProgress;
    late DateTime testDate;

    setUp(() {
      testDate = DateTime(2024, 1, 1);
      testProgress = DownloadProgress(
        storyId: 'test_story',
        progress: 0.75,
        status: DownloadStatus.downloading,
        errorMessage: null,
        lastUpdated: testDate,
      );
    });

    group('JSON Serialization', () {
      test('should serialize to JSON correctly', () {
        // Act
        final json = testProgress.toJson();

        // Assert
        expect(json['storyId'], equals('test_story'));
        expect(json['progress'], equals(0.75));
        expect(json['status'], equals('downloading'));
        expect(json['errorMessage'], isNull);
        expect(json['lastUpdated'], equals(testDate.toIso8601String()));
      });

      test('should deserialize from JSON correctly', () {
        // Arrange
        final json = {
          'storyId': 'test_story',
          'progress': 0.75,
          'status': 'downloading',
          'errorMessage': null,
          'lastUpdated': testDate.toIso8601String(),
        };

        // Act
        final progress = DownloadProgress.fromJson(json);

        // Assert
        expect(progress.storyId, equals('test_story'));
        expect(progress.progress, equals(0.75));
        expect(progress.status, equals(DownloadStatus.downloading));
        expect(progress.errorMessage, isNull);
        expect(progress.lastUpdated, equals(testDate));
      });

      test('should handle error message in JSON', () {
        // Arrange
        final progressWithError = DownloadProgress(
          storyId: 'test_story',
          progress: 0.0,
          status: DownloadStatus.failed,
          errorMessage: 'Download failed',
          lastUpdated: testDate,
        );

        // Act
        final json = progressWithError.toJson();
        final deserializedProgress = DownloadProgress.fromJson(json);

        // Assert
        expect(deserializedProgress.errorMessage, equals('Download failed'));
        expect(deserializedProgress.status, equals(DownloadStatus.failed));
      });
    });

    group('Equality and HashCode', () {
      test('should be equal when storyId is the same', () {
        // Arrange
        final progress1 = DownloadProgress(
          storyId: 'story1',
          progress: 0.5,
          status: DownloadStatus.downloading,
          lastUpdated: testDate,
        );

        final progress2 = DownloadProgress(
          storyId: 'story1',
          progress: 0.8, // Different progress
          status: DownloadStatus.downloaded, // Different status
          lastUpdated: testDate.add(const Duration(hours: 1)), // Different time
        );

        // Act & Assert
        expect(progress1, equals(progress2));
        expect(progress1.hashCode, equals(progress2.hashCode));
      });

      test('should not be equal when storyId is different', () {
        // Arrange
        final progress1 = DownloadProgress(
          storyId: 'story1',
          progress: 0.5,
          status: DownloadStatus.downloading,
          lastUpdated: testDate,
        );

        final progress2 = DownloadProgress(
          storyId: 'story2', // Different storyId
          progress: 0.5,
          status: DownloadStatus.downloading,
          lastUpdated: testDate,
        );

        // Act & Assert
        expect(progress1, isNot(equals(progress2)));
        expect(progress1.hashCode, isNot(equals(progress2.hashCode)));
      });
    });
  });

  group('DownloadStatus Enum Tests', () {
    test('should have correct JSON values', () {
      expect(DownloadStatus.notDownloaded.toString(), contains('notDownloaded'));
      expect(DownloadStatus.downloading.toString(), contains('downloading'));
      expect(DownloadStatus.downloaded.toString(), contains('downloaded'));
      expect(DownloadStatus.failed.toString(), contains('failed'));
      expect(DownloadStatus.paused.toString(), contains('paused'));
      expect(DownloadStatus.cancelled.toString(), contains('cancelled'));
    });
  });
}
