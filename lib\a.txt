import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:choice_once_upon_a_time/app/app_widget.dart';
import 'package:choice_once_upon_a_time/core/services/accessibility_service.dart';
import 'package:choice_once_upon_a_time/core/utils/app_logger.dart';
import 'package:choice_once_upon_a_time/core/services/story_rewards_service.dart';
import 'firebase_options.dart';

Future<void> main() async {
  WidgetsFlutterBinding.ensureInitialized();

  AppLogger.debug('[COMPONENT_LOAD] Full path: lib/main.dart - main()');

  // Load environment variables
  try {
    await dotenv.load(fileName: '.env');
  } catch (e) {
    // .env file might not exist in development, that's okay
    AppLogger.debug('Note: .env file not found, using default configuration');
  }

  // Initialize Firebase
  try {
    // Initialize Firebase with platform-specific options
    await Firebase.initializeApp(
      options: DefaultFirebaseOptions.currentPlatform,
    );
    AppLogger.debug('Firebase initialized successfully');
  } catch (e) {
    // Firebase initialization failed - app will still work with mock data
    AppLogger.debug('Firebase initialization failed: $e');
    AppLogger.debug('App will continue with mock data');
  }

  // Initialize core services
  try {
    await AccessibilityService.instance.initialize();
    await StoryRewardsService.instance.initialize();
    AppLogger.debug('Core services initialized successfully');
  } catch (e) {
    AppLogger.debug('Core services initialization failed: $e');
    AppLogger.debug('App will continue with default settings');
  }

  runApp(const ProviderScope(child: AppWidget()));
}