import 'package:flutter_test/flutter_test.dart';
import 'package:flutter/services.dart';
import 'package:choice_once_upon_a_time/core/services/new_story_service.dart';
import 'package:choice_once_upon_a_time/models/story_metadata_model.dart';

void main() {
  group('NewStoryService', () {
    late NewStoryService storyService;

    setUp(() {
      storyService = NewStoryService();
    });

    tearDown(() {
      storyService.clearCache();
    });

    group('getAllStoryMetadata', () {
      test('should return empty list when no stories found', () async {
        // Mock empty asset manifest
        TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger
            .setMockMethodCallHandler(
          const MethodChannel('flutter/assets'),
          (MethodCall methodCall) async {
            if (methodCall.method == 'loadString') {
              if (methodCall.arguments == 'AssetManifest.json') {
                return '{}';
              }
            }
            throw PlatformException(code: 'NOT_FOUND');
          },
        );

        final stories = await storyService.getAllStoryMetadata();
        expect(stories, isEmpty);
      });

      test('should return story metadata when stories exist', () async {
        // Mock asset manifest with story folders
        TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger
            .setMockMethodCallHandler(
          const MethodChannel('flutter/assets'),
          (MethodCall methodCall) async {
            if (methodCall.method == 'loadString') {
              if (methodCall.arguments == 'AssetManifest.json') {
                return '''
                {
                  "assets/stories/story013/story.json": ["assets/stories/story013/story.json"],
                  "assets/stories/story013/images/story_cover.jpg": ["assets/stories/story013/images/story_cover.jpg"]
                }
                ''';
              } else if (methodCall.arguments == 'assets/stories/story013/story.json') {
                return '''
                {
                  "title": "Test Story",
                  "moral": "Test Moral",
                  "age_group": "3-5",
                  "cover_image": "story_cover.jpg",
                  "setup": {
                    "estimated_time": "5 minutes"
                  }
                }
                ''';
              }
            }
            throw PlatformException(code: 'NOT_FOUND');
          },
        );

        final stories = await storyService.getAllStoryMetadata();
        expect(stories, hasLength(1));
        expect(stories.first.id, equals('story013'));
        expect(stories.first.getLocalizedTitle('en-US'), equals('Test Story'));
        expect(stories.first.targetMoralValue, equals('Test Moral'));
        expect(stories.first.dataSource, equals('new_asset'));
      });
    });

    group('isStoryAvailableLocally', () {
      test('should return true when story exists', () async {
        TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger
            .setMockMethodCallHandler(
          const MethodChannel('flutter/assets'),
          (MethodCall methodCall) async {
            if (methodCall.method == 'loadString' &&
                methodCall.arguments == 'assets/stories/story013/story.json') {
              return '{"title": "Test"}';
            }
            throw PlatformException(code: 'NOT_FOUND');
          },
        );

        final isAvailable = await storyService.isStoryAvailableLocally('story013');
        expect(isAvailable, isTrue);
      });

      test('should return false when story does not exist', () async {
        TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger
            .setMockMethodCallHandler(
          const MethodChannel('flutter/assets'),
          (MethodCall methodCall) async {
            throw PlatformException(code: 'NOT_FOUND');
          },
        );

        final isAvailable = await storyService.isStoryAvailableLocally('nonexistent');
        expect(isAvailable, isFalse);
      });
    });

    group('getStoryStatus', () {
      test('should return "play" when story is available', () async {
        TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger
            .setMockMethodCallHandler(
          const MethodChannel('flutter/assets'),
          (MethodCall methodCall) async {
            if (methodCall.method == 'loadString') {
              return '{"title": "Test"}';
            }
            throw PlatformException(code: 'NOT_FOUND');
          },
        );

        final status = await storyService.getStoryStatus('story013');
        expect(status, equals('play'));
      });

      test('should return "download" when story is not available', () async {
        TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger
            .setMockMethodCallHandler(
          const MethodChannel('flutter/assets'),
          (MethodCall methodCall) async {
            throw PlatformException(code: 'NOT_FOUND');
          },
        );

        final status = await storyService.getStoryStatus('nonexistent');
        expect(status, equals('download'));
      });
    });

    group('clearCache', () {
      test('should clear all caches', () async {
        // Load some data first
        TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger
            .setMockMethodCallHandler(
          const MethodChannel('flutter/assets'),
          (MethodCall methodCall) async {
            return '{}';
          },
        );

        await storyService.getAllStoryMetadata();
        
        // Clear cache
        storyService.clearCache();
        
        // This should work without issues
        expect(() => storyService.clearCache(), returnsNormally);
      });
    });
  });
}
