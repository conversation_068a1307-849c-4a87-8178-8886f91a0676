import 'dart:async';
import 'package:flutter/material.dart';
import 'package:choice_once_upon_a_time/models/firebase_story_metadata.dart';
import 'package:choice_once_upon_a_time/core/utils/app_logger.dart';

/// Service for showing download notifications and progress
class DownloadNotificationService {
  static final DownloadNotificationService _instance = DownloadNotificationService._internal();
  factory DownloadNotificationService() => _instance;
  DownloadNotificationService._internal();

  /// Shows a snackbar for download start
  void showDownloadStarted(BuildContext context, String storyTitle) {
    if (!context.mounted) return;
    
    AppLogger.debug('[DOWNLOAD_NOTIFICATION] Showing download started for: $storyTitle');
    
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const SizedBox(
              width: 16,
              height: 16,
              child: CircularProgressIndicator(
                strokeWidth: 2,
                valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                'Downloading "$storyTitle"...',
                style: const TextStyle(color: Colors.white),
              ),
            ),
          ],
        ),
        backgroundColor: Colors.blue,
        duration: const Duration(seconds: 2),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      ),
    );
  }

  /// Shows a snackbar for download completion
  void showDownloadCompleted(BuildContext context, String storyTitle) {
    if (!context.mounted) return;
    
    AppLogger.debug('[DOWNLOAD_NOTIFICATION] Showing download completed for: $storyTitle');
    
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(
              Icons.check_circle,
              color: Colors.white,
              size: 20,
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                '"$storyTitle" downloaded successfully!',
                style: const TextStyle(color: Colors.white),
              ),
            ),
          ],
        ),
        backgroundColor: Colors.green,
        duration: const Duration(seconds: 3),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
        action: SnackBarAction(
          label: 'Play',
          textColor: Colors.white,
          onPressed: () {
            // This could trigger navigation to the story player
            AppLogger.debug('[DOWNLOAD_NOTIFICATION] Play button pressed for: $storyTitle');
          },
        ),
      ),
    );
  }

  /// Shows a snackbar for download failure
  void showDownloadFailed(BuildContext context, String storyTitle, {String? errorMessage}) {
    if (!context.mounted) return;
    
    AppLogger.debug('[DOWNLOAD_NOTIFICATION] Showing download failed for: $storyTitle');
    
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(
              Icons.error,
              color: Colors.white,
              size: 20,
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Failed to download "$storyTitle"',
                    style: const TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  if (errorMessage != null) ...[
                    const SizedBox(height: 4),
                    Text(
                      errorMessage,
                      style: const TextStyle(
                        color: Colors.white70,
                        fontSize: 12,
                      ),
                    ),
                  ],
                ],
              ),
            ),
          ],
        ),
        backgroundColor: Colors.red,
        duration: const Duration(seconds: 4),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
        action: SnackBarAction(
          label: 'Retry',
          textColor: Colors.white,
          onPressed: () {
            AppLogger.debug('[DOWNLOAD_NOTIFICATION] Retry button pressed for: $storyTitle');
          },
        ),
      ),
    );
  }

  /// Shows a progress dialog for download
  void showDownloadProgress(
    BuildContext context,
    String storyTitle,
    Stream<DownloadProgress> progressStream,
  ) {
    if (!context.mounted) return;
    
    AppLogger.debug('[DOWNLOAD_NOTIFICATION] Showing download progress dialog for: $storyTitle');
    
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => DownloadProgressDialog(
        storyTitle: storyTitle,
        progressStream: progressStream,
      ),
    );
  }
}

/// Dialog that shows download progress
class DownloadProgressDialog extends StatefulWidget {
  final String storyTitle;
  final Stream<DownloadProgress> progressStream;

  const DownloadProgressDialog({
    super.key,
    required this.storyTitle,
    required this.progressStream,
  });

  @override
  State<DownloadProgressDialog> createState() => _DownloadProgressDialogState();
}

class _DownloadProgressDialogState extends State<DownloadProgressDialog> {
  DownloadProgress? _currentProgress;
  late StreamSubscription<DownloadProgress> _subscription;

  @override
  void initState() {
    super.initState();
    _subscription = widget.progressStream.listen(
      (progress) {
        if (mounted) {
          setState(() {
            _currentProgress = progress;
          });

          // Auto-close dialog when download completes or fails
          if (progress.status == DownloadStatus.downloaded ||
              progress.status == DownloadStatus.failed ||
              progress.status == DownloadStatus.cancelled) {
            Future.delayed(const Duration(seconds: 1), () {
              if (mounted) {
                Navigator.of(context).pop();
              }
            });
          }
        }
      },
      onError: (error) {
        AppLogger.debug('[DOWNLOAD_PROGRESS_DIALOG] Stream error: $error');
        if (mounted) {
          Navigator.of(context).pop();
        }
      },
    );
  }

  @override
  void dispose() {
    _subscription.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final progress = _currentProgress;
    
    return AlertDialog(
      title: Text(
        'Downloading Story',
        style: theme.textTheme.titleLarge?.copyWith(
          fontWeight: FontWeight.bold,
        ),
      ),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            widget.storyTitle,
            style: theme.textTheme.bodyLarge?.copyWith(
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 16),
          if (progress != null) ...[
            LinearProgressIndicator(
              value: progress.progress,
              backgroundColor: theme.colorScheme.surfaceContainerHighest,
              valueColor: AlwaysStoppedAnimation<Color>(
                _getProgressColor(progress.status, theme),
              ),
            ),
            const SizedBox(height: 8),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  _getStatusText(progress.status),
                  style: theme.textTheme.bodySmall,
                ),
                Text(
                  '${(progress.progress * 100).toInt()}%',
                  style: theme.textTheme.bodySmall?.copyWith(
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
            if (progress.errorMessage != null) ...[
              const SizedBox(height: 8),
              Text(
                progress.errorMessage!,
                style: theme.textTheme.bodySmall?.copyWith(
                  color: theme.colorScheme.error,
                ),
              ),
            ],
          ] else ...[
            const LinearProgressIndicator(),
            const SizedBox(height: 8),
            Text(
              'Preparing download...',
              style: theme.textTheme.bodySmall,
            ),
          ],
        ],
      ),
      actions: [
        if (progress?.status == DownloadStatus.downloading) ...[
          TextButton(
            onPressed: () {
              // Cancel download
              Navigator.of(context).pop();
            },
            child: const Text('Cancel'),
          ),
        ] else if (progress?.status == DownloadStatus.downloaded) ...[
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
            },
            child: const Text('Done'),
          ),
        ] else if (progress?.status == DownloadStatus.failed) ...[
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
            },
            child: const Text('Close'),
          ),
        ],
      ],
    );
  }

  Color _getProgressColor(DownloadStatus status, ThemeData theme) {
    switch (status) {
      case DownloadStatus.downloading:
        return theme.colorScheme.primary;
      case DownloadStatus.downloaded:
        return Colors.green;
      case DownloadStatus.failed:
      case DownloadStatus.cancelled:
        return theme.colorScheme.error;
      default:
        return theme.colorScheme.primary;
    }
  }

  String _getStatusText(DownloadStatus status) {
    switch (status) {
      case DownloadStatus.downloading:
        return 'Downloading...';
      case DownloadStatus.downloaded:
        return 'Download complete!';
      case DownloadStatus.failed:
        return 'Download failed';
      case DownloadStatus.cancelled:
        return 'Download cancelled';
      default:
        return 'Preparing...';
    }
  }
}
