import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:choice_once_upon_a_time/core/storage/offline_storage_service_stub.dart';
import 'package:choice_once_upon_a_time/core/audio/tts_service_interface.dart';
import 'package:choice_once_upon_a_time/core/audio/enhanced_voice_guidance_manager.dart';
import 'package:choice_once_upon_a_time/core/audio/unified_tts_service.dart';
import 'package:choice_once_upon_a_time/core/audio/sound_effect_player_service.dart';
import 'package:choice_once_upon_a_time/core/audio/voice_guidance_manager.dart';
import 'package:choice_once_upon_a_time/core/localization/screen_narration_service.dart';
import 'package:choice_once_upon_a_time/core/permissions/storage_permission_service.dart';
import 'package:choice_once_upon_a_time/features/story_library/data/story_repository.dart';

// Exception for service initialization failures
class ServiceInitializationException implements Exception {
  final String message;
  ServiceInitializationException(this.message);
  @override
  String toString() => message;
}

/// Provider for the offline storage service
final offlineStorageServiceProvider = Provider<OfflineStorageService>((ref) {
  return OfflineStorageService();
});

/// Provider for the TTS service interface - Using unified service to prevent conflicts
final ttsServiceProvider = Provider<TTSServiceInterface>((ref) {
  return UnifiedTTSService.instance;
});

/// Provider for the unified TTS service directly
final unifiedTTSServiceProvider = Provider<UnifiedTTSService>((ref) {
  return UnifiedTTSService.instance;
});

/// Provider for the sound effect player service
final soundEffectPlayerServiceProvider = Provider<SoundEffectPlayerService>((ref) {
  return SoundEffectPlayerService();
});

/// Provider for the story repository
final storyRepositoryProvider = Provider<StoryRepository>((ref) {
  return StoryRepository();
});

/// Provider for the screen narration service
final screenNarrationServiceProvider = Provider<ScreenNarrationService>((ref) {
  return ScreenNarrationService();
});

/// Voice Guidance Manager Provider
final voiceGuidanceManagerProvider = Provider<VoiceGuidanceManager>((ref) {
  final ttsService = ref.watch(ttsServiceProvider);
  return VoiceGuidanceManager(ttsService);
});

/// Enhanced Voice Guidance Manager Provider
final enhancedVoiceGuidanceManagerProvider = Provider<EnhancedVoiceGuidanceManager>((ref) {
  final ttsService = ref.watch(ttsServiceProvider);
  return EnhancedVoiceGuidanceManager(ttsService);
});

/// Storage Permission Service Provider
final storagePermissionServiceProvider = Provider<StoragePermissionService>((ref) {
  return StoragePermissionService();
});

/// Provider to initialize all services
final servicesInitializationProvider = FutureProvider<void>((ref) async {
  try {
    // Initialize offline storage
    final offlineStorage = ref.read(offlineStorageServiceProvider);
    await offlineStorage.initDatabase();

    // Initialize TTS service
    final ttsService = ref.read(ttsServiceProvider);
    final ttsInitialized = await ttsService.initialize();
    if (!ttsInitialized) {
      // Throw a specific exception if a critical service fails to initialize
      throw ServiceInitializationException("TTS Service failed to initialize!");
    }
    debugPrint("Services: TTS Service initialized.");

    // Initialize screen narration service
    final screenNarrationService = ref.read(screenNarrationServiceProvider);
    await screenNarrationService.initialize();
    debugPrint("Services: Screen Narration Service initialized with ${screenNarrationService.narrationCount} narrations.");

  } catch (e, stackTrace) {
    // Catch any exception during initialization and report it
    debugPrint("A service failed to initialize: $e");
    debugPrint(stackTrace.toString());
    // Re-throw the exception to make the provider enter an error state
    throw ServiceInitializationException("Service initialization failed: $e");
  }
});
