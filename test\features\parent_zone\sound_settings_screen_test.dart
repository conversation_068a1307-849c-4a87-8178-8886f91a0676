import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:choice_once_upon_a_time/features/parent_zone/presentation/screens/sound_settings_screen.dart';

void main() {
  group('SoundSettingsScreen Widget Tests', () {
    late ProviderContainer container;

    setUp(() {
      container = ProviderContainer();
    });

    tearDown(() {
      container.dispose();
    });

    Widget createTestWidget() {
      return UncontrolledProviderScope(
        container: container,
        child: const MaterialApp(
          home: SoundSettingsScreen(),
        ),
      );
    }

    testWidgets('displays sound settings screen with all elements', (WidgetTester tester) async {
      // Act
      await tester.pumpWidget(createTestWidget());
      await tester.pumpAndSettle();

      // Assert
      expect(find.text('Sound Settings'), findsOneWidget);
      expect(find.text('Audio Settings'), findsOneWidget);
      expect(find.text('Master Volume'), findsOneWidget);
      expect(find.text('Background Music'), findsOneWidget);
      expect(find.text('UI Sound Effects'), findsOneWidget);
      expect(find.byType(Slider), findsOneWidget);
      expect(find.byType(Switch), findsNWidgets(2)); // Music and SFX switches
    });

    testWidgets('displays correct initial values', (WidgetTester tester) async {
      // Act
      await tester.pumpWidget(createTestWidget());
      await tester.pumpAndSettle();

      // Assert - Check default values
      expect(find.text('80%'), findsOneWidget); // Default master volume
      expect(find.text('Enabled'), findsNWidgets(2)); // Music and SFX enabled by default
    });

    testWidgets('master volume slider updates value', (WidgetTester tester) async {
      // Act
      await tester.pumpWidget(createTestWidget());
      await tester.pumpAndSettle();

      // Find and interact with slider
      final slider = find.byType(Slider);
      expect(slider, findsOneWidget);

      // Drag slider to change value
      await tester.drag(slider, const Offset(50, 0));
      await tester.pumpAndSettle();

      // Assert - Volume percentage should have changed
      expect(find.text('80%'), findsNothing); // Original value should be gone
    });

    testWidgets('music toggle switch works', (WidgetTester tester) async {
      // Act
      await tester.pumpWidget(createTestWidget());
      await tester.pumpAndSettle();

      // Find music switch (first switch in the widget tree)
      final musicSwitch = find.byType(Switch).first;
      expect(musicSwitch, findsOneWidget);

      // Tap the switch
      await tester.tap(musicSwitch);
      await tester.pumpAndSettle();

      // Assert - Should show "Disabled" for music
      expect(find.text('Disabled'), findsAtLeastNWidgets(1));
    });

    testWidgets('SFX toggle switch works', (WidgetTester tester) async {
      // Act
      await tester.pumpWidget(createTestWidget());
      await tester.pumpAndSettle();

      // Find SFX switch (second switch in the widget tree)
      final sfxSwitch = find.byType(Switch).last;
      expect(sfxSwitch, findsOneWidget);

      // Tap the switch
      await tester.tap(sfxSwitch);
      await tester.pumpAndSettle();

      // Assert - Should show "Disabled" for SFX
      expect(find.text('Disabled'), findsAtLeastNWidgets(1));
    });

    testWidgets('reset to defaults button shows dialog', (WidgetTester tester) async {
      // Act
      await tester.pumpWidget(createTestWidget());
      await tester.pumpAndSettle();

      // Find and tap reset button
      final resetButton = find.text('Reset to Defaults');
      expect(resetButton, findsOneWidget);
      await tester.tap(resetButton);
      await tester.pumpAndSettle();

      // Assert - Dialog should appear
      expect(find.text('Reset Settings'), findsOneWidget);
      expect(find.text('Are you sure you want to reset all sound settings to their default values?'), findsOneWidget);
      expect(find.text('Cancel'), findsOneWidget);
      expect(find.text('Reset'), findsOneWidget);
    });

    testWidgets('reset dialog cancel button works', (WidgetTester tester) async {
      // Act
      await tester.pumpWidget(createTestWidget());
      await tester.pumpAndSettle();

      // Open reset dialog
      await tester.tap(find.text('Reset to Defaults'));
      await tester.pumpAndSettle();

      // Tap cancel
      await tester.tap(find.text('Cancel'));
      await tester.pumpAndSettle();

      // Assert - Dialog should be dismissed
      expect(find.text('Reset Settings'), findsNothing);
    });

    testWidgets('reset dialog reset button works', (WidgetTester tester) async {
      // Arrange - First change some settings
      await tester.pumpWidget(createTestWidget());
      await tester.pumpAndSettle();

      // Change music setting
      final musicSwitch = find.byType(Switch).first;
      await tester.tap(musicSwitch);
      await tester.pumpAndSettle();

      // Open reset dialog
      await tester.tap(find.text('Reset to Defaults'));
      await tester.pumpAndSettle();

      // Act - Tap reset
      await tester.tap(find.text('Reset'));
      await tester.pumpAndSettle();

      // Assert - Dialog should be dismissed and snackbar should appear
      expect(find.text('Reset Settings'), findsNothing);
      expect(find.text('Settings reset to defaults'), findsOneWidget);
    });

    testWidgets('back button navigation works', (WidgetTester tester) async {
      // This test would require a more complex setup with GoRouter
      // For now, we'll just verify the back button exists
      await tester.pumpWidget(createTestWidget());
      await tester.pumpAndSettle();

      expect(find.byIcon(Icons.arrow_back), findsOneWidget);
    });

    testWidgets('displays info note at bottom', (WidgetTester tester) async {
      // Act
      await tester.pumpWidget(createTestWidget());
      await tester.pumpAndSettle();

      // Assert
      expect(find.text('Settings are automatically saved and will apply to all stories. Changes take effect immediately.'), findsOneWidget);
      expect(find.byIcon(Icons.info_outline), findsOneWidget);
    });

    testWidgets('all section cards are displayed', (WidgetTester tester) async {
      // Act
      await tester.pumpWidget(createTestWidget());
      await tester.pumpAndSettle();

      // Assert - Check for section titles
      expect(find.text('Master Volume'), findsOneWidget);
      expect(find.text('Background Music'), findsOneWidget);
      expect(find.text('UI Sound Effects'), findsOneWidget);

      // Check for section subtitles
      expect(find.text('Controls overall app volume'), findsOneWidget);
      expect(find.text('Gentle music during stories'), findsOneWidget);
      expect(find.text('Button taps and interface sounds'), findsOneWidget);
    });

    testWidgets('volume icons are displayed', (WidgetTester tester) async {
      // Act
      await tester.pumpWidget(createTestWidget());
      await tester.pumpAndSettle();

      // Assert
      expect(find.byIcon(Icons.volume_up), findsAtLeastNWidgets(1));
      expect(find.byIcon(Icons.volume_down), findsOneWidget);
      expect(find.byIcon(Icons.music_note), findsOneWidget);
      expect(find.byIcon(Icons.touch_app), findsOneWidget);
    });
  });
}
