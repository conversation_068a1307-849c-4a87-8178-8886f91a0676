import 'package:flutter_test/flutter_test.dart';
import 'package:choice_once_upon_a_time/models/story_metadata_model.dart';

void main() {
  group('StoryMetadataModel', () {
    test('should create from JSON correctly', () {
      // Arrange
      final json = {
        'id': 'pip_pantry_puzzle',
        'title': {
          'en-US': 'Pip and the Pantry Puzzle',
          'es-ES': 'Pip y el Rompecabezas de la Despensa'
        },
        'coverImageUrl': 'story_covers/pip_cover.png',
        'loglineShort': {
          'en-US': 'Help Pip solve the mystery!',
          'es-ES': '¡Ayuda a Pip a resolver el misterio!'
        },
        'targetMoralValue': 'Honesty',
        'version': '1.0.0',
        'isNew': true,
        'hasUpdate': false,
        'isLocked': false,
        'supportedLanguages': ['en-US', 'es-ES'],
        'defaultLanguage': 'en-US',
        'isFree': true,
        'estimatedDurationMinutes': 8,
        'published': true,
        'targetAgeSubSegment': '4-6',
        'initialSceneId': 'scene_pip_01_intro',
      };

      // Act
      final model = StoryMetadataModel.fromJson(json);

      // Assert
      expect(model.id, 'pip_pantry_puzzle');
      expect(model.title['en-US'], 'Pip and the Pantry Puzzle');
      expect(model.title['es-ES'], 'Pip y el Rompecabezas de la Despensa');
      expect(model.coverImageUrl, 'story_covers/pip_cover.png');
      expect(model.targetMoralValue, 'Honesty');
      expect(model.version, '1.0.0');
      expect(model.isNew, true);
      expect(model.hasUpdate, false);
      expect(model.isLocked, false);
      expect(model.supportedLanguages, ['en-US', 'es-ES']);
      expect(model.defaultLanguage, 'en-US');
      expect(model.isFree, true);
      expect(model.estimatedDurationMinutes, 8);
      expect(model.published, true);
      expect(model.targetAgeSubSegment, '4-6');
      expect(model.initialSceneId, 'scene_pip_01_intro');
    });

    test('should convert to JSON correctly', () {
      // Arrange
      const model = StoryMetadataModel(
        id: 'test_story',
        title: {'en-US': 'Test Story'},
        coverImageUrl: 'test_cover.png',
        loglineShort: {'en-US': 'A test story'},
        targetMoralValue: 'Kindness',
        version: '1.0.0',
        supportedLanguages: ['en-US'],
        defaultLanguage: 'en-US',
        targetAgeSubSegment: '4-6',
        initialSceneId: 'scene_01',
      );

      // Act
      final json = model.toJson();

      // Assert
      expect(json['id'], 'test_story');
      expect(json['title'], {'en-US': 'Test Story'});
      expect(json['coverImageUrl'], 'test_cover.png');
      expect(json['targetMoralValue'], 'Kindness');
      expect(json['version'], '1.0.0');
      expect(json['isNew'], false);
      expect(json['hasUpdate'], false);
      expect(json['isLocked'], false);
      expect(json['isFree'], true);
    });

    test('should get localized title correctly', () {
      // Arrange
      const model = StoryMetadataModel(
        id: 'test_story',
        title: {
          'en-US': 'English Title',
          'es-ES': 'Título en Español'
        },
        coverImageUrl: 'test_cover.png',
        loglineShort: {'en-US': 'A test story'},
        targetMoralValue: 'Kindness',
        version: '1.0.0',
        supportedLanguages: ['en-US', 'es-ES'],
        defaultLanguage: 'en-US',
        targetAgeSubSegment: '4-6',
        initialSceneId: 'scene_01',
      );

      // Act & Assert
      expect(model.getLocalizedTitle('en-US'), 'English Title');
      expect(model.getLocalizedTitle('es-ES'), 'Título en Español');
      expect(model.getLocalizedTitle('fr-FR'), 'English Title'); // Falls back to default
    });

    test('should get localized logline correctly', () {
      // Arrange
      const model = StoryMetadataModel(
        id: 'test_story',
        title: {'en-US': 'Test Story'},
        coverImageUrl: 'test_cover.png',
        loglineShort: {
          'en-US': 'English logline',
          'es-ES': 'Logline en español'
        },
        targetMoralValue: 'Kindness',
        version: '1.0.0',
        supportedLanguages: ['en-US', 'es-ES'],
        defaultLanguage: 'en-US',
        targetAgeSubSegment: '4-6',
        initialSceneId: 'scene_01',
      );

      // Act & Assert
      expect(model.getLocalizedLogline('en-US'), 'English logline');
      expect(model.getLocalizedLogline('es-ES'), 'Logline en español');
      expect(model.getLocalizedLogline('fr-FR'), 'English logline'); // Falls back to default
    });

    test('should handle copyWith correctly', () {
      // Arrange
      const original = StoryMetadataModel(
        id: 'test_story',
        title: {'en-US': 'Original Title'},
        coverImageUrl: 'original_cover.png',
        loglineShort: {'en-US': 'Original logline'},
        targetMoralValue: 'Kindness',
        version: '1.0.0',
        supportedLanguages: ['en-US'],
        defaultLanguage: 'en-US',
        targetAgeSubSegment: '4-6',
        initialSceneId: 'scene_01',
      );

      // Act
      final updated = original.copyWith(
        title: {'en-US': 'Updated Title'},
        isNew: true,
      );

      // Assert
      expect(updated.id, 'test_story'); // Unchanged
      expect(updated.title['en-US'], 'Updated Title'); // Changed
      expect(updated.coverImageUrl, 'original_cover.png'); // Unchanged
      expect(updated.isNew, true); // Changed
    });

    test('should handle equality correctly', () {
      // Arrange
      const model1 = StoryMetadataModel(
        id: 'test_story',
        title: {'en-US': 'Test Story'},
        coverImageUrl: 'test_cover.png',
        loglineShort: {'en-US': 'A test story'},
        targetMoralValue: 'Kindness',
        version: '1.0.0',
        supportedLanguages: ['en-US'],
        defaultLanguage: 'en-US',
        targetAgeSubSegment: '4-6',
        initialSceneId: 'scene_01',
      );

      const model2 = StoryMetadataModel(
        id: 'test_story',
        title: {'en-US': 'Different Title'},
        coverImageUrl: 'different_cover.png',
        loglineShort: {'en-US': 'Different logline'},
        targetMoralValue: 'Honesty',
        version: '1.0.0',
        supportedLanguages: ['en-US'],
        defaultLanguage: 'en-US',
        targetAgeSubSegment: '4-6',
        initialSceneId: 'scene_01',
      );

      const model3 = StoryMetadataModel(
        id: 'different_story',
        title: {'en-US': 'Test Story'},
        coverImageUrl: 'test_cover.png',
        loglineShort: {'en-US': 'A test story'},
        targetMoralValue: 'Kindness',
        version: '1.0.0',
        supportedLanguages: ['en-US'],
        defaultLanguage: 'en-US',
        targetAgeSubSegment: '4-6',
        initialSceneId: 'scene_01',
      );

      // Act & Assert
      expect(model1 == model2, true); // Same id and version
      expect(model1 == model3, false); // Different id
      expect(model1.hashCode, model2.hashCode); // Same hash for equal objects
    });
  });
}
