import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:choice_once_upon_a_time/core/auth/session_manager.dart';
import 'package:choice_once_upon_a_time/features/parent_zone/data/user_profile_service.dart';
import 'package:choice_once_upon_a_time/shared_widgets/loading_indicator_widget.dart';
import 'package:choice_once_upon_a_time/core/utils/app_logger.dart';
import 'package:choice_once_upon_a_time/core/services/firebase_user_service.dart';

/// Welcome screen shown after successful authentication
/// Greets the user by name and guides them to profile selection
class WelcomeScreen extends ConsumerStatefulWidget {
  const WelcomeScreen({super.key});

  @override
  ConsumerState<WelcomeScreen> createState() => _WelcomeScreenState();
}

class _WelcomeScreenState extends ConsumerState<WelcomeScreen> {
  @override
  void initState() {
    super.initState();
    AppLogger.debug('[COMPONENT_LOAD] Full path: lib/features/app_init/presentation/screens/welcome_screen.dart - WelcomeScreen');
    
    // Auto-navigate after showing welcome message
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _autoNavigate();
    });
  }

  Future<void> _autoNavigate() async {
    // Wait a bit to show the welcome message
    await Future.delayed(const Duration(seconds: 3));
    
    if (mounted) {
      context.go('/profile_selection');
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final screenSize = MediaQuery.of(context).size;
    final currentSessionAsync = ref.watch(currentSessionProvider);

    return Scaffold(
      body: SafeArea(
        child: Container(
          width: double.infinity,
          height: double.infinity,
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [
                theme.colorScheme.primary.withValues(alpha: 0.1),
                theme.colorScheme.surface,
              ],
            ),
          ),
          child: currentSessionAsync.when(
            data: (session) => _buildWelcomeContent(context, theme, screenSize, session),
            loading: () => const Center(child: LoadingIndicatorWidget()),
            error: (error, stack) => _buildErrorState(theme, error),
          ),
        ),
      ),
    );
  }

  Widget _buildWelcomeContent(BuildContext context, ThemeData theme, Size screenSize, UserSession? session) {
    return Consumer(
      builder: (context, ref, child) {
        final firebaseUserInfoAsync = ref.watch(firebaseUserInfoProvider);
        final childProfilesAsync = ref.watch(firebaseChildProfilesProvider);

        return firebaseUserInfoAsync.when(
          data: (userInfo) => _buildWelcomeWithUserInfo(
            context,
            theme,
            screenSize,
            session,
            userInfo,
            childProfilesAsync,
          ),
          loading: () => _buildLoadingState(theme, screenSize),
          error: (error, stack) => _buildWelcomeWithSession(context, theme, screenSize, session),
        );
      },
    );
  }

  Widget _buildWelcomeWithUserInfo(
    BuildContext context,
    ThemeData theme,
    Size screenSize,
    UserSession? session,
    Map<String, dynamic>? userInfo,
    AsyncValue<List<UserProfile>> childProfilesAsync,
  ) {
    final firstName = userInfo?['firstName'] as String?;
    final lastName = userInfo?['lastName'] as String?;
    final displayName = firstName != null
        ? (lastName != null ? '$firstName $lastName' : firstName)
        : session?.displayName ?? 'Guest';

    return SingleChildScrollView(
      padding: const EdgeInsets.all(24),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          SizedBox(height: screenSize.height * 0.1),

          // Welcome illustration
          Container(
            width: 200,
            height: 200,
            decoration: BoxDecoration(
              color: theme.colorScheme.primary.withValues(alpha: 0.1),
              shape: BoxShape.circle,
            ),
            child: Icon(
              Icons.waving_hand,
              size: 100,
              color: theme.colorScheme.primary,
            ),
          ),

          const SizedBox(height: 32),

          // Welcome text with Firebase user info
          Text(
            'Welcome back, $displayName!',
            style: theme.textTheme.headlineLarge?.copyWith(
              fontWeight: FontWeight.bold,
              color: theme.colorScheme.primary,
            ),
            textAlign: TextAlign.center,
          ),

          const SizedBox(height: 16),

          Text(
            'Great to see you! Let\'s choose which child will be reading today.',
            style: theme.textTheme.bodyLarge?.copyWith(
              color: theme.colorScheme.onSurface.withValues(alpha: 0.8),
            ),
            textAlign: TextAlign.center,
          ),

          const SizedBox(height: 32),

          // Child profiles section
          childProfilesAsync.when(
            data: (profiles) => _buildChildProfilesSection(context, theme, profiles),
            loading: () => const LoadingIndicatorWidget(),
            error: (error, stack) => _buildChildProfilesError(theme),
          ),

          const SizedBox(height: 32),

          // Continue button
          ElevatedButton(
            onPressed: () => context.go('/profile_selection'),
            style: ElevatedButton.styleFrom(
              padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 16),
            ),
            child: const Text('Continue to Stories'),
          ),
        ],
      ),
    );
  }

  Widget _buildChildProfilesSection(BuildContext context, ThemeData theme, List<UserProfile> profiles) {
    if (profiles.isEmpty) {
      return Column(
        children: [
          Icon(
            Icons.child_care,
            size: 48,
            color: theme.colorScheme.onSurfaceVariant,
          ),
          const SizedBox(height: 16),
          Text(
            'No child profiles found.',
            style: theme.textTheme.bodyLarge?.copyWith(
              color: theme.colorScheme.onSurfaceVariant,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Create a child profile to get started!',
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.onSurfaceVariant.withValues(alpha: 0.7),
            ),
          ),
        ],
      );
    }

    return Column(
      children: [
        Text(
          'Child Profiles (${profiles.length})',
          style: theme.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
        const SizedBox(height: 16),
        ...profiles.take(3).map((profile) => _buildChildProfileCard(theme, profile)),
        if (profiles.length > 3) ...[
          const SizedBox(height: 8),
          Text(
            'and ${profiles.length - 3} more...',
            style: theme.textTheme.bodySmall?.copyWith(
              color: theme.colorScheme.onSurfaceVariant,
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildChildProfileCard(ThemeData theme, UserProfile profile) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: theme.colorScheme.surfaceContainerLow,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: theme.colorScheme.outline.withValues(alpha: 0.2),
        ),
      ),
      child: Row(
        children: [
          CircleAvatar(
            backgroundColor: profile.avatarColor,
            radius: 20,
            child: Text(
              profile.name.isNotEmpty ? profile.name[0].toUpperCase() : '?',
              style: const TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  profile.name,
                  style: theme.textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                Text(
                  'Age ${profile.age} • ${profile.storiesCompleted} stories completed',
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: theme.colorScheme.onSurfaceVariant,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildChildProfilesError(ThemeData theme) {
    return Column(
      children: [
        Icon(
          Icons.error_outline,
          size: 48,
          color: theme.colorScheme.error,
        ),
        const SizedBox(height: 16),
        Text(
          'Unable to load child profiles',
          style: theme.textTheme.bodyLarge?.copyWith(
            color: theme.colorScheme.error,
          ),
        ),
      ],
    );
  }

  Widget _buildLoadingState(ThemeData theme, Size screenSize) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(24),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          SizedBox(height: screenSize.height * 0.2),
          const LoadingIndicatorWidget(),
          const SizedBox(height: 24),
          Text(
            'Loading your information...',
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildWelcomeWithSession(BuildContext context, ThemeData theme, Size screenSize, UserSession? session) {
    final displayName = session?.displayName ?? 'Guest';

    return SingleChildScrollView(
      padding: const EdgeInsets.all(24),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          SizedBox(height: screenSize.height * 0.15),

          // Welcome illustration
          Container(
            width: 200,
            height: 200,
            decoration: BoxDecoration(
              color: theme.colorScheme.primary.withValues(alpha: 0.1),
              shape: BoxShape.circle,
            ),
            child: Icon(
              Icons.waving_hand,
              size: 100,
              color: theme.colorScheme.primary,
            ),
          ),

          const SizedBox(height: 32),

          // Welcome text
          Text(
            'Welcome, $displayName!',
            style: theme.textTheme.headlineLarge?.copyWith(
              fontWeight: FontWeight.bold,
              color: theme.colorScheme.primary,
            ),
            textAlign: TextAlign.center,
          ),

          const SizedBox(height: 16),

          Text(
            'Great to see you! Let\'s choose which child will be reading today.',
            style: theme.textTheme.bodyLarge?.copyWith(
              color: theme.colorScheme.onSurface.withValues(alpha: 0.8),
            ),
            textAlign: TextAlign.center,
          ),

          const SizedBox(height: 48),

          // Continue button
          ElevatedButton(
            onPressed: () => context.go('/profile_selection'),
            style: ElevatedButton.styleFrom(
              padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 16),
            ),
            child: const Text('Continue'),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorState(ThemeData theme, Object error) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 64,
            color: theme.colorScheme.error,
          ),
          const SizedBox(height: 16),
          Text(
            'Welcome!',
            style: theme.textTheme.titleLarge,
          ),
          const SizedBox(height: 8),
          Text(
            'Let\'s get started with your adventure.',
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          ElevatedButton(
            onPressed: () => context.go('/profile_selection'),
            child: const Text('Continue'),
          ),
        ],
      ),
    );
  }
}
