import 'package:flutter_test/flutter_test.dart';
import 'package:choice_once_upon_a_time/models/tts_models.dart';

void main() {
  group('TTSEvent', () {
    test('should create started event correctly', () {
      const text = 'Hello world';
      final event = TTSEvent.started(text);
      
      expect(event.type, TTSEventType.started);
      expect(event.text, text);
      expect(event.error, isNull);
      expect(event.wordIndex, isNull);
      expect(event.charIndex, isNull);
      expect(event.length, isNull);
      expect(event.timestamp, isA<DateTime>());
    });

    test('should create completed event correctly', () {
      final event = TTSEvent.completed();
      
      expect(event.type, TTSEventType.completed);
      expect(event.text, isNull);
      expect(event.error, isNull);
    });

    test('should create error event correctly', () {
      const errorMessage = 'TTS failed';
      final event = TTSEvent.error(errorMessage);
      
      expect(event.type, TTSEventType.error);
      expect(event.error, errorMessage);
      expect(event.text, isNull);
    });

    test('should create word boundary event correctly', () {
      final event = TTSEvent.wordBoundary(
        wordIndex: 5,
        charIndex: 25,
        length: 4,
      );
      
      expect(event.type, TTSEventType.wordBoundary);
      expect(event.wordIndex, 5);
      expect(event.charIndex, 25);
      expect(event.length, 4);
      expect(event.text, isNull);
      expect(event.error, isNull);
    });

    test('should create sentence boundary event correctly', () {
      final event = TTSEvent.sentenceBoundary(
        charIndex: 50,
        length: 20,
      );
      
      expect(event.type, TTSEventType.sentenceBoundary);
      expect(event.charIndex, 50);
      expect(event.length, 20);
      expect(event.wordIndex, isNull);
    });

    test('should serialize to/from JSON correctly', () {
      final originalEvent = TTSEvent.wordBoundary(
        wordIndex: 10,
        charIndex: 45,
        length: 6,
      );

      final json = originalEvent.toJson();
      final deserializedEvent = TTSEvent.fromJson(json);

      expect(deserializedEvent.type, originalEvent.type);
      expect(deserializedEvent.wordIndex, originalEvent.wordIndex);
      expect(deserializedEvent.charIndex, originalEvent.charIndex);
      expect(deserializedEvent.length, originalEvent.length);
    });
  });

  group('TTSParameters', () {
    test('should create default parameters correctly', () {
      const params = TTSParameters.defaultParams;
      
      expect(params.rate, 0.5);
      expect(params.pitch, 1.0);
      expect(params.volume, 1.0);
      expect(params.language, 'en-US');
      expect(params.voiceId, isNull);
    });

    test('should create custom parameters correctly', () {
      const params = TTSParameters(
        rate: 0.8,
        pitch: 1.2,
        volume: 0.9,
        language: 'es-ES',
        voiceId: 'voice123',
      );

      expect(params.rate, 0.8);
      expect(params.pitch, 1.2);
      expect(params.volume, 0.9);
      expect(params.language, 'es-ES');
      expect(params.voiceId, 'voice123');
    });

    test('should create copyWith correctly', () {
      const originalParams = TTSParameters(
        rate: 0.5,
        pitch: 1.0,
        volume: 1.0,
        language: 'en-US',
      );

      final newParams = originalParams.copyWith(
        rate: 0.7,
        language: 'fr-FR',
      );

      expect(newParams.rate, 0.7);
      expect(newParams.pitch, 1.0); // Unchanged
      expect(newParams.volume, 1.0); // Unchanged
      expect(newParams.language, 'fr-FR');
      expect(newParams.voiceId, isNull); // Unchanged
    });

    test('should serialize to/from JSON correctly', () {
      const originalParams = TTSParameters(
        rate: 0.6,
        pitch: 1.1,
        volume: 0.8,
        language: 'de-DE',
        voiceId: 'german_voice',
      );

      final json = originalParams.toJson();
      final deserializedParams = TTSParameters.fromJson(json);

      expect(deserializedParams, equals(originalParams));
    });
  });

  group('TTSVoice', () {
    test('should create voice correctly', () {
      const voice = TTSVoice(
        id: 'voice_001',
        name: 'English Female',
        language: 'en-US',
        gender: 'female',
        isSelected: true,
      );

      expect(voice.id, 'voice_001');
      expect(voice.name, 'English Female');
      expect(voice.language, 'en-US');
      expect(voice.gender, 'female');
      expect(voice.isSelected, true);
    });

    test('should create copyWith correctly', () {
      const originalVoice = TTSVoice(
        id: 'voice_002',
        name: 'Spanish Male',
        language: 'es-ES',
        gender: 'male',
        isSelected: false,
      );

      final newVoice = originalVoice.copyWith(
        isSelected: true,
        name: 'Spanish Male Premium',
      );

      expect(newVoice.id, 'voice_002'); // Unchanged
      expect(newVoice.name, 'Spanish Male Premium'); // Changed
      expect(newVoice.language, 'es-ES'); // Unchanged
      expect(newVoice.gender, 'male'); // Unchanged
      expect(newVoice.isSelected, true); // Changed
    });

    test('should serialize to/from JSON correctly', () {
      const originalVoice = TTSVoice(
        id: 'voice_003',
        name: 'French Female',
        language: 'fr-FR',
        gender: 'female',
        isSelected: false,
      );

      final json = originalVoice.toJson();
      final deserializedVoice = TTSVoice.fromJson(json);

      expect(deserializedVoice, equals(originalVoice));
    });
  });

  group('TTSState', () {
    test('should create initial state correctly', () {
      const state = TTSState.initial;
      
      expect(state.status, TTSStatus.uninitialized);
      expect(state.parameters, TTSParameters.defaultParams);
      expect(state.selectedVoice, isNull);
      expect(state.availableVoices, isEmpty);
      expect(state.error, isNull);
      expect(state.isAvailable, false);
    });

    test('should create ready state correctly', () {
      const state = TTSState.ready;
      
      expect(state.status, TTSStatus.ready);
      expect(state.isAvailable, true);
    });

    test('should create error state correctly', () {
      const errorMessage = 'TTS initialization failed';
      final state = TTSState.createError(errorMessage);
      
      expect(state.status, TTSStatus.error);
      expect(state.error, errorMessage);
      expect(state.isAvailable, false);
    });

    test('should create copyWith correctly', () {
      const originalState = TTSState(
        status: TTSStatus.ready,
        parameters: TTSParameters(rate: 0.5),
        isAvailable: true,
      );

      final newState = originalState.copyWith(
        status: TTSStatus.speaking,
        parameters: const TTSParameters(rate: 0.8),
      );

      expect(newState.status, TTSStatus.speaking);
      expect(newState.parameters.rate, 0.8);
      expect(newState.isAvailable, true); // Unchanged
    });

    test('should serialize to/from JSON correctly', () {
      const voice = TTSVoice(
        id: 'test_voice',
        name: 'Test Voice',
        language: 'en-US',
      );

      const originalState = TTSState(
        status: TTSStatus.speaking,
        parameters: TTSParameters(rate: 0.7, pitch: 1.1),
        selectedVoice: voice,
        availableVoices: [voice],
        isAvailable: true,
      );

      final json = originalState.toJson();
      final deserializedState = TTSState.fromJson(json);

      expect(deserializedState.status, originalState.status);
      expect(deserializedState.parameters, originalState.parameters);
      expect(deserializedState.selectedVoice, originalState.selectedVoice);
      expect(deserializedState.availableVoices, originalState.availableVoices);
      expect(deserializedState.isAvailable, originalState.isAvailable);
    });
  });

  group('TTSEventType enum', () {
    test('should have correct JSON values', () {
      expect(TTSEventType.started.name, 'started');
      expect(TTSEventType.completed.name, 'completed');
      expect(TTSEventType.paused.name, 'paused');
      expect(TTSEventType.resumed.name, 'resumed');
      expect(TTSEventType.stopped.name, 'stopped');
      expect(TTSEventType.error.name, 'error');
      expect(TTSEventType.wordBoundary.name, 'wordBoundary');
      expect(TTSEventType.sentenceBoundary.name, 'sentenceBoundary');
    });
  });

  group('TTSStatus enum', () {
    test('should have correct JSON values', () {
      expect(TTSStatus.uninitialized.name, 'uninitialized');
      expect(TTSStatus.initializing.name, 'initializing');
      expect(TTSStatus.ready.name, 'ready');
      expect(TTSStatus.speaking.name, 'speaking');
      expect(TTSStatus.paused.name, 'paused');
      expect(TTSStatus.error.name, 'error');
    });
  });
}
