import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:go_router/go_router.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:choice_once_upon_a_time/models/enhanced_story_model.dart';
import 'package:choice_once_upon_a_time/core/services/new_story_service.dart';
import 'package:choice_once_upon_a_time/core/services/enhanced_story_narration_service.dart';
import 'package:choice_once_upon_a_time/core/services/story_narration_service_interface.dart';
import 'package:choice_once_upon_a_time/core/services/asset_fallback_service.dart';
import 'package:choice_once_upon_a_time/shared_widgets/loading_indicator.dart';
import 'package:choice_once_upon_a_time/features/story_player/presentation/widgets/story_choice_popup_widget.dart';
import 'package:choice_once_upon_a_time/features/story_player/presentation/widgets/story_completion_flow_widget.dart';
import 'package:choice_once_upon_a_time/features/story_player/presentation/widgets/story_settings_widget.dart';
import 'package:choice_once_upon_a_time/core/services/story_progress_service.dart';
import 'package:choice_once_upon_a_time/core/utils/app_logger.dart';

/// New immersive story player with YouTube-like full-screen experience
/// Implements scene-by-scene loading, sentence-level TTS narration, and choice popup system
class NewStoryPlayerScreen extends StatefulWidget {
  final String storyId;

  const NewStoryPlayerScreen({
    super.key,
    required this.storyId,
  });

  @override
  State<NewStoryPlayerScreen> createState() => _NewStoryPlayerScreenState();
}

class _NewStoryPlayerScreenState extends State<NewStoryPlayerScreen>
    with TickerProviderStateMixin {
  // Services
  late final NewStoryService _storyService;
  late final IStoryNarrationService _narrationService;
  late final AssetFallbackService _fallbackService;
  late final StoryProgressService _progressService;

  // Animation controllers
  late final AnimationController _fadeController;
  late final AnimationController _scaleController;
  late final Animation<double> _fadeAnimation;
  late final Animation<double> _scaleAnimation;

  // Scene transition state
  bool _isTransitioning = false;
  String? _transitionMessage;

  // Configurable settings (stored in SharedPreferences)
  int _sceneTransitionDelay = 3; // seconds (2-5 range)
  int _choiceTransitionDelay = 2; // seconds (1-3 range)

  // Settings state
  StorySettings _storySettings = const StorySettings();
  bool _showSettings = false;

  // Progress tracking state
  StoryProgress? _currentProgress;
  DateTime? _sessionStartTime;
  Map<String, dynamic> _sessionChoices = {};

  // Story state
  EnhancedStoryModel? _story;
  EnhancedSceneModel? _currentScene;
  int _currentSceneIndex = 0;
  bool _isLoading = true;
  String? _error;

  // Narration state
  bool _isNarrating = false;
  bool _isPaused = false;
  int _currentSentenceIndex = 0;
  List<String> _currentSentences = [];

  // UI state
  bool _showControls = true;
  bool _showChoices = false;
  bool _showCompletion = false;
  String? _currentSceneImagePath;
  List<String> _visitedScenes = [];

  @override
  void initState() {
    super.initState();
    _initializeServices();
    _initializeAnimations();
    _loadSettings();
    _loadStory();
    _setupSystemUI();
  }

  /// Initialize services
  void _initializeServices() {
    AppLogger.debug('[NEW_STORY_PLAYER] Initializing services');
    _storyService = NewStoryService();
    _narrationService = EnhancedStoryNarrationService();
    _fallbackService = AssetFallbackService();
    _progressService = StoryProgressService();
  }

  /// Initialize animations
  void _initializeAnimations() {
    AppLogger.debug('[NEW_STORY_PLAYER] Initializing animations');
    
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _scaleController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _fadeController,
      curve: Curves.easeInOut,
    ));

    _scaleAnimation = Tween<double>(
      begin: 0.8,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _scaleController,
      curve: Curves.elasticOut,
    ));
  }

  /// Load settings from SharedPreferences
  Future<void> _loadSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final storySettings = await StorySettings.load();

      setState(() {
        _sceneTransitionDelay = prefs.getInt('scene_transition_delay') ?? 3;
        _choiceTransitionDelay = prefs.getInt('choice_transition_delay') ?? 2;
        _storySettings = storySettings;
      });

      AppLogger.debug('[NEW_STORY_PLAYER] Loaded settings - Scene delay: ${_sceneTransitionDelay}s, Choice delay: ${_choiceTransitionDelay}s');
      AppLogger.debug('[NEW_STORY_PLAYER] Story settings - Font: ${_storySettings.fontSize}, Transparency: ${_storySettings.subtitleTransparency}, Speed: ${_storySettings.narrationSpeed}');
    } catch (e) {
      AppLogger.debug('[NEW_STORY_PLAYER] Error loading settings: $e');
    }
  }

  /// Setup immersive system UI
  void _setupSystemUI() {
    SystemChrome.setEnabledSystemUIMode(SystemUiMode.immersiveSticky);
    SystemChrome.setPreferredOrientations([
      DeviceOrientation.landscapeLeft,
      DeviceOrientation.landscapeRight,
    ]);
  }

  /// Load the story and initialize
  Future<void> _loadStory() async {
    AppLogger.debug('[NEW_STORY_PLAYER] Loading story: ${widget.storyId}');

    try {
      setState(() {
        _isLoading = true;
        _error = null;
      });

      // Initialize narration service
      await _narrationService.initialize();

      // Load the story
      final story = await _storyService.loadStory(widget.storyId);

      if (story == null) {
        throw Exception('Story not found: ${widget.storyId}');
      }

      // Load or create progress
      await _loadOrCreateProgress(story);

      // Determine starting scene (from progress or first scene)
      final startingScene = _getStartingScene(story);
      if (startingScene == null) {
        throw Exception('No scenes found in story');
      }

      // Load scene image with fallback
      final sceneImagePath = await _storyService.getSceneImageWithFallback(
        widget.storyId,
        startingScene.image,
      );

      setState(() {
        _story = story;
        _currentScene = startingScene;
        _currentSceneIndex = story.scenes.indexOf(startingScene);
        _currentSceneImagePath = sceneImagePath;
        _isLoading = false;
        _sessionStartTime = DateTime.now();
      });

      // Track visited scene
      if (!_visitedScenes.contains(startingScene.id)) {
        _visitedScenes.add(startingScene.id);
      }

      // Start animations
      _fadeController.forward();
      _scaleController.forward();

      // Prepare narration
      _prepareSceneNarration();

      AppLogger.debug('[NEW_STORY_PLAYER] Story loaded successfully');
    } catch (e) {
      AppLogger.debug('[NEW_STORY_PLAYER] Error loading story: $e');
      setState(() {
        _error = 'Failed to load story: $e';
        _isLoading = false;
      });
    }
  }

  /// Prepare narration for current scene
  void _prepareSceneNarration() {
    if (_currentScene == null) return;

    // Split scene text into sentences
    _currentSentences = _splitIntoSentences(_currentScene!.text);
    _currentSentenceIndex = 0;

    AppLogger.debug('[NEW_STORY_PLAYER] Prepared ${_currentSentences.length} sentences for narration');
  }

  /// Split text into sentences
  List<String> _splitIntoSentences(String text) {
    // Simple sentence splitting - can be enhanced
    return text
        .split(RegExp(r'[.!?]+'))
        .map((s) => s.trim())
        .where((s) => s.isNotEmpty)
        .toList();
  }

  /// Start narration for current scene
  Future<void> _startNarration() async {
    if (_currentScene == null || _currentSentences.isEmpty) return;

    setState(() {
      _isNarrating = true;
      _isPaused = false;
    });

    AppLogger.debug('[NEW_STORY_PLAYER] Starting narration for scene: ${_currentScene!.id}');

    try {
      // Narrate each sentence
      for (int i = _currentSentenceIndex; i < _currentSentences.length; i++) {
        if (!_isNarrating || _isPaused) break;

        setState(() {
          _currentSentenceIndex = i;
        });

        final sentence = _currentSentences[i];
        await _narrationService.narrateText(
          sentence,
          emotionCue: _currentScene!.emotion,
          storyId: widget.storyId,
          sceneId: _currentScene!.id,
        );

        // Wait for pause duration between sentences
        if (i < _currentSentences.length - 1) {
          await Future.delayed(const Duration(milliseconds: 500));
        }
      }

      // Narration completed
      setState(() {
        _isNarrating = false;
        _currentSentenceIndex = 0;
      });

      // Check if scene has choices or should auto-advance
      _handleSceneCompletion();

    } catch (e) {
      AppLogger.debug('[NEW_STORY_PLAYER] Error during narration: $e');
      setState(() {
        _isNarrating = false;
      });
    }
  }

  /// Handle scene completion
  void _handleSceneCompletion() {
    if (_currentScene == null) return;

    if (_currentScene!.choices != null && _currentScene!.choices!.isNotEmpty) {
      // Show choices with configurable delay
      setState(() {
        _isTransitioning = true;
        _transitionMessage = 'Preparing choices...';
      });

      Future.delayed(Duration(seconds: _choiceTransitionDelay), () {
        setState(() {
          _showChoices = true;
          _isTransitioning = false;
          _transitionMessage = null;
        });
        AppLogger.debug('[NEW_STORY_PLAYER] Showing choices for scene: ${_currentScene!.id}');
      });
    } else if (_currentScene!.next != null) {
      // Check autoplay setting for scene progression
      if (_storySettings.isAutoplayEnabled) {
        // Auto-advance to next scene after delay
        _navigateToScene(_currentScene!.next!);
      } else {
        // Manual mode - show controls and wait for user interaction
        setState(() {
          _showControls = true;
        });
        AppLogger.debug('[NEW_STORY_PLAYER] Manual mode - waiting for user to advance scene');
      }
    } else {
      // End of story
      _handleStoryCompletion();
    }
  }

  /// Navigate to a specific scene with smooth transitions
  Future<void> _navigateToScene(String sceneId) async {
    if (_story == null || _isTransitioning) return;

    AppLogger.debug('[NEW_STORY_PLAYER] Navigating to scene: $sceneId');

    try {
      // Start transition
      setState(() {
        _isTransitioning = true;
        _transitionMessage = 'Loading next scene...';
      });

      // Fade out current scene
      await _fadeController.reverse();

      // Find the scene
      final scene = _story!.scenes.firstWhere(
        (s) => s.id == sceneId,
        orElse: () => throw Exception('Scene not found: $sceneId'),
      );

      // Load scene image with fallback
      final sceneImagePath = await _storyService.getSceneImageWithFallback(
        widget.storyId,
        scene.image,
      );

      // Update scene index
      final sceneIndex = _story!.scenes.indexOf(scene);

      setState(() {
        _currentScene = scene;
        _currentSceneIndex = sceneIndex;
        _currentSceneImagePath = sceneImagePath;
        _showChoices = false;
        _isNarrating = false;
        _isPaused = false;
        _transitionMessage = 'Preparing scene...';
      });

      // Track visited scene
      if (!_visitedScenes.contains(sceneId)) {
        _visitedScenes.add(sceneId);
      }

      // Save progress for new scene
      await _saveCurrentProgress();

      // Prepare new scene narration
      _prepareSceneNarration();

      // Wait for configurable delay
      await Future.delayed(Duration(seconds: _sceneTransitionDelay));

      // Fade in new scene
      await _fadeController.forward();

      // End transition
      setState(() {
        _isTransitioning = false;
        _transitionMessage = null;
      });

      // Auto-start narration after scene transition
      Future.delayed(const Duration(milliseconds: 500), () {
        _startNarration();
      });

    } catch (e) {
      AppLogger.debug('[NEW_STORY_PLAYER] Error navigating to scene: $e');
      setState(() {
        _error = 'Failed to navigate to scene: $e';
        _isTransitioning = false;
        _transitionMessage = null;
      });
    }
  }

  /// Handle story completion
  void _handleStoryCompletion() {
    AppLogger.debug('[NEW_STORY_PLAYER] Story completed');

    if (_story == null || _currentScene == null) return;

    // Save final progress
    _saveCurrentProgress(isCompleted: true);

    setState(() {
      _showCompletion = true;
      _showControls = false;
      _showChoices = false;
    });
  }

  /// Load or create progress for the story
  Future<void> _loadOrCreateProgress(EnhancedStoryModel story) async {
    try {
      final existingProgress = await _progressService.loadProgress(widget.storyId);

      if (existingProgress != null) {
        _currentProgress = existingProgress;
        _visitedScenes = List<String>.from(existingProgress.visitedScenes);
        _sessionChoices = Map<String, dynamic>.from(existingProgress.choicesMade);
        AppLogger.debug('[NEW_STORY_PLAYER] Loaded existing progress: ${existingProgress.progressPercentage.toStringAsFixed(1)}%');
      } else {
        // Create initial progress
        final firstScene = story.scenes.isNotEmpty ? story.scenes.first : null;
        if (firstScene != null) {
          _currentProgress = StoryProgressService.createInitialProgress(widget.storyId, firstScene.id);
          _visitedScenes = [firstScene.id];
          _sessionChoices = {};
          AppLogger.debug('[NEW_STORY_PLAYER] Created initial progress for story');
        }
      }
    } catch (e) {
      AppLogger.debug('[NEW_STORY_PLAYER] Error loading progress: $e');
      // Create fallback progress
      final firstScene = story.scenes.isNotEmpty ? story.scenes.first : null;
      if (firstScene != null) {
        _currentProgress = StoryProgressService.createInitialProgress(widget.storyId, firstScene.id);
        _visitedScenes = [firstScene.id];
        _sessionChoices = {};
      }
    }
  }

  /// Get starting scene based on progress
  EnhancedSceneModel? _getStartingScene(EnhancedStoryModel story) {
    if (_currentProgress != null && !_currentProgress!.isCompleted) {
      // Try to find the current scene from progress
      try {
        return story.scenes.firstWhere(
          (scene) => scene.id == _currentProgress!.currentSceneId,
        );
      } catch (e) {
        AppLogger.debug('[NEW_STORY_PLAYER] Current scene from progress not found, using first scene');
      }
    }

    // Default to first scene
    return story.scenes.isNotEmpty ? story.scenes.first : null;
  }

  /// Save current progress
  Future<void> _saveCurrentProgress({bool isCompleted = false}) async {
    if (_story == null || _currentScene == null || _currentProgress == null) return;

    try {
      final sessionDuration = _sessionStartTime != null
          ? DateTime.now().difference(_sessionStartTime!).inSeconds
          : 0;

      final updatedProgress = StoryProgressService.updateProgressWithScene(
        _currentProgress!,
        _currentScene!.id,
        _story!.scenes.length,
        newChoices: _sessionChoices,
        additionalPlayTime: sessionDuration,
        completed: isCompleted,
      );

      await _progressService.saveProgress(updatedProgress);
      _currentProgress = updatedProgress;

      AppLogger.debug('[NEW_STORY_PLAYER] Progress saved: ${updatedProgress.progressPercentage.toStringAsFixed(1)}%');
    } catch (e) {
      AppLogger.debug('[NEW_STORY_PLAYER] Error saving progress: $e');
    }
  }

  @override
  void dispose() {
    AppLogger.debug('[NEW_STORY_PLAYER] Disposing resources');

    // Save progress before disposing
    _saveCurrentProgress();

    // Restore system UI
    SystemChrome.setEnabledSystemUIMode(SystemUiMode.edgeToEdge);
    SystemChrome.setPreferredOrientations([
      DeviceOrientation.portraitUp,
      DeviceOrientation.portraitDown,
      DeviceOrientation.landscapeLeft,
      DeviceOrientation.landscapeRight,
    ]);

    _fadeController.dispose();
    _scaleController.dispose();
    _narrationService.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      body: _buildBody(),
    );
  }

  /// Build the main body
  Widget _buildBody() {
    if (_isLoading) {
      return const Center(
        child: LoadingIndicator(color: Colors.white),
      );
    }

    if (_error != null) {
      return _buildErrorWidget();
    }

    if (_story == null || _currentScene == null) {
      return const Center(
        child: Text(
          'Story not found',
          style: TextStyle(color: Colors.white),
        ),
      );
    }

    return Stack(
      children: [
        // Background scene image
        _buildSceneImage(),

        // Controls overlay
        if (_showControls && !_isTransitioning) _buildControlsOverlay(),

        // Choices overlay
        if (_showChoices && !_isTransitioning) _buildChoicesOverlay(),

        // Completion overlay
        if (_showCompletion && !_isTransitioning) _buildCompletionOverlay(),

        // Settings overlay
        if (_showSettings && !_isTransitioning) _buildSettingsOverlay(),

        // Transition loading overlay
        if (_isTransitioning) _buildTransitionOverlay(),
      ],
    );
  }

  /// Build scene image
  Widget _buildSceneImage() {
    return FadeTransition(
      opacity: _fadeAnimation,
      child: Container(
        width: double.infinity,
        height: double.infinity,
        child: _currentSceneImagePath != null
            ? Image.asset(
                _currentSceneImagePath!,
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) {
                  AppLogger.debug('[NEW_STORY_PLAYER] Failed to load scene image: $_currentSceneImagePath');
                  return Container(
                    color: Colors.grey[800],
                    child: const Center(
                      child: Icon(
                        Icons.image_not_supported,
                        color: Colors.white,
                        size: 64,
                      ),
                    ),
                  );
                },
              )
            : Container(
                color: Colors.grey[800],
                child: const Center(
                  child: Icon(
                    Icons.image,
                    color: Colors.white,
                    size: 64,
                  ),
                ),
              ),
      ),
    );
  }

  /// Build controls overlay
  Widget _buildControlsOverlay() {
    return Positioned(
      bottom: 0,
      left: 0,
      right: 0,
      child: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Colors.transparent,
              Colors.black.withValues(alpha: 0.7),
            ],
          ),
        ),
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Scene text with sentence highlighting
            _buildSceneText(),

            const SizedBox(height: 16),

            // Manual mode indicator
            if (!_storySettings.isAutoplayEnabled && _canAdvanceToNextScene() && !_isNarrating)
              _buildManualModeIndicator(),

            // Control buttons
            _buildControlButtons(),
          ],
        ),
      ),
    );
  }

  /// Build scene text with highlighting
  Widget _buildSceneText() {
    if (_currentSentences.isEmpty) return const SizedBox.shrink();

    final screenSize = MediaQuery.of(context).size;
    final isSmallScreen = screenSize.width < 600;

    return Container(
      width: double.infinity,
      constraints: BoxConstraints(
        maxHeight: screenSize.height * 0.3, // Limit to 30% of screen height
      ),
      padding: EdgeInsets.all(isSmallScreen ? 12 : 16),
      decoration: BoxDecoration(
        color: Colors.black.withValues(alpha: _storySettings.subtitleTransparency / 100),
        borderRadius: BorderRadius.circular(12),
      ),
      child: SingleChildScrollView(
        child: RichText(
          text: TextSpan(
            style: TextStyle(
              color: Colors.white,
              fontSize: _storySettings.fontSize,
              height: 1.4,
            ),
            children: _buildHighlightedText(),
          ),
          textAlign: TextAlign.center,
        ),
      ),
    );
  }

  /// Build highlighted text spans
  List<TextSpan> _buildHighlightedText() {
    final spans = <TextSpan>[];

    for (int i = 0; i < _currentSentences.length; i++) {
      final sentence = _currentSentences[i];
      final isCurrentSentence = i == _currentSentenceIndex && _isNarrating;
      
      spans.add(
        TextSpan(
          text: sentence + (i < _currentSentences.length - 1 ? '. ' : '.'),
          style: TextStyle(
            color: isCurrentSentence ? Colors.yellow : Colors.white,
            fontWeight: isCurrentSentence ? FontWeight.bold : FontWeight.normal,
          ),
        ),
      );
    }

    return spans;
  }

  /// Build control buttons
  Widget _buildControlButtons() {
    final screenSize = MediaQuery.of(context).size;
    final isSmallScreen = screenSize.width < 600;

    return Wrap(
      alignment: WrapAlignment.spaceEvenly,
      spacing: isSmallScreen ? 8 : 16,
      children: [
        // Play/Pause button
        _buildControlButton(
          icon: _isNarrating && !_isPaused ? Icons.pause : Icons.play_arrow,
          onPressed: _togglePlayPause,
          tooltip: _isNarrating && !_isPaused ? 'Pause' : 'Play',
        ),

        // Previous scene button
        _buildControlButton(
          icon: Icons.skip_previous,
          onPressed: _currentSceneIndex > 0 ? _goToPreviousScene : null,
          tooltip: 'Previous Scene',
        ),

        // Next scene button
        _buildControlButton(
          icon: Icons.skip_next,
          onPressed: _canAdvanceToNextScene() ? _goToNextScene : null,
          tooltip: 'Next Scene',
        ),

        // Settings button
        _buildControlButton(
          icon: Icons.settings,
          onPressed: () {
            setState(() {
              _showSettings = true;
            });
          },
          tooltip: 'Settings',
        ),

        // Exit button
        _buildControlButton(
          icon: Icons.close,
          onPressed: () => context.pop(),
          tooltip: 'Exit Story',
        ),
      ],
    );
  }

  /// Build individual control button
  Widget _buildControlButton({
    required IconData icon,
    required VoidCallback? onPressed,
    required String tooltip,
  }) {
    return Tooltip(
      message: tooltip,
      child: Container(
        decoration: BoxDecoration(
          color: Colors.black.withValues(alpha: 0.6),
          shape: BoxShape.circle,
        ),
        child: IconButton(
          icon: Icon(icon, color: Colors.white),
          onPressed: onPressed,
          iconSize: 32,
        ),
      ),
    );
  }

  /// Build manual mode indicator
  Widget _buildManualModeIndicator() {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: Colors.orange.withValues(alpha: 0.9),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(color: Colors.orange, width: 1),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          const Icon(
            Icons.touch_app,
            color: Colors.white,
            size: 20,
          ),
          const SizedBox(width: 8),
          const Text(
            'Tap Next to Continue',
            style: TextStyle(
              color: Colors.white,
              fontSize: 14,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(width: 8),
          Container(
            padding: const EdgeInsets.all(4),
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(12),
            ),
            child: const Icon(
              Icons.skip_next,
              color: Colors.white,
              size: 16,
            ),
          ),
        ],
      ),
    );
  }

  /// Toggle play/pause
  void _togglePlayPause() {
    if (_isNarrating) {
      setState(() {
        _isPaused = !_isPaused;
      });
      
      if (_isPaused) {
        _narrationService.pause();
      } else {
        _narrationService.play();
      }
    } else {
      _startNarration();
    }
  }

  /// Go to previous scene
  void _goToPreviousScene() {
    if (_currentSceneIndex > 0 && _story != null) {
      final previousScene = _story!.scenes[_currentSceneIndex - 1];
      _navigateToScene(previousScene.id);
    }
  }

  /// Go to next scene
  void _goToNextScene() {
    if (_currentScene?.next != null) {
      _navigateToScene(_currentScene!.next!);
    }
  }

  /// Check if can advance to next scene
  bool _canAdvanceToNextScene() {
    return _currentScene?.next != null && 
           (_currentScene?.choices == null || _currentScene!.choices!.isEmpty);
  }

  /// Build choices overlay
  Widget _buildChoicesOverlay() {
    if (_currentScene?.choices == null || _currentScene!.choices!.isEmpty) {
      return const SizedBox.shrink();
    }

    return StoryChoicePopupWidget(
      scene: _currentScene!,
      onChoiceSelected: _selectChoice,
      narrationService: _narrationService,
      onClose: () {
        setState(() {
          _showChoices = false;
        });
      },
    );
  }

  /// Build completion overlay
  Widget _buildCompletionOverlay() {
    if (_story == null || _currentScene == null) {
      return const SizedBox.shrink();
    }

    return StoryCompletionFlowWidget(
      story: _story!,
      finalScene: _currentScene!,
      visitedScenes: _visitedScenes,
      narrationService: _narrationService,
      onComplete: () => context.pop(),
      onReplay: () {
        // Restart the story
        setState(() {
          _showCompletion = false;
          _showControls = true;
          _visitedScenes.clear();
        });
        _loadStory();
      },
    );
  }

  /// Select a choice
  void _selectChoice(ChoiceOptionModel choice) {
    AppLogger.debug('[NEW_STORY_PLAYER] Choice selected: ${choice.option} -> ${choice.next}');

    // Track choice made
    if (_currentScene != null) {
      _sessionChoices['${_currentScene!.id}_choice'] = {
        'option': choice.option,
        'next': choice.next,
        'timestamp': DateTime.now().toIso8601String(),
      };
    }

    setState(() {
      _showChoices = false;
    });

    // Navigate to the chosen scene
    _navigateToScene(choice.next);
  }

  /// Build settings overlay
  Widget _buildSettingsOverlay() {
    return StorySettingsWidget(
      initialSettings: _storySettings,
      onSettingsChanged: _handleSettingsChanged,
      onClose: () {
        setState(() {
          _showSettings = false;
        });
      },
    );
  }

  /// Handle settings changes
  void _handleSettingsChanged(StorySettings newSettings) {
    setState(() {
      _storySettings = newSettings;
    });

    // Apply narration speed immediately if narrating
    if (_isNarrating) {
      _narrationService.setSpeechRate(_storySettings.narrationSpeed);
    }

    AppLogger.debug('[NEW_STORY_PLAYER] Settings updated - Font: ${_storySettings.fontSize}, Transparency: ${_storySettings.subtitleTransparency}, Speed: ${_storySettings.narrationSpeed}');
  }

  /// Build transition loading overlay
  Widget _buildTransitionOverlay() {
    return Container(
      color: Colors.black.withValues(alpha: 0.8),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const SizedBox(
              width: 64,
              height: 64,
              child: CircularProgressIndicator(
                color: Colors.white,
                strokeWidth: 3,
              ),
            ),
            const SizedBox(height: 24),
            if (_transitionMessage != null)
              Text(
                _transitionMessage!,
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 18,
                  fontWeight: FontWeight.w500,
                ),
                textAlign: TextAlign.center,
              ),
          ],
        ),
      ),
    );
  }

  /// Build error widget
  Widget _buildErrorWidget() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(Icons.error_outline, size: 64, color: Colors.red),
          const SizedBox(height: 16),
          Text(
            _error ?? 'An error occurred',
            style: const TextStyle(color: Colors.white),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: _loadStory,
            child: const Text('Retry'),
          ),
          const SizedBox(height: 8),
          TextButton(
            onPressed: () => context.pop(),
            child: const Text('Back', style: TextStyle(color: Colors.white)),
          ),
        ],
      ),
    );
  }
}
