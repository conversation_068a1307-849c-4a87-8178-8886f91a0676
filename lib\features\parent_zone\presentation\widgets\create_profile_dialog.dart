import 'package:flutter/material.dart';
import 'package:choice_once_upon_a_time/features/parent_zone/data/user_profile_service.dart';
import 'package:choice_once_upon_a_time/core/utils/app_logger.dart';

/// Dialog for creating new user profiles
class CreateProfileDialog extends StatefulWidget {
  final Function(UserProfile) onProfileCreated;

  const CreateProfileDialog({
    super.key,
    required this.onProfileCreated,
  });

  @override
  State<CreateProfileDialog> createState() => _CreateProfileDialogState();
}

class _CreateProfileDialogState extends State<CreateProfileDialog> {
  final _nameController = TextEditingController();
  int _selectedAge = 5;
  Color _selectedColor = Colors.blue;

  final List<Color> _avatarColors = [
    Colors.blue,
    Colors.pink,
    Colors.green,
    Colors.orange,
    Colors.purple,
    Colors.red,
    Colors.teal,
    Colors.indigo,
  ];

  @override
  void initState() {
    super.initState();
    AppLogger.debug('[COMPONENT_LOAD] Full path: lib/features/parent_zone/presentation/widgets/create_profile_dialog.dart - CreateProfileDialog');
  }

  @override
  void dispose() {
    _nameController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return AlertDialog(
      title: const Text('Create New Profile'),
      content: SingleChildScrollView(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Name input
            TextField(
              controller: _nameController,
              decoration: const InputDecoration(
                labelText: 'Child\'s Name',
                border: OutlineInputBorder(),
                hintText: 'Enter your child\'s name',
              ),
              textCapitalization: TextCapitalization.words,
              autofocus: true,
            ),
            
            const SizedBox(height: 16),
            
            // Age selector
            Row(
              children: [
                const Text('Age: '),
                const SizedBox(width: 8),
                Expanded(
                  child: DropdownButtonFormField<int>(
                    value: _selectedAge,
                    decoration: const InputDecoration(
                      border: OutlineInputBorder(),
                      contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                    ),
                    items: List.generate(10, (index) => index + 3)
                        .map((age) => DropdownMenuItem(
                              value: age,
                              child: Text('$age years old'),
                            ))
                        .toList(),
                    onChanged: (age) {
                      if (age != null) {
                        setState(() {
                          _selectedAge = age;
                        });
                      }
                    },
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 16),
            
            // Avatar color selector
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text('Avatar Color:'),
                const SizedBox(height: 8),
                Wrap(
                  spacing: 8,
                  runSpacing: 8,
                  children: _avatarColors.map((color) {
                    final isSelected = color == _selectedColor;
                    return GestureDetector(
                      onTap: () {
                        setState(() {
                          _selectedColor = color;
                        });
                      },
                      child: Container(
                        width: 40,
                        height: 40,
                        decoration: BoxDecoration(
                          color: color,
                          shape: BoxShape.circle,
                          border: Border.all(
                            color: isSelected ? theme.colorScheme.primary : Colors.grey,
                            width: isSelected ? 3 : 1,
                          ),
                        ),
                        child: isSelected
                            ? const Icon(
                                Icons.check,
                                color: Colors.white,
                                size: 20,
                              )
                            : null,
                      ),
                    );
                  }).toList(),
                ),
              ],
            ),
            
            const SizedBox(height: 16),
            
            // Preview
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: theme.colorScheme.surface,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: theme.colorScheme.outline.withValues(alpha: 0.3)),
              ),
              child: Row(
                children: [
                  Container(
                    width: 50,
                    height: 50,
                    decoration: BoxDecoration(
                      color: _selectedColor,
                      shape: BoxShape.circle,
                    ),
                    child: const Icon(
                      Icons.child_care,
                      color: Colors.white,
                      size: 25,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          _nameController.text.trim().isEmpty 
                              ? 'Child\'s Name' 
                              : _nameController.text.trim(),
                          style: theme.textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                            color: _nameController.text.trim().isEmpty 
                                ? theme.colorScheme.onSurface.withValues(alpha: 0.5)
                                : null,
                          ),
                        ),
                        Text(
                          '$_selectedAge years old',
                          style: theme.textTheme.bodyMedium?.copyWith(
                            color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Cancel'),
        ),
        ElevatedButton(
          onPressed: _nameController.text.trim().isEmpty
              ? null
              : () {
                  final profile = UserProfile(
                    id: DateTime.now().millisecondsSinceEpoch.toString(),
                    name: _nameController.text.trim(),
                    age: _selectedAge,
                    avatarColor: _selectedColor,
                    favoriteStories: [],
                    totalReadingTime: 0,
                    storiesCompleted: 0,
                    createdAt: DateTime.now(),
                    lastActiveAt: DateTime.now(),
                  );
                  
                  AppLogger.info('[CREATE_PROFILE_DIALOG] Creating profile for ${profile.name}, age ${profile.age}');
                  widget.onProfileCreated(profile);
                  Navigator.of(context).pop();
                },
          child: const Text('Create Profile'),
        ),
      ],
    );
  }
}
