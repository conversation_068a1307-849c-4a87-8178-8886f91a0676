import 'package:flutter_test/flutter_test.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:choice_once_upon_a_time/core/services/rewards_service.dart';

void main() {
  group('RewardsService', () {
    late RewardsService rewardsService;

    setUp(() async {
      // Initialize SharedPreferences with empty data
      SharedPreferences.setMockInitialValues({});
      rewardsService = RewardsService();
    });

    tearDown(() async {
      // Clear all rewards after each test
      await rewardsService.clearAllRewards();
    });

    test('should award completion reward', () async {
      // Act
      final reward = await rewardsService.awardCompletionReward('test_story', 'Unity Badge');

      // Assert
      expect(reward.rewardId, 'Unity Badge');
      expect(reward.rewardType, 'completion');
      expect(reward.storyId, 'test_story');
      expect(reward.sceneId, null);
      expect(reward.earnedAt, isA<DateTime>());
    });

    test('should award moral choice reward', () async {
      // Act
      final reward = await rewardsService.awardMoralChoiceReward(
        'test_story',
        'scene_1',
        'Sharing Star',
      );

      // Assert
      expect(reward.rewardId, 'Sharing Star');
      expect(reward.rewardType, 'moral_choice');
      expect(reward.storyId, 'test_story');
      expect(reward.sceneId, 'scene_1');
      expect(reward.earnedAt, isA<DateTime>());
    });

    test('should get all earned rewards', () async {
      // Arrange
      await rewardsService.awardCompletionReward('story1', 'Badge1');
      await rewardsService.awardMoralChoiceReward('story2', 'scene1', 'Star1');

      // Act
      final rewards = await rewardsService.getAllEarnedRewards();

      // Assert
      expect(rewards, hasLength(2));
      expect(rewards.any((r) => r.rewardId == 'Badge1'), true);
      expect(rewards.any((r) => r.rewardId == 'Star1'), true);
    });

    test('should get rewards for specific story', () async {
      // Arrange
      await rewardsService.awardCompletionReward('story1', 'Badge1');
      await rewardsService.awardMoralChoiceReward('story1', 'scene1', 'Star1');
      await rewardsService.awardCompletionReward('story2', 'Badge2');

      // Act
      final story1Rewards = await rewardsService.getRewardsForStory('story1');
      final story2Rewards = await rewardsService.getRewardsForStory('story2');

      // Assert
      expect(story1Rewards, hasLength(2));
      expect(story2Rewards, hasLength(1));
      expect(story1Rewards.every((r) => r.storyId == 'story1'), true);
      expect(story2Rewards.every((r) => r.storyId == 'story2'), true);
    });

    test('should get reward count', () async {
      // Arrange
      await rewardsService.awardCompletionReward('story1', 'Unity Badge');
      await rewardsService.awardCompletionReward('story2', 'Unity Badge');
      await rewardsService.awardMoralChoiceReward('story3', 'scene1', 'Sharing Star');

      // Act
      final unityBadgeCount = await rewardsService.getRewardCount('Unity Badge');
      final sharingStarCount = await rewardsService.getRewardCount('Sharing Star');
      final nonExistentCount = await rewardsService.getRewardCount('Non Existent');

      // Assert
      expect(unityBadgeCount, 2);
      expect(sharingStarCount, 1);
      expect(nonExistentCount, 0);
    });

    test('should get all reward counts', () async {
      // Arrange
      await rewardsService.awardCompletionReward('story1', 'Unity Badge');
      await rewardsService.awardCompletionReward('story2', 'Unity Badge');
      await rewardsService.awardMoralChoiceReward('story3', 'scene1', 'Sharing Star');

      // Act
      final counts = await rewardsService.getAllRewardCounts();

      // Assert
      expect(counts['Unity Badge'], 2);
      expect(counts['Sharing Star'], 1);
      expect(counts.length, 2);
    });

    test('should check if reward has been earned', () async {
      // Arrange
      await rewardsService.awardCompletionReward('story1', 'Unity Badge');

      // Act
      final hasEarned = await rewardsService.hasEarnedReward('story1', 'Unity Badge');
      final hasNotEarned = await rewardsService.hasEarnedReward('story1', 'Different Badge');

      // Assert
      expect(hasEarned, true);
      expect(hasNotEarned, false);
    });

    test('should check if story has been completed', () async {
      // Arrange
      await rewardsService.awardCompletionReward('story1', 'Unity Badge');
      await rewardsService.awardMoralChoiceReward('story2', 'scene1', 'Sharing Star');

      // Act
      final story1Completed = await rewardsService.hasCompletedStory('story1');
      final story2Completed = await rewardsService.hasCompletedStory('story2');
      final story3Completed = await rewardsService.hasCompletedStory('story3');

      // Assert
      expect(story1Completed, true);
      expect(story2Completed, false);
      expect(story3Completed, false);
    });

    test('should get unique earned rewards', () async {
      // Arrange
      await rewardsService.awardCompletionReward('story1', 'Unity Badge');
      await rewardsService.awardCompletionReward('story2', 'Unity Badge');
      await rewardsService.awardMoralChoiceReward('story3', 'scene1', 'Sharing Star');
      await rewardsService.awardMoralChoiceReward('story4', 'scene2', 'Community Medal');

      // Act
      final uniqueRewards = await rewardsService.getUniqueEarnedRewards();

      // Assert
      expect(uniqueRewards, hasLength(3));
      expect(uniqueRewards, contains('Unity Badge'));
      expect(uniqueRewards, contains('Sharing Star'));
      expect(uniqueRewards, contains('Community Medal'));
    });

    test('should clear all rewards', () async {
      // Arrange
      await rewardsService.awardCompletionReward('story1', 'Unity Badge');
      await rewardsService.awardMoralChoiceReward('story2', 'scene1', 'Sharing Star');

      // Verify rewards exist
      final rewardsBefore = await rewardsService.getAllEarnedRewards();
      expect(rewardsBefore, hasLength(2));

      // Act
      await rewardsService.clearAllRewards();

      // Assert
      final rewardsAfter = await rewardsService.getAllEarnedRewards();
      final countsAfter = await rewardsService.getAllRewardCounts();
      
      expect(rewardsAfter, isEmpty);
      expect(countsAfter, isEmpty);
    });

    test('should handle empty state gracefully', () async {
      // Act & Assert
      final rewards = await rewardsService.getAllEarnedRewards();
      final counts = await rewardsService.getAllRewardCounts();
      final uniqueRewards = await rewardsService.getUniqueEarnedRewards();
      final hasEarned = await rewardsService.hasEarnedReward('story1', 'Badge');
      final hasCompleted = await rewardsService.hasCompletedStory('story1');
      final count = await rewardsService.getRewardCount('Badge');

      expect(rewards, isEmpty);
      expect(counts, isEmpty);
      expect(uniqueRewards, isEmpty);
      expect(hasEarned, false);
      expect(hasCompleted, false);
      expect(count, 0);
    });
  });
}
