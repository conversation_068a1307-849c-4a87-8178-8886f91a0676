## **Comprehensive Screen Specifications & Requirements for UI/UX Generation (with User Flow)**

## **"Choice: Once Upon A Time" App**

Objective of this Document:  
This document provides a comprehensive and detailed breakdown of all anticipated screens, popups, and key UI components for the "Choice: Once Upon A Time" interactive bedtime story app. It is intended to serve as a direct input and functional specification guide for an AI agent (or development team) tasked with generating the UI/UX of the Flutter application. Each screen description outlines its purpose, key UI elements, core interactions, synergy with the empathetic narrator, specific design considerations, functional requirements, and explicit navigation/user flow links.  
Target Audience: Children aged 4-7 years and their parents.  
Core Project Pillars: Empathetic Narrator, Moral Value Integration, Bedtime Context, Intuitive Design, High-Quality Visuals.  
Assumed UI/UX Design Document for Reference: ui\_ux\_design\_v1 (Conceptual UI/UX Design)  
Assumed Schematics Webpage for Visual Reference: spa\_all\_screen\_schematics\_v1

### **I. Initial App Experience & Story Selection Screens**

**1\. App Launch Screen**

* **Purpose:** To provide an immediate, gentle, and welcoming first impression of the app, setting a calm and inviting tone.  
* **Key UI Elements:** App Logo / Name, subtle calming background animation, optional mascot visual.  
* **Key Interactions/Functionality:** Primarily passive; auto-transitions or skippable by tap.  
* **Narrator Synergy:** Brief, warm, skippable audio welcome.  
* **Design/UX Considerations:** Extremely calming, simple, high quality. Smooth transition.  
* **Navigation / User Flow Links:**  
  * **Arrives From:** Initial app startup by the user.  
  * **Leads To (Automatic/Tap):** Screen 2: Home Screen / Story Library (or Screen 4.1: FTUE / Tutorial Overlay on very first launch).  
* **Requirements/Specifications:**  
  * Must display app branding.  
  * Auto-transition to Home Screen/FTUE within 3-5 seconds.  
  * Optional: Skip by tap.  
  * Narrator audio brief, skippable.  
  * Lightweight, performant animation.

**2\. Home Screen / Story Library**

* **Purpose:** To allow children to easily browse, discover, and select stories. Primary content entry point.  
* **Key UI Elements:** Scrollable grid of Story Covers (art \+ title), "NEW" / "Update Available" / "Locked" badges, "For Parents" icon, (Future) "Favorites" icon, thematic background.  
* **Key Interactions/Functionality:** Scroll vertically. Tap story cover to select. Tap-and-hold for narrator cue (optional). Parent taps "For Parents" icon.  
* **Narrator Synergy:** Initial welcome/idle prompt. Story-specific audio cue on tap-and-hold/focus.  
* **Design/UX Considerations:** High-quality cover art. Legible titles. Smooth scrolling. Clear visual distinction for story states (locked/new/update). Large tap targets.  
* **Navigation / User Flow Links:**  
  * **Arrives From:**  
    * Screen 1: App Launch Screen (after initial launch sequence).  
    * Screen 4.1: FTUE / Tutorial Overlay (after FTUE completion or skip).  
    * Screen 3: Story Intro/Splash Screen (via "Back" button).  
    * Screen 7: In-Story Pause Screen/Menu (via "Go to Story Library" button).  
    * Screen 8: Story End Screen (via "Story Library" button).  
    * Screen 10: Parent Zone Dashboard (via "Back to Stories" button).  
  * **Leads To:**  
    * **Tap Story Cover (Unlocked, No Update):** Screen 3: Story Intro/Splash Screen.  
    * **Tap Story Cover (Update Available):** Potentially Screen 19: "Content Update Available" Notification/Popup first, then Screen 3\.  
    * **Tap Story Cover (Locked):** Subscription/Upgrade prompt (within Parent Zone context, e.g., leading to Screen 12: Subscription/Upgrade Screen after parental gate).  
    * **Tap "For Parents" Icon:** Screen 9: Parental Gate Screen.  
    * **(Future) Tap "Favorites" Icon:** Screen (New): My Favorites Screen.  
* **Requirements/Specifications:**  
  * Display all stories in a scrollable grid.  
  * Clear tappable cover art and title per story.  
  * Clear access to Parent Zone.  
  * Visual feedback on tap/hover for story covers.  
  * Performant with a growing library.  
  * Show story states (new, update, locked) clearly.

**3\. Story Intro/Splash Screen**

* **Purpose:** Create anticipation, confirm story selection, provide clear start point.  
* **Key UI Elements:** Full-screen story cover art/splash image, Story Title, large "Play" button, optional "Back" button.  
* **Key Interactions/Functionality:** Tap "Play" to start story. Tap "Back" to return to Story Library.  
* **Narrator Synergy:** Narrator introduces story or prompts "Play" (e.g., *"Are you ready for the story of \[Story Title\]? Let's begin\!"*).  
* **Design/UX Considerations:** Visually rich, consistent with story art. Prominent "Play" button. Smooth transition.  
* **Navigation / User Flow Links:**  
  * **Arrives From:** Screen 2: Home Screen / Story Library (after tapping a story cover) OR Screen 19: "Content Update Available" Notification/Popup (after choosing to play current version or after update completion).  
  * **Leads To (Tap "Play"):** Screen 4: Loading Screen (if needed), then Screen 5: Main In-Story Scene.  
  * **Leads To (Tap "Back"):** Screen 2: Home Screen / Story Library.  
* **Requirements/Specifications:**  
  * Display selected story's title and artwork.  
  * Prominent, tappable "Play" button.  
  * "Play" tap initiates story.  
  * Optional "Back" to Story Library.

**4\. Loading Screen**

* **Purpose:** Provide feedback during brief asset loading to prevent perceived unresponsiveness.  
* **Key UI Elements:** Simple, calming animation (pulsing stars, mascot), optional text "Loading Story...".  
* **Key Interactions/Functionality:** Passive; auto-transitions.  
* **Narrator Synergy:** Narrator: *"Our story is just about to begin..."*  
* **Design/UX Considerations:** Visually calm, consistent. Smooth, brief animation.  
* **Navigation / User Flow Links:**  
  * **Arrives From:** Screen 3: Story Intro/Splash Screen (after tapping "Play" and if assets need loading).  
  * **Leads To (Automatic):** Screen 5: Main In-Story Scene.  
* **Requirements/Specifications:**  
  * Display if loading \> \~1 second.  
  * Clear visual feedback of activity.  
  * Auto-transition to first story scene.  
  * As short as technically feasible.

**4.1. First-Time User Experience (FTUE) / Tutorial Overlay Screen**

* **Purpose:** Gently introduce first-time child users to basic app interactions (selecting stories, making choices) and the narrator.  
* **Key UI Elements:** Series of simple illustrative overlays on initial Story Library or first story. Animated hand/sparkle highlighting tappable areas. Minimal text. "Skip Tutorial" button.  
* **Key Interactions/Functionality:** Child follows visual/auditory prompts. Tapping highlighted element may advance tutorial. Skippable. Auto-disappears after key interactions shown or timeout.  
* **Narrator Synergy:** Primary guide for the tutorial (e.g., *"Hello\! To start an adventure, just tap on a picture like this one\!"*).  
* **Design/UX Considerations:** Extremely simple, brief, highly visual, playful, not a chore.  
* **Navigation / User Flow Links:**  
  * **Arrives From:** Screen 1: App Launch Screen (on very first app launch only).  
  * **Leads To (On Completion/Skip):** Screen 2: Home Screen / Story Library (with tutorial elements now hidden).  
* **Requirements/Specifications:**  
  * Only appears on first app launch for a new user/profile.  
  * Easily skippable.  
  * Clearly demonstrate how to select a story and make a choice (conceptually).  
  * Narrator audio clear and encouraging.

### **II. In-Story Experience Screens**

**5\. Main In-Story Scene (Narration & Illustration)**

* **Purpose:** Present core narrative content (illustrations, narrator's storytelling, dialogue), and ambient interactions.  
* **Key UI Elements:** Main Story Illustration Area, optional On-Screen Text Area (with word highlighting), persistent "Home" Icon, persistent subtle Narration Controls (Pause/Play, Replay Segment), optional "Discovery" Interaction Hotspots.  
* **Key Interactions/Functionality:** Child listens/views. Story progresses automatically (recommended). Child can use narration controls, tap "Home" to exit, tap "Discovery" elements.  
* **Narrator Synergy:** Primary driver. May acknowledge "Discovery" interactions.  
* **Design/UX Considerations:** Illustrations key. Perfect text/audio sync if text shown. Intuitive, non-distracting controls. Smooth scene transitions.  
* **Navigation / User Flow Links:**  
  * **Arrives From:** Screen 4: Loading Screen OR previous In-Story Scene OR Screen 7: In-Story Pause Screen/Menu (via "Resume").  
  * **Leads To (Automatic):** Next Screen 5: Main In-Story Scene (if linear segment) OR Screen 6: In-Story Choice Point OR Screen 8: Story End Screen.  
  * **Leads To (Tap "Home" Icon):** Screen 13: Exit Story Confirmation Popup.  
  * **Leads To (Tap Pause Icon):** Screen 7: In-Story Pause Screen/Menu.  
* **Requirements/Specifications:**  
  * Display current scene illustration.  
  * Play narrator audio.  
  * Functional Pause/Play, Replay controls.  
  * Clear exit to Story Library (via Home icon).  
  * (If text display) Highlight words in sync.  
  * Smooth auto-advance or clear prompt for manual advance.

**6\. In-Story Choice Point Presentation**

* **Purpose:** Clearly present a meaningful narrative choice, framed emotionally by the narrator.  
* **Key UI Elements:** Main Story Illustration (character at dilemma, may dim). 2-3 large, tappable Choice Options (illustrated orbs/cards with optional minimal keywords). Visual cues for active choices (gentle pulse/glow). Persistent "Home" and narration controls.  
* **Key Interactions/Functionality:** Child listens to narrator frame dilemma/options. Child taps one choice option.  
* **Narrator Synergy:** Emotionally frames dilemma. Voices each option. Acknowledges selection and transitions to chosen branch.  
* **Design/UX Considerations:** Choices visually distinct, large, well-spaced. Illustrations clearly convey option meaning. Immediate feedback on selection. Obvious that interaction is expected.  
* **Navigation / User Flow Links:**  
  * **Arrives From:** Screen 5: Main In-Story Scene (when a choice point is reached in the narrative).  
  * **Leads To (Tap a Choice Option):** The specific Screen 5: Main In-Story Scene that corresponds to the start of the chosen narrative branch.  
  * **Leads To (Tap "Home" Icon):** Screen 13: Exit Story Confirmation Popup.  
  * **Leads To (Tap Pause Icon):** Screen 7: In-Story Pause Screen/Menu.  
* **Requirements/Specifications:**  
  * Clearly display 2-3 tappable choice options.  
  * Narrator audio explains each option.  
  * Register single tap as selection.  
  * Immediate visual/auditory feedback on selection.  
  * Narrative branches correctly based on selection.

**7\. In-Story Pause Screen/Menu**

* **Purpose:** Provide options when story is paused by child or parent.  
* **Key UI Elements:** Overlay screen (story dims/blurs). "Story Paused" title. Large buttons: "Resume Story" (▶), "Replay Current Segment" (↺), "Go to Story Library" (🏡). (Optional) "Settings" (⚙️) icon leading to Parent Zone (via gate).  
* **Key Interactions/Functionality:** Tap "Resume" to continue. Tap "Replay" to restart current segment. Tap "Go to Story Library" to exit story (may trigger Screen 13).  
* **Narrator Synergy:** None active. Calm tone maintained.  
* **Design/UX Considerations:** Large, clear buttons. Simple, temporary interruption feel.  
* **Navigation / User Flow Links:**  
  * **Arrives From:** Tapping "Pause" icon on Screen 5: Main In-Story Scene or Screen 6: In-Story Choice Point OR automatically from Screen 8.1: "Are You Still There?" Idle Prompt after further timeout.  
  * **Leads To (Tap "Resume Story"):** Returns to the exact point in Screen 5 or Screen 6 where paused.  
  * **Leads To (Tap "Replay Current Segment"):** Restarts current audio/visual segment of Screen 5 or Screen 6\.  
  * **Leads To (Tap "Go to Story Library"):** Screen 13: Exit Story Confirmation Popup.  
  * **(Optional) Leads To (Tap "Settings" icon):** Screen 9: Parental Gate Screen.  
* **Requirements/Specifications:**  
  * Appears on pause. Offers Resume, Replay, Go to Library. Options function as described.

**8\. Story End Screen**

* **Purpose:** Provide gentle conclusion, acknowledge completion, offer next steps or transition to sleep. Includes narrator-led moral discussion.  
* **Key UI Elements:** Gentle concluding illustration. "The End\!" message. "Story Library" button (📖). "All Done for Tonight?" / "Time for Sleep?" button (🌙).  
* **Key Interactions/Functionality:** Tap "Story Library". Tap "All Done for Tonight?".  
* **Narrator Synergy:** Delivers final narrative lines. Initiates "End-of-Story Discussion." Gives final warm goodnight.  
* **Design/UX Considerations:** Exceptionally calming/reassuring. Reinforce positive feelings. Natural transition to sleep.  
* **Navigation / User Flow Links:**  
  * **Arrives From:** The final narrative scene (Screen 5: Main In-Story Scene) of any story branch.  
  * **Leads To (Tap "Story Library"):** Screen 2: Home Screen / Story Library.  
  * **Leads To (Tap "All Done for Tonight?"):** Screen 20: Calm Exit Screen or directly triggers app exit (OS level, less ideal).  
* **Requirements/Specifications:**  
  * Appears after final narrative scene.  
  * Displays concluding visual/message.  
  * Includes narrator-led moral discussion audio.  
  * Clear options to return to library or end session.

**8.1. "Are You Still There?" Idle Prompt (In-Story)**

* **Purpose:** Gently re-engage a child if the app is idle for an extended period during a story, or offer a way to pause/exit.  
* **Key UI Elements:** Soft visual overlay (screen dims, friendly character peeks in, pulsing animation). Optional text "Still with us?". Clear "Tap to Continue" area/button (▶️).  
* **Key Interactions/Functionality:** Tapping dismisses prompt and resumes story/reactivates choices. No interaction after further period may auto-pause to Screen 7\.  
* **Narrator Synergy:** Narrator softly asks: *"Hello? Are you still there, little explorer?"* or *"Still thinking about what \[Character Name\] should do?"*  
* **Design/UX Considerations:** Very gentle, not a reprimand. Trigger after significant idle time (1-2 mins). Easy to dismiss.  
* **Navigation / User Flow Links:**  
  * **Arrives From:** Prolonged inactivity on Screen 5: Main In-Story Scene or Screen 6: In-Story Choice Point.  
  * **Leads To (Tap to Continue):** Returns to the exact point in Screen 5 or Screen 6\.  
  * **Leads To (Automatic after further timeout):** Screen 7: In-Story Pause Screen/Menu.  
* **Requirements/Specifications:**  
  * Detect prolonged inactivity.  
  * Display gentle visual/auditory re-engagement prompt.  
  * Allow easy dismissal or lead to paused state.

### **III. Parent Zone & Related Screens**

**9\. Parental Gate Screen**

* **Purpose:** Prevent accidental child access to parent-specific settings and purchase areas.  
* **Key UI Elements:** "For Grown-Ups" title. Simple instruction for gate mechanism (e.g., "Press and hold for 3 seconds"). Interactive element for gate (button to hold). Visual feedback for interaction (filling circle). "Back"/"Cancel" option.  
* **Key Interactions/Functionality:** Parent performs action. Success grants access. Failure/cancel returns to child's view.  
* **Narrator Synergy:** None.  
* **Design/UX Considerations:** Difficult for child (4-7) but not frustrating for parent. Clear instructions.  
* **Navigation / User Flow Links:**  
  * **Arrives From:** Tapping "For Parents" icon on Screen 2: Home Screen / Story Library (or potentially from Screen 7: In-Story Pause Screen/Menu if settings accessible from there).  
  * **Leads To (Successful Gate):** Screen 10: Parent Zone Dashboard.  
  * **Leads To (Cancel/Back):** Previous screen (e.g., Screen 2 or Screen 7).  
* **Requirements/Specifications:**  
  * Effectively gate Parent Zone access.  
  * Clear instructions.  
  * Way to cancel/return.  
  * Grant access on successful completion.

**10\. Parent Zone Dashboard**

* **Purpose:** Central hub for parents to access settings, account info, content management, and app philosophy.  
* **Key UI Elements:** "Parent Zone" title. List of navigable menu items (Account, Sound, Narration, Downloads, About Values, Help, Privacy, etc.) with clear labels/icons. "Back to Stories" button.  
* **Key Interactions/Functionality:** Tap menu item to navigate. Tap "Back to Stories" to return to child's view.  
* **Narrator Synergy:** None.  
* **Design/UX Considerations:** Clean, clear, professional UI. Intuitive navigation (standard mobile settings patterns). Maintain app's calm visual style but with utilitarian feel.  
* **Navigation / User Flow Links:**  
  * **Arrives From:** Screen 9: Parental Gate Screen (on successful completion).  
  * **Leads To (Tap Menu Item):** Corresponding detail screen (e.g., Screen 11: Sound Settings, Screen 12: Subscription Screen, Screen 12.1, 12.2, 12.3, 12.4).  
  * **Leads To (Tap "Back to Stories"):** Screen 2: Home Screen / Story Library.  
* **Requirements/Specifications:**  
  * Provide access to all defined parent-facing sections.  
  * Clear, intuitive navigation.  
  * Clear exit path to child's interface.

**11\. Parent Zone \- Sound Settings Screen**

* **Purpose:** Allow parents to customize app audio settings.  
* **Key UI Elements:** "Sound Settings" title. Sliders/toggles for Master Volume, Background Music (On/Off), UI Sound Effects (On/Off). "Save Settings" (if not instant). "Back" button.  
* **Key Interactions/Functionality:** Adjust sliders/toggles. Changes applied instantly or on save.  
* **Narrator Synergy:** None.  
* **Design/UX Considerations:** Standard, easy-to-use UI controls. Clear labels. Visual feedback on change.  
* **Navigation / User Flow Links:**  
  * **Arrives From:** Screen 10: Parent Zone Dashboard (tapping "Sound Settings").  
  * **Leads To (Tap "Back" or "Save"):** Screen 10: Parent Zone Dashboard.  
* **Requirements/Specifications:**  
  * Control master volume, BGM, UI SFX.  
  * Settings must be persistent.  
  * Way to save (if needed) and go back.

**12\. Subscription/Upgrade Screen (within Parent Zone)**

* **Purpose:** Clearly present subscription options and benefits to parents, facilitating upgrade.  
* **Key UI Elements:** "Unlock All Stories\!" title. Value proposition text. Sections for each plan (Monthly, Annual, Family Tier) showing price, benefits. "Subscribe Now"/"Start Trial" button per plan. "Restore Purchases" link. "Back" button.  
* **Key Interactions/Functionality:** Review plans. Tap "Subscribe" to initiate native IAP flow. Tap "Restore Purchases".  
* **Narrator Synergy:** None.  
* **Design/UX Considerations:** Extremely clear, transparent, easy to compare. Obvious value prop. Prominent CTAs. Trust signals.  
* **Navigation / User Flow Links:**  
  * **Arrives From:** Screen 10: Parent Zone Dashboard (tapping "Subscription" or "Upgrade") OR contextually from Screen 2: Home Screen / Story Library (if tapping a locked story, after passing Screen 9: Parental Gate).  
  * **Leads To (Tap "Subscribe Now"):** Native platform In-App Purchase flow (iOS App Store / Google Play Store). On success/failure, returns to this screen or a "Subscription Confirmed" state of this screen, or back to Parent Dashboard.  
  * **Leads To (Tap "Back"):** Screen 10: Parent Zone Dashboard or previous screen.  
* **Requirements/Specifications:**  
  * Clearly display plans, prices, features.  
  * Use native IAP mechanism.  
  * "Restore Purchases" option.  
  * Transparent pricing/terms.

**12.1. Parent Zone \- "About Stories & Moral Values" Screen**

* **Purpose:** Provide parents with understanding of app's educational philosophy, core moral values, and their integration.  
* **Key UI Elements:** "Our Story Philosophy & Moral Values" title. Scrollable content. Intro text. List/grid of Core Moral Values (tappable). Brief definition/example per value. (Future: Link to resources). "Back" button.  
* **Key Interactions/Functionality:** Read/scroll. (Future) Tap values for more info/related stories.  
* **Narrator Synergy:** None.  
* **Design/UX Considerations:** Clear, concise, reassuring text. Clean, readable visuals. Reinforce ethical approach.  
* **Navigation / User Flow Links:**  
  * **Arrives From:** Screen 10: Parent Zone Dashboard (tapping "About Stories & Moral Values").  
  * **Leads To (Tap "Back"):** Screen 10: Parent Zone Dashboard.  
* **Requirements/Specifications:**  
  * Accurately reflect educational framework (Task 1.2).  
  * List core moral values.  
  * Easy for parents to understand.

**12.2. Parent Zone \- "Manage Downloads / Storage" Screen**

* **Purpose:** Allow parents to view/manage downloaded stories and device storage used by the app.  
* **Key UI Elements:** "Manage Downloaded Stories" title. Storage summary (Total Used, Device Free). Scrollable list of downloaded stories (Thumbnail, Title, Size, "Delete" button/icon 🗑️). "Delete All Stories" button. "Back" button.  
* **Key Interactions/Functionality:** View stories/sizes. Tap "Delete" (triggers Screen 16). Tap "Delete All" (triggers Screen 16).  
* **Narrator Synergy:** None.  
* **Design/UX Considerations:** Clear info. Deletion requires confirmation. Feedback on successful deletion.  
* **Navigation / User Flow Links:**  
  * **Arrives From:** Screen 10: Parent Zone Dashboard (tapping "Manage Downloads") OR Screen 17: "Storage Full" Warning Popup (via "Manage Storage" button).  
  * **Leads To (Tap "Delete" or "Delete All", after confirmation):** Stays on this screen, list updates.  
  * **Leads To (Tap "Back"):** Screen 10: Parent Zone Dashboard.  
* **Requirements/Specifications:**  
  * Accurately list downloaded stories and sizes.  
  * Functionality to delete individual/all stories.  
  * Deletion actions must be confirmed.

**12.3. Parent Zone \- "Help & Support / FAQ" Screen**

* **Purpose:** Provide answers to common questions, troubleshooting, and support contact.  
* **Key UI Elements:** "Help & Support" title. Optional search bar. Categorized list of FAQs (tappable questions, accordion answers). "Contact Us" button/section (email/link). "Back" button.  
* **Key Interactions/Functionality:** Browse/search FAQs. Tap question to expand/collapse answer. Access support contact.  
* **Narrator Synergy:** None.  
* **Design/UX Considerations:** Well-organized, easy to navigate. Clear, concise answers. Easy to find contact info.  
* **Navigation / User Flow Links:**  
  * **Arrives From:** Screen 10: Parent Zone Dashboard (tapping "Help & Support").  
  * **Leads To (Tap "Contact Us"):** Opens email client or support webpage.  
  * **Leads To (Tap "Back"):** Screen 10: Parent Zone Dashboard.  
* **Requirements/Specifications:**  
  * Relevant FAQs and answers.  
  * Clear way to contact support.

**12.4. Parent Zone \- Child Profile Selection/Creation Screen (Future Feature)**

* **Purpose:** (If multiple profiles supported) Allows parent to select or create a child profile for personalized experience.  
* **Key UI Elements:** "Who is reading today?" title. Visual cards/icons for existing profiles (avatar, name). "Add New Profile" button (+ icon). "Back" button.  
* **Key Interactions/Functionality:** Tap profile to select and proceed to library. Tap "Add New Profile" to go to Profile Creation screen (12.4.1).  
* **Narrator Synergy:** (If at app launch after gate) Narrator: *"Hello\! Who is joining me for a story adventure today? Please ask your grown-up to help you choose."*  
* **Design/UX Considerations:** Simple, visual. Child-friendly avatars. Minimal creation steps.  
* **Navigation / User Flow Links:**  
  * **Arrives From:** Screen 10: Parent Zone Dashboard (tapping "Child Profiles") OR Potentially after Screen 9: Parental Gate on first setup if profiles are enabled.  
  * **Leads To (Tap Profile):** Screen 2: Home Screen / Story Library (context set to selected profile).  
  * **Leads To (Tap "Add New Profile"):** Screen 12.4.1: Create/Edit Child Profile Popup/Screen.  
  * **Leads To (Tap "Back"):** Screen 10: Parent Zone Dashboard.  
* **Requirements/Specifications:**  
  * Display existing profiles.  
  * Allow profile selection.  
  * Path to create new profile.

**12.4.1. Parent Zone \- Create/Edit Child Profile Popup/Screen (Future Feature)**

* **Purpose:** Allows parents to input minimal details for a new child profile or edit an existing one.  
* **Key UI Elements:** "Create Profile" / "Edit Profile" title. Text input for "Child's Name". Avatar selection area (grid/carousel of friendly avatars). "Save"/"Done" button. "Cancel" button.  
* **Key Interactions/Functionality:** Parent enters name, selects avatar. Tap "Save" creates/updates profile. Tap "Cancel" discards.  
* **Narrator Synergy:** None.  
* **Design/UX Considerations:** Extremely simple. Fun avatar selection.  
* **Navigation / User Flow Links:**  
  * **Arrives From:** Screen 12.4: Child Profile Selection Screen (tapping "Add New Profile" or "Edit" on an existing profile).  
  * **Leads To (Tap "Save" or "Cancel"):** Screen 12.4: Child Profile Selection Screen.  
* **Requirements/Specifications:**  
  * Allow name input.  
  * Allow avatar selection from predefined set.  
  * Save or cancel profile creation/editing.

### **IV. General Popups & Notifications**

**13\. Exit Story Confirmation Popup**

* **Purpose:** Prevent accidental story exit; inform about progress saving (if applicable).  
* **Key UI Elements:** Modal popup. Question: "Go back to Story Library?". Optional warning about progress. "Yes"/"No" buttons.  
* **Key Interactions/Functionality:** Tap "Yes" to exit to Library. Tap "No" to resume story.  
* **Narrator Synergy:** Narrator might voice prompt: *"Are you sure you want to leave our story for now?"*  
* **Design/UX Considerations:** Simple, clear, quick. Unambiguous buttons.  
* **Navigation / User Flow Links:**  
  * **Arrives From:** Tapping "Home" icon on Screen 5: Main In-Story Scene or Screen 6: In-Story Choice Point OR from Screen 7: In-Story Pause Screen/Menu (via "Go to Library").  
  * **Leads To (Tap "Yes"):** Screen 2: Home Screen / Story Library.  
  * **Leads To (Tap "No"):** Dismisses popup, returns to the in-story screen where it was triggered.  
* **Requirements/Specifications:**  
  * Appears on Home tap from story.  
  * Clear options to confirm/cancel exit.

**14\. Offline Notification Popup**

* **Purpose:** Inform user when offline and trying to access online-only content; reassure about downloaded content.  
* **Key UI Elements:** Modal popup. Icon (⚠️ or no-wifi). "No Internet Connection" title. Informative message. "OK" button.  
* **Key Interactions/Functionality:** Tap "OK" to dismiss. App shows only downloaded content.  
* **Narrator Synergy:** None typically. Gentle sound effect.  
* **Design/UX Considerations:** Reassuring, not alarming. Clear guidance.  
* **Navigation / User Flow Links:**  
  * **Arrives From:** Attempting to access an online-only feature or download a new story while the device is detected as offline.  
  * **Leads To (Tap "OK"):** Dismisses popup. App UI should reflect offline state (e.g., graying out non-downloaded stories).  
* **Requirements/Specifications:**  
  * Appear if online action attempted while offline.  
  * Clear explanation and options.  
  * Dismissible.

**15\. Missing TTS Voice Data Popup**

* **Purpose:** Inform user if required TTS voice data for selected language is missing; guide resolution.  
* **Key UI Elements:** Modal popup. Icon (🗣️). "Missing Voice Data" title. Informative message. Buttons: "Go to Device Settings" (or "How to?"), "Read Without Narration," "Cancel."  
* **Key Interactions/Functionality:** Choose to go to settings, read without narration, or cancel.  
* **Narrator Synergy:** None for this system message.  
* **Design/UX Considerations:** Actionable steps. "Read Without Narration" as valid fallback if offered.  
* **Navigation / User Flow Links:**  
  * **Arrives From:** Attempting to play a story (Screen 3: Story Intro/Splash Screen or Screen 5: Main In-Story Scene) if the required TTS voice/language pack is not found on the device.  
  * **Leads To (Tap "Go to Device Settings"):** Ideally, deep-links to OS voice settings. If not possible, shows a help screen/graphic with instructions.  
  * **Leads To (Tap "Read Without Narration"):** Proceeds to Screen 5: Main In-Story Scene with TTS disabled (text display only).  
  * **Leads To (Tap "Cancel"):** Returns to Screen 2: Home Screen / Story Library or Screen 3: Story Intro/Splash Screen.  
* **Requirements/Specifications:**  
  * Detect missing TTS voice/language data.  
  * Inform user clearly.  
  * Offer actionable solutions/alternatives.

**16\. "Delete Story?" Confirmation Popup (Context: Parent Zone \- Manage Downloads)**

* **Purpose:** Confirm parent's intent to delete downloaded story/stories.  
* **Key UI Elements:** Modal popup. Question: "Delete '\[Story Title\]'?" or "Delete All?". Warning about removal and re-download. "Delete" (Red/Warning color) and "Cancel" buttons.  
* **Key Interactions/Functionality:** Tap "Delete" to proceed. Tap "Cancel" to close.  
* **Narrator Synergy:** None.  
* **Design/UX Considerations:** Destructive action clear. Reassurance about re-download.  
* **Navigation / User Flow Links:**  
  * **Arrives From:** Tapping "Delete" or "Delete All" on Screen 12.2: Parent Zone \- Manage Downloads / Storage Screen.  
  * **Leads To (Tap "Delete"):** Deletes content, updates Screen 12.2, dismisses popup.  
  * **Leads To (Tap "Cancel"):** Dismisses popup, returns to Screen 12.2.  
* **Requirements/Specifications:**  
  * Clear statement of what will be deleted.  
  * Explicit confirmation required.

**17\. "Storage Full" Warning Popup**

* **Purpose:** Inform user (parent) that device storage is insufficient for a new download.  
* **Key UI Elements:** Modal popup. Icon (🈵). "Not Enough Storage" title. Message explaining issue and suggesting solutions. "Manage Storage" button (links to Screen 12.2). "OK" button.  
* **Key Interactions/Functionality:** Tap "Manage Storage" or "OK".  
* **Narrator Synergy:** None.  
* **Design/UX Considerations:** Actionable ("Manage Storage" link). Informative and helpful tone.  
* **Navigation / User Flow Links:**  
  * **Arrives From:** Attempting a story download (from Screen 2: Home Screen / Story Library or Screen 19: Content Update Available Notification/Popup) when insufficient device storage is detected.  
  * **Leads To (Tap "Manage Storage"):** Screen 12.2: Parent Zone \- Manage Downloads / Storage Screen.  
  * **Leads To (Tap "OK"):** Dismisses popup, returns to the screen where download was initiated.  
* **Requirements/Specifications:**  
  * Appear if download fails due to insufficient storage.  
  * Clear explanation and path to resolution.

**18\. "Rate Our App?" Popup (Optional, Timed Carefully)**

* **Purpose:** Gently prompt satisfied parents to rate the app.  
* **Key UI Elements:** Modal popup. Friendly message. Call to action. Buttons: "Rate Now," "Later," "No, Thanks."  
* **Key Interactions/Functionality:** "Rate Now" links to store. "Later" dismisses (may re-prompt later). "No, Thanks" dismisses (should not show often).  
* **Narrator Synergy:** None.  
* **Design/UX Considerations:** Timing is critical (after positive experiences). Easy to dismiss. Positive, appreciative tone. Use platform-idiomatic rating prompts if possible.  
* **Navigation / User Flow Links:**  
  * **Arrives From:** Triggered programmatically after certain positive usage milestones (e.g., within Parent Zone after viewing multiple stories, or after several app sessions).  
  * **Leads To (Tap "Rate Now"):** Opens the platform's App Store/Play Store page for the app.  
  * **Leads To (Tap "Later" or "No, Thanks"):** Dismisses popup, returns user to their current screen.  
* **Requirements/Specifications:**  
  * Use platform-idiomatic rating requests.  
  * Non-intrusive and easily dismissible.

**19\. "Content Update Available" Notification/Popup (In Story Library context)**

* **Purpose:** Inform users that a new version of a downloaded story is available.  
* **Key UI Elements:** Visual badge/icon (🔃) on story cover in Story Library (Screen 2). (Optional) If story tapped, a small modal prompt before Story Intro Screen (Screen 3): "'\[Story Title\]' has an update\! Download new version? (XX MB)" with "Update Now" / "Play Current Version" / "Later" buttons.  
* **Key Interactions/Functionality:** Badge is informational. If prompt shown: "Update Now" starts download. "Play Current Version" proceeds with old version. "Later" dismisses.  
* **Narrator Synergy:** If prompt appears, narrator: *"Oh look\! It seems \[Character Name\]'s story has a little update with new surprises\! Shall we get the latest version?"*  
* **Design/UX Considerations:** Badge noticeable but not distracting. Update process smooth, clear progress. Allow use of old version.  
* **Navigation / User Flow Links:**  
  * **Badge Appears On:** Screen 2: Home Screen / Story Library.  
  * **Popup (if implemented) Appears After:** Tapping an updatable story on Screen 2\.  
  * **Leads To (Popup \- "Update Now"):** Initiates download process (visualized on Screen 2 story card or a temporary download progress indicator), then to Screen 3: Story Intro/Splash Screen for the *new* version.  
  * **Leads To (Popup \- "Play Current Version"):** Screen 3: Story Intro/Splash Screen for the *old* version.  
  * **Leads To (Popup \- "Later"):** Dismisses popup, remains on Screen 2\.  
* **Requirements/Specifications:**  
  * Detect updated story versions.  
  * Clearly indicate updates.  
  * Simple mechanism to download/apply updates.

**20\. Calm Exit Screen (If "All Done for Tonight?" is tapped from Story End Screen)**

* **Purpose:** Provide a final, extremely calm screen reinforcing bedtime transition.  
* **Key UI Elements:** Full-screen, very soft, dreamy illustration (starry night, sleeping moon). Minimal text: "Sweet Dreams...". No interactive buttons.  
* **Key Interactions/Functionality:** Passive viewing. App might auto-close after delay, or parent uses OS controls.  
* **Narrator Synergy:** Final, very soft goodnight whisper: *"The stars are twinkling just for you... Goodnight, little one."*  
* **Design/UX Considerations:** Calmest screen. No stimulating elements. Gentle visual/auditory "tuck-in."  
* **Navigation / User Flow Links:**  
  * **Arrives From:** Screen 8: Story End Screen (after tapping "All Done for Tonight?").  
  * **Leads To:** App closes after a delay (e.g., 10-20 seconds) OR user/parent exits manually via OS. This screen does not navigate elsewhere within the app.  
* **Requirements/Specifications:**  
  * Visually and audibly extremely calming.  
  * If auto-closing, provide sufficient time for message absorption.

This expanded set of screen specifications, now including explicit navigation and user flow links, should provide a very robust guide for the AI agent tasked with UI/UX generation for "Choice: Once Upon A Time."