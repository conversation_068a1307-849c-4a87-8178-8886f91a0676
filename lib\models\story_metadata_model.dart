import 'package:json_annotation/json_annotation.dart';
import 'package:choice_once_upon_a_time/models/rewards_model.dart';

part 'story_metadata_model.g.dart';

/// Model for story metadata used in the story library
/// Contains minimal information needed for display in the story list
@JsonSerializable()
class StoryMetadataModel {
  /// Unique identifier for the story (e.g., "pip_pantry_puzzle")
  final String id;

  /// Localized story titles (e.g., {"en-US": "Pip and the Pantry Puzzle"})
  final Map<String, String> title;

  /// URL to the story cover image in Firebase Storage
  final String coverImageUrl;

  /// Short description for the story
  final Map<String, String> loglineShort;

  /// The moral value this story teaches (e.g., "Honesty")
  final String targetMoralValue;

  /// Story version (e.g., "1.0.0")
  final String version;

  /// Whether this story is marked as new
  final bool isNew;

  /// Whether there's an update available for this story
  final bool hasUpdate;

  /// Whether this story is locked (requires subscription)
  final bool isLocked;

  /// List of supported language codes
  final List<String> supportedLanguages;

  /// Default language for this story
  final String defaultLanguage;

  /// Whether this story is free to access
  final bool isFree;

  /// Estimated duration in minutes
  final int estimatedDurationMinutes;

  /// Whether the story is published and visible to users
  final bool published;

  /// Target age sub-segment (e.g., "4-6")
  final String targetAgeSubSegment;

  /// Initial scene ID for this story
  final String initialSceneId;

  /// Data source: 'asset' or 'firestore'
  final String dataSource;

  /// Estimated size in MB for download management
  final int estimatedSizeMb;

  /// Whether the user has progress in this story
  final bool hasProgress;

  /// Rewards available for this story
  final RewardsModel? rewards;

  const StoryMetadataModel({
    required this.id,
    required this.title,
    required this.coverImageUrl,
    required this.loglineShort,
    required this.targetMoralValue,
    this.version = '1.0.0',
    this.isNew = false,
    this.hasUpdate = false,
    this.isLocked = true,
    this.supportedLanguages = const ['en-US'],
    this.defaultLanguage = 'en-US',
    this.isFree = false,
    this.estimatedDurationMinutes = 10,
    this.published = true,
    required this.targetAgeSubSegment,
    required this.initialSceneId,
    this.dataSource = 'asset',
    this.estimatedSizeMb = 20, // Default to 20MB if not specified
    this.hasProgress = false, // Default to no progress
    this.rewards,
  });

  /// Creates a StoryMetadataModel from JSON
  factory StoryMetadataModel.fromJson(Map<String, dynamic> json) =>
      _$StoryMetadataModelFromJson(json);

  /// Converts the StoryMetadataModel to JSON
  Map<String, dynamic> toJson() => _$StoryMetadataModelToJson(this);

  /// Gets the localized title for the given language code
  /// Falls back to default language if not available
  String getLocalizedTitle(String languageCode) {
    return title[languageCode] ?? title[defaultLanguage] ?? title.values.first;
  }

  /// Gets the localized logline for the given language code
  /// Falls back to default language if not available
  String getLocalizedLogline(String languageCode) {
    return loglineShort[languageCode] ?? 
           loglineShort[defaultLanguage] ?? 
           loglineShort.values.first;
  }

  /// Creates a copy of this model with updated fields
  StoryMetadataModel copyWith({
    String? id,
    Map<String, String>? title,
    String? coverImageUrl,
    Map<String, String>? loglineShort,
    String? targetMoralValue,
    String? version,
    bool? isNew,
    bool? hasUpdate,
    bool? isLocked,
    List<String>? supportedLanguages,
    String? defaultLanguage,
    bool? isFree,
    int? estimatedDurationMinutes,
    bool? published,
    String? targetAgeSubSegment,
    String? initialSceneId,
    String? dataSource,
    int? estimatedSizeMb,
    bool? hasProgress,
    RewardsModel? rewards,
  }) {
    return StoryMetadataModel(
      id: id ?? this.id,
      title: title ?? this.title,
      coverImageUrl: coverImageUrl ?? this.coverImageUrl,
      loglineShort: loglineShort ?? this.loglineShort,
      targetMoralValue: targetMoralValue ?? this.targetMoralValue,
      version: version ?? this.version,
      isNew: isNew ?? this.isNew,
      hasUpdate: hasUpdate ?? this.hasUpdate,
      isLocked: isLocked ?? this.isLocked,
      supportedLanguages: supportedLanguages ?? this.supportedLanguages,
      defaultLanguage: defaultLanguage ?? this.defaultLanguage,
      isFree: isFree ?? this.isFree,
      estimatedDurationMinutes: estimatedDurationMinutes ?? this.estimatedDurationMinutes,
      published: published ?? this.published,
      targetAgeSubSegment: targetAgeSubSegment ?? this.targetAgeSubSegment,
      initialSceneId: initialSceneId ?? this.initialSceneId,
      dataSource: dataSource ?? this.dataSource,
      estimatedSizeMb: estimatedSizeMb ?? this.estimatedSizeMb,
      hasProgress: hasProgress ?? this.hasProgress,
      rewards: rewards ?? this.rewards,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is StoryMetadataModel && other.id == id && other.version == version;
  }

  @override
  int get hashCode => Object.hash(id, version);

  @override
  String toString() {
    return 'StoryMetadataModel(id: $id, version: $version, title: $title)';
  }
}
