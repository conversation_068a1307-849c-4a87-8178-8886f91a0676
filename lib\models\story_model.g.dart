// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'story_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

StoryModel _$StoryModelFromJson(Map<String, dynamic> json) => StoryModel(
      id: json['storyId'] as String,
      title: json['workingTitle'] as String,
      coverImageUrl: json['coverImageUrl'] as String?,
      targetMoralValue: json['targetCoreMoralValue'] as String,
      targetAgeSubSegment: json['targetAgeSubSegment'] as String,
      version: json['version'] as String? ?? '1.0.0',
      initialSceneId: json['initialSceneId'] as String?,
      scenes: (json['scenes'] as List<dynamic>)
          .map((e) => SceneModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      narratorPersonaGuidance: json['narratorPersonaGuidance'] as String,
      supportedLanguages: (json['supportedLanguages'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const ['en-US'],
      defaultLanguage: json['defaultLanguage'] as String,
      assetManifestUrl: json['assetManifestUrl'] as String?,
      estimatedDurationMinutes:
          (json['estimatedDurationMinutes'] as num?)?.toInt() ?? 10,
      isFree: json['isFree'] as bool? ?? true,
      published: json['published'] as bool? ?? true,
      logline: (json['logline'] as Map<String, dynamic>?)?.map(
            (k, e) => MapEntry(k, e as String),
          ) ??
          const {},
      rewards: json['rewards'] == null
          ? null
          : RewardsModel.fromJson(json['rewards'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$StoryModelToJson(StoryModel instance) =>
    <String, dynamic>{
      'storyId': instance.id,
      'workingTitle': instance.title,
      'coverImageUrl': instance.coverImageUrl,
      'targetCoreMoralValue': instance.targetMoralValue,
      'targetAgeSubSegment': instance.targetAgeSubSegment,
      'version': instance.version,
      'initialSceneId': instance.initialSceneId,
      'scenes': instance.scenes,
      'narratorPersonaGuidance': instance.narratorPersonaGuidance,
      'supportedLanguages': instance.supportedLanguages,
      'defaultLanguage': instance.defaultLanguage,
      'assetManifestUrl': instance.assetManifestUrl,
      'estimatedDurationMinutes': instance.estimatedDurationMinutes,
      'isFree': instance.isFree,
      'published': instance.published,
      'logline': instance.logline,
      'rewards': instance.rewards,
    };
