import 'package:flutter/material.dart';
import 'package:choice_once_upon_a_time/core/services/story_narration_service_interface.dart';
import 'package:choice_once_upon_a_time/models/narration_models.dart';
import 'package:choice_once_upon_a_time/core/utils/app_logger.dart';

/// Main narration widget that displays text with word-level highlighting
/// and provides narration controls
class StoryNarrationWidget extends StatefulWidget {
  final IStoryNarrationService narrationService;
  final String text;
  final String? emotionCue;
  final bool showControls;
  final bool autoStart;
  final VoidCallback? onNarrationComplete;
  final VoidCallback? onNarrationStart;
  final VoidCallback? onNarrationPause;
  final VoidCallback? onNarrationResume;

  const StoryNarrationWidget({
    super.key,
    required this.narrationService,
    required this.text,
    this.emotionCue,
    this.showControls = true,
    this.autoStart = false,
    this.onNarrationComplete,
    this.onNarrationStart,
    this.onNarrationPause,
    this.onNarrationResume,
  });

  @override
  State<StoryNarrationWidget> createState() => _StoryNarrationWidgetState();
}

class _StoryNarrationWidgetState extends State<StoryNarrationWidget>
    with TickerProviderStateMixin {
  
  // Animation controllers
  late AnimationController _highlightController;
  late AnimationController _progressController;
  late Animation<double> _highlightAnimation;
  late Animation<double> _progressAnimation;

  // State
  NarrationState _narrationState = NarrationState.idle;
  final List<WordHighlight> _wordHighlights = [];
  int _currentWordIndex = 0;
  double _progress = 0.0;

  // Text styling
  late TextStyle _baseTextStyle;
  late TextStyle _highlightedTextStyle;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _setupNarrationListeners();
    
    if (widget.autoStart) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _startNarration();
      });
    }
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    _initializeTextStyles();
  }

  @override
  void dispose() {
    _highlightController.dispose();
    _progressController.dispose();
    super.dispose();
  }

  /// Initialize animations
  void _initializeAnimations() {
    _highlightController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _progressController = AnimationController(
      duration: const Duration(milliseconds: 100),
      vsync: this,
    );

    _highlightAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _highlightController,
      curve: Curves.easeInOut,
    ));

    _progressAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _progressController,
      curve: Curves.linear,
    ));
  }

  /// Initialize text styles based on theme
  void _initializeTextStyles() {
    final theme = Theme.of(context);
    
    _baseTextStyle = theme.textTheme.bodyLarge?.copyWith(
      fontSize: 18,
      height: 1.5,
      color: theme.colorScheme.onSurface,
    ) ?? const TextStyle(fontSize: 18, height: 1.5);

    _highlightedTextStyle = _baseTextStyle.copyWith(
      backgroundColor: theme.colorScheme.primary.withValues(alpha: 0.3),
      fontWeight: FontWeight.w600,
      color: theme.colorScheme.primary,
    );
  }

  /// Set up narration service listeners
  void _setupNarrationListeners() {
    widget.narrationService.stateStream.listen((state) {
      if (mounted) {
        setState(() {
          _narrationState = state;
          _progress = state.progress;
        });

        // Handle state changes
        switch (state.status) {
          case NarrationStatus.playing:
            widget.onNarrationStart?.call();
            _progressController.forward();
            break;
          case NarrationStatus.paused:
            widget.onNarrationPause?.call();
            _progressController.stop();
            break;
          case NarrationStatus.completed:
            widget.onNarrationComplete?.call();
            _progressController.forward();
            break;
          case NarrationStatus.error:
            AppLogger.error('[NarrationWidget] Narration error: ${state.error}');
            break;
          default:
            break;
        }
      }
    });

    widget.narrationService.wordHighlightStream.listen((wordHighlight) {
      if (mounted && wordHighlight.isActive) {
        setState(() {
          _currentWordIndex = wordHighlight.startIndex;
        });
        _highlightController.forward().then((_) {
          _highlightController.reverse();
        });
      }
    });
  }

  /// Start narration
  Future<void> _startNarration() async {
    try {
      await widget.narrationService.narrateText(
        widget.text,
        emotionCue: widget.emotionCue,
      );
    } catch (e) {
      AppLogger.error('[NarrationWidget] Failed to start narration', e);
    }
  }

  /// Toggle play/pause
  Future<void> _togglePlayPause() async {
    try {
      if (_narrationState.status == NarrationStatus.playing) {
        await widget.narrationService.pause();
      } else {
        await widget.narrationService.play();
      }
    } catch (e) {
      AppLogger.error('[NarrationWidget] Failed to toggle play/pause', e);
    }
  }

  /// Stop narration
  Future<void> _stopNarration() async {
    try {
      await widget.narrationService.stop();
    } catch (e) {
      AppLogger.error('[NarrationWidget] Failed to stop narration', e);
    }
  }

  /// Replay narration
  Future<void> _replayNarration() async {
    try {
      await widget.narrationService.stop();
      await _startNarration();
    } catch (e) {
      AppLogger.error('[NarrationWidget] Failed to replay narration', e);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        // Text display with highlighting
        Expanded(
          child: _buildTextDisplay(),
        ),
        
        // Progress indicator
        if (widget.showControls) ...[
          const SizedBox(height: 16),
          _buildProgressIndicator(),
          const SizedBox(height: 16),
          _buildNarrationControls(),
        ],
      ],
    );
  }

  /// Build the text display with word highlighting
  Widget _buildTextDisplay() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
        ),
      ),
      child: SingleChildScrollView(
        child: AnimatedBuilder(
          animation: _highlightAnimation,
          builder: (context, child) {
            return _buildHighlightedText();
          },
        ),
      ),
    );
  }

  /// Build text with word-level highlighting
  Widget _buildHighlightedText() {
    final words = widget.text.split(RegExp(r'\s+'));
    final textSpans = <TextSpan>[];
    
    for (int i = 0; i < words.length; i++) {
      final word = words[i];
      final isHighlighted = i == _currentWordIndex && 
                           _narrationState.status == NarrationStatus.playing;
      
      textSpans.add(TextSpan(
        text: word,
        style: isHighlighted 
            ? _highlightedTextStyle.copyWith(
                backgroundColor: _highlightedTextStyle.backgroundColor?.withValues(
                  alpha: _highlightAnimation.value * 0.3,
                ),
              )
            : _baseTextStyle,
      ));
      
      // Add space between words (except for the last word)
      if (i < words.length - 1) {
        textSpans.add(TextSpan(
          text: ' ',
          style: _baseTextStyle,
        ));
      }
    }

    return RichText(
      text: TextSpan(children: textSpans),
      textAlign: TextAlign.left,
    );
  }

  /// Build progress indicator
  Widget _buildProgressIndicator() {
    return Column(
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Progress',
              style: Theme.of(context).textTheme.bodySmall,
            ),
            Text(
              '${(_progress * 100).toInt()}%',
              style: Theme.of(context).textTheme.bodySmall,
            ),
          ],
        ),
        const SizedBox(height: 8),
        AnimatedBuilder(
          animation: _progressAnimation,
          builder: (context, child) {
            return LinearProgressIndicator(
              value: _progress,
              backgroundColor: Theme.of(context).colorScheme.surfaceContainerHighest,
              valueColor: AlwaysStoppedAnimation<Color>(
                Theme.of(context).colorScheme.primary,
              ),
            );
          },
        ),
      ],
    );
  }

  /// Build narration controls
  Widget _buildNarrationControls() {
    final theme = Theme.of(context);
    final isPlaying = _narrationState.status == NarrationStatus.playing;
    final isPaused = _narrationState.status == NarrationStatus.paused;
    final isLoading = _narrationState.status == NarrationStatus.loading;

    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: [
        // Play/Pause button
        IconButton(
          onPressed: isLoading ? null : _togglePlayPause,
          icon: Icon(
            isPlaying ? Icons.pause : Icons.play_arrow,
            size: 32,
          ),
          style: IconButton.styleFrom(
            backgroundColor: theme.colorScheme.primary,
            foregroundColor: theme.colorScheme.onPrimary,
            padding: const EdgeInsets.all(12),
          ),
        ),
        
        // Stop button
        IconButton(
          onPressed: (isPlaying || isPaused) ? _stopNarration : null,
          icon: const Icon(Icons.stop, size: 24),
          style: IconButton.styleFrom(
            backgroundColor: theme.colorScheme.secondary,
            foregroundColor: theme.colorScheme.onSecondary,
            padding: const EdgeInsets.all(12),
          ),
        ),
        
        // Replay button
        IconButton(
          onPressed: isLoading ? null : _replayNarration,
          icon: const Icon(Icons.replay, size: 24),
          style: IconButton.styleFrom(
            backgroundColor: theme.colorScheme.tertiary,
            foregroundColor: theme.colorScheme.onTertiary,
            padding: const EdgeInsets.all(12),
          ),
        ),
      ],
    );
  }
}
