import 'package:flutter/material.dart';
import 'package:choice_once_upon_a_time/models/enhanced_story_model.dart';
import 'package:choice_once_upon_a_time/core/services/story_narration_service_interface.dart';
import 'package:choice_once_upon_a_time/core/utils/app_logger.dart';

/// Enhanced choice popup widget with narrator voice support
class StoryChoicePopupWidget extends StatefulWidget {
  final EnhancedSceneModel scene;
  final Function(ChoiceOptionModel) onChoiceSelected;
  final IStoryNarrationService narrationService;
  final VoidCallback? onClose;

  const StoryChoicePopupWidget({
    super.key,
    required this.scene,
    required this.onChoiceSelected,
    required this.narrationService,
    this.onClose,
  });

  @override
  State<StoryChoicePopupWidget> createState() => _StoryChoicePopupWidgetState();
}

class _StoryChoicePopupWidgetState extends State<StoryChoicePopupWidget>
    with TickerProviderStateMixin {
  late final AnimationController _scaleController;
  late final AnimationController _fadeController;
  late final Animation<double> _scaleAnimation;
  late final Animation<double> _fadeAnimation;

  bool _isNarrating = false;
  int _currentChoiceIndex = -1;
  bool _hasNarratedIntro = false;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _startIntroNarration();
  }

  /// Initialize animations
  void _initializeAnimations() {
    _scaleController = AnimationController(
      duration: const Duration(milliseconds: 400),
      vsync: this,
    );

    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _scaleAnimation = Tween<double>(
      begin: 0.8,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _scaleController,
      curve: Curves.elasticOut,
    ));

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _fadeController,
      curve: Curves.easeInOut,
    ));

    // Start animations
    _fadeController.forward();
    _scaleController.forward();
  }

  /// Start intro narration for the choice
  Future<void> _startIntroNarration() async {
    if (_hasNarratedIntro) return;

    setState(() {
      _isNarrating = true;
    });

    try {
      // Narrate the choice question
      const choiceIntro = "What should happen next? Listen to your options.";
      await widget.narrationService.narrateText(
        choiceIntro,
        emotionCue: 'curious',
        storyId: 'choice_intro',
        sceneId: widget.scene.id,
      );

      // Wait a moment before narrating choices
      await Future.delayed(const Duration(milliseconds: 500));

      // Narrate each choice option
      if (widget.scene.choices != null) {
        for (int i = 0; i < widget.scene.choices!.length; i++) {
          final choice = widget.scene.choices![i];
          
          setState(() {
            _currentChoiceIndex = i;
          });

          // Narrate the choice option
          final choiceText = "Option ${i + 1}: ${choice.option}";
          await widget.narrationService.narrateText(
            choiceText,
            emotionCue: 'neutral',
            storyId: 'choice_option',
            sceneId: widget.scene.id,
          );

          // Pause between choices
          if (i < widget.scene.choices!.length - 1) {
            await Future.delayed(const Duration(milliseconds: 800));
          }
        }
      }

      setState(() {
        _isNarrating = false;
        _currentChoiceIndex = -1;
        _hasNarratedIntro = true;
      });

      AppLogger.debug('[CHOICE_POPUP] Completed narrating all choices');

    } catch (e) {
      AppLogger.debug('[CHOICE_POPUP] Error during choice narration: $e');
      setState(() {
        _isNarrating = false;
        _currentChoiceIndex = -1;
        _hasNarratedIntro = true;
      });
    }
  }

  /// Handle choice selection
  Future<void> _selectChoice(ChoiceOptionModel choice, int index) async {
    AppLogger.debug('[CHOICE_POPUP] Choice selected: ${choice.option}');

    // Stop any ongoing narration
    await widget.narrationService.stop();

    // Highlight selected choice briefly
    setState(() {
      _currentChoiceIndex = index;
    });

    // Brief confirmation narration
    try {
      await widget.narrationService.narrateText(
        "You chose: ${choice.option}",
        emotionCue: 'pleased',
        storyId: 'choice_confirmation',
        sceneId: widget.scene.id,
      );
    } catch (e) {
      AppLogger.debug('[CHOICE_POPUP] Error during confirmation narration: $e');
    }

    // Wait a moment then close
    await Future.delayed(const Duration(milliseconds: 500));

    // Animate out
    await _fadeController.reverse();
    await _scaleController.reverse();

    // Call the selection callback
    widget.onChoiceSelected(choice);
  }

  @override
  void dispose() {
    _scaleController.dispose();
    _fadeController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return FadeTransition(
      opacity: _fadeAnimation,
      child: Container(
        color: Colors.black.withValues(alpha: 0.8),
        child: Center(
          child: ScaleTransition(
            scale: _scaleAnimation,
            child: _buildChoiceDialog(),
          ),
        ),
      ),
    );
  }

  /// Build the choice dialog
  Widget _buildChoiceDialog() {
    final screenSize = MediaQuery.of(context).size;
    final maxHeight = screenSize.height * 0.8; // Limit to 80% of screen height

    return Container(
      margin: EdgeInsets.symmetric(
        horizontal: screenSize.width > 600 ? 64 : 32,
        vertical: 32,
      ),
      constraints: BoxConstraints(
        maxHeight: maxHeight,
        maxWidth: screenSize.width > 600 ? 500 : double.infinity,
      ),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.3),
            blurRadius: 20,
            offset: const Offset(0, 10),
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Header with close button
          _buildDialogHeader(),

          // Scrollable content
          Flexible(
            child: SingleChildScrollView(
              padding: const EdgeInsets.symmetric(horizontal: 24),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // Narration status
                  if (_isNarrating) _buildNarrationStatus(),

                  const SizedBox(height: 24),

                  // Choice buttons with responsive layout
                  if (widget.scene.choices != null)
                    _buildChoiceLayout(),

                  const SizedBox(height: 16),

                  // Instructions
                  _buildInstructions(),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// Build dialog header
  Widget _buildDialogHeader() {
    return Padding(
      padding: const EdgeInsets.all(24),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Expanded(
            child: Text(
              'What should happen next?',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
                color: Colors.deepPurple,
              ),
              textAlign: TextAlign.center,
            ),
          ),
          if (widget.onClose != null)
            IconButton(
              icon: const Icon(Icons.close),
              onPressed: widget.onClose,
              color: Colors.grey[600],
            ),
        ],
      ),
    );
  }

  /// Build narration status indicator
  Widget _buildNarrationStatus() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: Colors.blue[50],
        borderRadius: BorderRadius.circular(20),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          const SizedBox(
            width: 16,
            height: 16,
            child: CircularProgressIndicator(
              strokeWidth: 2,
              valueColor: AlwaysStoppedAnimation<Color>(Colors.blue),
            ),
          ),
          const SizedBox(width: 8),
          Text(
            'Listening to options...',
            style: TextStyle(
              color: Colors.blue[600],
              fontSize: 12,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  /// Build responsive choice layout
  Widget _buildChoiceLayout() {
    final screenSize = MediaQuery.of(context).size;
    final isSmallScreen = screenSize.width < 600;
    final choices = widget.scene.choices!;

    if (isSmallScreen || choices.length > 2) {
      // Vertical layout for small screens or many choices
      return Column(
        children: choices.asMap().entries.map((entry) {
          final index = entry.key;
          final choice = entry.value;
          return _buildChoiceCard(choice, index, isHorizontal: false);
        }).toList(),
      );
    } else {
      // Horizontal layout for larger screens with 2 choices
      return Row(
        children: choices.asMap().entries.map((entry) {
          final index = entry.key;
          final choice = entry.value;
          return Expanded(
            child: Padding(
              padding: EdgeInsets.symmetric(horizontal: index == 0 ? 0 : 8),
              child: _buildChoiceCard(choice, index, isHorizontal: true),
            ),
          );
        }).toList(),
      );
    }
  }

  /// Build individual choice card with scene icon
  Widget _buildChoiceCard(ChoiceOptionModel choice, int index, {required bool isHorizontal}) {
    final isCurrentChoice = _currentChoiceIndex == index;
    final isNarrating = _isNarrating && isCurrentChoice;

    return Container(
      width: double.infinity,
      margin: EdgeInsets.only(
        bottom: isHorizontal ? 0 : 12,
        right: isHorizontal && index == 0 ? 8 : 0,
        left: isHorizontal && index == 1 ? 8 : 0,
      ),
      constraints: BoxConstraints(
        minHeight: isHorizontal ? 120 : 80,
        maxHeight: isHorizontal ? 200 : 120,
      ),
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16),
          border: Border.all(
            color: isCurrentChoice ? Colors.deepPurple : Colors.grey[300]!,
            width: isCurrentChoice ? 3 : 1,
          ),
          boxShadow: isCurrentChoice
              ? [
                  BoxShadow(
                    color: Colors.deepPurple.withValues(alpha: 0.3),
                    blurRadius: 12,
                    offset: const Offset(0, 4),
                  ),
                ]
              : [
                  BoxShadow(
                    color: Colors.grey.withValues(alpha: 0.1),
                    blurRadius: 4,
                    offset: const Offset(0, 2),
                  ),
                ],
        ),
        child: Material(
          color: Colors.transparent,
          child: InkWell(
            onTap: _hasNarratedIntro && !_isNarrating
                ? () => _selectChoice(choice, index)
                : null,
            borderRadius: BorderRadius.circular(16),
            child: Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: isCurrentChoice ? Colors.deepPurple[50] : Colors.white,
                borderRadius: BorderRadius.circular(16),
              ),
              child: isHorizontal
                  ? _buildHorizontalChoiceContent(choice, index, isCurrentChoice, isNarrating)
                  : _buildVerticalChoiceContent(choice, index, isCurrentChoice, isNarrating),
            ),
          ),
        ),
      ),
    );
  }

  /// Build horizontal choice content (side-by-side layout)
  Widget _buildHorizontalChoiceContent(ChoiceOptionModel choice, int index, bool isCurrentChoice, bool isNarrating) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        // Scene icon
        Expanded(
          flex: 3,
          child: _buildSceneIcon(choice, isCurrentChoice),
        ),

        const SizedBox(height: 12),

        // Choice text
        Expanded(
          flex: 2,
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                choice.option,
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: isCurrentChoice ? FontWeight.w600 : FontWeight.w500,
                  color: isCurrentChoice ? Colors.deepPurple : Colors.grey[800],
                ),
                textAlign: TextAlign.center,
                maxLines: 3,
                overflow: TextOverflow.ellipsis,
              ),

              const SizedBox(height: 8),

              // Choice indicator
              _buildChoiceIndicator(index, isCurrentChoice, isNarrating),
            ],
          ),
        ),
      ],
    );
  }

  /// Build vertical choice content (traditional layout)
  Widget _buildVerticalChoiceContent(ChoiceOptionModel choice, int index, bool isCurrentChoice, bool isNarrating) {
    return Row(
      children: [
        // Scene icon
        _buildSceneIcon(choice, isCurrentChoice, size: 48),

        const SizedBox(width: 16),

        // Choice text
        Expanded(
          child: Text(
            choice.option,
            style: TextStyle(
              fontSize: 16,
              fontWeight: isCurrentChoice ? FontWeight.w600 : FontWeight.normal,
              color: isCurrentChoice ? Colors.deepPurple : Colors.grey[800],
            ),
            textAlign: TextAlign.left,
            maxLines: 3,
            overflow: TextOverflow.ellipsis,
          ),
        ),

        const SizedBox(width: 12),

        // Choice indicator
        _buildChoiceIndicator(index, isCurrentChoice, isNarrating),
      ],
    );
  }

  /// Build scene icon with fallback
  Widget _buildSceneIcon(ChoiceOptionModel choice, bool isCurrentChoice, {double? size}) {
    final iconSize = size ?? 64;

    return Container(
      width: iconSize,
      height: iconSize,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: isCurrentChoice ? Colors.deepPurple : Colors.grey[300]!,
          width: 2,
        ),
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(10),
        child: Image.asset(
          choice.getVisualPath(widget.scene.id.split('_')[0]), // Extract story ID
          fit: BoxFit.cover,
          errorBuilder: (context, error, stackTrace) {
            return Container(
              color: Colors.grey[100],
              child: Icon(
                Icons.image_outlined,
                size: iconSize * 0.4,
                color: Colors.grey[400],
              ),
            );
          },
        ),
      ),
    );
  }

  /// Build choice indicator (number or loading)
  Widget _buildChoiceIndicator(int index, bool isCurrentChoice, bool isNarrating) {
    return Container(
      width: 32,
      height: 32,
      decoration: BoxDecoration(
        color: isCurrentChoice ? Colors.deepPurple : Colors.grey[300],
        shape: BoxShape.circle,
      ),
      child: Center(
        child: isNarrating
            ? const SizedBox(
                width: 16,
                height: 16,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                ),
              )
            : Text(
                '${index + 1}',
                style: TextStyle(
                  color: isCurrentChoice ? Colors.white : Colors.grey[600],
                  fontWeight: FontWeight.bold,
                  fontSize: 14,
                ),
              ),
      ),
    );
  }

  /// Build instruction text
  Widget _buildInstructions() {
    String instructionText;
    if (!_hasNarratedIntro) {
      instructionText = 'Listen to the narrator describe your options...';
    } else if (!_isNarrating) {
      instructionText = 'Tap on your choice to continue the story';
    } else {
      return const SizedBox.shrink();
    }

    return Text(
      instructionText,
      style: TextStyle(
        color: Colors.grey[600],
        fontSize: 14,
        fontStyle: FontStyle.italic,
      ),
      textAlign: TextAlign.center,
    );
  }
}
