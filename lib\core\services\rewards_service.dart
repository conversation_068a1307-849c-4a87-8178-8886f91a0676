import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:logger/logger.dart';
import 'package:choice_once_upon_a_time/models/rewards_model.dart';

/// Service for tracking and managing user rewards
class RewardsService {
  static final RewardsService _instance = RewardsService._internal();
  factory RewardsService() => _instance;
  RewardsService._internal();

  final Logger _logger = Logger();
  static const String _rewardsKey = 'earned_rewards';
  static const String _rewardCountsKey = 'reward_counts';

  /// Awards a completion reward for finishing a story
  /// 
  /// [storyId] - The ID of the completed story
  /// [rewardId] - The reward identifier/name
  /// 
  /// Returns the earned reward object
  Future<EarnedReward> awardCompletionReward(String storyId, String rewardId) async {
    try {
      _logger.i('[RewardsService] Awarding completion reward: $rewardId for story: $storyId');

      final reward = EarnedReward(
        rewardId: rewardId,
        rewardType: 'completion',
        earnedAt: DateTime.now(),
        storyId: storyId,
      );

      await _saveEarnedReward(reward);
      await _incrementRewardCount(rewardId);

      _logger.i('[RewardsService] Successfully awarded completion reward: $rewardId');
      return reward;

    } catch (e) {
      _logger.e('[RewardsService] Failed to award completion reward: $e');
      throw RewardsException('Failed to award completion reward', details: e.toString());
    }
  }

  /// Awards a moral choice reward for making a good choice
  /// 
  /// [storyId] - The ID of the story
  /// [sceneId] - The ID of the scene where the choice was made
  /// [rewardId] - The reward identifier/name
  /// 
  /// Returns the earned reward object
  Future<EarnedReward> awardMoralChoiceReward(
    String storyId,
    String sceneId,
    String rewardId,
  ) async {
    try {
      _logger.i('[RewardsService] Awarding moral choice reward: $rewardId for story: $storyId, scene: $sceneId');

      final reward = EarnedReward(
        rewardId: rewardId,
        rewardType: 'moral_choice',
        earnedAt: DateTime.now(),
        storyId: storyId,
        sceneId: sceneId,
      );

      await _saveEarnedReward(reward);
      await _incrementRewardCount(rewardId);

      _logger.i('[RewardsService] Successfully awarded moral choice reward: $rewardId');
      return reward;

    } catch (e) {
      _logger.e('[RewardsService] Failed to award moral choice reward: $e');
      throw RewardsException('Failed to award moral choice reward', details: e.toString());
    }
  }

  /// Gets all earned rewards for a user
  /// 
  /// Returns a list of all earned rewards
  Future<List<EarnedReward>> getAllEarnedRewards() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final rewardsJson = prefs.getStringList(_rewardsKey) ?? [];

      final rewards = <EarnedReward>[];
      for (final rewardJson in rewardsJson) {
        try {
          final rewardData = jsonDecode(rewardJson) as Map<String, dynamic>;
          rewards.add(EarnedReward.fromJson(rewardData));
        } catch (e) {
          _logger.w('[RewardsService] Failed to parse reward JSON: $e');
        }
      }

      _logger.d('[RewardsService] Retrieved ${rewards.length} earned rewards');
      return rewards;

    } catch (e) {
      _logger.e('[RewardsService] Failed to get earned rewards: $e');
      return [];
    }
  }

  /// Gets earned rewards for a specific story
  /// 
  /// [storyId] - The ID of the story
  /// 
  /// Returns a list of rewards earned for the story
  Future<List<EarnedReward>> getRewardsForStory(String storyId) async {
    final allRewards = await getAllEarnedRewards();
    return allRewards.where((reward) => reward.storyId == storyId).toList();
  }

  /// Gets the count of how many times a specific reward has been earned
  /// 
  /// [rewardId] - The reward identifier
  /// 
  /// Returns the count of times the reward has been earned
  Future<int> getRewardCount(String rewardId) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final countsJson = prefs.getString(_rewardCountsKey);
      
      if (countsJson == null) return 0;

      final counts = jsonDecode(countsJson) as Map<String, dynamic>;
      return counts[rewardId] ?? 0;

    } catch (e) {
      _logger.e('[RewardsService] Failed to get reward count for $rewardId: $e');
      return 0;
    }
  }

  /// Gets all reward counts
  /// 
  /// Returns a map of reward IDs to their counts
  Future<Map<String, int>> getAllRewardCounts() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final countsJson = prefs.getString(_rewardCountsKey);
      
      if (countsJson == null) return {};

      final counts = jsonDecode(countsJson) as Map<String, dynamic>;
      return counts.map((key, value) => MapEntry(key, value as int));

    } catch (e) {
      _logger.e('[RewardsService] Failed to get all reward counts: $e');
      return {};
    }
  }

  /// Checks if a specific reward has been earned for a story
  /// 
  /// [storyId] - The ID of the story
  /// [rewardId] - The reward identifier
  /// 
  /// Returns true if the reward has been earned for the story
  Future<bool> hasEarnedReward(String storyId, String rewardId) async {
    final storyRewards = await getRewardsForStory(storyId);
    return storyRewards.any((reward) => reward.rewardId == rewardId);
  }

  /// Checks if the completion reward has been earned for a story
  /// 
  /// [storyId] - The ID of the story
  /// 
  /// Returns true if the completion reward has been earned
  Future<bool> hasCompletedStory(String storyId) async {
    final storyRewards = await getRewardsForStory(storyId);
    return storyRewards.any((reward) => reward.rewardType == 'completion');
  }

  /// Gets unique reward types that have been earned
  /// 
  /// Returns a list of unique reward IDs
  Future<List<String>> getUniqueEarnedRewards() async {
    final allRewards = await getAllEarnedRewards();
    final uniqueRewards = <String>{};
    
    for (final reward in allRewards) {
      uniqueRewards.add(reward.rewardId);
    }

    return uniqueRewards.toList();
  }

  /// Clears all earned rewards (for testing or reset purposes)
  Future<void> clearAllRewards() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_rewardsKey);
      await prefs.remove(_rewardCountsKey);
      
      _logger.i('[RewardsService] Cleared all rewards');

    } catch (e) {
      _logger.e('[RewardsService] Failed to clear rewards: $e');
      throw RewardsException('Failed to clear rewards', details: e.toString());
    }
  }

  /// Saves an earned reward to persistent storage
  Future<void> _saveEarnedReward(EarnedReward reward) async {
    final prefs = await SharedPreferences.getInstance();
    final rewardsJson = prefs.getStringList(_rewardsKey) ?? [];
    
    rewardsJson.add(jsonEncode(reward.toJson()));
    await prefs.setStringList(_rewardsKey, rewardsJson);
  }

  /// Increments the count for a specific reward
  Future<void> _incrementRewardCount(String rewardId) async {
    final prefs = await SharedPreferences.getInstance();
    final countsJson = prefs.getString(_rewardCountsKey);
    
    Map<String, dynamic> counts = {};
    if (countsJson != null) {
      counts = jsonDecode(countsJson) as Map<String, dynamic>;
    }

    counts[rewardId] = (counts[rewardId] ?? 0) + 1;
    await prefs.setString(_rewardCountsKey, jsonEncode(counts));
  }
}

/// Custom exception for rewards operations
class RewardsException implements Exception {
  final String message;
  final String? details;

  const RewardsException(this.message, {this.details});

  @override
  String toString() {
    if (details != null) {
      return 'RewardsException: $message\nDetails: $details';
    }
    return 'RewardsException: $message';
  }
}
