# MVP Development Sprints for "Choice: Once Upon A Time" (Phase 3)

**Overarching Principles for All Sprints:**
* **TDD is King:** The "Detailed Technical Design Document" (your Task 2.7 document) is the primary blueprint. All development must adhere to its specifications.
* **Modularity:** Build reusable widgets and services as outlined in the TDD (Section 3.1).
* **Responsiveness:** Ensure UI adapts to various screen sizes using Flutter's responsive tools.
* **Testability:** Write code that is easy to test. Develop unit and widget tests for core logic and components as you go (TDD Section 10).
* **Version Control:** Use Git for all code, with frequent, meaningful commits.
* **Regular Internal Reviews:** At the end of each sprint or major feature block, conduct internal team reviews against the TDD and UI/UX specifications.

---

## Sprint 0: Project Setup & Core Architectural Foundations

* **Goal:** Initialize the Flutter project and implement the core architectural elements as defined in the TDD, setting a solid base for feature development.
* **Key Tasks & Activities:**
    1.  **Initialize Flutter Project:** Use the latest stable Flutter version.
    2.  **Implement Folder Structure:** Set up the feature-first folder structure (`app/`, `core/`, `features/`, `shared_widgets/`, `models/`, `l10n/`) as detailed in TDD Section 3.1.
    3.  **Core App Setup (`app/` directory):**
        * Create `main.dart` and the main `AppWidget` (`app_widget.dart`).
        * Initialize essential app-wide services (placeholder for Riverpod providers).
    4.  **Implement Global Theme:** Create `theme.dart` and define the global `ThemeData` based on the Art Style Definition (Task 2.3 Part 1, especially colors and typography). Refer to TDD Section 3.2.
    5.  **Setup Navigation (GoRouter):**
        * Integrate the `go_router` package.
        * Create the `AppRouter` class (`app/routing/app_router.dart`) and define initial top-level routes for placeholder screens (e.g., `/launch`, `/home`, `/parent_gate_entry`) as per TDD Section 3.4.
    6.  **Develop Initial Shared Widgets (`shared_widgets/`):**
        * Implement 1-2 very basic shared widgets identified in TDD Section 3.2 (e.g., `PrimaryButtonWidget`, `LoadingIndicatorWidget`) with styling from Task 2.3.
    7.  **Setup Basic Unit Tests:** Write initial unit tests for any core utility functions or constants created.
* **Primary TDD References:** Sections 3.1, 3.2, 3.4.
* **Key Supporting Documents:** Task 2.3 (Art Style Definition).
* **Deliverables:**
    * Initialized Flutter project with correct folder structure.
    * Basic `AppWidget` with theme and GoRouter setup.
    * A few core shared widgets.
    * Initial unit tests structure.

---

## Sprint 1: Data Modeling, Firebase Setup & Core Screen Shells

* **Goal:** Define all data structures, set up the Firebase backend, and implement the UI shells for primary navigation screens and initial app flow.
* **Key Tasks & Activities:**
    1.  **Implement Dart Data Models (`models/`):**
        * Create all Dart classes for `StoryMetadataModel`, `StoryModel`, `SceneModel`, `TextSegmentModel`, `ChoiceModel`, `BackgroundMusicConfig`, `SoundEffectConfig`, `UserModel`, `DownloadedStoryEntry` (for Isar), etc., as per TDD Sections 3.5 and 5.1.
        * Include `fromJson` factory constructors. Write unit tests for all model parsing logic.
    2.  **Setup Firebase Project (BaaS):**
        * Configure Firebase Authentication (Anonymous and Email/Password) (TDD Section 4.3).
        * Set up Cloud Firestore with initial collections (`stories`, `users`, `app_config`) and security rules (TDD Sections 4.3, 5.1).
        * Set up Firebase Cloud Storage with bucket structure and access rules (TDD Section 4.3, 6.1).
    3.  **Implement Core Services Shells (`core/` & Riverpod Setup):**
        * Set up Riverpod for DI as per TDD Section 3.1 & 3.3.
        * Create basic `StoryRepository` (in `features/story_library/data/`) with methods to fetch (initially mocked or hardcoded) `StoryMetadataModel` list.
        * Basic `OfflineStorageService` (`core/storage/`) with Isar initialization and schema for `DownloadedStoryEntry`.
        * Basic `TTSService` shell (`core/audio/`) with `flutter_tts` package integrated, and placeholder methods for `initialize`, `setLanguage`, `speakSegment` as per TDD Section 3.6.
        * Basic `SoundEffectPlayerService` shell.
    4.  **Develop UI Shells (from "Task 2.2 update" & TDD Section 3.2):**
        * Screen 1: `AppLaunchScreen` (with auto-transition logic).
        * Screen 4.1: `FTUEScreen` shell (logic to show on first launch).
        * Screen 2: `StoryLibraryScreen` (UI shell with scrollable grid for `StoryCoverCardWidget`s; fetch data via `StoryLibraryProvider`).
        * Screen 9: `ParentalGateScreen` (with specified interaction).
        * Screen 10: `ParentZoneDashboardScreen` shell (with navigable items leading to placeholder screens).
* **Primary TDD References:** Sections 3.1, 3.2, 3.3, 3.4, 3.5, 3.6, 3.7, 4, 5.
* **Key Supporting Documents:** Task 2.2 update (Screen Specifications), Task 2.3 (Art Style Definition), Story JSONs (for model reference).
* **Deliverables:**
    * All Dart data models implemented and tested.
    * Firebase project configured.
    * Core services shells integrated with Riverpod.
    * UI shells for App Launch, FTUE, Story Library, Parental Gate, and Parent Zone Dashboard.

---

## Sprint 2: Core Story Player - Narrative Display & Branching Logic

* **Goal:** Implement the core story playing experience, including dynamic display of narrator text and visuals, and functional branching choices based on story JSONs.
* **Key Tasks & Activities:**
    1.  **Story Loading & Parsing:**
        * Enhance `StoryRepository` and `StoryPlayerProvider` (TDD Section 3.3, 3.5) to load a full `StoryModel` (e.g., `story_01.json`) from assets (or Firestore if basic fetch is ready) and parse it into Dart objects.
    2.  **Implement `StoryPlayerScreen` (Screens 5 & 6 from "Task 2.2 update"):**
        * Display current scene's background image (placeholder logic: white background + `sceneId` text if image not found, using asset paths from Task 2.3).
        * Display `narratorSegments[].text` using `NarratorTextDisplayWidget` (TDD Section 3.2). Display `emotionCue` text alongside for reference.
    3.  **Implement Branching Narrative Engine (TDD Section 3.5):**
        * When a `SceneModel` of `sceneType == "choice_point"` is active, render its `choices` list using `ChoiceButtonWidget`s (TDD Section 3.2).
        * Implement logic in `StoryPlayerProvider`: when a `ChoiceButtonWidget` is tapped, retrieve its `leadsToSceneId` and update the `currentScene`.
    4.  **Linear Scene Progression:** For `sceneType == "narration_illustration"`, after all narrator segments are notionally "played" (e.g., after manual "next segment" tap for now), transition to `nextSceneId` if present (TDD Section 3.5).
    5.  **Implement Narration Controls UI (`NarrationControlsWidget` - TDD Section 3.2 [cite: 69]):**
        * Add UI buttons for Pause/Play, Replay Segment. These buttons will call placeholder methods in `TTSService` via `StoryPlayerProvider` for now.
    6.  **Implement Story Intro/Splash Screen (Screen 3) and Loading Screen (Screen 4) functionality leading to the `StoryPlayerScreen`.**
* **Primary TDD References:** Sections 3.2, 3.3, 3.5, 3.6.
* **Key Supporting Documents:** Task 2.2 update (Screens 3, 4, 5, 6), Task 2.3 (Asset Naming for Placeholders), Story JSON files.
* **Deliverables:**
    * Ability to select and launch a story.
    * `StoryPlayerScreen` displaying narrator text and scene background placeholders.
    * Functional choice points navigating to correct story branches.
    * Placeholder narrator controls UI.

---

## Sprint 3: Empathetic Narrator (On-Device TTS - MVP) & UI Sounds

* **Goal:** Integrate on-device TTS for the empathetic narrator with basic emotional modulation and implement core UI sounds.
* **Key Tasks & Activities:**
    1.  **Full `TTSService` Implementation (TDD Section 3.6):**
        * Complete `flutter_tts` integration: initialization, voice selection (guiding user if voice data missing - Screen 15), language setting.
        * Implement logic to translate `emotionCue` from `TextSegmentModel` into `flutter_tts` parameters (pitch, rate, volume) or SSML.
        * Connect `NarrationControlsWidget` (Pause/Play, Replay) to actual `TTSService` methods.
        * Implement text highlighting synced with TTS progress (if feasible for MVP).
    2.  **Integrate `TTSService` with `StoryPlayerProvider`:** For managing TTS lifecycle per narrator segment.
    3.  **Implement UI Sounds:**
        * Integrate `SoundEffectPlayerService`.
        * Add gentle UI sounds for button taps, choice selections, screen transitions as per Sound Design Concept (Task 2.4) and TDD Section 3.8[cite: 169]. Use asset paths from Task 2.3.
    4.  **Implement In-Story UI Screens (from "Task 2.2 update"):**
        * Screen 7: `In-Story Pause Screen/Menu`.
        * Screen 8: `Story End Screen` shell (placeholder for narrator-led moral discussion).
        * Screen 8.1: `"Are You Still There?" Idle Prompt`.
* **Primary TDD References:** Sections 3.6, 3.8.
* **Key Supporting Documents:** Task 2.2 update (Screens 5, 6, 7, 8, 8.1, 15), Task 2.4 (Sound Design Concept).
* **Deliverables:**
    * Narrator text spoken via on-device TTS with basic emotional modulation.
    * Functional narrator controls.
    * Basic UI sounds implemented.
    * Pause, End, and Idle Prompt screens functional.

---

## Sprint 4: Full Story Integration, Offline Functionality & Core Popups

* **Goal:** Ensure all three initial stories are fully playable with narration, implement core offline capabilities, and develop essential notification popups.
* **Key Tasks & Activities:**
    1.  **Full Story Integration:**
        * Ensure `story_01.json`, `story_02.json`, and `story_03.json` are correctly parsed and all branches are playable end-to-end with TTS narration and choice logic.
        * Refine `emotionCue` mapping to TTS parameters based on testing.
    2.  **Implement `OfflineStorageService` Download & Access Logic (TDD Section 3.7):**
        * Implement `downloadStory` logic: fetch story JSON and asset manifest (from Firebase Storage as per TDD Section 6.1), download binary assets (image placeholders using correct paths for now, actual files can be swapped later), store locally using Isar and file system.
        * Implement `isStoryDownloaded` and logic in `StoryRepository` to serve local content.
        * Add UI elements (e.g., download button/indicator on `StoryCoverCardWidget`) to trigger downloads and show progress.
    3.  **Implement Key Popups (from "Task 2.2 update"):**
        * Screen 13: `ExitStoryConfirmationPopup` (with narrator voice prompt if designed).
        * Screen 14: `OfflineNotificationPopup`.
        * Screen 19: `ContentUpdateAvailableNotification/Popup` shell (badge on story, basic popup logic, actual update mechanism later).
* **Primary TDD References:** Sections 3.7, 4.3, 5, 6.
* **Key Supporting Documents:** Task 2.2 update (Popups), Story JSONs.
* **Deliverables:**
    * All 3 initial stories fully playable with narration and branching.
    * Functional story download for offline access (story data + asset path references).
    * Core notification popups implemented.

---

## Sprint 5: Parent Zone Basics & Monetization Shell

* **Goal:** Implement essential Parent Zone screens for settings and the UI shell for the monetization flow.
* **Key Tasks & Activities:**
    1.  **Implement Parent Zone - Sound Settings (Screen 11 from "Task 2.2 update"):**
        * Allow parents to control master volume, background music (placeholder toggle), UI SFX (toggle).
        * Persist settings using `SettingsProvider` (Riverpod) and `OfflineStorageService` (Isar).
    2.  **Implement Parent Zone Informational Screens (Shells with placeholder text from "Task 2.2 update"):**
        * Screen 12.1: "About Stories & Moral Values".
        * Screen 12.2: "Manage Downloads / Storage" (UI shell; robust delete functionality to be tested in next sprint).
        * Screen 12.3: "Help & Support / FAQ".
    3.  **Implement Monetization UI Shell (Screen 12 from "Task 2.2 update"):**
        * Develop the UI for the Subscription/Upgrade screen.
        * Integrate `in_app_purchase` package to fetch product details from app stores (no live transactions yet, just display of plans as per TDD Section 7.3).
        * "Restore Purchases" button UI.
    4.  **Implement Remaining Critical Popups (from "Task 2.2 update"):**
        * Screen 16: `"Delete Story?" Confirmation Popup`.
        * Screen 17: `"Storage Full" Warning Popup`.
    5.  **Implement Calm Exit Screen (Screen 20 from "Task 2.2 update"):** With final narrator goodnight.
    6.  **Basic Parent Authentication (TDD Section 4.3):** If required for persisting settings across devices or for IAP shell functionality, implement basic Firebase Email/Password auth for the Parent Zone.
* **Primary TDD References:** TDD Sections relevant to Parent Zone, Monetization, IAP integration.
* **Key Supporting Documents:** Task 2.2 update (Screens 11, 12, 12.1-12.3, 16, 17, 20), Task 1.8 (Monetization Strategy).
* **Deliverables:**
    * Functional sound settings in Parent Zone.
    * UI shells for other key Parent Zone screens.
    * Subscription screen displaying fetched product details.
    * Remaining critical popups implemented.
    * Calm Exit Screen.

---

## Sprint 6: MVP Polish, Initial Asset Integration & Thorough Internal QA Focus

* **Goal:** Polish the UI of all MVP screens, integrate any available final/near-final visual and audio assets, and conduct comprehensive internal testing to prepare for formal QA and beta testing.
* **Key Tasks & Activities:**
    1.  **UI Polish & Responsiveness Checks:** Review all implemented MVP screens against the Art Style Guide (Task 2.3) and UI/UX Specifications (Task 2.2 update) for visual consistency, pixel-perfect alignment (where applicable), and responsiveness on various device sizes/orientations. Refine animations and transitions.
    2.  **Initial Asset Integration:**
        * Replace image placeholders (`white background + sceneId`) with any completed art assets (from Task 2.3 asset list) for the 3 stories and core UI. Ensure correct paths based on naming conventions (TDD Section 6.1).
        * Integrate any finalized UI sounds or placeholder background music loops (Task 2.4, TDD Section 6).
    3.  **Thorough Internal Functional & Narrative Testing (TDD Section 10):**
        * Systematically test all user flows outlined in "Task 2.2 update."
        * Verify all branching logic in the 3 stories, ensuring choices lead to correct paths and outcomes.
        * Rigorously test on-device TTS narration: clarity, (attempted) emotional delivery for various `emotionCue`s, synchronization, and controls.
        * Test offline functionality: story downloading, playing downloaded stories while offline, storage management UI.
        * Test parental gate and all implemented parent settings.
        * Test all popups and error states.
    4.  **Usability Review:** Internal team members conduct usability walkthroughs from child and parent perspectives.
    5.  **Bug Fixing:** Address all critical and major bugs found during internal QA. Document findings.
    6.  **Code Review & Refactoring:** Review key parts of the codebase for clarity, efficiency, adherence to TDD, modularity, and testability.
    7.  **Unit and Widget Testing Push:** Ensure good test coverage for core logic and UI components.
* **Primary TDD References:** Entire TDD, especially Sections 3 (Frontend), 6 (Asset Management), 9 (Performance), 10 (Testing).
* **Key Supporting Documents:** Task 2.2 update, Task 2.3, Task 2.4, Story JSONs.
* **Deliverables:**
    * A feature-complete MVP candidate build for the initial 3 stories, with polished UI and initial assets integrated.
    * Internal QA test logs and bug fix reports.
    * Source code reviewed and refined.
    * App ready for the next phase: Phase 4 - Testing & Refinement (including external Beta Testing).

---

This sprint plan provides a structured approach to developing your MVP. Remember that sprint goals can be adjusted based on team velocity and any challenges encountered. Good luck!