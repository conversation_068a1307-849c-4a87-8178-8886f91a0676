import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:choice_once_upon_a_time/core/utils/app_logger.dart';
import 'package:choice_once_upon_a_time/features/parent_zone/data/user_profile_service.dart';
import 'package:choice_once_upon_a_time/features/parent_zone/presentation/widgets/create_profile_dialog.dart';
import 'package:choice_once_upon_a_time/core/services/firebase_user_service.dart';

/// Screen for managing user profiles in the parent zone
class UserProfilesScreen extends ConsumerStatefulWidget {
  const UserProfilesScreen({super.key});

  @override
  ConsumerState<UserProfilesScreen> createState() => _UserProfilesScreenState();
}

class _UserProfilesScreenState extends ConsumerState<UserProfilesScreen> {
  bool _isLoading = false;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final screenSize = MediaQuery.of(context).size;
    final isSmallScreen = screenSize.width < 600;
    final profilesAsync = ref.watch(userProfilesProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('User Profiles'),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => context.pop(),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: () => _showCreateProfileDialog(context),
            tooltip: 'Add new profile',
          ),
        ],
      ),
      body: Column(
        children: [
          // Header section
          Container(
            margin: const EdgeInsets.all(16),
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: theme.colorScheme.primaryContainer.withValues(alpha: 0.3),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(
                      Icons.people,
                      color: theme.colorScheme.primary,
                      size: 24,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      'Family Profiles',
                      style: theme.textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: theme.colorScheme.primary,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                Text(
                  'Manage reading profiles for each child in your family.',
                  style: theme.textTheme.bodyMedium?.copyWith(
                    color: theme.colorScheme.onSurfaceVariant,
                  ),
                ),
              ],
            ),
          ),

          // Profiles list
          Expanded(
            child: profilesAsync.when(
              data: (profiles) => profiles.isEmpty
                  ? _buildEmptyState(theme)
                  : ListView.builder(
                      padding: const EdgeInsets.symmetric(horizontal: 16),
                      itemCount: profiles.length,
                      itemBuilder: (context, index) {
                        final profile = profiles[index];
                        return _buildProfileCard(theme, profile, isSmallScreen);
                      },
                    ),
              loading: () => const Center(child: CircularProgressIndicator()),
              error: (error, stack) => Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(Icons.error, size: 48, color: Colors.red[400]),
                    const SizedBox(height: 16),
                    Text('Error loading profiles: $error'),
                    const SizedBox(height: 16),
                    ElevatedButton(
                      onPressed: () => ref.refresh(userProfilesProvider),
                      child: const Text('Retry'),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState(ThemeData theme) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.person_add_outlined,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            'No Profiles Yet',
            style: theme.textTheme.titleLarge?.copyWith(
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Create a profile for each child to track their reading progress.',
            style: theme.textTheme.bodyMedium?.copyWith(
              color: Colors.grey[500],
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: () => _showCreateProfileDialog(context),
            icon: const Icon(Icons.add),
            label: const Text('Create First Profile'),
          ),
        ],
      ),
    );
  }

  Widget _buildProfileCard(ThemeData theme, UserProfile profile, bool isSmallScreen) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      child: Card(
        elevation: 2,
        child: InkWell(
          onTap: () => _viewProfileDetails(profile),
          borderRadius: BorderRadius.circular(12),
          child: Padding(
            padding: EdgeInsets.all(isSmallScreen ? 12 : 16),
            child: Row(
              children: [
                // Avatar
                CircleAvatar(
                  radius: isSmallScreen ? 24 : 30,
                  backgroundColor: profile.avatarColor.withValues(alpha: 0.2),
                  child: Text(
                    profile.name.substring(0, 1).toUpperCase(),
                    style: TextStyle(
                      fontSize: isSmallScreen ? 18 : 22,
                      fontWeight: FontWeight.bold,
                      color: profile.avatarColor,
                    ),
                  ),
                ),
                SizedBox(width: isSmallScreen ? 12 : 16),

                // Profile info
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        profile.name,
                        style: theme.textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                          fontSize: isSmallScreen ? 14 : 16,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        'Age ${profile.age}',
                        style: theme.textTheme.bodySmall?.copyWith(
                          color: theme.colorScheme.onSurfaceVariant,
                          fontSize: isSmallScreen ? 11 : 12,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Row(
                        children: [
                          Icon(
                            Icons.auto_stories,
                            size: isSmallScreen ? 14 : 16,
                            color: theme.colorScheme.primary,
                          ),
                          const SizedBox(width: 4),
                          Text(
                            '${profile.storiesCompleted} stories',
                            style: theme.textTheme.bodySmall?.copyWith(
                              fontSize: isSmallScreen ? 10 : 11,
                            ),
                          ),
                          const SizedBox(width: 16),
                          Icon(
                            Icons.access_time,
                            size: isSmallScreen ? 14 : 16,
                            color: theme.colorScheme.primary,
                          ),
                          const SizedBox(width: 4),
                          Text(
                            '${profile.totalReadingTime}min',
                            style: theme.textTheme.bodySmall?.copyWith(
                              fontSize: isSmallScreen ? 10 : 11,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),

                // Actions
                PopupMenuButton<String>(
                  onSelected: (value) => _handleProfileAction(value, profile),
                  itemBuilder: (context) => [
                    const PopupMenuItem(
                      value: 'edit',
                      child: Row(
                        children: [
                          Icon(Icons.edit, size: 18),
                          SizedBox(width: 8),
                          Text('Edit'),
                        ],
                      ),
                    ),
                    const PopupMenuItem(
                      value: 'delete',
                      child: Row(
                        children: [
                          Icon(Icons.delete, size: 18, color: Colors.red),
                          SizedBox(width: 8),
                          Text('Delete', style: TextStyle(color: Colors.red)),
                        ],
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  void _showCreateProfileDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => CreateProfileDialog(
        onProfileCreated: (profile) async {
          setState(() {
            _isLoading = true;
          });

          final service = ref.read(userProfileServiceProvider);
          final firebaseService = ref.read(firebaseUserServiceProvider);

          // Save to local storage first
          final localSuccess = await service.createProfile(profile);

          // Also save to Firebase if local save was successful
          bool firebaseSuccess = false;
          if (localSuccess) {
            firebaseSuccess = await firebaseService.createChildProfile(profile);
            if (!firebaseSuccess) {
              AppLogger.warning('UserProfiles: Failed to sync profile to Firebase');
            }
          }

          if (localSuccess) {
            ref.refresh(userProfilesProvider);
            AppLogger.info('UserProfiles: Created new profile for ${profile.name} (Firebase: $firebaseSuccess)');
            if (mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(content: Text('Profile created for ${profile.name}')),
              );
            }
          } else {
            if (mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('Failed to create profile. Name may already exist.')),
              );
            }
          }

          if (mounted) {
            setState(() {
              _isLoading = false;
            });
          }
        },
      ),
    );
  }

  void _viewProfileDetails(UserProfile profile) {
    AppLogger.debug('UserProfiles: Viewing details for ${profile.name}');
    // Navigate to profile details screen
    // context.go('/parent_zone/profiles/${profile.id}');
  }

  void _handleProfileAction(String action, UserProfile profile) {
    switch (action) {
      case 'edit':
        AppLogger.debug('UserProfiles: Editing profile ${profile.name}');
        // Show edit dialog
        break;
      case 'delete':
        _showDeleteConfirmation(profile);
        break;
    }
  }

  void _showDeleteConfirmation(UserProfile profile) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Profile'),
        content: Text('Are you sure you want to delete ${profile.name}\'s profile? This action cannot be undone.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.of(context).pop();

              setState(() {
                _isLoading = true;
              });

              final service = ref.read(userProfileServiceProvider);
              final firebaseService = ref.read(firebaseUserServiceProvider);

              // Delete from local storage first
              final localSuccess = await service.deleteProfile(profile.id);

              // Also delete from Firebase if local delete was successful
              bool firebaseSuccess = false;
              if (localSuccess) {
                firebaseSuccess = await firebaseService.deleteChildProfile(profile.id);
                if (!firebaseSuccess) {
                  AppLogger.warning('UserProfiles: Failed to delete profile from Firebase');
                }
              }

              if (localSuccess) {
                ref.refresh(userProfilesProvider);
                AppLogger.info('UserProfiles: Deleted profile for ${profile.name} (Firebase: $firebaseSuccess)');
                if (mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(content: Text('Profile deleted for ${profile.name}')),
                  );
                }
              } else {
                if (mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(content: Text('Failed to delete profile')),
                  );
                }
              }

              if (mounted) {
                setState(() {
                  _isLoading = false;
                });
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }
}

// UserProfile class is now imported from user_profile_service.dart
// CreateProfileDialog is now imported from create_profile_dialog.dart
