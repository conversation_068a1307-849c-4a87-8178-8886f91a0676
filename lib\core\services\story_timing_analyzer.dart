import 'dart:async';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:choice_once_upon_a_time/core/utils/app_logger.dart';

/// Service for analyzing and measuring story playback timing
/// Measures scene loading, narration duration, pauses, and transitions
class StoryTimingAnalyzer {
  static const String _logPrefix = 'STORY_TIMING';
  
  // Timing measurements
  final Map<String, SceneTimingData> _sceneTimings = {};
  final List<TimingEvent> _timingEvents = [];
  
  // Current timing state
  DateTime? _sceneStartTime;
  DateTime? _narrationStartTime;
  DateTime? _transitionStartTime;
  String? _currentSceneId;
  
  // Configuration for child-friendly timing
  static const double childFriendlyTTSSpeed = 0.35; // 0.3-0.4 as specified
  static const int minimumSceneDisplayMs = 5000; // 5 seconds minimum
  static const int sentencePauseMs = 1500; // 1.5 seconds between sentences
  static const int sceneTransitionMs = 800; // Scene transition animation
  
  /// Start timing analysis for a new scene
  void startSceneTiming(String sceneId) {
    _currentSceneId = sceneId;
    _sceneStartTime = DateTime.now();
    
    _addTimingEvent(TimingEventType.sceneLoadStart, sceneId);
    AppLogger.info('$_logPrefix: Started timing for scene: $sceneId');
  }
  
  /// Mark scene loading as complete
  void markSceneLoaded(String sceneId) {
    if (_sceneStartTime == null) return;
    
    final loadTime = DateTime.now().difference(_sceneStartTime!).inMilliseconds;
    _addTimingEvent(TimingEventType.sceneLoadComplete, sceneId, duration: loadTime);
    
    AppLogger.info('$_logPrefix: Scene $sceneId loaded in ${loadTime}ms');
  }
  
  /// Start narration timing
  void startNarrationTiming(String sceneId, String text) {
    _narrationStartTime = DateTime.now();
    _addTimingEvent(TimingEventType.narrationStart, sceneId, text: text);
    
    // Calculate expected narration duration based on text length and TTS speed
    final expectedDuration = _calculateExpectedNarrationDuration(text);
    AppLogger.info('$_logPrefix: Started narration for scene $sceneId, expected duration: ${expectedDuration}ms');
  }
  
  /// Mark narration as complete
  void markNarrationComplete(String sceneId) {
    if (_narrationStartTime == null) return;
    
    final narrationTime = DateTime.now().difference(_narrationStartTime!).inMilliseconds;
    _addTimingEvent(TimingEventType.narrationComplete, sceneId, duration: narrationTime);
    
    AppLogger.info('$_logPrefix: Narration for scene $sceneId completed in ${narrationTime}ms');
  }
  
  /// Start sentence pause timing
  void startSentencePause(String sceneId, int sentenceIndex) {
    _addTimingEvent(TimingEventType.sentencePauseStart, sceneId, sentenceIndex: sentenceIndex);
  }
  
  /// Mark sentence pause as complete
  void markSentencePauseComplete(String sceneId, int sentenceIndex) {
    _addTimingEvent(TimingEventType.sentencePauseComplete, sceneId, sentenceIndex: sentenceIndex);
  }
  
  /// Start scene transition timing
  void startSceneTransition(String fromSceneId, String toSceneId) {
    _transitionStartTime = DateTime.now();
    _addTimingEvent(TimingEventType.transitionStart, fromSceneId, targetScene: toSceneId);
  }
  
  /// Mark scene transition as complete
  void markSceneTransitionComplete(String fromSceneId, String toSceneId) {
    if (_transitionStartTime == null) return;
    
    final transitionTime = DateTime.now().difference(_transitionStartTime!).inMilliseconds;
    _addTimingEvent(TimingEventType.transitionComplete, fromSceneId, 
                   targetScene: toSceneId, duration: transitionTime);
    
    AppLogger.info('$_logPrefix: Transition from $fromSceneId to $toSceneId completed in ${transitionTime}ms');
  }
  
  /// Complete timing analysis for a scene
  void completeSceneTiming(String sceneId) {
    if (_sceneStartTime == null) return;
    
    final totalTime = DateTime.now().difference(_sceneStartTime!).inMilliseconds;
    
    // Calculate timing data for this scene
    final sceneEvents = _timingEvents.where((e) => e.sceneId == sceneId).toList();
    final timingData = _calculateSceneTimingData(sceneId, sceneEvents, totalTime);
    
    _sceneTimings[sceneId] = timingData;
    _addTimingEvent(TimingEventType.sceneComplete, sceneId, duration: totalTime);
    
    AppLogger.info('$_logPrefix: Scene $sceneId completed in ${totalTime}ms');
    _logSceneTimingAnalysis(timingData);
  }
  
  /// Calculate expected narration duration based on text and TTS speed
  int _calculateExpectedNarrationDuration(String text) {
    // Average reading speed for children: 100-150 words per minute
    // With TTS speed of 0.35, adjust accordingly
    final wordCount = text.split(' ').length;
    final wordsPerMinute = 120 * childFriendlyTTSSpeed; // Adjusted for TTS speed
    final expectedMinutes = wordCount / wordsPerMinute;
    return (expectedMinutes * 60 * 1000).round(); // Convert to milliseconds
  }
  
  /// Calculate comprehensive timing data for a scene
  SceneTimingData _calculateSceneTimingData(String sceneId, List<TimingEvent> events, int totalTime) {
    int? loadTime;
    int? narrationTime;
    int totalPauseTime = 0;
    int? transitionTime;
    
    for (int i = 0; i < events.length; i++) {
      final event = events[i];
      
      switch (event.type) {
        case TimingEventType.sceneLoadComplete:
          loadTime = event.duration;
          break;
        case TimingEventType.narrationComplete:
          narrationTime = event.duration;
          break;
        case TimingEventType.sentencePauseComplete:
          // Find corresponding pause start
          final pauseStart = events.lastWhere(
            (e) => e.type == TimingEventType.sentencePauseStart && 
                   e.sentenceIndex == event.sentenceIndex,
            orElse: () => event,
          );
          if (pauseStart != event) {
            totalPauseTime += event.timestamp.difference(pauseStart.timestamp).inMilliseconds;
          }
          break;
        case TimingEventType.transitionComplete:
          transitionTime = event.duration;
          break;
        default:
          break;
      }
    }
    
    return SceneTimingData(
      sceneId: sceneId,
      totalTime: totalTime,
      loadTime: loadTime ?? 0,
      narrationTime: narrationTime ?? 0,
      pauseTime: totalPauseTime,
      transitionTime: transitionTime ?? 0,
      events: events,
    );
  }
  
  /// Add a timing event
  void _addTimingEvent(
    TimingEventType type, 
    String sceneId, {
    int? duration,
    String? text,
    String? targetScene,
    int? sentenceIndex,
  }) {
    final event = TimingEvent(
      type: type,
      sceneId: sceneId,
      timestamp: DateTime.now(),
      duration: duration,
      text: text,
      targetScene: targetScene,
      sentenceIndex: sentenceIndex,
    );
    
    _timingEvents.add(event);
  }
  
  /// Log detailed timing analysis for a scene
  void _logSceneTimingAnalysis(SceneTimingData data) {
    AppLogger.info('$_logPrefix: === SCENE TIMING ANALYSIS ===');
    AppLogger.info('$_logPrefix: Scene ID: ${data.sceneId}');
    AppLogger.info('$_logPrefix: Total Time: ${data.totalTime}ms');
    AppLogger.info('$_logPrefix: Load Time: ${data.loadTime}ms');
    AppLogger.info('$_logPrefix: Narration Time: ${data.narrationTime}ms');
    AppLogger.info('$_logPrefix: Pause Time: ${data.pauseTime}ms');
    AppLogger.info('$_logPrefix: Transition Time: ${data.transitionTime}ms');
    
    // Check if timing meets child-friendly requirements
    _validateChildFriendlyTiming(data);
  }
  
  /// Validate if timing meets child-friendly requirements
  void _validateChildFriendlyTiming(SceneTimingData data) {
    final issues = <String>[];
    
    // Check minimum scene display time
    if (data.totalTime < minimumSceneDisplayMs) {
      issues.add('Scene display time (${data.totalTime}ms) is below minimum (${minimumSceneDisplayMs}ms)');
    }
    
    // Check if narration is too fast
    if (data.narrationTime > 0) {
      final effectiveSpeed = data.narrationTime / (data.narrationTime + data.pauseTime);
      if (effectiveSpeed > 0.7) { // More than 70% narration vs pause time
        issues.add('Narration may be too fast for children (${(effectiveSpeed * 100).toInt()}% narration vs pause time)');
      }
    }
    
    if (issues.isEmpty) {
      AppLogger.info('$_logPrefix: ✅ Scene timing meets child-friendly requirements');
    } else {
      AppLogger.warning('$_logPrefix: ⚠️ Child-friendly timing issues:');
      for (final issue in issues) {
        AppLogger.warning('$_logPrefix:   - $issue');
      }
    }
  }
  
  /// Get timing data for a specific scene
  SceneTimingData? getSceneTimingData(String sceneId) {
    return _sceneTimings[sceneId];
  }
  
  /// Get all timing data
  Map<String, SceneTimingData> getAllTimingData() {
    return Map.from(_sceneTimings);
  }
  
  /// Generate comprehensive timing report
  TimingReport generateTimingReport() {
    final allScenes = _sceneTimings.values.toList();
    
    if (allScenes.isEmpty) {
      return TimingReport.empty();
    }
    
    final totalStoryTime = allScenes.fold(0, (sum, scene) => sum + scene.totalTime);
    final averageSceneTime = totalStoryTime / allScenes.length;
    final averageLoadTime = allScenes.fold(0, (sum, scene) => sum + scene.loadTime) / allScenes.length;
    final averageNarrationTime = allScenes.fold(0, (sum, scene) => sum + scene.narrationTime) / allScenes.length;
    
    // Find performance bottlenecks
    final slowestScene = allScenes.reduce((a, b) => a.totalTime > b.totalTime ? a : b);
    final fastestScene = allScenes.reduce((a, b) => a.totalTime < b.totalTime ? a : b);
    
    return TimingReport(
      totalScenes: allScenes.length,
      totalStoryTime: totalStoryTime,
      averageSceneTime: averageSceneTime.round(),
      averageLoadTime: averageLoadTime.round(),
      averageNarrationTime: averageNarrationTime.round(),
      slowestScene: slowestScene,
      fastestScene: fastestScene,
      sceneTimings: allScenes,
      childFriendlyCompliance: _calculateChildFriendlyCompliance(allScenes),
    );
  }
  
  /// Calculate child-friendly compliance percentage
  double _calculateChildFriendlyCompliance(List<SceneTimingData> scenes) {
    if (scenes.isEmpty) return 0.0;
    
    int compliantScenes = 0;
    
    for (final scene in scenes) {
      bool isCompliant = true;
      
      // Check minimum display time
      if (scene.totalTime < minimumSceneDisplayMs) {
        isCompliant = false;
      }
      
      // Check narration pacing
      if (scene.narrationTime > 0) {
        final effectiveSpeed = scene.narrationTime / (scene.narrationTime + scene.pauseTime);
        if (effectiveSpeed > 0.7) {
          isCompliant = false;
        }
      }
      
      if (isCompliant) {
        compliantScenes++;
      }
    }
    
    return compliantScenes / scenes.length;
  }
  
  /// Clear all timing data
  void clearTimingData() {
    _sceneTimings.clear();
    _timingEvents.clear();
    _sceneStartTime = null;
    _narrationStartTime = null;
    _transitionStartTime = null;
    _currentSceneId = null;
    
    AppLogger.info('$_logPrefix: Timing data cleared');
  }
  
  /// Export timing data as JSON for analysis
  Map<String, dynamic> exportTimingData() {
    return {
      'sceneTimings': _sceneTimings.map((key, value) => MapEntry(key, value.toJson())),
      'timingEvents': _timingEvents.map((e) => e.toJson()).toList(),
      'configuration': {
        'childFriendlyTTSSpeed': childFriendlyTTSSpeed,
        'minimumSceneDisplayMs': minimumSceneDisplayMs,
        'sentencePauseMs': sentencePauseMs,
        'sceneTransitionMs': sceneTransitionMs,
      },
    };
  }
}

/// Types of timing events
enum TimingEventType {
  sceneLoadStart,
  sceneLoadComplete,
  narrationStart,
  narrationComplete,
  sentencePauseStart,
  sentencePauseComplete,
  transitionStart,
  transitionComplete,
  sceneComplete,
}

/// Individual timing event
class TimingEvent {
  final TimingEventType type;
  final String sceneId;
  final DateTime timestamp;
  final int? duration;
  final String? text;
  final String? targetScene;
  final int? sentenceIndex;

  const TimingEvent({
    required this.type,
    required this.sceneId,
    required this.timestamp,
    this.duration,
    this.text,
    this.targetScene,
    this.sentenceIndex,
  });

  Map<String, dynamic> toJson() {
    return {
      'type': type.toString(),
      'sceneId': sceneId,
      'timestamp': timestamp.toIso8601String(),
      'duration': duration,
      'text': text,
      'targetScene': targetScene,
      'sentenceIndex': sentenceIndex,
    };
  }
}

/// Comprehensive timing data for a scene
class SceneTimingData {
  final String sceneId;
  final int totalTime;
  final int loadTime;
  final int narrationTime;
  final int pauseTime;
  final int transitionTime;
  final List<TimingEvent> events;

  const SceneTimingData({
    required this.sceneId,
    required this.totalTime,
    required this.loadTime,
    required this.narrationTime,
    required this.pauseTime,
    required this.transitionTime,
    required this.events,
  });

  Map<String, dynamic> toJson() {
    return {
      'sceneId': sceneId,
      'totalTime': totalTime,
      'loadTime': loadTime,
      'narrationTime': narrationTime,
      'pauseTime': pauseTime,
      'transitionTime': transitionTime,
      'events': events.map((e) => e.toJson()).toList(),
    };
  }
}

/// Comprehensive timing report
class TimingReport {
  final int totalScenes;
  final int totalStoryTime;
  final int averageSceneTime;
  final int averageLoadTime;
  final int averageNarrationTime;
  final SceneTimingData? slowestScene;
  final SceneTimingData? fastestScene;
  final List<SceneTimingData> sceneTimings;
  final double childFriendlyCompliance;

  const TimingReport({
    required this.totalScenes,
    required this.totalStoryTime,
    required this.averageSceneTime,
    required this.averageLoadTime,
    required this.averageNarrationTime,
    this.slowestScene,
    this.fastestScene,
    required this.sceneTimings,
    required this.childFriendlyCompliance,
  });

  factory TimingReport.empty() {
    return const TimingReport(
      totalScenes: 0,
      totalStoryTime: 0,
      averageSceneTime: 0,
      averageLoadTime: 0,
      averageNarrationTime: 0,
      sceneTimings: [],
      childFriendlyCompliance: 0.0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'totalScenes': totalScenes,
      'totalStoryTime': totalStoryTime,
      'averageSceneTime': averageSceneTime,
      'averageLoadTime': averageLoadTime,
      'averageNarrationTime': averageNarrationTime,
      'slowestScene': slowestScene?.toJson(),
      'fastestScene': fastestScene?.toJson(),
      'sceneTimings': sceneTimings.map((s) => s.toJson()).toList(),
      'childFriendlyCompliance': childFriendlyCompliance,
    };
  }
}

/// Provider for story timing analyzer
final storyTimingAnalyzerProvider = Provider<StoryTimingAnalyzer>((ref) {
  return StoryTimingAnalyzer();
});
