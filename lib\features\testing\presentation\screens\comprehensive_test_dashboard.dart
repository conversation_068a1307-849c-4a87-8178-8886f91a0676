import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:choice_once_upon_a_time/core/testing/story_timing_test_runner.dart';
import 'package:choice_once_upon_a_time/core/testing/fallback_asset_test_runner.dart';
import 'package:choice_once_upon_a_time/core/services/story_timing_analyzer.dart';
import 'package:choice_once_upon_a_time/core/services/fallback_asset_manager.dart';
import 'package:choice_once_upon_a_time/core/repositories/enhanced_story_repository.dart';
import 'package:choice_once_upon_a_time/core/services/story_download_service.dart';
import 'package:choice_once_upon_a_time/core/documentation/timing_documentation_generator.dart';
import 'package:choice_once_upon_a_time/core/utils/app_logger.dart';

/// Comprehensive test dashboard for all story system testing
class ComprehensiveTestDashboard extends ConsumerStatefulWidget {
  const ComprehensiveTestDashboard({super.key});

  @override
  ConsumerState<ComprehensiveTestDashboard> createState() => _ComprehensiveTestDashboardState();
}

class _ComprehensiveTestDashboardState extends ConsumerState<ComprehensiveTestDashboard> {
  bool _isRunningTests = false;
  String _testStatus = 'Ready to run comprehensive story system tests';
  
  // Test results
  Map<String, StoryTimingTestResult> _timingResults = {};
  List<FallbackAssetTestResult> _fallbackResults = [];
  String _comprehensiveReport = '';
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Comprehensive Test Dashboard'),
        backgroundColor: Colors.purple[800],
        foregroundColor: Colors.white,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildTestOverview(),
            const SizedBox(height: 24),
            _buildTestControls(),
            const SizedBox(height: 24),
            _buildTestStatus(),
            const SizedBox(height: 24),
            _buildTestResults(),
          ],
        ),
      ),
    );
  }

  /// Build test overview section
  Widget _buildTestOverview() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Story System Comprehensive Testing',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 16),
            Text(
              'This dashboard runs comprehensive tests on the entire story system to validate:',
              style: Theme.of(context).textTheme.bodyMedium,
            ),
            const SizedBox(height: 12),
            _buildTestCategories(),
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.blue[50],
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.blue[200]!),
              ),
              child: Row(
                children: [
                  Icon(Icons.info, color: Colors.blue[700]),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      'Tests are designed to validate child-friendly timing requirements and ensure robust fallback behavior for missing assets.',
                      style: TextStyle(color: Colors.blue[700]),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Build test categories
  Widget _buildTestCategories() {
    final categories = [
      {
        'title': 'Story Timing Analysis',
        'icon': Icons.timer,
        'color': Colors.orange,
        'tests': [
          'Scene-by-scene timing measurement',
          'Child-friendly TTS speed validation (0.3-0.4 rate)',
          'Minimum scene display time enforcement (5 seconds)',
          'Sentence pause timing (1.5 seconds)',
          'Autoplay functionality testing',
          'Performance bottleneck identification',
        ],
      },
      {
        'title': 'Fallback Asset System',
        'icon': Icons.image_not_supported,
        'color': Colors.green,
        'tests': [
          'Missing image fallback to default_image.png',
          'Missing audio fallback to default_happy.mp3',
          'Asset existence validation',
          'Cache performance testing',
          'Story-specific asset resolution',
          'Edge case handling',
        ],
      },
    ];

    return Column(
      children: categories.map((category) => _buildCategoryCard(category)).toList(),
    );
  }

  /// Build individual category card
  Widget _buildCategoryCard(Map<String, dynamic> category) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(category['icon'], color: category['color'], size: 20),
                const SizedBox(width: 8),
                Text(
                  category['title'],
                  style: const TextStyle(fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 8),
            ...((category['tests'] as List<String>).map((test) => Padding(
              padding: const EdgeInsets.only(left: 28, bottom: 2),
              child: Row(
                children: [
                  Icon(Icons.check_circle_outline, size: 12, color: Colors.grey[600]),
                  const SizedBox(width: 6),
                  Expanded(
                    child: Text(
                      test,
                      style: TextStyle(fontSize: 12, color: Colors.grey[700]),
                    ),
                  ),
                ],
              ),
            ))),
          ],
        ),
      ),
    );
  }

  /// Build test controls
  Widget _buildTestControls() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Test Execution',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                ElevatedButton.icon(
                  onPressed: _isRunningTests ? null : _runComprehensiveTests,
                  icon: _isRunningTests 
                      ? const SizedBox(
                          width: 16,
                          height: 16,
                          child: CircularProgressIndicator(strokeWidth: 2),
                        )
                      : const Icon(Icons.play_arrow),
                  label: Text(_isRunningTests ? 'Running Tests...' : 'Run All Tests'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.purple[600],
                    foregroundColor: Colors.white,
                  ),
                ),
                const SizedBox(width: 16),
                ElevatedButton.icon(
                  onPressed: (_timingResults.isEmpty && _fallbackResults.isEmpty) ? null : _clearAllResults,
                  icon: const Icon(Icons.clear),
                  label: const Text('Clear Results'),
                ),
                const SizedBox(width: 16),
                ElevatedButton.icon(
                  onPressed: _comprehensiveReport.isEmpty ? null : _showComprehensiveReport,
                  icon: const Icon(Icons.description),
                  label: const Text('View Report'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// Build test status
  Widget _buildTestStatus() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Test Status',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                Icon(
                  _isRunningTests ? Icons.hourglass_empty : Icons.info,
                  color: _isRunningTests ? Colors.orange : Colors.blue,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    _testStatus,
                    style: TextStyle(
                      color: _isRunningTests ? Colors.orange : Colors.black87,
                    ),
                  ),
                ),
              ],
            ),
            if (_isRunningTests) ...[
              const SizedBox(height: 12),
              const LinearProgressIndicator(),
            ],
          ],
        ),
      ),
    );
  }

  /// Build test results
  Widget _buildTestResults() {
    if (_timingResults.isEmpty && _fallbackResults.isEmpty) {
      return const Card(
        child: Padding(
          padding: EdgeInsets.all(16.0),
          child: Text('No test results yet. Run comprehensive tests to see results.'),
        ),
      );
    }

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Test Results Summary',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 16),
            _buildResultsSummary(),
            const SizedBox(height: 16),
            if (_timingResults.isNotEmpty) _buildTimingResultsPreview(),
            if (_fallbackResults.isNotEmpty) _buildFallbackResultsPreview(),
          ],
        ),
      ),
    );
  }

  /// Build results summary
  Widget _buildResultsSummary() {
    final timingSuccess = _timingResults.values.where((r) => r.success).length;
    final fallbackSuccess = _fallbackResults.where((r) => r.success).length;
    
    return Row(
      children: [
        Expanded(
          child: _buildSummaryCard(
            'Timing Tests',
            '$timingSuccess/${_timingResults.length}',
            Icons.timer,
            Colors.orange,
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: _buildSummaryCard(
            'Fallback Tests',
            '$fallbackSuccess/${_fallbackResults.length}',
            Icons.image_not_supported,
            Colors.green,
          ),
        ),
      ],
    );
  }

  /// Build summary card
  Widget _buildSummaryCard(String title, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: 8),
          Text(
            value,
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          Text(
            title,
            style: TextStyle(
              fontSize: 12,
              color: color,
            ),
          ),
        ],
      ),
    );
  }

  /// Build timing results preview
  Widget _buildTimingResultsPreview() {
    return ExpansionTile(
      title: const Text('Timing Test Results'),
      children: _timingResults.entries.map((entry) {
        final result = entry.value;
        return ListTile(
          leading: Icon(
            result.success ? Icons.check_circle : Icons.error,
            color: result.success ? Colors.green : Colors.red,
          ),
          title: Text(entry.key),
          subtitle: result.success 
              ? Text('${result.sceneCount} scenes, ${(result.timingReport.childFriendlyCompliance * 100).toStringAsFixed(1)}% compliance')
              : Text('Error: ${result.error}'),
        );
      }).toList(),
    );
  }

  /// Build fallback results preview
  Widget _buildFallbackResultsPreview() {
    return ExpansionTile(
      title: const Text('Fallback Test Results'),
      children: _fallbackResults.map((result) {
        return ListTile(
          leading: Icon(
            result.success ? Icons.check_circle : Icons.error,
            color: result.success ? Colors.green : Colors.red,
          ),
          title: Text(result.testName),
          subtitle: Text(result.message),
        );
      }).toList(),
    );
  }

  /// Run comprehensive tests
  Future<void> _runComprehensiveTests() async {
    setState(() {
      _isRunningTests = true;
      _testStatus = 'Initializing comprehensive test suite...';
      _timingResults.clear();
      _fallbackResults.clear();
      _comprehensiveReport = '';
    });

    try {
      // Initialize services
      final timingAnalyzer = StoryTimingAnalyzer();
      final fallbackManager = FallbackAssetManager();
      final downloadService = StoryDownloadService();
      final storyRepository = EnhancedStoryRepository(downloadService: downloadService);

      // Run timing tests
      setState(() {
        _testStatus = 'Running story timing analysis tests...';
      });

      final timingTestRunner = StoryTimingTestRunner(
        timingAnalyzer: timingAnalyzer,
        storyRepository: storyRepository,
      );
      _timingResults = await timingTestRunner.runTimingTests();

      // Run fallback asset tests
      setState(() {
        _testStatus = 'Running fallback asset system tests...';
      });

      final fallbackTestRunner = FallbackAssetTestRunner(fallbackManager: fallbackManager);
      _fallbackResults = await fallbackTestRunner.runFallbackTests();

      // Generate comprehensive documentation
      setState(() {
        _testStatus = 'Generating comprehensive documentation...';
      });

      final timingData = timingAnalyzer.exportTimingData();
      _comprehensiveReport = TimingDocumentationGenerator.generateComprehensiveTimingReport(
        testResults: _timingResults,
        timingData: timingData,
        additionalNotes: _generateAdditionalNotes(),
      );

      setState(() {
        _testStatus = 'Comprehensive testing completed successfully.';
        _isRunningTests = false;
      });

      // Show completion message
      if (mounted) {
        final totalTests = _timingResults.length + _fallbackResults.length;
        final successfulTests = _timingResults.values.where((r) => r.success).length + 
                               _fallbackResults.where((r) => r.success).length;
        
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Comprehensive testing completed: $successfulTests/$totalTests tests passed'),
            backgroundColor: successfulTests == totalTests ? Colors.green : Colors.orange,
          ),
        );
      }

    } catch (e, stackTrace) {
      AppLogger.error('[COMPREHENSIVE_TEST] Error running comprehensive tests', e, stackTrace);
      
      setState(() {
        _testStatus = 'Error running comprehensive tests: $e';
        _isRunningTests = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error running comprehensive tests: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// Generate additional notes for the report
  String _generateAdditionalNotes() {
    final buffer = StringBuffer();
    
    buffer.writeln('### Test Environment');
    buffer.writeln('- **Platform:** Flutter');
    buffer.writeln('- **Test Framework:** Custom Story System Test Suite');
    buffer.writeln('- **Test Date:** ${DateTime.now().toIso8601String()}');
    buffer.writeln();
    
    buffer.writeln('### Fallback Asset Test Summary');
    final fallbackSuccess = _fallbackResults.where((r) => r.success).length;
    buffer.writeln('- **Total Fallback Tests:** ${_fallbackResults.length}');
    buffer.writeln('- **Successful:** $fallbackSuccess');
    buffer.writeln('- **Success Rate:** ${_fallbackResults.isNotEmpty ? (fallbackSuccess / _fallbackResults.length * 100).toStringAsFixed(1) : 0}%');
    
    return buffer.toString();
  }

  /// Clear all results
  void _clearAllResults() {
    setState(() {
      _timingResults.clear();
      _fallbackResults.clear();
      _comprehensiveReport = '';
      _testStatus = 'Ready to run comprehensive story system tests';
    });
  }

  /// Show comprehensive report
  void _showComprehensiveReport() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Comprehensive Test Report'),
        content: SizedBox(
          width: double.maxFinite,
          height: 500,
          child: SingleChildScrollView(
            child: Text(
              _comprehensiveReport,
              style: const TextStyle(fontFamily: 'monospace', fontSize: 11),
            ),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }
}
