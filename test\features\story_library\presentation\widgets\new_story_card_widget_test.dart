import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:choice_once_upon_a_time/features/story_library/presentation/widgets/new_story_card_widget.dart';
import 'package:choice_once_upon_a_time/models/story_metadata_model.dart';

void main() {
  group('NewStoryCardWidget', () {
    late StoryMetadataModel testStory;

    setUp(() {
      testStory = StoryMetadataModel(
        id: 'test_story',
        title: {'en-US': 'Test Story Title'},
        coverImageUrl: 'assets/test/cover.jpg',
        loglineShort: {'en-US': 'A test story'},
        targetMoralValue: 'Test Moral',
        targetAgeSubSegment: '3-5',
        estimatedDurationMinutes: 5,
        isFree: true,
        isLocked: false,
        published: true,
        dataSource: 'new_asset',
        version: '1.0.0',
        supportedLanguages: ['en-US'],
        defaultLanguage: 'en-US',
        initialSceneId: 'scene_1',
      );
    });

    testWidgets('should display story information correctly', (WidgetTester tester) async {
      bool tapped = false;

      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: Scaffold(
              body: NewStoryCardWidget(
                story: testStory,
                languageCode: 'en-US',
                onTap: () => tapped = true,
              ),
            ),
          ),
        ),
      );

      // Wait for the widget to build
      await tester.pumpAndSettle();

      // Check if story title is displayed
      expect(find.text('Test Story Title'), findsOneWidget);
      
      // Check if moral value is displayed
      expect(find.text('Test Moral'), findsOneWidget);
      
      // Check if duration and age group are displayed
      expect(find.text('5 min • 3-5'), findsOneWidget);
    });

    testWidgets('should handle tap events', (WidgetTester tester) async {
      bool tapped = false;

      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: Scaffold(
              body: NewStoryCardWidget(
                story: testStory,
                languageCode: 'en-US',
                onTap: () => tapped = true,
              ),
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // Tap on the card
      await tester.tap(find.byType(NewStoryCardWidget));
      await tester.pumpAndSettle();

      expect(tapped, isTrue);
    });

    testWidgets('should show loading state initially', (WidgetTester tester) async {
      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: Scaffold(
              body: NewStoryCardWidget(
                story: testStory,
                languageCode: 'en-US',
                onTap: () {},
              ),
            ),
          ),
        ),
      );

      // Should show loading state initially
      expect(find.text('Loading...'), findsOneWidget);
    });

    testWidgets('should adapt to one column layout', (WidgetTester tester) async {
      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: Scaffold(
              body: NewStoryCardWidget(
                story: testStory,
                languageCode: 'en-US',
                onTap: () {},
                isOneColumn: true,
              ),
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // Widget should render without errors in one column mode
      expect(find.byType(NewStoryCardWidget), findsOneWidget);
    });

    testWidgets('should handle long titles with ellipsis', (WidgetTester tester) async {
      final longTitleStory = testStory.copyWith(
        title: {'en-US': 'This is a very long story title that should be truncated with ellipsis'},
      );

      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: Scaffold(
              body: SizedBox(
                width: 200, // Constrain width to force truncation
                child: NewStoryCardWidget(
                  story: longTitleStory,
                  languageCode: 'en-US',
                  onTap: () {},
                ),
              ),
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // Should find the text widget (even if truncated)
      expect(find.textContaining('This is a very long'), findsOneWidget);
    });

    testWidgets('should handle missing cover image gracefully', (WidgetTester tester) async {
      final storyWithMissingImage = testStory.copyWith(
        coverImageUrl: 'assets/nonexistent/image.jpg',
      );

      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: Scaffold(
              body: NewStoryCardWidget(
                story: storyWithMissingImage,
                languageCode: 'en-US',
                onTap: () {},
              ),
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // Should render without crashing
      expect(find.byType(NewStoryCardWidget), findsOneWidget);
    });
  });
}
