{"story_id": "story013", "age_group": "3-5", "difficulty": "easy", "title": "The Lost Toy", "moral": "Kindness", "cover_image": "story_cover.jpg", "estimated_time": "5 minutes", "setup": {"setting": "A sunny park with green grass and tall trees", "tone": "Playful and warm", "context": "A girl learns to help her friend find a lost toy", "brief_intro": "Welcome to a sunny park adventure! Join <PERSON> and <PERSON> as they play, but oh no—<PERSON> loses his favorite teddy bear. Will <PERSON> help him find it? Let’s find out!", "background_music": "calm_piano.mp3"}, "narrator_profile": {"name": "<PERSON>", "voice": {"name": "en-US-Wavenet-A", "pitch": 0.9, "rate": 0.95, "volume": 1.0}, "default_voice": true}, "characters": [{"name": "<PERSON>", "description": "A curious girl who loves to play", "role": "Protagonist", "voice": {"name": "en-GB-Standard-A", "pitch": 1.3, "rate": 1.2, "volume": 0.9}}, {"name": "<PERSON>", "description": "<PERSON>'s friend who lost his toy", "role": "Supporting", "voice": {"name": "en-US-Standard-D", "pitch": 1.4, "rate": 1.25, "volume": 0.85}}], "scenes": [{"id": "scene_1", "text": "<PERSON> and <PERSON> were playing in a sunny park when <PERSON> realized his toy was missing.", "speaker": "narrator", "emotion": "surprised", "image": "park_intro.jpg", "pause_duration": 2000, "next": "scene_2", "progress_weight": 1, "interactive_elements": [], "emotional_sound_effects": "gentle_wind.mp3"}, {"id": "scene_2", "text": "Oh no! My teddy bear is gone!", "speaker": "<PERSON>", "emotion": "sad", "image": "alex_sad.jpg", "pause_duration": 1500, "next": "scene_3", "progress_weight": 1, "interactive_elements": [], "emotional_sound_effects": "soft_sigh.mp3"}, {"id": "scene_3", "text": "<PERSON> saw <PERSON> looking sad. What should she do?", "speaker": "narrator", "emotion": "curious", "image": "choice_moment.jpg", "pause_duration": 0, "choices": [{"option": "Help <PERSON> find the toy", "visual": "helping_hand_icon.jpg", "next": "scene_4a"}, {"option": "Keep playing", "visual": "playing_icon.jpg", "next": "scene_4b"}], "progress_weight": 2, "interactive_elements": []}, {"id": "scene_4a", "text": "<PERSON> said, 'Don’t worry, <PERSON>! Let’s look together.' They found the teddy bear under a bush.", "speaker": "narrator", "emotion": "happy", "image": "finding_toy.jpg", "pause_duration": 2000, "outcome": "good", "rewards": {"completion": 1, "good": 1}, "reflection": {"text": "Why was helping <PERSON> a kind thing to do?", "emotion": "curious"}, "progress_weight": 1, "emotional_sound_effects": "cheerful_music.mp3"}, {"id": "scene_4b", "text": "<PERSON> kept playing, and <PERSON> sat alone, feeling sad. 'I miss my teddy bear,' he whispered.", "speaker": "narrator", "emotion": "gentle", "image": "alex_alone.jpg", "pause_duration": 2000, "outcome": "bad", "rewards": {"completion": 1, "good": 0}, "reflection": {"text": "What could <PERSON> do next time to make <PERSON> happy?", "emotion": "hopeful"}, "progress_weight": 1, "emotional_sound_effects": "soft_sad_music.mp3"}], "vocabulary": [{"word": "missing", "image": "missing.jpg", "explanation": "When something is not where it should be, like <PERSON>'s teddy bear."}, {"word": "sad", "image": "sad.jpg", "explanation": "Feeling unhappy, like <PERSON> when he lost his toy."}, {"word": "help", "image": "help.jpg", "explanation": "To assist someone, like <PERSON> helping <PERSON>."}, {"word": "kind", "image": "kind.jpg", "explanation": "Being nice and caring to others."}, {"word": "friend", "image": "friend.jpg", "explanation": "Someone you like to play with, like <PERSON> and <PERSON>."}], "post_story": {"discussion": {"text": "What a kind adventure! <PERSON> learned that helping a friend can make everyone happy. What would happen if she made a different choice?", "vocabulary_discussion": [{"word": "missing", "explanation": "When something is not where it should be, like <PERSON>'s teddy bear.", "image": "missing.jpg"}, {"word": "sad", "explanation": "Feeling unhappy, like <PERSON> when he lost his toy.", "image": "sad.jpg"}, {"word": "help", "explanation": "To assist someone, like <PERSON> helping <PERSON>.", "image": "help.jpg"}, {"word": "kind", "explanation": "Being nice and caring to others.", "image": "kind.jpg"}, {"word": "friend", "explanation": "Someone you like to play with, like <PERSON> and <PERSON>.", "image": "friend.jpg"}], "emotion": "curious"}, "replay_prompt": {"text": "Let’s go back to the park! Want to hear <PERSON> and <PERSON>’s story again?", "emotion": "excited"}, "parental_discussion_prompts": ["How did <PERSON>’s choice affect <PERSON>’s feelings?", "What can you do to help a friend who is sad?"], "feedback_section": {"rating": "5-star", "comments": "optional"}}}