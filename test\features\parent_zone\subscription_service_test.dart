import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:in_app_purchase/in_app_purchase.dart';
import 'package:choice_once_upon_a_time/features/parent_zone/data/subscription_service.dart';

// Generate mocks
@GenerateMocks([InAppPurchase, ProductDetails])
import 'subscription_service_test.mocks.dart';

void main() {
  group('SubscriptionService', () {
    late SubscriptionService subscriptionService;
    late MockInAppPurchase mockInAppPurchase;
    late MockProductDetails mockProductDetails;

    setUp(() {
      mockInAppPurchase = MockInAppPurchase();
      mockProductDetails = MockProductDetails();
      subscriptionService = SubscriptionService(inAppPurchase: mockInAppPurchase);
    });

    group('initializeStore', () {
      test('initializes successfully when IAP is available', () async {
        // Arrange
        when(mockInAppPurchase.isAvailable()).thenAnswer((_) async => true);
        when(mockInAppPurchase.purchaseStream).thenAnswer((_) => const Stream.empty());

        // Act
        await subscriptionService.initializeStore();

        // Assert
        expect(subscriptionService.isInitialized, isTrue);
        expect(subscriptionService.isAvailable, isTrue);
        verify(mockInAppPurchase.isAvailable()).called(1);
      });

      test('handles unavailable IAP gracefully', () async {
        // Arrange
        when(mockInAppPurchase.isAvailable()).thenAnswer((_) async => false);

        // Act
        await subscriptionService.initializeStore();

        // Assert
        expect(subscriptionService.isInitialized, isTrue);
        expect(subscriptionService.isAvailable, isFalse);
      });

      test('throws SubscriptionException on error', () async {
        // Arrange
        when(mockInAppPurchase.isAvailable()).thenThrow(Exception('IAP error'));

        // Act & Assert
        expect(
          () => subscriptionService.initializeStore(),
          throwsA(isA<SubscriptionException>()),
        );
      });

      test('does not reinitialize if already initialized', () async {
        // Arrange
        when(mockInAppPurchase.isAvailable()).thenAnswer((_) async => true);
        when(mockInAppPurchase.purchaseStream).thenAnswer((_) => const Stream.empty());
        
        // Initialize once
        await subscriptionService.initializeStore();
        
        // Act - try to initialize again
        await subscriptionService.initializeStore();

        // Assert - isAvailable should only be called once
        verify(mockInAppPurchase.isAvailable()).called(1);
      });
    });

    group('fetchProducts', () {
      setUp(() async {
        // Initialize the service first
        when(mockInAppPurchase.isAvailable()).thenAnswer((_) async => true);
        when(mockInAppPurchase.purchaseStream).thenAnswer((_) => const Stream.empty());
        await subscriptionService.initializeStore();
      });

      test('fetches products successfully', () async {
        // Arrange
        final mockResponse = ProductDetailsResponse(
          productDetails: [mockProductDetails],
          notFoundIDs: [],
          error: null,
        );
        when(mockInAppPurchase.queryProductDetails(any))
            .thenAnswer((_) async => mockResponse);

        // Act
        await subscriptionService.fetchProducts();

        // Assert
        expect(subscriptionService.products, contains(mockProductDetails));
        verify(mockInAppPurchase.queryProductDetails(any)).called(1);
      });

      test('throws SubscriptionException when not initialized', () async {
        // Arrange
        final uninitializedService = SubscriptionService(inAppPurchase: mockInAppPurchase);

        // Act & Assert
        expect(
          () => uninitializedService.fetchProducts(),
          throwsA(isA<SubscriptionException>()),
        );
      });

      test('throws SubscriptionException when IAP not available', () async {
        // Arrange
        when(mockInAppPurchase.isAvailable()).thenAnswer((_) async => false);
        final unavailableService = SubscriptionService(inAppPurchase: mockInAppPurchase);
        await unavailableService.initializeStore();

        // Act & Assert
        expect(
          () => unavailableService.fetchProducts(),
          throwsA(isA<SubscriptionException>()),
        );
      });

      test('throws SubscriptionException on query error', () async {
        // Arrange
        final mockResponse = ProductDetailsResponse(
          productDetails: [],
          notFoundIDs: [],
          error: IAPError(
            source: IAPSource.AppStore,
            code: 'test_error',
            message: 'Test error message',
          ),
        );
        when(mockInAppPurchase.queryProductDetails(any))
            .thenAnswer((_) async => mockResponse);

        // Act & Assert
        expect(
          () => subscriptionService.fetchProducts(),
          throwsA(isA<SubscriptionException>()),
        );
      });
    });

    group('getProduct', () {
      setUp(() async {
        // Initialize and set up products
        when(mockInAppPurchase.isAvailable()).thenAnswer((_) async => true);
        when(mockInAppPurchase.purchaseStream).thenAnswer((_) => const Stream.empty());
        await subscriptionService.initializeStore();

        when(mockProductDetails.id).thenReturn('monthly_premium');
        final mockResponse = ProductDetailsResponse(
          productDetails: [mockProductDetails],
          notFoundIDs: [],
          error: null,
        );
        when(mockInAppPurchase.queryProductDetails(any))
            .thenAnswer((_) async => mockResponse);
        await subscriptionService.fetchProducts();
      });

      test('returns product when found', () {
        // Act
        final result = subscriptionService.getProduct('monthly_premium');

        // Assert
        expect(result, equals(mockProductDetails));
      });

      test('returns null when product not found', () {
        // Act
        final result = subscriptionService.getProduct('nonexistent_product');

        // Assert
        expect(result, isNull);
      });
    });

    group('hasActiveSubscription', () {
      test('returns false for Sprint 5 implementation', () {
        // Act
        final result = subscriptionService.hasActiveSubscription();

        // Assert
        expect(result, isFalse);
      });
    });

    group('restorePurchases', () {
      setUp(() async {
        // Initialize the service first
        when(mockInAppPurchase.isAvailable()).thenAnswer((_) async => true);
        when(mockInAppPurchase.purchaseStream).thenAnswer((_) => const Stream.empty());
        await subscriptionService.initializeStore();
      });

      test('calls InAppPurchase restorePurchases', () async {
        // Arrange
        when(mockInAppPurchase.restorePurchases()).thenAnswer((_) async {});

        // Act
        await subscriptionService.restorePurchases();

        // Assert
        verify(mockInAppPurchase.restorePurchases()).called(1);
      });

      test('throws SubscriptionException when not initialized', () async {
        // Arrange
        final uninitializedService = SubscriptionService(inAppPurchase: mockInAppPurchase);

        // Act & Assert
        expect(
          () => uninitializedService.restorePurchases(),
          throwsA(isA<SubscriptionException>()),
        );
      });

      test('throws SubscriptionException on error', () async {
        // Arrange
        when(mockInAppPurchase.restorePurchases()).thenThrow(Exception('Restore failed'));

        // Act & Assert
        expect(
          () => subscriptionService.restorePurchases(),
          throwsA(isA<SubscriptionException>()),
        );
      });
    });
  });

  group('SubscriptionException', () {
    test('creates exception with message', () {
      // Arrange
      const message = 'Test error message';

      // Act
      final exception = SubscriptionException(message);

      // Assert
      expect(exception.message, equals(message));
      expect(exception.code, isNull);
    });

    test('creates exception with message and code', () {
      // Arrange
      const message = 'Test error message';
      const code = 'test_error_code';

      // Act
      final exception = SubscriptionException(message, code: code);

      // Assert
      expect(exception.message, equals(message));
      expect(exception.code, equals(code));
    });

    test('toString returns formatted message', () {
      // Arrange
      const message = 'Test error message';
      final exception = SubscriptionException(message);

      // Act
      final result = exception.toString();

      // Assert
      expect(result, equals('SubscriptionException: $message'));
    });
  });
}
