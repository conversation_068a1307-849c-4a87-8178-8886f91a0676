import 'package:flutter/material.dart';

/// Utility class for responsive design calculations
class ResponsiveUtils {
  /// Breakpoints for different screen sizes
  static const double mobileBreakpoint = 600;
  static const double tabletBreakpoint = 900;
  static const double desktopBreakpoint = 1200;
  
  /// Minimum supported width
  static const double minWidth = 320;
  
  /// Maximum supported width for optimal layout
  static const double maxWidth = 1400;

  /// Get the current device type based on screen width
  static DeviceType getDeviceType(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    
    if (width < mobileBreakpoint) {
      return DeviceType.mobile;
    } else if (width < tabletBreakpoint) {
      return DeviceType.tablet;
    } else {
      return DeviceType.desktop;
    }
  }

  /// Check if the current device is mobile
  static bool isMobile(BuildContext context) {
    return getDeviceType(context) == DeviceType.mobile;
  }

  /// Check if the current device is tablet
  static bool isTablet(BuildContext context) {
    return getDeviceType(context) == DeviceType.tablet;
  }

  /// Check if the current device is desktop
  static bool isDesktop(BuildContext context) {
    return getDeviceType(context) == DeviceType.desktop;
  }

  /// Get responsive padding based on screen size
  static EdgeInsets getResponsivePadding(BuildContext context) {
    final deviceType = getDeviceType(context);
    
    switch (deviceType) {
      case DeviceType.mobile:
        return const EdgeInsets.all(16.0);
      case DeviceType.tablet:
        return const EdgeInsets.all(24.0);
      case DeviceType.desktop:
        return const EdgeInsets.all(32.0);
    }
  }

  /// Get responsive margin based on screen size
  static EdgeInsets getResponsiveMargin(BuildContext context) {
    final deviceType = getDeviceType(context);
    
    switch (deviceType) {
      case DeviceType.mobile:
        return const EdgeInsets.all(8.0);
      case DeviceType.tablet:
        return const EdgeInsets.all(12.0);
      case DeviceType.desktop:
        return const EdgeInsets.all(16.0);
    }
  }

  /// Get responsive font size
  static double getResponsiveFontSize(
    BuildContext context,
    double baseFontSize, {
    double? mobileScale,
    double? tabletScale,
    double? desktopScale,
  }) {
    final deviceType = getDeviceType(context);
    
    switch (deviceType) {
      case DeviceType.mobile:
        return baseFontSize * (mobileScale ?? 0.9);
      case DeviceType.tablet:
        return baseFontSize * (tabletScale ?? 1.0);
      case DeviceType.desktop:
        return baseFontSize * (desktopScale ?? 1.1);
    }
  }

  /// Get responsive icon size
  static double getResponsiveIconSize(BuildContext context, double baseSize) {
    final deviceType = getDeviceType(context);
    
    switch (deviceType) {
      case DeviceType.mobile:
        return baseSize * 0.9;
      case DeviceType.tablet:
        return baseSize;
      case DeviceType.desktop:
        return baseSize * 1.1;
    }
  }

  /// Get responsive spacing
  static double getResponsiveSpacing(BuildContext context, double baseSpacing) {
    final deviceType = getDeviceType(context);
    
    switch (deviceType) {
      case DeviceType.mobile:
        return baseSpacing * 0.8;
      case DeviceType.tablet:
        return baseSpacing;
      case DeviceType.desktop:
        return baseSpacing * 1.2;
    }
  }

  /// Get responsive grid column count
  static int getResponsiveGridColumns(BuildContext context, {
    int mobileColumns = 1,
    int tabletColumns = 2,
    int desktopColumns = 3,
  }) {
    final deviceType = getDeviceType(context);
    
    switch (deviceType) {
      case DeviceType.mobile:
        return mobileColumns;
      case DeviceType.tablet:
        return tabletColumns;
      case DeviceType.desktop:
        return desktopColumns;
    }
  }

  /// Get responsive container width with constraints
  static double getResponsiveWidth(
    BuildContext context, {
    double? mobileWidth,
    double? tabletWidth,
    double? desktopWidth,
  }) {
    final screenWidth = MediaQuery.of(context).size.width;
    final deviceType = getDeviceType(context);
    
    double targetWidth;
    
    switch (deviceType) {
      case DeviceType.mobile:
        targetWidth = mobileWidth ?? screenWidth;
        break;
      case DeviceType.tablet:
        targetWidth = tabletWidth ?? screenWidth * 0.8;
        break;
      case DeviceType.desktop:
        targetWidth = desktopWidth ?? 800;
        break;
    }
    
    // Ensure width is within supported bounds
    return targetWidth.clamp(minWidth, maxWidth);
  }

  /// Get responsive height
  static double getResponsiveHeight(
    BuildContext context,
    double fraction, {
    double? minHeight,
    double? maxHeight,
  }) {
    final screenHeight = MediaQuery.of(context).size.height;
    final calculatedHeight = screenHeight * fraction;
    
    if (minHeight != null && calculatedHeight < minHeight) {
      return minHeight;
    }
    
    if (maxHeight != null && calculatedHeight > maxHeight) {
      return maxHeight;
    }
    
    return calculatedHeight;
  }

  /// Get responsive border radius
  static BorderRadius getResponsiveBorderRadius(BuildContext context, double baseRadius) {
    final deviceType = getDeviceType(context);
    
    double multiplier;
    switch (deviceType) {
      case DeviceType.mobile:
        multiplier = 0.8;
        break;
      case DeviceType.tablet:
        multiplier = 1.0;
        break;
      case DeviceType.desktop:
        multiplier = 1.2;
        break;
    }
    
    return BorderRadius.circular(baseRadius * multiplier);
  }

  /// Get responsive elevation
  static double getResponsiveElevation(BuildContext context, double baseElevation) {
    final deviceType = getDeviceType(context);
    
    switch (deviceType) {
      case DeviceType.mobile:
        return baseElevation * 0.8;
      case DeviceType.tablet:
        return baseElevation;
      case DeviceType.desktop:
        return baseElevation * 1.2;
    }
  }

  /// Check if screen is in landscape orientation
  static bool isLandscape(BuildContext context) {
    return MediaQuery.of(context).orientation == Orientation.landscape;
  }

  /// Check if screen is in portrait orientation
  static bool isPortrait(BuildContext context) {
    return MediaQuery.of(context).orientation == Orientation.portrait;
  }

  /// Get safe area padding
  static EdgeInsets getSafeAreaPadding(BuildContext context) {
    return MediaQuery.of(context).padding;
  }

  /// Get responsive app bar height
  static double getResponsiveAppBarHeight(BuildContext context) {
    final deviceType = getDeviceType(context);
    
    switch (deviceType) {
      case DeviceType.mobile:
        return kToolbarHeight;
      case DeviceType.tablet:
        return kToolbarHeight * 1.1;
      case DeviceType.desktop:
        return kToolbarHeight * 1.2;
    }
  }

  /// Get responsive button height
  static double getResponsiveButtonHeight(BuildContext context) {
    final deviceType = getDeviceType(context);
    
    switch (deviceType) {
      case DeviceType.mobile:
        return 48.0;
      case DeviceType.tablet:
        return 52.0;
      case DeviceType.desktop:
        return 56.0;
    }
  }

  /// Get responsive card aspect ratio
  static double getResponsiveAspectRatio(BuildContext context, {
    double mobileRatio = 16 / 9,
    double tabletRatio = 4 / 3,
    double desktopRatio = 16 / 10,
  }) {
    final deviceType = getDeviceType(context);
    
    switch (deviceType) {
      case DeviceType.mobile:
        return mobileRatio;
      case DeviceType.tablet:
        return tabletRatio;
      case DeviceType.desktop:
        return desktopRatio;
    }
  }

  /// Get responsive layout constraints
  static BoxConstraints getResponsiveConstraints(BuildContext context) {
    final screenSize = MediaQuery.of(context).size;
    
    return BoxConstraints(
      minWidth: minWidth,
      maxWidth: maxWidth.clamp(minWidth, screenSize.width),
      minHeight: 0,
      maxHeight: screenSize.height,
    );
  }

  /// Get responsive text theme adjustments
  static TextTheme getResponsiveTextTheme(BuildContext context, TextTheme baseTheme) {
    final deviceType = getDeviceType(context);
    
    double scaleFactor;
    switch (deviceType) {
      case DeviceType.mobile:
        scaleFactor = 0.9;
        break;
      case DeviceType.tablet:
        scaleFactor = 1.0;
        break;
      case DeviceType.desktop:
        scaleFactor = 1.1;
        break;
    }
    
    return baseTheme.copyWith(
      displayLarge: baseTheme.displayLarge?.copyWith(
        fontSize: (baseTheme.displayLarge?.fontSize ?? 32) * scaleFactor,
      ),
      displayMedium: baseTheme.displayMedium?.copyWith(
        fontSize: (baseTheme.displayMedium?.fontSize ?? 28) * scaleFactor,
      ),
      displaySmall: baseTheme.displaySmall?.copyWith(
        fontSize: (baseTheme.displaySmall?.fontSize ?? 24) * scaleFactor,
      ),
      headlineLarge: baseTheme.headlineLarge?.copyWith(
        fontSize: (baseTheme.headlineLarge?.fontSize ?? 22) * scaleFactor,
      ),
      headlineMedium: baseTheme.headlineMedium?.copyWith(
        fontSize: (baseTheme.headlineMedium?.fontSize ?? 20) * scaleFactor,
      ),
      headlineSmall: baseTheme.headlineSmall?.copyWith(
        fontSize: (baseTheme.headlineSmall?.fontSize ?? 18) * scaleFactor,
      ),
      titleLarge: baseTheme.titleLarge?.copyWith(
        fontSize: (baseTheme.titleLarge?.fontSize ?? 16) * scaleFactor,
      ),
      titleMedium: baseTheme.titleMedium?.copyWith(
        fontSize: (baseTheme.titleMedium?.fontSize ?? 14) * scaleFactor,
      ),
      titleSmall: baseTheme.titleSmall?.copyWith(
        fontSize: (baseTheme.titleSmall?.fontSize ?? 12) * scaleFactor,
      ),
      bodyLarge: baseTheme.bodyLarge?.copyWith(
        fontSize: (baseTheme.bodyLarge?.fontSize ?? 16) * scaleFactor,
      ),
      bodyMedium: baseTheme.bodyMedium?.copyWith(
        fontSize: (baseTheme.bodyMedium?.fontSize ?? 14) * scaleFactor,
      ),
      bodySmall: baseTheme.bodySmall?.copyWith(
        fontSize: (baseTheme.bodySmall?.fontSize ?? 12) * scaleFactor,
      ),
    );
  }
}

/// Enum for different device types
enum DeviceType {
  mobile,
  tablet,
  desktop,
}

/// Extension on BuildContext for easy access to responsive utilities
extension ResponsiveContext on BuildContext {
  /// Get device type
  DeviceType get deviceType => ResponsiveUtils.getDeviceType(this);
  
  /// Check if mobile
  bool get isMobile => ResponsiveUtils.isMobile(this);
  
  /// Check if tablet
  bool get isTablet => ResponsiveUtils.isTablet(this);
  
  /// Check if desktop
  bool get isDesktop => ResponsiveUtils.isDesktop(this);
  
  /// Get responsive padding
  EdgeInsets get responsivePadding => ResponsiveUtils.getResponsivePadding(this);
  
  /// Get responsive margin
  EdgeInsets get responsiveMargin => ResponsiveUtils.getResponsiveMargin(this);
  
  /// Check if landscape
  bool get isLandscape => ResponsiveUtils.isLandscape(this);
  
  /// Check if portrait
  bool get isPortrait => ResponsiveUtils.isPortrait(this);
}
