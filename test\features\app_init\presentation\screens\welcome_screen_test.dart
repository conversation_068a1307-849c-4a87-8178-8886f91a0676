import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:choice_once_upon_a_time/features/app_init/presentation/screens/welcome_screen.dart';
import 'package:choice_once_upon_a_time/core/auth/session_manager.dart';
import 'package:choice_once_upon_a_time/core/auth/user_session.dart';

void main() {
  group('WelcomeScreen', () {
    late GoRouter router;
    
    setUp(() {
      router = GoRouter(
        routes: [
          GoRoute(
            path: '/',
            builder: (context, state) => const WelcomeScreen(),
          ),
          GoRoute(
            path: '/profile_selection',
            builder: (context, state) => const Scaffold(
              body: Text('Profile Selection'),
            ),
          ),
        ],
      );
    });

    testWidgets('displays welcome message for authenticated user', (WidgetTester tester) async {
      final session = UserSession(
        id: 'test-id',
        email: '<EMAIL>',
        displayName: 'Test User',
        createdAt: DateTime.now(),
        expiresAt: DateTime.now().add(const Duration(days: 7)),
      );

      await tester.pumpWidget(
        ProviderScope(
          overrides: [
            currentSessionProvider.overrideWith((ref) => AsyncValue.data(session)),
          ],
          child: MaterialApp.router(
            routerConfig: router,
          ),
        ),
      );

      await tester.pumpAndSettle();

      // Check that welcome message is displayed
      expect(find.text('Welcome, Test User!'), findsOneWidget);
      expect(find.text('Great to see you! Let\'s choose which child will be reading today.'), findsOneWidget);
      expect(find.byIcon(Icons.waving_hand), findsOneWidget);
    });

    testWidgets('displays generic welcome for guest user', (WidgetTester tester) async {
      await tester.pumpWidget(
        ProviderScope(
          overrides: [
            currentSessionProvider.overrideWith((ref) => const AsyncValue.data(null)),
          ],
          child: MaterialApp.router(
            routerConfig: router,
          ),
        ),
      );

      await tester.pumpAndSettle();

      // Check that generic welcome message is displayed
      expect(find.text('Welcome, Guest!'), findsOneWidget);
      expect(find.text('Great to see you! Let\'s choose which child will be reading today.'), findsOneWidget);
    });

    testWidgets('shows loading state while session is loading', (WidgetTester tester) async {
      await tester.pumpWidget(
        ProviderScope(
          overrides: [
            currentSessionProvider.overrideWith((ref) => const AsyncValue.loading()),
          ],
          child: MaterialApp.router(
            routerConfig: router,
          ),
        ),
      );

      await tester.pump();

      // Check that loading indicator is displayed
      expect(find.byType(CircularProgressIndicator), findsAtLeastNWidget(1));
    });

    testWidgets('handles session error gracefully', (WidgetTester tester) async {
      await tester.pumpWidget(
        ProviderScope(
          overrides: [
            currentSessionProvider.overrideWith((ref) => AsyncValue.error('Error', StackTrace.empty)),
          ],
          child: MaterialApp.router(
            routerConfig: router,
          ),
        ),
      );

      await tester.pumpAndSettle();

      // Check that error state is handled gracefully
      expect(find.text('Welcome!'), findsOneWidget);
      expect(find.text('Let\'s get started with your adventure.'), findsOneWidget);
      expect(find.byIcon(Icons.error_outline), findsOneWidget);
    });

    testWidgets('continue button navigates to profile selection', (WidgetTester tester) async {
      await tester.pumpWidget(
        ProviderScope(
          overrides: [
            currentSessionProvider.overrideWith((ref) => const AsyncValue.data(null)),
          ],
          child: MaterialApp.router(
            routerConfig: router,
          ),
        ),
      );

      await tester.pumpAndSettle();

      // Find and tap the continue button
      final continueButton = find.text('Continue');
      expect(continueButton, findsOneWidget);
      
      await tester.tap(continueButton);
      await tester.pumpAndSettle();

      // Check that we navigated to profile selection
      expect(find.text('Profile Selection'), findsOneWidget);
    });

    testWidgets('auto-navigates after delay', (WidgetTester tester) async {
      await tester.pumpWidget(
        ProviderScope(
          overrides: [
            currentSessionProvider.overrideWith((ref) => const AsyncValue.data(null)),
          ],
          child: MaterialApp.router(
            routerConfig: router,
          ),
        ),
      );

      await tester.pumpAndSettle();

      // Wait for auto-navigation (3 seconds)
      await tester.pump(const Duration(seconds: 3));
      await tester.pumpAndSettle();

      // Check that we auto-navigated to profile selection
      expect(find.text('Profile Selection'), findsOneWidget);
    });
  });
}
