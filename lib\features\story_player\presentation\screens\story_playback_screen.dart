import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:choice_once_upon_a_time/models/enhanced_story_model.dart';
import 'package:choice_once_upon_a_time/features/story_library/data/enhanced_story_repository.dart';
import 'package:choice_once_upon_a_time/core/services/story_narration_service_interface.dart';
import 'package:choice_once_upon_a_time/core/services/enhanced_story_narration_service.dart';
import 'package:choice_once_upon_a_time/core/services/story_settings_service.dart';
import 'package:choice_once_upon_a_time/core/services/story_rewards_service.dart';
import 'package:choice_once_upon_a_time/shared_widgets/loading_indicator.dart';
import 'package:choice_once_upon_a_time/features/story_player/presentation/widgets/story_playback_widget.dart';
import 'package:choice_once_upon_a_time/features/story_player/presentation/widgets/story_completion_widget.dart';
import 'package:choice_once_upon_a_time/core/utils/app_logger.dart';

/// Dedicated screen for story playback and narration
/// Handles the main story experience with scene navigation and choices
class StoryPlaybackScreen extends StatefulWidget {
  final String storyId;

  const StoryPlaybackScreen({
    super.key,
    required this.storyId,
  });

  @override
  State<StoryPlaybackScreen> createState() => _StoryPlaybackScreenState();
}

class _StoryPlaybackScreenState extends State<StoryPlaybackScreen> {
  // Services
  late final EnhancedStoryRepository _repository;
  late final IStoryNarrationService _narrationService;
  late final StorySettingsService _settingsService;
  late final StoryRewardsService _rewardsService;

  // State
  EnhancedStoryModel? _story;
  EnhancedSceneModel? _currentScene;
  bool _isLoading = true;
  bool _isCompleted = false;
  String? _error;
  final List<String> _visitedScenes = [];

  @override
  void initState() {
    super.initState();
    AppLogger.debug('[COMPONENT_LOAD] Full path: lib/features/story_player/presentation/screens/story_playback_screen.dart - StoryPlaybackScreen');
    _initializeServices();
    _loadStory();
  }

  /// Initialize services
  void _initializeServices() {
    AppLogger.debug('[STORY_PLAYBACK] Initializing services');
    _repository = EnhancedStoryRepository();
    _narrationService = EnhancedStoryNarrationService();
    _settingsService = StorySettingsService.instance;
    _rewardsService = StoryRewardsService.instance;
  }

  /// Load the story and initialize services
  Future<void> _loadStory() async {
    AppLogger.debug('[STORY_PLAYBACK in load story] Function: _loadStory called with parameters: storyId=${widget.storyId}');

    try {
      setState(() {
        _isLoading = true;
        _error = null;
      });

      // Initialize services
      await Future.wait([
        _settingsService.initialize(),
        _rewardsService.initialize(),
        _narrationService.initialize(),
      ]);

      // Load the story
      final story = await _repository.fetchEnhancedStoryById(widget.storyId);

      // Log the story JSON asset path
      final storyJsonPath = 'assets/stories/${widget.storyId}/story.json';
      AppLogger.debug('[STORY_PLAYBACK XYZ1] Asset loaded: $storyJsonPath');

      setState(() {
        _story = story;
        _currentScene = story.initialScene;
        _isLoading = false;
      });

      AppLogger.debug('[STORY_PLAYBACK XYZ2] Function: _loadStory completed successfully');
    } catch (e) {
      AppLogger.error('[STORY_PLAYBACK XYZ3] Function: _loadStory failed', e);
      setState(() {
        _error = 'Failed to load story: $e';
        _isLoading = false;
      });
    }
  }

  /// Navigate to a specific scene
  Future<void> _navigateToScene(String sceneId) async {
    AppLogger.debug('[STORY_PLAYBACK XYZ4] Function: _navigateToScene called with parameters: sceneId=$sceneId, currentSceneId=${_currentScene?.id}');

    final scene = _story?.getSceneById(sceneId);
    if (scene != null) {
      AppLogger.debug('[SCENE_DEBUG XYZ5] Scene transition: ${_currentScene?.id} → $sceneId');

      // Log the scene image asset path
      final imagePath = scene.getImagePath(widget.storyId);
      AppLogger.debug('[STORY_PLAYBACK impath] Asset loaded: $imagePath');

      setState(() {
        _currentScene = scene;
        if (!_visitedScenes.contains(sceneId)) {
          _visitedScenes.add(sceneId);
        }
      });

      AppLogger.debug('[SCENE_DEBUG] Scene $sceneId loaded - Image: $imagePath');
      AppLogger.debug('[STORY_PLAYBACK] Function: _navigateToScene completed');
    } else {
      AppLogger.error('[STORY_PLAYBACK] Function: _navigateToScene failed - scene not found: $sceneId');
    }
  }

  /// Handle choice selection
  Future<void> _onChoiceSelected(ChoiceOptionModel choice) async {
    AppLogger.debug('[STORY_PLAYBACK] Choice selected: ${choice.option} -> ${choice.next}');

    // Award choice reward
    if (_story != null && _currentScene != null) {
      await _rewardsService.awardChoiceReward(
        _story!.storyId,
        choice.option,
        choice.option,
        _story!.moral,
      );
    }

    // Navigate to next scene
    await _navigateToScene(choice.next);

    // Check if story is complete
    if (_currentScene?.isEnding == true) {
      await _completeStory();
    }
  }

  /// Complete the story
  Future<void> _completeStory() async {
    AppLogger.debug('[STORY_PLAYBACK] Story completed');

    if (_story != null) {
      await _rewardsService.completeStory(
        _story!.storyId,
        _story!.title,
        _story!.moral,
      );
    }

    setState(() {
      _isCompleted = true;
    });
  }

  /// Restart the story
  Future<void> _restartStory() async {
    AppLogger.debug('[STORY_PLAYBACK] Restarting story');

    setState(() {
      _currentScene = _story?.initialScene;
      _visitedScenes.clear();
      _isCompleted = false;
    });
  }

  @override
  void dispose() {
    AppLogger.debug('[STORY_PLAYBACK] Disposing resources');
    _narrationService.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        AppLogger.debug('[STORY_PLAYBACK] Scaffold Widget Size: ${constraints.maxWidth}x${constraints.maxHeight}');
        return Scaffold(
          body: _buildBody(),
        );
      },
    );
  }

  /// Build the main body
  Widget _buildBody() {
    return LayoutBuilder(
      builder: (context, constraints) {
        AppLogger.debug('[STORY_PLAYBACK] Body Widget Size: ${constraints.maxWidth}x${constraints.maxHeight}');
        if (_isLoading) {
          return Container(
            color: Colors.blue[100], // Light blue for loading state
            child: LayoutBuilder(
              builder: (context, constraints) {
                AppLogger.debug('[STORY_PLAYBACK] LoadingIndicator Widget Size: ${constraints.maxWidth}x${constraints.maxHeight}');
                return const Center(child: LoadingIndicator());
              },
            ),
          );
        }

        if (_error != null) {
          return _buildErrorWidget();
        }

        if (_story == null || _currentScene == null) {
          return Container(
            color: Colors.red[100], // Light red for error state
            child: LayoutBuilder(
              builder: (context, constraints) {
                AppLogger.debug('[STORY_PLAYBACK] StoryNotFound Widget Size: ${constraints.maxWidth}x${constraints.maxHeight}');
                return const Center(
                  child: Text('Story not found'),
                );
              },
            ),
          );
        }

        return _isCompleted ? _buildCompletionWidget() : _buildStoryWidget();
      },
    );
  }

  /// Build story widget
  Widget _buildStoryWidget() {
    return Container(
      color: Colors.green[100], // Light green for story playback
      child: LayoutBuilder(
        builder: (context, constraints) {
          AppLogger.debug('[STORY_PLAYBACK] StoryPlaybackWidget Size: ${constraints.maxWidth}x${constraints.maxHeight}');
          return StoryPlaybackWidget(
            story: _story!,
            scene: _currentScene!,
            onChoiceSelected: _onChoiceSelected,
            onSceneComplete: () async {
              AppLogger.debug('[STORY_PLAYBACK] Manual scene completion triggered by user');
              if (_currentScene?.next != null) {
                await _navigateToScene(_currentScene!.next!);
              } else {
                await _completeStory();
              }
            },
            narrationService: _narrationService,
            settingsService: _settingsService,
          );
        },
      ),
    );
  }

  /// Build completion widget
  Widget _buildCompletionWidget() {
    return Container(
      color: Colors.yellow[100], // Light yellow for completion
      child: LayoutBuilder(
        builder: (context, constraints) {
          AppLogger.debug('[STORY_PLAYBACK] StoryCompletionWidget Size: ${constraints.maxWidth}x${constraints.maxHeight}');
          return StoryCompletionWidget(
            story: _story!,
            visitedScenes: _visitedScenes,
            onRestart: _restartStory,
            onExit: () => context.pop(),
            rewardsService: _rewardsService,
          );
        },
      ),
    );
  }

  /// Build error widget
  Widget _buildErrorWidget() {
    return Container(
      color: Colors.red[100], // Light red for error
      child: LayoutBuilder(
        builder: (context, constraints) {
          AppLogger.debug('[STORY_PLAYBACK] ErrorWidget Size: ${constraints.maxWidth}x${constraints.maxHeight}');
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                LayoutBuilder(
                  builder: (context, constraints) {
                    AppLogger.debug('[STORY_PLAYBACK] ErrorIcon Widget Size: ${constraints.maxWidth}x${constraints.maxHeight}');
                    return const Icon(Icons.error_outline, size: 64, color: Colors.red);
                  },
                ),
                const SizedBox(height: 16),
                LayoutBuilder(
                  builder: (context, constraints) {
                    AppLogger.debug('[STORY_PLAYBACK] ErrorText Widget Size: ${constraints.maxWidth}x${constraints.maxHeight}');
                    return Text(
                      _error ?? 'An error occurred',
                      style: const TextStyle(color: Colors.red),
                      textAlign: TextAlign.center,
                    );
                  },
                ),
                const SizedBox(height: 16),
                LayoutBuilder(
                  builder: (context, constraints) {
                    AppLogger.debug('[STORY_PLAYBACK] RetryButton Widget Size: ${constraints.maxWidth}x${constraints.maxHeight}');
                    return ElevatedButton(
                      onPressed: _loadStory,
                      child: const Text('Retry'),
                    );
                  },
                ),
                const SizedBox(height: 8),
                LayoutBuilder(
                  builder: (context, constraints) {
                    AppLogger.debug('[STORY_PLAYBACK] BackButton Widget Size: ${constraints.maxWidth}x${constraints.maxHeight}');
                    return TextButton(
                      onPressed: () => context.pop(),
                      child: const Text('Back'),
                    );
                  },
                ),
              ],
            ),
          );
        },
      ),
    );
  }
}