import 'dart:io';
import 'dart:convert';
import 'package:archive/archive.dart';
import 'package:path_provider/path_provider.dart';
import 'package:logger/logger.dart';

/// Service for extracting ZIP files containing story content
class ZipExtractionService {
  static final ZipExtractionService _instance = ZipExtractionService._internal();
  factory ZipExtractionService() => _instance;
  ZipExtractionService._internal();

  final Logger _logger = Logger();

  /// Extracts a story ZIP file to local storage
  /// 
  /// [zipFilePath] - Path to the ZIP file to extract
  /// [storyId] - ID of the story for organizing extracted files
  /// [onProgress] - Optional callback for extraction progress (0.0 to 1.0)
  /// 
  /// Returns the path to the extracted story directory
  Future<String> extractStoryZip(
    String zipFilePath,
    String storyId, {
    Function(double progress)? onProgress,
  }) async {
    try {
      _logger.i('[ZipExtractionService] Starting extraction for story: $storyId');

      // Get application documents directory
      final appDir = await getApplicationDocumentsDirectory();
      final storiesDir = Directory('${appDir.path}/stories');
      final storyDir = Directory('${storiesDir.path}/$storyId');

      // Create directories if they don't exist
      await storyDir.create(recursive: true);

      // Read the ZIP file
      final zipFile = File(zipFilePath);
      if (!await zipFile.exists()) {
        throw ZipExtractionException('ZIP file not found: $zipFilePath');
      }

      final bytes = await zipFile.readAsBytes();
      final archive = ZipDecoder().decodeBytes(bytes);

      _logger.i('[ZipExtractionService] ZIP contains ${archive.files.length} files');

      // Extract files with progress tracking
      int extractedCount = 0;
      final totalFiles = archive.files.length;

      for (final file in archive.files) {
        if (file.isFile) {
          final filePath = '${storyDir.path}/${file.name}';
          final outputFile = File(filePath);

          // Create parent directories if needed
          await outputFile.parent.create(recursive: true);

          // Write file content
          await outputFile.writeAsBytes(file.content as List<int>);
          
          _logger.d('[ZipExtractionService] Extracted: ${file.name}');
        }

        extractedCount++;
        if (onProgress != null) {
          onProgress(extractedCount / totalFiles);
        }
      }

      // Clean up the ZIP file
      await zipFile.delete();
      
      _logger.i('[ZipExtractionService] Successfully extracted story to: ${storyDir.path}');
      return storyDir.path;

    } catch (e) {
      _logger.e('[ZipExtractionService] Failed to extract story $storyId: $e');
      if (e is ZipExtractionException) {
        rethrow;
      }
      throw ZipExtractionException(
        'Failed to extract story ZIP file',
        details: e.toString(),
      );
    }
  }

  /// Validates that a story directory contains required files
  /// 
  /// [storyPath] - Path to the extracted story directory
  /// 
  /// Returns true if the story has all required files
  Future<bool> validateStoryStructure(String storyPath) async {
    try {
      final storyDir = Directory(storyPath);
      if (!await storyDir.exists()) {
        return false;
      }

      // Check for required story.json file
      final storyJsonFile = File('$storyPath/story.json');
      if (!await storyJsonFile.exists()) {
        _logger.w('[ZipExtractionService] Missing story.json in: $storyPath');
        return false;
      }

      // Validate story.json structure
      try {
        final jsonString = await storyJsonFile.readAsString();
        final jsonData = jsonDecode(jsonString) as Map<String, dynamic>;
        
        // Check for required fields
        if (!jsonData.containsKey('id') || !jsonData.containsKey('title')) {
          _logger.w('[ZipExtractionService] Invalid story.json structure in: $storyPath');
          return false;
        }

        _logger.i('[ZipExtractionService] Story structure validation passed: $storyPath');
        return true;

      } catch (e) {
        _logger.w('[ZipExtractionService] Failed to parse story.json in: $storyPath - $e');
        return false;
      }

    } catch (e) {
      _logger.e('[ZipExtractionService] Error validating story structure: $e');
      return false;
    }
  }

  /// Gets the local storage path for a story
  /// 
  /// [storyId] - ID of the story
  /// 
  /// Returns the path where the story should be stored locally
  Future<String> getStoryPath(String storyId) async {
    final appDir = await getApplicationDocumentsDirectory();
    return '${appDir.path}/stories/$storyId';
  }

  /// Checks if a story is already downloaded and extracted locally
  /// 
  /// [storyId] - ID of the story to check
  /// 
  /// Returns true if the story exists locally and is valid
  Future<bool> isStoryDownloaded(String storyId) async {
    try {
      final storyPath = await getStoryPath(storyId);
      return await validateStoryStructure(storyPath);
    } catch (e) {
      _logger.d('[ZipExtractionService] Story $storyId not found locally: $e');
      return false;
    }
  }

  /// Deletes a locally stored story
  /// 
  /// [storyId] - ID of the story to delete
  /// 
  /// Returns true if the story was successfully deleted
  Future<bool> deleteLocalStory(String storyId) async {
    try {
      final storyPath = await getStoryPath(storyId);
      final storyDir = Directory(storyPath);
      
      if (await storyDir.exists()) {
        await storyDir.delete(recursive: true);
        _logger.i('[ZipExtractionService] Deleted local story: $storyId');
        return true;
      }
      
      return false;
    } catch (e) {
      _logger.e('[ZipExtractionService] Failed to delete local story $storyId: $e');
      return false;
    }
  }

  /// Gets the size of a locally stored story in bytes
  /// 
  /// [storyId] - ID of the story
  /// 
  /// Returns the total size in bytes, or 0 if not found
  Future<int> getLocalStorySize(String storyId) async {
    try {
      final storyPath = await getStoryPath(storyId);
      final storyDir = Directory(storyPath);
      
      if (!await storyDir.exists()) {
        return 0;
      }

      int totalSize = 0;
      await for (final entity in storyDir.list(recursive: true)) {
        if (entity is File) {
          final stat = await entity.stat();
          totalSize += stat.size;
        }
      }

      return totalSize;
    } catch (e) {
      _logger.e('[ZipExtractionService] Failed to calculate story size for $storyId: $e');
      return 0;
    }
  }

  /// Lists all locally downloaded stories
  /// 
  /// Returns a list of story IDs that are available locally
  Future<List<String>> listLocalStories() async {
    try {
      final appDir = await getApplicationDocumentsDirectory();
      final storiesDir = Directory('${appDir.path}/stories');
      
      if (!await storiesDir.exists()) {
        return [];
      }

      final storyIds = <String>[];
      await for (final entity in storiesDir.list()) {
        if (entity is Directory) {
          final storyId = entity.path.split('/').last;
          if (await validateStoryStructure(entity.path)) {
            storyIds.add(storyId);
          }
        }
      }

      _logger.i('[ZipExtractionService] Found ${storyIds.length} local stories');
      return storyIds;

    } catch (e) {
      _logger.e('[ZipExtractionService] Failed to list local stories: $e');
      return [];
    }
  }
}

/// Custom exception for ZIP extraction operations
class ZipExtractionException implements Exception {
  final String message;
  final String? details;

  const ZipExtractionException(this.message, {this.details});

  @override
  String toString() {
    if (details != null) {
      return 'ZipExtractionException: $message\nDetails: $details';
    }
    return 'ZipExtractionException: $message';
  }
}
