import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:choice_once_upon_a_time/features/story_library/presentation/providers/new_story_library_provider.dart';
import 'package:choice_once_upon_a_time/core/services/new_story_service.dart';
import 'package:choice_once_upon_a_time/features/story_library/data/story_repository.dart';
import 'package:choice_once_upon_a_time/core/services/story_download_manager.dart';
import 'package:choice_once_upon_a_time/models/story_metadata_model.dart';
import 'package:choice_once_upon_a_time/models/firebase_story_metadata.dart';

import 'enhanced_story_library_provider_test.mocks.dart';

@GenerateMocks([
  NewStoryService,
  StoryRepository,
  StoryDownloadManager,
])
void main() {
  group('Enhanced Story Library Provider Tests', () {
    late ProviderContainer container;
    late MockNewStoryService mockStoryService;
    late MockStoryRepository mockStoryRepository;
    late MockStoryDownloadManager mockDownloadManager;

    setUp(() {
      mockStoryService = MockNewStoryService();
      mockStoryRepository = MockStoryRepository();
      mockDownloadManager = MockStoryDownloadManager();

      container = ProviderContainer(
        overrides: [
          newStoryServiceProvider.overrideWithValue(mockStoryService),
          storyRepositoryProvider.overrideWithValue(mockStoryRepository),
          storyDownloadManagerProvider.overrideWithValue(mockDownloadManager),
        ],
      );
    });

    tearDown(() {
      container.dispose();
    });

    group('Story Loading', () {
      test('should load stories from repository on initialization', () async {
        // Arrange
        final testStories = [
          StoryMetadataModel(
            id: 'story1',
            title: {'en-US': 'Test Story 1'},
            coverImageUrl: 'cover1.jpg',
            loglineShort: {'en-US': 'A test story'},
            targetMoralValue: 'friendship',
            targetAgeSubSegment: '5-7',
            estimatedDurationMinutes: 10,
            isFree: true,
            isLocked: false,
            published: true,
            dataSource: 'assets',
            version: '1.0.0',
            supportedLanguages: ['en-US'],
            defaultLanguage: 'en-US',
            initialSceneId: 'scene_1',
          ),
          StoryMetadataModel(
            id: 'story2',
            title: {'en-US': 'Test Story 2'},
            coverImageUrl: 'cover2.jpg',
            loglineShort: {'en-US': 'Another test story'},
            targetMoralValue: 'kindness',
            targetAgeSubSegment: '6-8',
            estimatedDurationMinutes: 15,
            isFree: true,
            isLocked: false,
            published: true,
            dataSource: 'firebase',
            version: '1.0.0',
            supportedLanguages: ['en-US'],
            defaultLanguage: 'en-US',
            initialSceneId: 'scene_1',
          ),
        ];

        when(mockStoryRepository.fetchStoryMetadataList())
            .thenAnswer((_) async => testStories);
        when(mockDownloadManager.downloadProgress)
            .thenAnswer((_) => const Stream.empty());

        // Act
        final notifier = container.read(newStoryLibraryProvider.notifier);
        await Future.delayed(const Duration(milliseconds: 100)); // Wait for initialization

        final state = container.read(newStoryLibraryProvider);

        // Assert
        expect(state.isLoading, isFalse);
        expect(state.stories.length, equals(2));
        expect(state.stories[0].id, equals('story1'));
        expect(state.stories[1].id, equals('story2'));
        expect(state.error, isNull);

        verify(mockStoryRepository.fetchStoryMetadataList()).called(1);
      });

      test('should handle loading errors gracefully', () async {
        // Arrange
        when(mockStoryRepository.fetchStoryMetadataList())
            .thenThrow(Exception('Failed to load stories'));
        when(mockDownloadManager.downloadProgress)
            .thenAnswer((_) => const Stream.empty());

        // Act
        final notifier = container.read(newStoryLibraryProvider.notifier);
        await Future.delayed(const Duration(milliseconds: 100)); // Wait for initialization

        final state = container.read(newStoryLibraryProvider);

        // Assert
        expect(state.isLoading, isFalse);
        expect(state.stories, isEmpty);
        expect(state.error, isNotNull);
        expect(state.error, contains('Failed to load stories'));
      });
    });

    group('Story Status Management', () {
      test('should return correct status for downloadable story', () async {
        // Arrange
        const storyId = 'firebase_story';

        when(mockDownloadManager.isDownloading(storyId)).thenReturn(false);
        when(mockDownloadManager.isDownloadFailed(storyId)).thenReturn(false);
        when(mockStoryRepository.getStoryStatus(storyId))
            .thenAnswer((_) async => 'download');
        when(mockDownloadManager.downloadProgress)
            .thenAnswer((_) => const Stream.empty());

        // Act
        final notifier = container.read(newStoryLibraryProvider.notifier);
        final status = await notifier.getStoryStatus(storyId);

        // Assert
        expect(status, equals('download'));
        verify(mockStoryRepository.getStoryStatus(storyId)).called(1);
      });

      test('should return downloading status when story is being downloaded', () async {
        // Arrange
        const storyId = 'firebase_story';

        when(mockDownloadManager.isDownloading(storyId)).thenReturn(true);
        when(mockDownloadManager.downloadProgress)
            .thenAnswer((_) => const Stream.empty());

        // Act
        final notifier = container.read(newStoryLibraryProvider.notifier);
        final status = await notifier.getStoryStatus(storyId);

        // Assert
        expect(status, equals('downloading'));
        verifyNever(mockStoryRepository.getStoryStatus(storyId));
      });

      test('should return download_failed status when download failed', () async {
        // Arrange
        const storyId = 'firebase_story';

        when(mockDownloadManager.isDownloading(storyId)).thenReturn(false);
        when(mockDownloadManager.isDownloadFailed(storyId)).thenReturn(true);
        when(mockDownloadManager.downloadProgress)
            .thenAnswer((_) => const Stream.empty());

        // Act
        final notifier = container.read(newStoryLibraryProvider.notifier);
        final status = await notifier.getStoryStatus(storyId);

        // Assert
        expect(status, equals('download_failed'));
        verifyNever(mockStoryRepository.getStoryStatus(storyId));
      });
    });

    group('Story Download Management', () {
      test('should download story successfully', () async {
        // Arrange
        const storyId = 'firebase_story';

        when(mockDownloadManager.downloadStory(storyId))
            .thenAnswer((_) async => true);
        when(mockStoryRepository.fetchStoryMetadataList())
            .thenAnswer((_) async => []);
        when(mockDownloadManager.downloadProgress)
            .thenAnswer((_) => const Stream.empty());

        // Act
        final notifier = container.read(newStoryLibraryProvider.notifier);
        final result = await notifier.downloadStory(storyId);

        // Assert
        expect(result, isTrue);
        verify(mockDownloadManager.downloadStory(storyId)).called(1);
        verify(mockStoryRepository.fetchStoryMetadataList()).called(atLeast(1)); // Called during refresh
      });

      test('should handle download failure', () async {
        // Arrange
        const storyId = 'firebase_story';

        when(mockDownloadManager.downloadStory(storyId))
            .thenAnswer((_) async => false);
        when(mockDownloadManager.downloadProgress)
            .thenAnswer((_) => const Stream.empty());

        // Act
        final notifier = container.read(newStoryLibraryProvider.notifier);
        final result = await notifier.downloadStory(storyId);

        // Assert
        expect(result, isFalse);
        verify(mockDownloadManager.downloadStory(storyId)).called(1);
      });

      test('should handle download exception', () async {
        // Arrange
        const storyId = 'firebase_story';

        when(mockDownloadManager.downloadStory(storyId))
            .thenThrow(Exception('Download failed'));
        when(mockDownloadManager.downloadProgress)
            .thenAnswer((_) => const Stream.empty());

        // Act
        final notifier = container.read(newStoryLibraryProvider.notifier);
        final result = await notifier.downloadStory(storyId);

        // Assert
        expect(result, isFalse);
        verify(mockDownloadManager.downloadStory(storyId)).called(1);
      });
    });

    group('Download Progress Tracking', () {
      test('should return correct download progress', () {
        // Arrange
        const storyId = 'firebase_story';
        const expectedProgress = 0.75;

        when(mockDownloadManager.getDownloadProgressPercentage(storyId))
            .thenReturn(expectedProgress);
        when(mockDownloadManager.downloadProgress)
            .thenAnswer((_) => const Stream.empty());

        // Act
        final notifier = container.read(newStoryLibraryProvider.notifier);
        final progress = notifier.getDownloadProgress(storyId);

        // Assert
        expect(progress, equals(expectedProgress));
        verify(mockDownloadManager.getDownloadProgressPercentage(storyId)).called(1);
      });

      test('should cancel download correctly', () async {
        // Arrange
        const storyId = 'firebase_story';

        when(mockDownloadManager.cancelDownload(storyId))
            .thenAnswer((_) async {});
        when(mockDownloadManager.downloadProgress)
            .thenAnswer((_) => const Stream.empty());

        // Act
        final notifier = container.read(newStoryLibraryProvider.notifier);
        await notifier.cancelDownload(storyId);

        // Assert
        verify(mockDownloadManager.cancelDownload(storyId)).called(1);
      });
    });

    group('Search Functionality', () {
      test('should update search query', () {
        // Arrange
        const searchQuery = 'adventure';
        when(mockDownloadManager.downloadProgress)
            .thenAnswer((_) => const Stream.empty());

        // Act
        final notifier = container.read(newStoryLibraryProvider.notifier);
        notifier.search(searchQuery);

        final state = container.read(newStoryLibraryProvider);

        // Assert
        expect(state.searchQuery, equals(searchQuery));
      });
    });

    group('Refresh Functionality', () {
      test('should refresh stories and clear caches', () async {
        // Arrange
        when(mockStoryService.clearCache()).thenReturn(null);
        when(mockStoryRepository.clearCache()).thenReturn(null);
        when(mockStoryRepository.fetchStoryMetadataList())
            .thenAnswer((_) async => []);
        when(mockDownloadManager.downloadProgress)
            .thenAnswer((_) => const Stream.empty());

        // Act
        final notifier = container.read(newStoryLibraryProvider.notifier);
        await notifier.refresh();

        // Assert
        verify(mockStoryService.clearCache()).called(1);
        verify(mockStoryRepository.clearCache()).called(1);
        verify(mockStoryRepository.fetchStoryMetadataList()).called(atLeast(1));
      });
    });

    group('Download Progress Stream', () {
      test('should listen to download progress updates', () async {
        // Arrange
        final progressController = StreamController<DownloadProgress>();
        when(mockDownloadManager.downloadProgress)
            .thenAnswer((_) => progressController.stream);

        // Act
        final notifier = container.read(newStoryLibraryProvider.notifier);
        
        // Emit a progress update
        progressController.add(DownloadProgress(
          storyId: 'test_story',
          progress: 0.5,
          status: DownloadStatus.downloading,
          lastUpdated: DateTime.now(),
        ));

        await Future.delayed(const Duration(milliseconds: 100));

        // Assert
        // The notifier should have received the progress update
        // This would trigger a state rebuild in the actual implementation
        verify(mockDownloadManager.downloadProgress).called(1);

        // Cleanup
        await progressController.close();
      });
    });
  });
}
