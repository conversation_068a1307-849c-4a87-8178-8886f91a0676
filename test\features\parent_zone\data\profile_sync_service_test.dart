import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:choice_once_upon_a_time/features/parent_zone/data/profile_sync_service.dart';
import 'package:choice_once_upon_a_time/features/parent_zone/data/user_profile_service.dart';

void main() {
  group('ProfileSyncService', () {
    late ProfileSyncService syncService;
    late UserProfileService userProfileService;

    setUp(() async {
      SharedPreferences.setMockInitialValues({});
      userProfileService = UserProfileService();
      syncService = ProfileSyncService(userProfileService);
    });

    group('syncProfiles', () {
      test('returns offline mode when no connectivity', () async {
        // Mock no connectivity by using the default implementation
        final result = await syncService.syncProfiles();

        expect(result.success, isTrue);
        expect(result.syncMode, equals(SyncMode.offline));
        expect(result.message, contains('offline'));
      });

      test('returns local profiles when sync fails', () async {
        // Add a local profile first
        final profile = UserProfile(
          id: 'test-1',
          name: 'Test User',
          age: 5,
          avatarColor: Colors.blue,
          favoriteStories: [],
          totalReadingTime: 0,
          storiesCompleted: 0,
          createdAt: DateTime.now(),
        );

        await userProfileService.createProfile(profile);

        final result = await syncService.syncProfiles();

        expect(result.success, isTrue);
        expect(result.profiles, hasLength(1));
        expect(result.profiles.first.name, equals('Test User'));
      });

      test('handles empty local profiles', () async {
        final result = await syncService.syncProfiles();

        expect(result.success, isTrue);
        expect(result.profiles, isEmpty);
        expect(result.syncMode, equals(SyncMode.offline));
      });
    });

    group('getLastSyncTime', () {
      test('returns null when no sync has occurred', () async {
        final lastSync = await syncService.getLastSyncTime();
        expect(lastSync, isNull);
      });

      test('returns correct timestamp after sync', () async {
        final beforeSync = DateTime.now();
        
        // Perform sync to set timestamp
        await syncService.syncProfiles();
        
        final afterSync = DateTime.now();
        final lastSync = await syncService.getLastSyncTime();

        expect(lastSync, isNotNull);
        expect(lastSync!.isAfter(beforeSync.subtract(const Duration(seconds: 1))), isTrue);
        expect(lastSync.isBefore(afterSync.add(const Duration(seconds: 1))), isTrue);
      });
    });

    group('needsSync', () {
      test('returns true when no sync has occurred', () async {
        final needsSync = await syncService.needsSync();
        expect(needsSync, isTrue);
      });

      test('returns false when sync is recent', () async {
        // Perform sync
        await syncService.syncProfiles();
        
        final needsSync = await syncService.needsSync();
        expect(needsSync, isFalse);
      });

      test('returns true when sync is old', () async {
        // Mock old sync time
        final prefs = await SharedPreferences.getInstance();
        final oldTime = DateTime.now().subtract(const Duration(days: 2));
        await prefs.setString('last_profile_sync', oldTime.toIso8601String());
        
        final needsSync = await syncService.needsSync();
        expect(needsSync, isTrue);
      });
    });

    group('retryPendingSync', () {
      test('handles no pending sync gracefully', () async {
        // Should not throw when no pending sync
        await syncService.retryPendingSync();
        
        // Verify no error occurred
        expect(true, isTrue); // Test passes if no exception
      });

      test('processes pending sync when available', () async {
        // Mock pending sync data
        final profile = UserProfile(
          id: 'test-1',
          name: 'Test User',
          age: 5,
          avatarColor: Colors.blue,
          favoriteStories: [],
          totalReadingTime: 0,
          storiesCompleted: 0,
          createdAt: DateTime.now(),
        );

        final prefs = await SharedPreferences.getInstance();
        await prefs.setString('pending_profile_sync', '[${profile.toJson()}]');
        
        // Retry pending sync
        await syncService.retryPendingSync();
        
        // Verify pending sync was cleared
        final pendingSync = prefs.getString('pending_profile_sync');
        expect(pendingSync, isNull);
      });
    });

    group('ProfileSyncResult', () {
      test('creates result with correct properties', () {
        final profiles = [
          UserProfile(
            id: 'test-1',
            name: 'Test User',
            age: 5,
            avatarColor: Colors.blue,
            favoriteStories: [],
            totalReadingTime: 0,
            storiesCompleted: 0,
            createdAt: DateTime.now(),
          ),
        ];

        final result = ProfileSyncResult(
          success: true,
          profiles: profiles,
          syncMode: SyncMode.online,
          message: 'Sync successful',
        );

        expect(result.success, isTrue);
        expect(result.profiles, hasLength(1));
        expect(result.syncMode, equals(SyncMode.online));
        expect(result.message, equals('Sync successful'));
      });

      test('toString returns formatted string', () {
        final result = ProfileSyncResult(
          success: true,
          profiles: [],
          syncMode: SyncMode.offline,
          message: 'Test message',
        );

        final string = result.toString();
        expect(string, contains('success: true'));
        expect(string, contains('profiles: 0'));
        expect(string, contains('mode: SyncMode.offline'));
        expect(string, contains('message: Test message'));
      });
    });

    group('UserProfileSync extension', () {
      test('isModifiedSince returns true when lastSync is null', () {
        final profile = UserProfile(
          id: 'test-1',
          name: 'Test User',
          age: 5,
          avatarColor: Colors.blue,
          favoriteStories: [],
          totalReadingTime: 0,
          storiesCompleted: 0,
          createdAt: DateTime.now(),
        );

        expect(profile.isModifiedSince(null), isTrue);
      });

      test('isModifiedSince returns true when profile is newer', () {
        final profile = UserProfile(
          id: 'test-1',
          name: 'Test User',
          age: 5,
          avatarColor: Colors.blue,
          favoriteStories: [],
          totalReadingTime: 0,
          storiesCompleted: 0,
          createdAt: DateTime.now(),
          lastActiveAt: DateTime.now(),
        );

        final oldSync = DateTime.now().subtract(const Duration(hours: 1));
        expect(profile.isModifiedSince(oldSync), isTrue);
      });

      test('isModifiedSince returns false when profile is older', () {
        final oldTime = DateTime.now().subtract(const Duration(hours: 2));
        final profile = UserProfile(
          id: 'test-1',
          name: 'Test User',
          age: 5,
          avatarColor: Colors.blue,
          favoriteStories: [],
          totalReadingTime: 0,
          storiesCompleted: 0,
          createdAt: oldTime,
          lastActiveAt: oldTime,
        );

        final recentSync = DateTime.now().subtract(const Duration(hours: 1));
        expect(profile.isModifiedSince(recentSync), isFalse);
      });

      test('toSyncJson includes sync timestamp', () {
        final profile = UserProfile(
          id: 'test-1',
          name: 'Test User',
          age: 5,
          avatarColor: Colors.blue,
          favoriteStories: [],
          totalReadingTime: 0,
          storiesCompleted: 0,
          createdAt: DateTime.now(),
        );

        final syncJson = profile.toSyncJson();
        
        expect(syncJson, containsPair('id', 'test-1'));
        expect(syncJson, containsPair('name', 'Test User'));
        expect(syncJson, contains('syncTimestamp'));
        expect(syncJson['syncTimestamp'], isA<String>());
      });
    });

    group('SyncMode enum', () {
      test('has correct values', () {
        expect(SyncMode.values, hasLength(2));
        expect(SyncMode.values, contains(SyncMode.online));
        expect(SyncMode.values, contains(SyncMode.offline));
      });
    });

    group('error handling', () {
      test('handles SharedPreferences errors gracefully', () async {
        // This test verifies that the service doesn't crash on storage errors
        // In a real implementation, you might mock SharedPreferences to throw errors
        
        final result = await syncService.syncProfiles();
        
        // Should still return a result even if storage fails
        expect(result, isNotNull);
        expect(result.profiles, isNotNull);
      });

      test('handles malformed sync data gracefully', () async {
        // Mock malformed pending sync data
        final prefs = await SharedPreferences.getInstance();
        await prefs.setString('pending_profile_sync', 'invalid json');
        
        // Should not throw
        await syncService.retryPendingSync();
        
        expect(true, isTrue); // Test passes if no exception
      });
    });
  });
}
