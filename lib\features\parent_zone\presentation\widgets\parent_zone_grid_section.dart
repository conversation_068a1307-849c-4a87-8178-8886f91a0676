import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

/// A widget representing the Parent Zone link on the Home Screen grid.
///
/// This provides a clear entry point for parents to access settings and
/// other parent-specific features.
class ParentZoneGridSection extends StatelessWidget {
  const ParentZoneGridSection({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final screenSize = MediaQuery.of(context).size;

    // Responsive icon size
    final iconSize = screenSize.width < 600 ? 40.0 : 48.0;

    return GestureDetector(
      onTap: () {
        // Navigate to the parental gate first, then the parent zone.
        context.go('/parent_gate_entry');
      },
      child: Card(
        elevation: 4.0,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16.0),
        ),
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(16.0),
            gradient: LinearGradient(
              colors: [
                theme.colorScheme.secondary.withValues(alpha: 0.8),
                theme.colorScheme.secondary.withValues(alpha: 0.9),
              ],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
          ),
          child: Padding(
            padding: EdgeInsets.symmetric(
              horizontal: screenSize.width * 0.02,
              vertical: 8.0,
            ),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.admin_panel_settings_outlined,
                  size: iconSize,
                  color: theme.colorScheme.onSecondary,
                ),
                const SizedBox(height: 8.0),
                Text(
                  'Parent Zone',
                  style: theme.textTheme.titleMedium?.copyWith(
                    color: theme.colorScheme.onSecondary,
                    fontWeight: FontWeight.bold,
                  ),
                  textAlign: TextAlign.center,
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
