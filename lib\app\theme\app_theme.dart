import 'package:flutter/material.dart';

class AppTheme {
  static const String primaryFontFamily = 'NunitoSans'; // Placeholder, user adds actual font

  // Define your colors here based on Task 2.3 document
  static const Color primaryColor = Color(0xFFFFDAB9); // Soft Peach
  static const Color secondaryColor = Color(0xFFA0D2DB); // Dusty Teal
  static const Color accentColor = Color(0xFFFF7F50); // Coral Pink
  static const Color backgroundColor = Color(0xFFFFF9E6); // Warm Cream
  static const Color textColor = Color(0xFF333333); // Dark Gray for text
  static const Color errorColor = Colors.red;

  static ThemeData get lightTheme {
    return ThemeData(
      primaryColor: primaryColor,
      scaffoldBackgroundColor: backgroundColor,
      colorScheme: const ColorScheme.light(
        primary: primaryColor,
        secondary: secondaryColor,
        tertiary: accentColor,
        error: errorColor,
        surface: backgroundColor,
        onPrimary: Colors.white, // Text on primary color
        onSecondary: Colors.white, // Text on secondary color
        onTertiary: Colors.white, // Text on tertiary color
        onError: Colors.white,
        onSurface: textColor,
      ),
      fontFamily: primaryFontFamily,
      textTheme: const TextTheme(
        displayLarge: TextStyle(fontSize: 32.0, fontWeight: FontWeight.bold, color: textColor, fontFamily: primaryFontFamily),
        displayMedium: TextStyle(fontSize: 28.0, fontWeight: FontWeight.bold, color: textColor, fontFamily: primaryFontFamily),
        displaySmall: TextStyle(fontSize: 24.0, fontWeight: FontWeight.bold, color: textColor, fontFamily: primaryFontFamily),
        headlineMedium: TextStyle(fontSize: 20.0, fontWeight: FontWeight.bold, color: textColor, fontFamily: primaryFontFamily),
        headlineSmall: TextStyle(fontSize: 18.0, fontWeight: FontWeight.w600, color: textColor, fontFamily: primaryFontFamily),
        titleLarge: TextStyle(fontSize: 16.0, fontWeight: FontWeight.w600, color: textColor, fontFamily: primaryFontFamily),
        bodyLarge: TextStyle(fontSize: 16.0, color: textColor, fontFamily: primaryFontFamily),
        bodyMedium: TextStyle(fontSize: 14.0, color: textColor, fontFamily: primaryFontFamily),
        labelLarge: TextStyle(fontSize: 16.0, fontWeight: FontWeight.bold, color: Colors.white, fontFamily: primaryFontFamily), // For buttons
      ),
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: primaryColor,
          foregroundColor: textColor, // Text color on button
          padding: const EdgeInsets.symmetric(horizontal: 24.0, vertical: 12.0),
          textStyle: const TextStyle(fontSize: 16.0, fontWeight: FontWeight.bold, fontFamily: primaryFontFamily),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8.0), // Soft rounded corners
          ),
        ),
      ),
      appBarTheme: const AppBarTheme(
        backgroundColor: primaryColor,
        foregroundColor: textColor, // Title text color
        elevation: 0, // Flat design
        titleTextStyle: TextStyle(fontSize: 20.0, fontWeight: FontWeight.bold, color: textColor, fontFamily: primaryFontFamily),
      ),
      cardTheme: const CardThemeData(
        elevation: 2.0,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.all(Radius.circular(12.0)), // Soft rounded corners
        ),
      ),
      // Define other theme properties like iconTheme, inputDecorationTheme etc. as needed
    );
  }
  // Optional: Define AppTheme.darkTheme similarly if planned
}
