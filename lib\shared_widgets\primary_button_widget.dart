import 'package:flutter/material.dart';

class PrimaryButtonWidget extends StatelessWidget {
  final String text;
  final VoidCallback? onPressed;
  final ButtonStyle? style; // Optional custom style
  final Widget? icon; // Optional icon

  const PrimaryButtonWidget({
    super.key,
    required this.text,
    required this.onPressed,
    this.style,
    this.icon,
  });

  @override
  Widget build(BuildContext context) {
    final buttonStyle = style ?? Theme.of(context).elevatedButtonTheme.style;

    if (icon != null) {
      return ElevatedButton.icon(
        icon: icon!,
        label: Text(text),
        onPressed: onPressed,
        style: buttonStyle,
      );
    } else {
      return ElevatedButton(
        onPressed: onPressed,
        style: buttonStyle,
        child: Text(text),
      );
    }
  }
}
