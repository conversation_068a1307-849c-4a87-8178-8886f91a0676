import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:choice_once_upon_a_time/app/providers/service_providers.dart';
import 'package:choice_once_upon_a_time/shared/widgets/primary_button_widget.dart';

/// Screen displayed when user has been idle for too long
class IdlePromptScreen extends ConsumerStatefulWidget {
  const IdlePromptScreen({super.key});

  @override
  ConsumerState<IdlePromptScreen> createState() => _IdlePromptScreenState();
}

class _IdlePromptScreenState extends ConsumerState<IdlePromptScreen>
    with TickerProviderStateMixin {
  late AnimationController _pulseController;
  late AnimationController _fadeController;
  late Animation<double> _pulseAnimation;
  late Animation<double> _fadeAnimation;
  
  bool _isPlayingPrompt = false;
  int _countdownSeconds = 30;
  
  @override
  void initState() {
    super.initState();
    
    // Set up animations
    _pulseController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );
    
    _pulseAnimation = Tween<double>(
      begin: 0.8,
      end: 1.2,
    ).animate(CurvedAnimation(
      parent: _pulseController,
      curve: Curves.easeInOut,
    ));
    
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(_fadeController);
    
    // Start animations
    _pulseController.repeat(reverse: true);
    _fadeController.forward();
    
    // Auto-play the idle prompt
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _playIdlePrompt();
      _startCountdown();
    });
  }

  @override
  void dispose() {
    _pulseController.dispose();
    _fadeController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Scaffold(
      backgroundColor: Colors.black.withOpacity(0.8),
      body: FadeTransition(
        opacity: _fadeAnimation,
        child: Center(
          child: Container(
            margin: const EdgeInsets.all(24),
            padding: const EdgeInsets.all(32),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(20),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.3),
                  blurRadius: 20,
                  offset: const Offset(0, 10),
                ),
              ],
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Animated character
                AnimatedBuilder(
                  animation: _pulseAnimation,
                  builder: (context, child) {
                    return Transform.scale(
                      scale: _pulseAnimation.value,
                      child: Container(
                        width: 100,
                        height: 100,
                        decoration: BoxDecoration(
                          color: Colors.orange[100],
                          shape: BoxShape.circle,
                        ),
                        child: Icon(
                          Icons.waving_hand,
                          size: 50,
                          color: Colors.orange[600],
                        ),
                      ),
                    );
                  },
                ),
                
                const SizedBox(height: 24),
                
                // Title
                Text(
                  'Are You Still There?',
                  style: theme.textTheme.headlineMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: Colors.orange[700],
                  ),
                  textAlign: TextAlign.center,
                ),
                
                const SizedBox(height: 16),
                
                // Message
                Text(
                  'I haven\'t heard from you in a while.\nWould you like to continue the story?',
                  style: theme.textTheme.bodyLarge?.copyWith(
                    color: Colors.grey[600],
                  ),
                  textAlign: TextAlign.center,
                ),
                
                const SizedBox(height: 24),
                
                // Countdown
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  decoration: BoxDecoration(
                    color: Colors.orange[50],
                    borderRadius: BorderRadius.circular(20),
                    border: Border.all(color: Colors.orange[200]!),
                  ),
                  child: Text(
                    'Auto-exit in $_countdownSeconds seconds',
                    style: TextStyle(
                      color: Colors.orange[700],
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
                
                const SizedBox(height: 32),
                
                // Action buttons
                Column(
                  children: [
                    // Continue button
                    SizedBox(
                      width: double.infinity,
                      child: PrimaryButtonWidget(
                        text: 'Yes, Continue Story',
                        onPressed: () => _continueStory(context),
                        icon: const Icon(Icons.play_arrow),
                      ),
                    ),
                    
                    const SizedBox(height: 12),
                    
                    // Replay prompt button
                    SizedBox(
                      width: double.infinity,
                      child: OutlinedButton.icon(
                        onPressed: _isPlayingPrompt ? _stopPrompt : _playIdlePrompt,
                        icon: Icon(_isPlayingPrompt ? Icons.stop : Icons.volume_up),
                        label: Text(_isPlayingPrompt ? 'Stop' : 'Repeat Message'),
                      ),
                    ),
                    
                    const SizedBox(height: 24),
                    
                    // Exit button
                    TextButton.icon(
                      onPressed: () => _exitToLibrary(context),
                      icon: Icon(
                        Icons.home,
                        color: Colors.grey[600],
                      ),
                      label: Text(
                        'Return to Library',
                        style: TextStyle(
                          color: Colors.grey[600],
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Future<void> _playIdlePrompt() async {
    final ttsService = ref.read(ttsServiceProvider);
    
    setState(() {
      _isPlayingPrompt = true;
    });

    try {
      // Play the idle prompt with a gentle, caring tone
      await ttsService.speakText(
        'Are you still there? I haven\'t heard from you in a while. Would you like to continue our story together?',
        emotionCue: 'gentle',
      );
    } catch (e) {
      debugPrint('Error playing idle prompt: $e');
    } finally {
      if (mounted) {
        setState(() {
          _isPlayingPrompt = false;
        });
      }
    }
  }

  void _stopPrompt() {
    final ttsService = ref.read(ttsServiceProvider);
    ttsService.stop();
    setState(() {
      _isPlayingPrompt = false;
    });
  }

  void _startCountdown() {
    Future.delayed(const Duration(seconds: 1), () {
      if (mounted && _countdownSeconds > 0) {
        setState(() {
          _countdownSeconds--;
        });
        _startCountdown();
      } else if (mounted && _countdownSeconds == 0) {
        _exitToLibrary(context);
      }
    });
  }

  void _continueStory(BuildContext context) {
    // Close the idle prompt and continue the story
    Navigator.of(context).pop();
  }

  void _exitToLibrary(BuildContext context) {
    // Exit to the story library
    Navigator.of(context).pop();
    context.go('/home');
  }
}
