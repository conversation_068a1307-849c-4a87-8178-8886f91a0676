import 'package:flutter/material.dart';
import 'package:choice_once_upon_a_time/models/enhanced_story_model.dart';
import 'package:choice_once_upon_a_time/core/services/story_narration_service_interface.dart';
import 'package:choice_once_upon_a_time/core/utils/app_logger.dart';
import 'package:choice_once_upon_a_time/features/story_player/presentation/widgets/vocabulary_discussion_widget.dart';

/// Comprehensive story completion flow widget
/// Handles rewards, moral lessons, vocabulary learning, and profile persistence
class StoryCompletionFlowWidget extends StatefulWidget {
  final EnhancedStoryModel story;
  final EnhancedSceneModel finalScene;
  final List<String> visitedScenes;
  final IStoryNarrationService narrationService;
  final VoidCallback onComplete;
  final VoidCallback? onReplay;

  const StoryCompletionFlowWidget({
    super.key,
    required this.story,
    required this.finalScene,
    required this.visitedScenes,
    required this.narrationService,
    required this.onComplete,
    this.onReplay,
  });

  @override
  State<StoryCompletionFlowWidget> createState() => _StoryCompletionFlowWidgetState();
}

class _StoryCompletionFlowWidgetState extends State<StoryCompletionFlowWidget>
    with TickerProviderStateMixin {
  
  // Animation controllers
  late final AnimationController _fadeController;
  late final AnimationController _slideController;
  late final Animation<double> _fadeAnimation;
  late final Animation<Offset> _slideAnimation;

  // Flow state
  CompletionPhase _currentPhase = CompletionPhase.rewards;
  bool _isNarrating = false;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _startCompletionFlow();
  }

  /// Initialize animations
  void _initializeAnimations() {
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _slideController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _fadeController,
      curve: Curves.easeInOut,
    ));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 1),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _slideController,
      curve: Curves.elasticOut,
    ));

    _fadeController.forward();
    _slideController.forward();
  }

  /// Start the completion flow
  Future<void> _startCompletionFlow() async {
    AppLogger.debug('[STORY_COMPLETION] Starting completion flow');
    
    // Start with rewards phase
    await _handleRewardsPhase();
  }

  /// Handle rewards phase
  Future<void> _handleRewardsPhase() async {
    setState(() {
      _currentPhase = CompletionPhase.rewards;
      _isNarrating = true;
    });

    try {
      // Narrate completion message
      final completionMessage = _getCompletionMessage();
      await widget.narrationService.narrateText(
        completionMessage,
        emotionCue: 'pleased',
        storyId: widget.story.storyId,
        sceneId: 'completion',
      );

      // Narrate rewards
      if (widget.finalScene.rewards != null) {
        final rewardsMessage = _getRewardsMessage();
        await widget.narrationService.narrateText(
          rewardsMessage,
          emotionCue: 'excited',
          storyId: widget.story.storyId,
          sceneId: 'rewards',
        );
      }

      setState(() {
        _isNarrating = false;
      });

      // Auto-advance to moral lesson after delay
      Future.delayed(const Duration(seconds: 3), () {
        if (mounted) {
          _transitionToMoralLesson();
        }
      });

    } catch (e) {
      AppLogger.debug('[STORY_COMPLETION] Error during rewards narration: $e');
      setState(() {
        _isNarrating = false;
      });
    }
  }

  /// Transition to moral lesson phase
  Future<void> _transitionToMoralLesson() async {
    await _slideController.reverse();
    
    setState(() {
      _currentPhase = CompletionPhase.moralLesson;
      _isNarrating = true;
    });

    await _slideController.forward();

    try {
      // Narrate moral lesson
      final moralMessage = _getMoralLessonMessage();
      await widget.narrationService.narrateText(
        moralMessage,
        emotionCue: 'gentle',
        storyId: widget.story.storyId,
        sceneId: 'moral',
      );

      setState(() {
        _isNarrating = false;
      });

      // Auto-advance to vocabulary after delay
      Future.delayed(const Duration(seconds: 3), () {
        if (mounted) {
          _transitionToVocabulary();
        }
      });

    } catch (e) {
      AppLogger.debug('[STORY_COMPLETION] Error during moral lesson narration: $e');
      setState(() {
        _isNarrating = false;
      });
    }
  }

  /// Transition to vocabulary phase
  Future<void> _transitionToVocabulary() async {
    // Check if vocabulary discussion is available
    final vocabularyEntries = widget.story.postStory.discussion?.vocabularyDiscussion;

    if (vocabularyEntries == null || vocabularyEntries.isEmpty) {
      // Skip vocabulary if not available
      _transitionToFinal();
      return;
    }

    await _slideController.reverse();

    setState(() {
      _currentPhase = CompletionPhase.vocabulary;
      _isNarrating = false;
    });

    await _slideController.forward();
  }

  /// Transition to final phase
  Future<void> _transitionToFinal() async {
    await _slideController.reverse();
    
    setState(() {
      _currentPhase = CompletionPhase.finalPhase;
      _isNarrating = false;
    });

    await _slideController.forward();

    // Save progress to child profile
    await _saveProgressToProfile();
  }

  /// Save progress to child profile
  Future<void> _saveProgressToProfile() async {
    try {
      AppLogger.debug('[STORY_COMPLETION] Saving progress to child profile');
      
      // TODO: Implement child profile persistence
      // This would save:
      // - Story completion
      // - Rewards earned
      // - Vocabulary words learned
      // - Moral lessons learned
      
      AppLogger.debug('[STORY_COMPLETION] Progress saved successfully');
    } catch (e) {
      AppLogger.debug('[STORY_COMPLETION] Error saving progress: $e');
    }
  }

  /// Get completion message based on outcome
  String _getCompletionMessage() {
    final outcome = widget.finalScene.outcome ?? 'neutral';
    
    switch (outcome.toLowerCase()) {
      case 'good':
        return "Wonderful! You made great choices in this story. ${widget.story.title} is complete!";
      case 'bad':
        return "The story is complete! Every choice teaches us something important. Well done for finishing ${widget.story.title}!";
      default:
        return "Congratulations! You have completed ${widget.story.title}. What an adventure!";
    }
  }

  /// Get rewards message
  String _getRewardsMessage() {
    final rewards = widget.finalScene.rewards;
    if (rewards == null) return "You've earned the reward of a completed story!";

    final completionRewards = rewards.completion ?? 0;
    final goodChoiceRewards = rewards.good ?? 0;
    
    return "You've earned $completionRewards completion stars and $goodChoiceRewards choice stars! Great job!";
  }

  /// Get moral lesson message
  String _getMoralLessonMessage() {
    final reflection = widget.finalScene.reflection;
    if (reflection != null) {
      return "Let's think about what we learned: ${reflection.text}";
    }
    
    return "The moral of this story is about ${widget.story.moral}. Remember this lesson in your own life!";
  }

  @override
  void dispose() {
    _fadeController.dispose();
    _slideController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return FadeTransition(
      opacity: _fadeAnimation,
      child: Container(
        color: Colors.black.withValues(alpha: 0.9),
        child: Center(
          child: SlideTransition(
            position: _slideAnimation,
            child: _buildCurrentPhase(),
          ),
        ),
      ),
    );
  }

  /// Build current phase widget
  Widget _buildCurrentPhase() {
    switch (_currentPhase) {
      case CompletionPhase.rewards:
        return _buildRewardsPhase();
      case CompletionPhase.moralLesson:
        return _buildMoralLessonPhase();
      case CompletionPhase.vocabulary:
        return _buildVocabularyPhase();
      case CompletionPhase.finalPhase:
        return _buildFinalPhase();
    }
  }

  /// Build rewards phase
  Widget _buildRewardsPhase() {
    final screenSize = MediaQuery.of(context).size;
    final isSmallScreen = screenSize.width < 600;

    return Container(
      margin: EdgeInsets.all(isSmallScreen ? 16 : 32),
      constraints: BoxConstraints(
        maxHeight: screenSize.height * 0.8,
        maxWidth: isSmallScreen ? double.infinity : 500,
      ),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
      ),
      child: SingleChildScrollView(
        padding: EdgeInsets.all(isSmallScreen ? 16 : 24),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Icon(
              Icons.celebration,
              size: 64,
              color: Colors.amber,
            ),
            const SizedBox(height: 16),
            Text(
              'Story Complete!',
              style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: Colors.deepPurple,
                fontSize: isSmallScreen ? 20 : null,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            Text(
              _getCompletionMessage(),
              style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                fontSize: isSmallScreen ? 14 : null,
              ),
              textAlign: TextAlign.center,
            ),
            if (widget.finalScene.rewards != null) ...[
              const SizedBox(height: 24),
              _buildRewardsDisplay(),
            ],
            if (_isNarrating) ...[
              const SizedBox(height: 16),
              const CircularProgressIndicator(),
            ],
          ],
        ),
      ),
    );
  }

  /// Build rewards display
  Widget _buildRewardsDisplay() {
    final rewards = widget.finalScene.rewards!;
    final screenSize = MediaQuery.of(context).size;
    final isSmallScreen = screenSize.width < 600;

    return Container(
      padding: EdgeInsets.all(isSmallScreen ? 12 : 16),
      decoration: BoxDecoration(
        color: Colors.amber[50],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.amber[200]!),
      ),
      child: isSmallScreen
          ? Column(
              children: [
                _buildRewardItem(
                  icon: Icons.star,
                  label: 'Completion',
                  value: '${rewards.completion}',
                  color: Colors.blue,
                ),
                const SizedBox(height: 16),
                _buildRewardItem(
                  icon: Icons.favorite,
                  label: 'Good Choices',
                  value: '${rewards.good}',
                  color: Colors.red,
                ),
              ],
            )
          : Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                _buildRewardItem(
                  icon: Icons.star,
                  label: 'Completion',
                  value: '${rewards.completion}',
                  color: Colors.blue,
                ),
                _buildRewardItem(
                  icon: Icons.favorite,
                  label: 'Good Choices',
                  value: '${rewards.good}',
                  color: Colors.red,
                ),
              ],
            ),
    );
  }

  /// Build individual reward item
  Widget _buildRewardItem({
    required IconData icon,
    required String label,
    required String value,
    required Color color,
  }) {
    return Column(
      children: [
        Icon(icon, color: color, size: 32),
        const SizedBox(height: 4),
        Text(
          value,
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
        Text(
          label,
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey[600],
          ),
        ),
      ],
    );
  }

  /// Build moral lesson phase
  Widget _buildMoralLessonPhase() {
    final screenSize = MediaQuery.of(context).size;
    final isSmallScreen = screenSize.width < 600;

    return Container(
      margin: EdgeInsets.all(isSmallScreen ? 16 : 32),
      constraints: BoxConstraints(
        maxHeight: screenSize.height * 0.8,
        maxWidth: isSmallScreen ? double.infinity : 500,
      ),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
      ),
      child: SingleChildScrollView(
        padding: EdgeInsets.all(isSmallScreen ? 16 : 24),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Icon(
              Icons.lightbulb,
              size: 64,
              color: Colors.orange,
            ),
            const SizedBox(height: 16),
            Text(
              'Moral Lesson',
              style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: Colors.deepPurple,
                fontSize: isSmallScreen ? 20 : null,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            Text(
              _getMoralLessonMessage(),
              style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                fontSize: isSmallScreen ? 14 : null,
              ),
              textAlign: TextAlign.center,
            ),
            if (_isNarrating) ...[
              const SizedBox(height: 16),
              const CircularProgressIndicator(),
            ],
          ],
        ),
      ),
    );
  }

  /// Build vocabulary phase
  Widget _buildVocabularyPhase() {
    final vocabularyEntries = widget.story.postStory.discussion?.vocabularyDiscussion;

    if (vocabularyEntries == null || vocabularyEntries.isEmpty) {
      return const SizedBox.shrink();
    }

    // Convert model entries to widget entries
    final widgetEntries = vocabularyEntries.map((entry) => VocabularyEntry(
      word: entry.word,
      explanation: entry.explanation,
      image: entry.image,
    )).toList();

    return VocabularyDiscussionWidget(
      vocabularyEntries: widgetEntries,
      storyId: widget.story.storyId,
      narrationService: widget.narrationService,
      onComplete: _transitionToFinal,
      onSkip: _transitionToFinal,
    );
  }

  /// Build final phase
  Widget _buildFinalPhase() {
    final screenSize = MediaQuery.of(context).size;
    final isSmallScreen = screenSize.width < 600;

    return Container(
      margin: EdgeInsets.all(isSmallScreen ? 16 : 32),
      constraints: BoxConstraints(
        maxHeight: screenSize.height * 0.8,
        maxWidth: isSmallScreen ? double.infinity : 500,
      ),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
      ),
      child: SingleChildScrollView(
        padding: EdgeInsets.all(isSmallScreen ? 16 : 24),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Icon(
              Icons.check_circle,
              size: 64,
              color: Colors.green,
            ),
            const SizedBox(height: 16),
            Text(
              'Well Done!',
              style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: Colors.deepPurple,
                fontSize: isSmallScreen ? 20 : null,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            Text(
              'Your progress has been saved. Ready for another adventure?',
              style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                fontSize: isSmallScreen ? 14 : null,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            isSmallScreen
                ? Column(
                    children: [
                      if (widget.onReplay != null) ...[
                        SizedBox(
                          width: double.infinity,
                          child: ElevatedButton.icon(
                            onPressed: widget.onReplay,
                            icon: const Icon(Icons.replay),
                            label: const Text('Play Again'),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.blue,
                              foregroundColor: Colors.white,
                              padding: const EdgeInsets.symmetric(vertical: 12),
                            ),
                          ),
                        ),
                        const SizedBox(height: 12),
                      ],
                      SizedBox(
                        width: double.infinity,
                        child: ElevatedButton.icon(
                          onPressed: widget.onComplete,
                          icon: const Icon(Icons.home),
                          label: const Text('Back to Library'),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.green,
                            foregroundColor: Colors.white,
                            padding: const EdgeInsets.symmetric(vertical: 12),
                          ),
                        ),
                      ),
                    ],
                  )
                : Row(
                    mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                    children: [
                      if (widget.onReplay != null)
                        Flexible(
                          child: ElevatedButton.icon(
                            onPressed: widget.onReplay,
                            icon: const Icon(Icons.replay),
                            label: const Text('Play Again'),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.blue,
                              foregroundColor: Colors.white,
                            ),
                          ),
                        ),
                      if (widget.onReplay != null) const SizedBox(width: 12),
                      Flexible(
                        child: ElevatedButton.icon(
                          onPressed: widget.onComplete,
                          icon: const Icon(Icons.home),
                          label: const Text('Back to Library'),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.green,
                            foregroundColor: Colors.white,
                          ),
                        ),
                      ),
                    ],
                  ),
          ],
        ),
      ),
    );
  }
}

/// Completion flow phases
enum CompletionPhase {
  rewards,
  moralLesson,
  vocabulary,
  finalPhase,
}
