import 'dart:async';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:in_app_purchase/in_app_purchase.dart';

/// Subscription service for managing in-app purchases
class SubscriptionService {
  final InAppPurchase _inAppPurchase;
  late StreamSubscription<List<PurchaseDetails>> _subscription;
  
  // Product IDs - these should match your App Store Connect / Google Play Console setup
  static const String monthlyPremiumId = 'monthly_premium';
  static const String annualPremiumId = 'annual_premium';
  
  static const Set<String> _productIds = {
    monthlyPremiumId,
    annualPremiumId,
  };

  List<ProductDetails> _products = [];
  bool _isAvailable = false;
  bool _isInitialized = false;

  SubscriptionService({InAppPurchase? inAppPurchase})
      : _inAppPurchase = inAppPurchase ?? InAppPurchase.instance;

  /// Available products
  List<ProductDetails> get products => _products;

  /// Whether IAP is available on this platform
  bool get isAvailable => _isAvailable;

  /// Whether the service has been initialized
  bool get isInitialized => _isInitialized;

  /// Initialize the in-app purchase store
  Future<void> initializeStore() async {
    if (_isInitialized) return;

    try {
      // Check if IAP is available
      _isAvailable = await _inAppPurchase.isAvailable();
      
      if (!_isAvailable) {
        print('IAP: In-app purchases not available on this device');
        return;
      }

      // Listen to purchase updates
      _subscription = _inAppPurchase.purchaseStream.listen(
        _onPurchaseUpdate,
        onDone: () => print('IAP: Purchase stream closed'),
        onError: (error) => print('IAP: Purchase stream error: $error'),
      );

      _isInitialized = true;
      print('IAP: Store initialized successfully');
    } catch (e) {
      print('IAP: Failed to initialize store: $e');
      throw SubscriptionException('Failed to initialize store: $e');
    }
  }

  /// Fetch available products from the store
  Future<void> fetchProducts() async {
    if (!_isInitialized) {
      throw SubscriptionException('Store not initialized');
    }

    if (!_isAvailable) {
      throw SubscriptionException('In-app purchases not available');
    }

    try {
      final ProductDetailsResponse response = await _inAppPurchase.queryProductDetails(_productIds);
      
      if (response.error != null) {
        throw SubscriptionException('Failed to fetch products: ${response.error!.message}');
      }

      _products = response.productDetails;
      
      if (response.notFoundIDs.isNotEmpty) {
        print('IAP: Products not found: ${response.notFoundIDs}');
      }

      print('IAP: Fetched ${_products.length} products');
      for (final product in _products) {
        print('IAP: Product - ${product.id}: ${product.title} (${product.price})');
      }
    } catch (e) {
      print('IAP: Error fetching products: $e');
      throw SubscriptionException('Failed to fetch products: $e');
    }
  }

  /// Purchase a subscription
  Future<void> purchaseSubscription(String productId) async {
    if (!_isInitialized) {
      throw SubscriptionException('Store not initialized');
    }

    if (!_isAvailable) {
      throw SubscriptionException('In-app purchases not available');
    }

    final product = _products.firstWhere(
      (p) => p.id == productId,
      orElse: () => throw SubscriptionException('Product not found: $productId'),
    );

    try {
      final PurchaseParam purchaseParam = PurchaseParam(productDetails: product);
      
      // For subscriptions, use buyNonConsumable
      final bool success = await _inAppPurchase.buyNonConsumable(purchaseParam: purchaseParam);
      
      if (!success) {
        throw SubscriptionException('Failed to initiate purchase');
      }

      print('IAP: Purchase initiated for $productId');
    } catch (e) {
      print('IAP: Purchase error: $e');
      throw SubscriptionException('Purchase failed: $e');
    }
  }

  /// Restore previous purchases
  Future<void> restorePurchases() async {
    if (!_isInitialized) {
      throw SubscriptionException('Store not initialized');
    }

    if (!_isAvailable) {
      throw SubscriptionException('In-app purchases not available');
    }

    try {
      await _inAppPurchase.restorePurchases();
      print('IAP: Restore purchases initiated');
    } catch (e) {
      print('IAP: Restore purchases error: $e');
      throw SubscriptionException('Failed to restore purchases: $e');
    }
  }

  /// Handle purchase updates
  void _onPurchaseUpdate(List<PurchaseDetails> purchaseDetailsList) {
    for (final PurchaseDetails purchaseDetails in purchaseDetailsList) {
      print('IAP: Purchase update - ${purchaseDetails.productID}: ${purchaseDetails.status}');
      
      switch (purchaseDetails.status) {
        case PurchaseStatus.pending:
          _handlePendingPurchase(purchaseDetails);
          break;
        case PurchaseStatus.purchased:
          _handleSuccessfulPurchase(purchaseDetails);
          break;
        case PurchaseStatus.error:
          _handleFailedPurchase(purchaseDetails);
          break;
        case PurchaseStatus.restored:
          _handleRestoredPurchase(purchaseDetails);
          break;
        case PurchaseStatus.canceled:
          _handleCanceledPurchase(purchaseDetails);
          break;
      }

      // Complete the purchase if it's not pending
      if (purchaseDetails.pendingCompletePurchase) {
        _inAppPurchase.completePurchase(purchaseDetails);
      }
    }
  }

  void _handlePendingPurchase(PurchaseDetails purchaseDetails) {
    print('IAP: Purchase pending for ${purchaseDetails.productID}');
    // Show loading state to user
  }

  void _handleSuccessfulPurchase(PurchaseDetails purchaseDetails) {
    print('IAP: Purchase successful for ${purchaseDetails.productID}');
    
    // In a real implementation, you would:
    // 1. Verify the purchase with your backend
    // 2. Update user's subscription status
    // 3. Unlock premium content
    
    // For Sprint 5 (UI shell), we just log the success
    print('IAP: Would verify purchase and unlock premium content');
  }

  void _handleFailedPurchase(PurchaseDetails purchaseDetails) {
    print('IAP: Purchase failed for ${purchaseDetails.productID}: ${purchaseDetails.error}');
    // Show error message to user
  }

  void _handleRestoredPurchase(PurchaseDetails purchaseDetails) {
    print('IAP: Purchase restored for ${purchaseDetails.productID}');
    
    // In a real implementation, you would:
    // 1. Verify the restored purchase
    // 2. Update user's subscription status
    // 3. Unlock premium content
    
    // For Sprint 5 (UI shell), we just log the restoration
    print('IAP: Would verify restored purchase and unlock premium content');
  }

  void _handleCanceledPurchase(PurchaseDetails purchaseDetails) {
    print('IAP: Purchase canceled for ${purchaseDetails.productID}');
    // Handle cancellation (usually just dismiss loading state)
  }

  /// Get product details by ID
  ProductDetails? getProduct(String productId) {
    try {
      return _products.firstWhere((product) => product.id == productId);
    } catch (e) {
      return null;
    }
  }

  /// Check if user has active subscription (mock implementation for Sprint 5)
  bool hasActiveSubscription() {
    // In a real implementation, this would check:
    // 1. Local subscription status
    // 2. Server-side verification
    // 3. Receipt validation
    
    // For Sprint 5, return false (free tier)
    return false;
  }

  /// Dispose of resources
  void dispose() {
    if (_isInitialized) {
      _subscription.cancel();
    }
  }
}

/// Custom exception for subscription-related errors
class SubscriptionException implements Exception {
  final String message;
  final String? code;

  SubscriptionException(this.message, {this.code});

  @override
  String toString() => 'SubscriptionException: $message';
}

/// Provider for SubscriptionService
final subscriptionServiceProvider = Provider<SubscriptionService>((ref) {
  final service = SubscriptionService();
  
  // Dispose when provider is disposed
  ref.onDispose(() {
    service.dispose();
  });
  
  return service;
});

/// Provider for subscription status
final subscriptionStatusProvider = Provider<bool>((ref) {
  final subscriptionService = ref.watch(subscriptionServiceProvider);
  return subscriptionService.hasActiveSubscription();
});

/// Provider for available products
final availableProductsProvider = Provider<List<ProductDetails>>((ref) {
  final subscriptionService = ref.watch(subscriptionServiceProvider);
  return subscriptionService.products;
});
