import 'dart:async';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:choice_once_upon_a_time/core/audio/voice_guidance_manager.dart';
import 'package:choice_once_upon_a_time/l10n/app_localizations.dart';
import 'package:choice_once_upon_a_time/core/utils/app_logger.dart';

class ParentalGateScreen extends ConsumerStatefulWidget {
  const ParentalGateScreen({super.key});

  @override
  ConsumerState<ParentalGateScreen> createState() => _ParentalGateScreenState();
}

class _ParentalGateScreenState extends ConsumerState<ParentalGateScreen>
    with TickerProviderStateMixin {
  static const Duration _holdDuration = Duration(seconds: 3);

  Timer? _holdTimer;
  bool _isHolding = false;
  double _progress = 0.0;
  late AnimationController _progressController;
  late Animation<double> _progressAnimation;

  @override
  void initState() {
    super.initState();
    AppLogger.debug('ParentalGate: initState called');
    _progressController = AnimationController(
      duration: _holdDuration,
      vsync: this,
    );
    _progressAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _progressController,
      curve: Curves.linear,
    ));

    _progressAnimation.addListener(() {
      setState(() {
        _progress = _progressAnimation.value;
      });
    });

    _progressController.addStatusListener((status) {
      AppLogger.debug('ParentalGate: Animation status: $status');
      if (status == AnimationStatus.completed) {
        AppLogger.debug('ParentalGate: Animation completed, calling _onGatePassed');
        _onGatePassed();
      }
    });

    WidgetsBinding.instance.addPostFrameCallback((_) {
      _playScreenIntro();
    });
  }

  Future<void> _playScreenIntro() async {
    if (!mounted) {
      AppLogger.debug('ParentalGate: _playScreenIntro skipped, not mounted');
      return;
    }
    AppLogger.debug('ParentalGate: _playScreenIntro called');
    try {
      final voiceGuidanceManager = ref.read(voiceGuidanceManagerProvider);
      final l10n = AppLocalizations.of(context);
      if (l10n == null) {
        AppLogger.warning('ParentalGate: AppLocalizations is null in _playScreenIntro');
        return;
      }
      await voiceGuidanceManager.stopGuide();
      await voiceGuidanceManager.playGuide(context, (l10n) => l10n.parentalGateScreenIntro);
    } catch (e, stackTrace) {
      AppLogger.error('ParentalGate: Error in _playScreenIntro', e, stackTrace);
    }
  }

  Future<void> _playHoldButtonPrompt() async {
    if (!mounted) {
      print('DEBUG: _playHoldButtonPrompt skipped, not mounted');
      return;
    }
    print('DEBUG: _playHoldButtonPrompt called');
    try {
      final voiceGuidanceManager = ref.read(voiceGuidanceManagerProvider);
      final l10n = AppLocalizations.of(context);
      if (l10n == null) {
        print('DEBUG: AppLocalizations is null in _playHoldButtonPrompt');
        return;
      }
      await voiceGuidanceManager.stopGuide();
      await voiceGuidanceManager.playGuide(context, (l10n) => l10n.parentalGateHoldButtonPrompt);
    } catch (e, stackTrace) {
      print('DEBUG: Error in _playHoldButtonPrompt: $e\n$stackTrace');
    }
  }

  Future<void> _playKeepHoldingPrompt() async {
    if (!mounted) {
      print('DEBUG: _playKeepHoldingPrompt skipped, not mounted');
      return;
    }
    print('DEBUG: _playKeepHoldingPrompt called');
    try {
      final voiceGuidanceManager = ref.read(voiceGuidanceManagerProvider);
      final l10n = AppLocalizations.of(context);
      if (l10n == null) {
        print('DEBUG: AppLocalizations is null in _playKeepHoldingPrompt');
        return;
      }
      await voiceGuidanceManager.stopGuide();
      await voiceGuidanceManager.playGuide(context, (l10n) => l10n.parentalGateKeepHoldingPrompt, emotionCue: 'encouraging');
    } catch (e, stackTrace) {
      print('DEBUG: Error in _playKeepHoldingPrompt: $e\n$stackTrace');
    }
  }

  Future<void> _playGatePassedPrompt() async {
    if (!mounted) {
      print('DEBUG: _playGatePassedPrompt skipped, not mounted');
      return;
    }
    print('DEBUG: _playGatePassedPrompt called');
    try {
      final voiceGuidanceManager = ref.read(voiceGuidanceManagerProvider);
      final l10n = AppLocalizations.of(context);
      if (l10n == null) {
        print('DEBUG: AppLocalizations is null in _playGatePassedPrompt');
        return;
      }
      await voiceGuidanceManager.stopGuide();
      await voiceGuidanceManager.playGuide(context, (l10n) => l10n.parentalGatePassedPrompt);
    } catch (e, stackTrace) {
      print('DEBUG: Error in _playGatePassedPrompt: $e\n$stackTrace');
    }
  }

  @override
  void dispose() {
    print('DEBUG: dispose called');
    _holdTimer?.cancel();
    _progressController.dispose();
    if (mounted && ProviderScope.containerOf(context, listen: false).exists(voiceGuidanceManagerProvider)) {
      try {
        ref.read(voiceGuidanceManagerProvider).stopGuide();
      } catch (e) {
        print('DEBUG: Error stopping voice guidance in dispose: $e');
      }
    }
    super.dispose();
  }

  void _onTapDown(TapDownDetails details) {
    print('DEBUG: _onTapDown called');
    setState(() {
      _isHolding = true;
    });
    _progressController.forward();
    _playKeepHoldingPrompt();
  }

  void _onTapUp(TapUpDetails details) {
    print('DEBUG: _onTapUp called');
    _resetHold();
    _playHoldButtonPrompt();
  }

  void _onTapCancel() {
    print('DEBUG: _onTapCancel called');
    _resetHold();
    _playHoldButtonPrompt();
  }

  void _resetHold() {
    print('DEBUG: _resetHold called');
    setState(() {
      _isHolding = false;
      _progress = 0.0;
    });
    _progressController.reset();
  }

  void _onGatePassed() {
    print('DEBUG: _onGatePassed called, mounted: $mounted');
    if (!mounted) {
      print('DEBUG: _onGatePassed skipped, not mounted');
      return;
    }

    _playGatePassedPrompt().then((_) {
      if (mounted) {
        print('DEBUG: Navigating to /parent_auth');
        try {
          context.go('/parent_gate_entry/auth');
        } catch (e, stackTrace) {
          print('DEBUG: Navigation error: $e\n$stackTrace');
        }
      } else {
        print('DEBUG: Navigation skipped, not mounted');
      }
    }).catchError((e, stackTrace) {
      print('DEBUG: Error in _playGatePassedPrompt during _onGatePassed: $e\n$stackTrace');
      if (mounted) {
        print('DEBUG: Attempting navigation despite prompt error');
        try {
          context.go('/parent_gate_entry/auth');
        } catch (e, stackTrace) {
          print('DEBUG: Navigation error: $e\n$stackTrace');
        }
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Parent Zone Access'),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () {
            print('DEBUG: Back button pressed');
            try {
              ref.read(voiceGuidanceManagerProvider).stopGuide();
              context.go('/home');
            } catch (e, stackTrace) {
              print('DEBUG: Back button navigation error: $e\n$stackTrace');
            }
          },
        ),
      ),
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.security,
              size: 80,
              color: theme.colorScheme.secondary,
            ),

            const SizedBox(height: 32),

            Text(
              'Parent Zone Access',
              style: theme.textTheme.headlineMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),

            const SizedBox(height: 16),

            Text(
              'This area is for parents and guardians only.',
              style: theme.textTheme.bodyLarge?.copyWith(
                color: Colors.grey[600],
              ),
              textAlign: TextAlign.center,
            ),

            const SizedBox(height: 48),

            Text(
              'Press and hold the button below for 3 seconds to continue',
              style: theme.textTheme.bodyMedium?.copyWith(
                color: Colors.grey[700],
              ),
              textAlign: TextAlign.center,
            ),

            const SizedBox(height: 32),

            GestureDetector(
              onTapDown: _onTapDown,
              onTapUp: _onTapUp,
              onTapCancel: _onTapCancel,
              child: Container(
                width: 120,
                height: 120,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: _isHolding
                      ? theme.colorScheme.primary.withValues(alpha: 0.8)
                      : theme.colorScheme.primary,
                  boxShadow: [
                    BoxShadow(
                      color: theme.colorScheme.primary.withValues(alpha: 0.3),
                      blurRadius: 8,
                      spreadRadius: 2,
                    ),
                  ],
                ),
                child: Stack(
                  alignment: Alignment.center,
                  children: [
                    SizedBox(
                      width: 100,
                      height: 100,
                      child: CircularProgressIndicator(
                        value: _progress,
                        strokeWidth: 4,
                        backgroundColor: Colors.white.withValues(alpha: 0.3),
                        valueColor: const AlwaysStoppedAnimation<Color>(Colors.white),
                      ),
                    ),
                    const Icon(
                      Icons.lock_open,
                      size: 40,
                      color: Colors.white,
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 24),

            Text(
              _isHolding ? 'Keep holding...' : 'Hold to access',
              style: theme.textTheme.bodyMedium?.copyWith(
                color: Colors.grey[600],
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
          ),
        ),
      ),
    );
  }
}

// Add these keys to your app_en.arb (and other languages):
// "parentalGateScreenIntro": "Welcome to the Parent Zone access screen. This area is for parents and guardians only.",
// "parentalGateHoldButtonPrompt": "To continue, please press and hold the button below for 3 seconds.",
// "parentalGateKeepHoldingPrompt": "Keep holding... Almost there!",
// "parentalGatePassedPrompt": "Access granted. Loading Parent Zone."