import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:choice_once_upon_a_time/core/services/fallback_asset_manager.dart';
import 'package:choice_once_upon_a_time/core/utils/app_logger.dart';

/// Widget that displays images with automatic fallback to default_image.png
class FallbackImageWidget extends ConsumerWidget {
  final String imagePath;
  final String? storyId;
  final double? width;
  final double? height;
  final BoxFit fit;
  final Widget? placeholder;
  final Widget? errorWidget;
  final bool showFallbackIndicator;

  const FallbackImageWidget({
    super.key,
    required this.imagePath,
    this.storyId,
    this.width,
    this.height,
    this.fit = BoxFit.cover,
    this.placeholder,
    this.errorWidget,
    this.showFallbackIndicator = false,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final fallbackManager = ref.watch(fallbackAssetManagerProvider);
    
    return FutureBuilder<String>(
      future: storyId != null 
          ? fallbackManager.getStoryAssetWithFallback(
              storyId: storyId!,
              assetPath: imagePath,
            )
          : fallbackManager.getImageAssetPath(imagePath),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return placeholder ?? _buildDefaultPlaceholder();
        }
        
        if (snapshot.hasError) {
          AppLogger.error('[FALLBACK_IMAGE] Error resolving image path: $imagePath', snapshot.error);
          return errorWidget ?? _buildDefaultError();
        }
        
        final resolvedPath = snapshot.data ?? FallbackAssetManager.defaultImagePath;
        final isUsingFallback = resolvedPath == FallbackAssetManager.defaultImagePath;
        
        return Stack(
          children: [
            _buildImage(resolvedPath),
            if (showFallbackIndicator && isUsingFallback)
              _buildFallbackIndicator(),
          ],
        );
      },
    );
  }

  /// Build the actual image widget
  Widget _buildImage(String imagePath) {
    if (imagePath.startsWith('assets/')) {
      return Image.asset(
        imagePath,
        width: width,
        height: height,
        fit: fit,
        errorBuilder: (context, error, stackTrace) {
          AppLogger.warning('[FALLBACK_IMAGE] Failed to load asset image: $imagePath');
          return Image.asset(
            FallbackAssetManager.defaultImagePath,
            width: width,
            height: height,
            fit: fit,
          );
        },
      );
    } else {
      return Image.file(
        File(imagePath),
        width: width,
        height: height,
        fit: fit,
        errorBuilder: (context, error, stackTrace) {
          AppLogger.warning('[FALLBACK_IMAGE] Failed to load file image: $imagePath');
          return Image.asset(
            FallbackAssetManager.defaultImagePath,
            width: width,
            height: height,
            fit: fit,
          );
        },
      );
    }
  }

  /// Build default placeholder
  Widget _buildDefaultPlaceholder() {
    return Container(
      width: width,
      height: height,
      color: Colors.grey[300],
      child: const Center(
        child: CircularProgressIndicator(),
      ),
    );
  }

  /// Build default error widget
  Widget _buildDefaultError() {
    return Container(
      width: width,
      height: height,
      color: Colors.grey[300],
      child: const Icon(
        Icons.error,
        color: Colors.red,
      ),
    );
  }

  /// Build fallback indicator
  Widget _buildFallbackIndicator() {
    return Positioned(
      top: 4,
      right: 4,
      child: Container(
        padding: const EdgeInsets.all(2),
        decoration: BoxDecoration(
          color: Colors.orange.withValues(alpha: 0.8),
          borderRadius: BorderRadius.circular(4),
        ),
        child: const Icon(
          Icons.image_not_supported,
          size: 12,
          color: Colors.white,
        ),
      ),
    );
  }
}

/// Widget that handles audio playback with automatic fallback to default_happy.mp3
class FallbackAudioWidget extends ConsumerStatefulWidget {
  final String audioPath;
  final String? storyId;
  final VoidCallback? onPlay;
  final VoidCallback? onPause;
  final VoidCallback? onStop;
  final bool autoPlay;
  final bool showControls;
  final bool showFallbackIndicator;

  const FallbackAudioWidget({
    super.key,
    required this.audioPath,
    this.storyId,
    this.onPlay,
    this.onPause,
    this.onStop,
    this.autoPlay = false,
    this.showControls = true,
    this.showFallbackIndicator = false,
  });

  @override
  ConsumerState<FallbackAudioWidget> createState() => _FallbackAudioWidgetState();
}

class _FallbackAudioWidgetState extends ConsumerState<FallbackAudioWidget> {
  bool _isPlaying = false;
  bool _isLoading = false;
  String? _resolvedAudioPath;
  bool _isUsingFallback = false;

  @override
  void initState() {
    super.initState();
    _resolveAudioPath();
  }

  /// Resolve audio path with fallback
  Future<void> _resolveAudioPath() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final fallbackManager = ref.read(fallbackAssetManagerProvider);
      
      final resolvedPath = widget.storyId != null 
          ? await fallbackManager.getStoryAssetWithFallback(
              storyId: widget.storyId!,
              assetPath: widget.audioPath,
            )
          : await fallbackManager.getAudioAssetPath(widget.audioPath);
      
      setState(() {
        _resolvedAudioPath = resolvedPath;
        _isUsingFallback = resolvedPath == FallbackAssetManager.defaultAudioPath;
        _isLoading = false;
      });

      if (widget.autoPlay && _resolvedAudioPath != null) {
        _playAudio();
      }

    } catch (e) {
      AppLogger.error('[FALLBACK_AUDIO] Error resolving audio path: ${widget.audioPath}', e);
      setState(() {
        _resolvedAudioPath = FallbackAssetManager.defaultAudioPath;
        _isUsingFallback = true;
        _isLoading = false;
      });
    }
  }

  /// Play audio
  void _playAudio() {
    if (_resolvedAudioPath == null) return;
    
    setState(() {
      _isPlaying = true;
    });
    
    // TODO: Implement actual audio playback
    AppLogger.info('[FALLBACK_AUDIO] Playing audio: $_resolvedAudioPath');
    widget.onPlay?.call();
  }

  /// Pause audio
  void _pauseAudio() {
    setState(() {
      _isPlaying = false;
    });
    
    // TODO: Implement actual audio pause
    AppLogger.info('[FALLBACK_AUDIO] Pausing audio: $_resolvedAudioPath');
    widget.onPause?.call();
  }

  /// Stop audio
  void _stopAudio() {
    setState(() {
      _isPlaying = false;
    });
    
    // TODO: Implement actual audio stop
    AppLogger.info('[FALLBACK_AUDIO] Stopping audio: $_resolvedAudioPath');
    widget.onStop?.call();
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    if (!widget.showControls) {
      return const SizedBox.shrink();
    }

    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        // Play/Pause button
        IconButton(
          onPressed: _isPlaying ? _pauseAudio : _playAudio,
          icon: Icon(_isPlaying ? Icons.pause : Icons.play_arrow),
        ),
        
        // Stop button
        IconButton(
          onPressed: _stopAudio,
          icon: const Icon(Icons.stop),
        ),
        
        // Fallback indicator
        if (widget.showFallbackIndicator && _isUsingFallback)
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: Colors.orange.withValues(alpha: 0.8),
              borderRadius: BorderRadius.circular(4),
            ),
            child: const Text(
              'Default Audio',
              style: TextStyle(
                color: Colors.white,
                fontSize: 10,
              ),
            ),
          ),
      ],
    );
  }
}

/// Widget that displays asset metadata for debugging
class AssetDebugWidget extends ConsumerWidget {
  final String assetPath;
  final String? storyId;

  const AssetDebugWidget({
    super.key,
    required this.assetPath,
    this.storyId,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final metadataAsync = ref.watch(assetMetadataProvider(assetPath));
    
    return metadataAsync.when(
      data: (metadata) => _buildDebugInfo(metadata),
      loading: () => const CircularProgressIndicator(),
      error: (error, stack) => Text('Error: $error'),
    );
  }

  Widget _buildDebugInfo(AssetMetadata metadata) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(8),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            Text('Asset Debug Info', style: const TextStyle(fontWeight: FontWeight.bold)),
            const SizedBox(height: 4),
            Text('Original: ${metadata.originalPath}'),
            Text('Resolved: ${metadata.resolvedPath}'),
            Text('Exists: ${metadata.exists}'),
            Text('Type: ${metadata.assetType}'),
            Text('Needs Fallback: ${metadata.needsFallback}'),
            if (metadata.needsFallback)
              Container(
                padding: const EdgeInsets.all(4),
                decoration: BoxDecoration(
                  color: Colors.orange.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(4),
                ),
                child: const Text(
                  'Using Fallback Asset',
                  style: TextStyle(
                    color: Colors.orange,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }
}

/// Utility widget for preloading assets
class AssetPreloader extends ConsumerStatefulWidget {
  final List<String> assetPaths;
  final Widget child;
  final Widget? loadingWidget;

  const AssetPreloader({
    super.key,
    required this.assetPaths,
    required this.child,
    this.loadingWidget,
  });

  @override
  ConsumerState<AssetPreloader> createState() => _AssetPreloaderState();
}

class _AssetPreloaderState extends ConsumerState<AssetPreloader> {
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _preloadAssets();
  }

  Future<void> _preloadAssets() async {
    try {
      final fallbackManager = ref.read(fallbackAssetManagerProvider);
      await fallbackManager.preloadCommonAssets(widget.assetPaths);
      
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    } catch (e) {
      AppLogger.error('[ASSET_PRELOADER] Error preloading assets', e);
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return widget.loadingWidget ?? const Center(
        child: CircularProgressIndicator(),
      );
    }

    return widget.child;
  }
}
