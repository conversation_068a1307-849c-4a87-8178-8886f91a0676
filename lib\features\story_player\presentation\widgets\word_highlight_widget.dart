import 'package:flutter/material.dart';
import 'package:choice_once_upon_a_time/core/services/story_narration_service_interface.dart';
import 'package:choice_once_upon_a_time/models/narration_models.dart';
import 'package:choice_once_upon_a_time/core/utils/app_logger.dart';

/// Widget that displays text with synchronized word-level highlighting
/// following TTS speech with smooth animations and visual feedback
class WordHighlightWidget extends StatefulWidget {
  final IStoryNarrationService narrationService;
  final String text;
  final TextStyle? baseTextStyle;
  final TextStyle? highlightedTextStyle;
  final Color? highlightColor;
  final Duration highlightDuration;
  final bool enableAnimations;
  final bool enableGlow;
  final double fontSize;
  final double lineHeight;
  final TextAlign textAlign;
  final EdgeInsets padding;

  const WordHighlightWidget({
    super.key,
    required this.narrationService,
    required this.text,
    this.baseTextStyle,
    this.highlightedTextStyle,
    this.highlightColor,
    this.highlightDuration = const Duration(milliseconds: 300),
    this.enableAnimations = true,
    this.enableGlow = true,
    this.fontSize = 18,
    this.lineHeight = 1.5,
    this.textAlign = TextAlign.left,
    this.padding = const EdgeInsets.all(16),
  });

  @override
  State<WordHighlightWidget> createState() => _WordHighlightWidgetState();
}

class _WordHighlightWidgetState extends State<WordHighlightWidget>
    with TickerProviderStateMixin {
  
  // Animation controllers
  late AnimationController _highlightController;
  late AnimationController _glowController;
  late Animation<double> _highlightAnimation;
  late Animation<double> _glowAnimation;

  // State
  List<WordData> _words = [];
  int _currentHighlightedWordIndex = -1;
  NarrationState _narrationState = NarrationState.idle;

  // Text styles
  late TextStyle _baseStyle;
  late TextStyle _highlightedStyle;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _setupNarrationListeners();
    _parseText();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    _initializeTextStyles();
  }

  @override
  void didUpdateWidget(WordHighlightWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.text != widget.text) {
      _parseText();
    }
  }

  @override
  void dispose() {
    _highlightController.dispose();
    _glowController.dispose();
    super.dispose();
  }

  /// Initialize animations
  void _initializeAnimations() {
    _highlightController = AnimationController(
      duration: widget.highlightDuration,
      vsync: this,
    );

    _glowController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );

    _highlightAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _highlightController,
      curve: Curves.easeInOut,
    ));

    _glowAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _glowController,
      curve: Curves.easeInOut,
    ));
  }

  /// Initialize text styles
  void _initializeTextStyles() {
    final theme = Theme.of(context);
    
    _baseStyle = widget.baseTextStyle ?? theme.textTheme.bodyLarge?.copyWith(
      fontSize: widget.fontSize,
      height: widget.lineHeight,
      color: theme.colorScheme.onSurface,
    ) ?? TextStyle(
      fontSize: widget.fontSize,
      height: widget.lineHeight,
      color: theme.colorScheme.onSurface,
    );

    final highlightColor = widget.highlightColor ?? theme.colorScheme.primary;
    
    _highlightedStyle = widget.highlightedTextStyle ?? _baseStyle.copyWith(
      backgroundColor: highlightColor.withValues(alpha: 0.3),
      fontWeight: FontWeight.w600,
      color: highlightColor,
      shadows: widget.enableGlow ? [
        Shadow(
          color: highlightColor.withValues(alpha: 0.5),
          blurRadius: 4,
          offset: const Offset(0, 0),
        ),
      ] : null,
    );
  }

  /// Set up narration service listeners
  void _setupNarrationListeners() {
    widget.narrationService.stateStream.listen((state) {
      if (mounted) {
        setState(() {
          _narrationState = state;
        });
      }
    });

    widget.narrationService.wordHighlightStream.listen((wordHighlight) {
      if (mounted && wordHighlight.isActive) {
        _highlightWord(wordHighlight);
      }
    });
  }

  /// Parse text into words with position data
  void _parseText() {
    final words = <WordData>[];
    final wordMatches = RegExp(r'\b\w+\b').allMatches(widget.text);
    
    for (final match in wordMatches) {
      words.add(WordData(
        text: match.group(0) ?? '',
        startIndex: match.start,
        endIndex: match.end,
        isHighlighted: false,
      ));
    }
    
    setState(() {
      _words = words;
      _currentHighlightedWordIndex = -1;
    });
    
    AppLogger.debug('[WordHighlight] Parsed ${words.length} words from text');
  }

  /// Highlight a specific word
  void _highlightWord(WordHighlight wordHighlight) {
    // Find the word index based on character position
    int wordIndex = -1;
    for (int i = 0; i < _words.length; i++) {
      if (_words[i].startIndex <= wordHighlight.startIndex &&
          _words[i].endIndex >= wordHighlight.endIndex) {
        wordIndex = i;
        break;
      }
    }

    if (wordIndex == -1) return;

    // Clear previous highlight
    if (_currentHighlightedWordIndex >= 0 && _currentHighlightedWordIndex < _words.length) {
      _words[_currentHighlightedWordIndex] = _words[_currentHighlightedWordIndex].copyWith(
        isHighlighted: false,
      );
    }

    // Set new highlight
    _words[wordIndex] = _words[wordIndex].copyWith(isHighlighted: true);
    _currentHighlightedWordIndex = wordIndex;

    setState(() {});

    // Animate highlight
    if (widget.enableAnimations) {
      _highlightController.forward().then((_) {
        if (mounted) {
          _highlightController.reverse();
        }
      });

      if (widget.enableGlow) {
        _glowController.forward().then((_) {
          if (mounted) {
            _glowController.reverse();
          }
        });
      }
    }

    AppLogger.debug('[WordHighlight] Highlighted word: ${wordHighlight.word} at index $wordIndex');
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: widget.padding,
      child: AnimatedBuilder(
        animation: Listenable.merge([_highlightAnimation, _glowAnimation]),
        builder: (context, child) {
          return _buildHighlightedText();
        },
      ),
    );
  }

  /// Build text with word-level highlighting
  Widget _buildHighlightedText() {
    final textSpans = <TextSpan>[];
    int currentIndex = 0;
    
    for (int i = 0; i < _words.length; i++) {
      final word = _words[i];
      
      // Add any text before this word
      if (word.startIndex > currentIndex) {
        final beforeText = widget.text.substring(currentIndex, word.startIndex);
        textSpans.add(TextSpan(
          text: beforeText,
          style: _baseStyle,
        ));
      }
      
      // Add the word with appropriate styling
      final isHighlighted = word.isHighlighted && 
                           _narrationState.status == NarrationStatus.playing;
      
      TextStyle wordStyle = isHighlighted ? _highlightedStyle : _baseStyle;
      
      // Apply animation effects if enabled
      if (isHighlighted && widget.enableAnimations) {
        final animationValue = _highlightAnimation.value;
        final glowValue = widget.enableGlow ? _glowAnimation.value : 0.0;
        
        wordStyle = wordStyle.copyWith(
          backgroundColor: wordStyle.backgroundColor?.withValues(
            alpha: (wordStyle.backgroundColor?.a ?? 0.3) * animationValue,
          ),
          shadows: widget.enableGlow ? [
            Shadow(
              color: (widget.highlightColor ?? Theme.of(context).colorScheme.primary)
                  .withValues(alpha: 0.5 * glowValue),
              blurRadius: 4 + (glowValue * 2),
              offset: const Offset(0, 0),
            ),
          ] : null,
        );
      }
      
      textSpans.add(TextSpan(
        text: word.text,
        style: wordStyle,
      ));
      
      currentIndex = word.endIndex;
    }
    
    // Add any remaining text
    if (currentIndex < widget.text.length) {
      final remainingText = widget.text.substring(currentIndex);
      textSpans.add(TextSpan(
        text: remainingText,
        style: _baseStyle,
      ));
    }

    return RichText(
      text: TextSpan(children: textSpans),
      textAlign: widget.textAlign,
    );
  }
}

/// Data class for word information
class WordData {
  final String text;
  final int startIndex;
  final int endIndex;
  final bool isHighlighted;

  const WordData({
    required this.text,
    required this.startIndex,
    required this.endIndex,
    required this.isHighlighted,
  });

  WordData copyWith({
    String? text,
    int? startIndex,
    int? endIndex,
    bool? isHighlighted,
  }) {
    return WordData(
      text: text ?? this.text,
      startIndex: startIndex ?? this.startIndex,
      endIndex: endIndex ?? this.endIndex,
      isHighlighted: isHighlighted ?? this.isHighlighted,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is WordData &&
        other.text == text &&
        other.startIndex == startIndex &&
        other.endIndex == endIndex &&
        other.isHighlighted == isHighlighted;
  }

  @override
  int get hashCode {
    return text.hashCode ^
        startIndex.hashCode ^
        endIndex.hashCode ^
        isHighlighted.hashCode;
  }

  @override
  String toString() {
    return 'WordData(text: $text, startIndex: $startIndex, endIndex: $endIndex, isHighlighted: $isHighlighted)';
  }
}
