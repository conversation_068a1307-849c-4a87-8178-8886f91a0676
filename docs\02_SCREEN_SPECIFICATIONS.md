---
## File 2: `docs/02_SCREEN_SPECIFICATIONS.md`
---

# Comprehensive Screen Specifications & Requirements for UI/UX Generation (with User Flow)
## "Choice: Once Upon A Time" App
*(Content based on your uploaded "Task 2.2 update" document)*

**Objective of this Document:**
This document provides a comprehensive and detailed breakdown of all anticipated screens, popups, and key UI components for the "Choice: Once Upon A Time" interactive bedtime story app. It is intended to serve as a direct input and functional specification guide for an AI agent (or development team) tasked with generating the UI/UX of the Flutter application. Each screen description outlines its purpose, key UI elements, core interactions, synergy with the empathetic narrator, specific design considerations, functional requirements, and explicit navigation/user flow links.

**Target Audience:** Children aged 4-7 years and their parents.
**Core Project Pillars:** Empathetic Narrator, Moral Value Integration, Bedtime Context, Intuitive Design, High-Quality Visuals.
**Assumed UI/UX Design Document for Reference:** ui_ux_design_v1 (Conceptual UI/UX Design)
**Assumed Schematics Webpage for Visual Reference:** spa_all_screen_schematics_v1

## I. Initial App Experience & Story Selection Screens

### 1. App Launch Screen
* **Purpose:** To provide an immediate, gentle, and welcoming first impression of the app, setting a calm and inviting tone.
* **Key UI Elements:** App Logo / Name, subtle calming background animation, optional mascot visual.
* **Key Interactions/Functionality:** Primarily passive; auto-transitions or skippable by tap.
* **Narrator Synergy:** Brief, warm, skippable audio welcome.
* **Design/UX Considerations:** Extremely calming, simple, high quality. Smooth transition.
* **Navigation / User Flow Links:**
    * Arrives From: Initial app startup by the user.
    * Leads To (Automatic/Tap): Screen 2: Home Screen / Story Library (or Screen 4.1: FTUE / Tutorial Overlay on very first launch).
* **Requirements/Specifications:**
    * Must display app branding.
    * Auto-transition to Home Screen/FTUE within 3-5 seconds.
    * Optional: Skip by tap.
    * Narrator audio brief, skippable.
    * Lightweight, performant animation.

### 2. Home Screen / Story Library
* **Purpose:** To allow children to easily browse, discover, and select stories. Primary content entry point.
* **Key UI Elements:** Scrollable grid of Story Covers (art + title), "NEW" / "Update Available" / "Locked" badges, "For Parents" icon, (Future) "Favorites" icon, thematic background.
* **Key Interactions/Functionality:** Scroll vertically. Tap story cover to select. Tap-and-hold for narrator cue (optional). Parent taps "For Parents" icon.
* **Narrator Synergy:** Initial welcome/idle prompt. Story-specific audio cue on tap-and-hold/focus.
* **Design/UX Considerations:** High-quality cover art. Legible titles. Smooth scrolling. Clear visual distinction for story states (locked/new/update). Large tap targets.
* **Navigation / User Flow Links:**
    * Arrives From: Screen 1, Screen 4.1, Screen 3 (Back), Screen 7 (Go to Library), Screen 8 (Story Library), Screen 10 (Back to Stories).
    * Leads To: Screen 3 (Tap Unlocked Story), Screen 19 (Tap Story with Update), Parent Zone via Screen 9 (Tap Locked Story or "For Parents"), (Future) My Favorites Screen.
* **Requirements/Specifications:** Display all stories in a scrollable grid. Clear tappable cover art and title. Clear access to Parent Zone. Visual feedback on tap/hover. Performant. Show story states.

### 3. Story Intro/Splash Screen
* **Purpose:** Create anticipation, confirm story selection, provide clear start point.
* **Key UI Elements:** Full-screen story cover art/splash image, Story Title, large "Play" button, optional "Back" button.
* **Key Interactions/Functionality:** Tap "Play" to start story. Tap "Back" to return to Story Library.
* **Narrator Synergy:** Narrator introduces story or prompts "Play."
* **Design/UX Considerations:** Visually rich, consistent with story art. Prominent "Play" button. Smooth transition.
* **Navigation / User Flow Links:** Arrives from Screen 2 or Screen 19. Leads to Screen 4 (then 5) or Screen 2.
* **Requirements/Specifications:** Display selected story's title/artwork. Prominent "Play." "Back" option.

### 4. Loading Screen
* **Purpose:** Provide feedback during brief asset loading.
* **Key UI Elements:** Simple, calming animation, optional text "Loading Story...".
* **Key Interactions/Functionality:** Passive; auto-transitions.
* **Narrator Synergy:** Narrator: "Our story is just about to begin...".
* **Design/UX Considerations:** Visually calm, consistent. Smooth, brief.
* **Navigation / User Flow Links:** Arrives from Screen 3. Leads to Screen 5.
* **Requirements/Specifications:** Display if loading > ~1 second. Clear feedback. Auto-transition. Short duration.

### 4.1. First-Time User Experience (FTUE) / Tutorial Overlay Screen
* **Purpose:** Gently introduce basic app interactions.
* **Key UI Elements:** Simple illustrative overlays, animated highlights, minimal text, "Skip Tutorial" button.
* **Key Interactions/Functionality:** Follow prompts, tap highlighted elements, skippable.
* **Narrator Synergy:** Primary guide for the tutorial.
* **Design/UX Considerations:** Extremely simple, brief, visual, playful.
* **Navigation / User Flow Links:** Arrives from Screen 1 (first launch). Leads to Screen 2.
* **Requirements/Specifications:** First launch only. Skippable. Demonstrate story selection & choice. Encouraging narrator.

## II. In-Story Experience Screens

### 5. Main In-Story Scene (Narration & Illustration)
* **Purpose:** Present core narrative content.
* **Key UI Elements:** Main Story Illustration Area, optional On-Screen Text (word highlighting), persistent "Home" Icon, persistent subtle Narration Controls (Pause/Play, Replay Segment), optional "Discovery" Interaction Hotspots.
* **Key Interactions/Functionality:** Listen/view. Auto-progress (recommended). Use narration controls, Home, Discovery.
* **Narrator Synergy:** Primary driver. May acknowledge Discovery.
* **Design/UX Considerations:** Illustrations key. Text/audio sync. Intuitive, non-distracting controls. Smooth transitions.
* **Navigation / User Flow Links:** Arrives from Screen 4, previous Screen 5, or Screen 7. Leads to next Screen 5, Screen 6, Screen 8, or Screen 13 (Home tap), Screen 7 (Pause tap).
* **Requirements/Specifications:** Display illustration. Play narrator. Functional controls. Clear exit. Word highlight. Smooth advance.

### 6. In-Story Choice Point Presentation
* **Purpose:** Clearly present a meaningful narrative choice, framed by narrator.
* **Key UI Elements:** Main Story Illustration (may dim). 2-3 large, tappable Choice Options (illustrated orbs/cards, minimal keywords). Visual cues for active choices. Persistent controls.
* **Key Interactions/Functionality:** Listen to narrator frame dilemma. Tap one choice.
* **Narrator Synergy:** Emotionally frames dilemma. Voices options. Acknowledges selection.
* **Design/UX Considerations:** Choices distinct, large, well-spaced. Illustrations convey meaning. Immediate feedback. Obvious interaction needed.
* **Navigation / User Flow Links:** Arrives from Screen 5. Leads to specific Screen 5 branch, Screen 13 (Home tap), or Screen 7 (Pause tap).
* **Requirements/Specifications:** Display 2-3 tappable choices. Narrator explains options. Single tap selection. Feedback. Correct branching.

### 7. In-Story Pause Screen/Menu
* **Purpose:** Options when story is paused.
* **Key UI Elements:** Overlay screen. "Story Paused" title. Buttons: "Resume Story", "Replay Current Segment", "Go to Story Library". Optional "Settings" icon (to Parent Zone via gate).
* **Key Interactions/Functionality:** Tap buttons for respective actions.
* **Narrator Synergy:** None active. Calm tone maintained.
* **Design/UX Considerations:** Large, clear buttons. Simple, temporary feel.
* **Navigation / User Flow Links:** Arrives from Pause tap on Screen 5/6 or auto from Screen 8.1. Leads to Screen 5/6 (Resume), restarts segment (Replay), Screen 13 (Go to Library), or Screen 9 (Settings tap).
* **Requirements/Specifications:** Appears on pause. Offers Resume, Replay, Go to Library.

### 8. Story End Screen
* **Purpose:** Gentle conclusion, acknowledge completion, next steps, narrator-led moral discussion.
* **Key UI Elements:** Concluding illustration. "The End!" message. "Story Library" button. "All Done for Tonight?" button.
* **Key Interactions/Functionality:** Tap buttons.
* **Narrator Synergy:** Final lines, initiates moral discussion, final goodnight.
* **Design/UX Considerations:** Exceptionally calming. Reinforce positive feelings. Natural sleep transition.
* **Navigation / User Flow Links:** Arrives from final Screen 5. Leads to Screen 2 or Screen 20.
* **Requirements/Specifications:** Appears at end. Concluding visual/message. Moral discussion audio. Options to return/end.

### 8.1. "Are You Still There?" Idle Prompt (In-Story)
* **Purpose:** Gently re-engage idle child or offer pause/exit.
* **Key UI Elements:** Soft visual overlay, optional text "Still with us?". "Tap to Continue" button.
* **Key Interactions/Functionality:** Tap dismisses/resumes. Further inactivity auto-pauses to Screen 7.
* **Narrator Synergy:** Softly asks: "Hello? Are you still there...?"
* **Design/UX Considerations:** Very gentle. Trigger after 1-2 mins idle. Easy to dismiss.
* **Navigation / User Flow Links:** Arrives from inactivity on Screen 5/6. Leads to Screen 5/6 or Screen 7.
* **Requirements/Specifications:** Detect inactivity. Gentle prompt. Easy dismissal or pause.

## III. Parent Zone & Related Screens

### 9. Parental Gate Screen
* **Purpose:** Prevent accidental child access to parent settings/purchases.
* **Key UI Elements:** "For Grown-Ups" title. Instruction for gate (e.g., "Press and hold for 3 seconds"). Interactive element for gate. "Back"/"Cancel".
* **Key Interactions/Functionality:** Parent performs action. Success grants access.
* **Narrator Synergy:** None.
* **Design/UX Considerations:** Difficult for child (4-7), not frustrating for parent. Clear instructions.
* **Navigation / User Flow Links:** Arrives from "For Parents" tap (Screen 2) or "Settings" tap (Screen 7). Leads to Screen 10 or previous.
* **Requirements/Specifications:** Effective gate. Clear instructions. Cancel option. Grant access on success.

### 10. Parent Zone Dashboard
* **Purpose:** Hub for parent settings, account, content management, philosophy.
* **Key UI Elements:** "Parent Zone" title. List of navigable menu items (Account, Sound, Narration, Downloads, About Values, Help, Privacy, etc.) with labels/icons. "Back to Stories" button.
* **Key Interactions/Functionality:** Tap menu item. Tap "Back to Stories".
* **Narrator Synergy:** None.
* **Design/UX Considerations:** Clean, clear, professional. Intuitive navigation. Calm visual style, utilitarian feel.
* **Navigation / User Flow Links:** Arrives from Screen 9. Leads to corresponding detail screens (11, 12, 12.1-12.4) or Screen 2.
* **Requirements/Specifications:** Access all parent sections. Intuitive navigation. Clear exit.

### 11. Parent Zone - Sound Settings Screen
* **Purpose:** Allow parents to customize app audio settings.
* **Key UI Elements:** "Sound Settings" title. Sliders/toggles for Master Volume, Background Music (On/Off), UI Sound Effects (On/Off). "Save Settings" (if not instant). "Back" button.
* **Key Interactions/Functionality:** Adjust controls. Changes applied.
* **Narrator Synergy:** None.
* **Design/UX Considerations:** Standard controls. Clear labels. Visual feedback.
* **Navigation / User Flow Links:** Arrives from Screen 10. Leads to Screen 10.
* **Requirements/Specifications:** Control volumes, BGM, UI SFX. Persistent settings. Save/back.

### 12. Subscription/Upgrade Screen (within Parent Zone)
* **Purpose:** Clearly present subscription options and benefits, facilitating upgrade.
* **Key UI Elements:** "Unlock All Stories!" title. Value prop text. Plan sections (Monthly, Annual, Family Tier) with price/benefits. "Subscribe Now"/"Start Trial" button per plan. "Restore Purchases" link. "Back" button.
* **Key Interactions/Functionality:** Review plans. Tap "Subscribe" (native IAP). Tap "Restore Purchases".
* **Narrator Synergy:** None.
* **Design/UX Considerations:** Clear, transparent, easy comparison. Obvious value. Prominent CTAs. Trust signals.
* **Navigation / User Flow Links:** Arrives from Screen 10 or contextually from Screen 2 (locked story) via Screen 9. Leads to Native IAP flow, then returns or to Parent Dashboard.
* **Requirements/Specifications:** Display plans, prices, features. Native IAP. Restore Purchases. Transparent terms.

### 12.1. Parent Zone - "About Stories & Moral Values" Screen
* **Purpose:** Provide parents understanding of app's philosophy, moral values.
* **Key UI Elements:** Title. Scrollable content. Intro text. List/grid of Core Moral Values (tappable). Definition/example per value. (Future: Link to resources). "Back" button.
* **Key Interactions/Functionality:** Read/scroll. (Future) Tap values for more.
* **Narrator Synergy:** None.
* **Design/UX Considerations:** Clear, concise, reassuring text. Readable. Reinforce ethics.
* **Navigation / User Flow Links:** Arrives from Screen 10. Leads to Screen 10.
* **Requirements/Specifications:** Reflect framework (Task 1.2). List values. Easy to understand.

### 12.2. Parent Zone - "Manage Downloads / Storage" Screen
* **Purpose:** Allow parents to view/manage downloaded stories and storage.
* **Key UI Elements:** Title. Storage summary. Scrollable list of downloaded stories (Thumbnail, Title, Size, "Delete" icon 🗑️). "Delete All Stories" button. "Back" button.
* **Key Interactions/Functionality:** View. Tap "Delete" (triggers Screen 16). Tap "Delete All" (triggers Screen 16).
* **Narrator Synergy:** None.
* **Design/UX Considerations:** Clear info. Deletion confirmation. Feedback on deletion.
* **Navigation / User Flow Links:** Arrives from Screen 10 or Screen 17. Stays on screen (list updates) or leads to Screen 10 (Back).
* **Requirements/Specifications:** List stories/sizes. Delete individual/all. Confirm deletions.

### 12.3. Parent Zone - "Help & Support / FAQ" Screen
* **Purpose:** Answers, troubleshooting, support contact.
* **Key UI Elements:** Title. Optional search. Categorized FAQs (tappable questions, accordion answers). "Contact Us" button/section. "Back" button.
* **Key Interactions/Functionality:** Browse/search. Tap FAQ. Access support.
* **Narrator Synergy:** None.
* **Design/UX Considerations:** Well-organized. Clear answers. Easy contact.
* **Navigation / User Flow Links:** Arrives from Screen 10. Leads to email/web (Contact Us) or Screen 10 (Back).
* **Requirements/Specifications:** Relevant FAQs. Clear contact method.

### 12.4. Parent Zone - Child Profile Selection/Creation Screen (Future Feature)
* **Purpose:** (If multiple profiles) Select or create child profile.
* **Key UI Elements:** Title. Visual cards for profiles. "Add New Profile" button. "Back" button.
* **Key Interactions/Functionality:** Tap profile to select. Tap "Add New" to go to 12.4.1.
* **Narrator Synergy:** (If at app launch post-gate) Narrator guidance for parent to help child select.
* **Design/UX Considerations:** Simple, visual. Child-friendly avatars. Minimal creation steps.
* **Navigation / User Flow Links:** Arrives from Screen 10 or post-gate setup. Leads to Screen 2 (profile selected), Screen 12.4.1 (Add New), or Screen 10 (Back).
* **Requirements/Specifications:** Display profiles. Allow selection. Path to create.

### 12.4.1. Parent Zone - Create/Edit Child Profile Popup/Screen (Future Feature)
* **Purpose:** Parent inputs minimal details for child profile.
* **Key UI Elements:** Title. Text input "Child's Name". Avatar selection area. "Save"/"Done". "Cancel".
* **Key Interactions/Functionality:** Enter name, select avatar. Save/Cancel.
* **Narrator Synergy:** None.
* **Design/UX Considerations:** Extremely simple. Fun avatar selection.
* **Navigation / User Flow Links:** Arrives from Screen 12.4. Leads to Screen 12.4.
* **Requirements/Specifications:** Name input. Avatar selection. Save/cancel.

## IV. General Popups & Notifications

### 13. Exit Story Confirmation Popup
* **Purpose:** Prevent accidental exit; inform about progress saving.
* **Key UI Elements:** Modal. Question "Go back to Story Library?". Optional progress warning. "Yes"/"No" buttons.
* **Key Interactions/Functionality:** Tap "Yes" (exit). Tap "No" (resume).
* **Narrator Synergy:** Narrator: "Are you sure you want to leave our story for now?".
* **Design/UX Considerations:** Simple, clear, quick. Unambiguous.
* **Navigation / User Flow Links:** Arrives from Home tap (Screen 5/6) or "Go to Library" (Screen 7). Leads to Screen 2 (Yes) or dismisses (No).
* **Requirements/Specifications:** Appears on Home tap. Clear options.

### 14. Offline Notification Popup
* **Purpose:** Inform user when offline trying online action; reassure about downloaded.
* **Key UI Elements:** Modal. Icon (⚠️/no-wifi). "No Internet Connection" title. Info message. "OK".
* **Key Interactions/Functionality:** Tap "OK" dismisses. App shows downloaded.
* **Narrator Synergy:** None typically. Gentle sound.
* **Design/UX Considerations:** Reassuring. Clear guidance.
* **Navigation / User Flow Links:** Arrives from online action attempt while offline. Leads to dismiss; UI reflects offline state.
* **Requirements/Specifications:** Appear on offline attempt. Clear explanation. Dismissible.

### 15. Missing TTS Voice Data Popup
* **Purpose:** Inform if TTS voice data missing; guide resolution.
* **Key UI Elements:** Modal. Icon (🗣️). "Missing Voice Data" title. Info message. Buttons: "Go to Device Settings" (or "How to?"), "Read Without Narration," "Cancel."
* **Key Interactions/Functionality:** Choose action.
* **Narrator Synergy:** None for system message.
* **Design/UX Considerations:** Actionable. "Read Without Narration" as fallback.
* **Navigation / User Flow Links:** Arrives from story play attempt (Screen 3/5) if TTS data missing. Leads to OS settings (or help), Screen 5 (no narration), or Screen 2/3 (Cancel).
* **Requirements/Specifications:** Detect missing TTS. Inform clearly. Offer solutions.

### 16. "Delete Story?" Confirmation Popup (Context: Parent Zone - Manage Downloads)
* **Purpose:** Confirm parent's intent to delete downloaded story/stories.
* **Key UI Elements:** Modal. Question: "Delete '[Story Title]'?" or "Delete All?". Warning. "Delete" (Red) and "Cancel" buttons.
* **Key Interactions/Functionality:** Tap "Delete" or "Cancel".
* **Narrator Synergy:** None.
* **Design/UX Considerations:** Destructive action clear. Reassurance on re-download.
* **Navigation / User Flow Links:** Arrives from Screen 12.2. Leads to updating Screen 12.2 (Delete) or dismisses (Cancel).
* **Requirements/Specifications:** Clear statement. Explicit confirmation.

### 17. "Storage Full" Warning Popup
* **Purpose:** Inform parent that device storage is insufficient for download.
* **Key UI Elements:** Modal. Icon (🈵). "Not Enough Storage" title. Message. "Manage Storage" button (links to 12.2). "OK".
* **Key Interactions/Functionality:** Tap button.
* **Narrator Synergy:** None.
* **Design/UX Considerations:** Actionable. Informative, helpful.
* **Navigation / User Flow Links:** Arrives from download attempt (Screen 2/19) on low storage. Leads to Screen 12.2 or dismisses.
* **Requirements/Specifications:** Appear on low storage for download. Clear explanation, resolution path.

### 18. "Rate Our App?" Popup (Optional, Timed Carefully)
* **Purpose:** Gently prompt satisfied parents to rate.
* **Key UI Elements:** Modal. Friendly message. CTA. Buttons: "Rate Now," "Later," "No, Thanks."
* **Key Interactions/Functionality:** "Rate Now" (to store). "Later" (dismiss/re-prompt). "No, Thanks" (dismiss, less frequent).
* **Narrator Synergy:** None.
* **Design/UX Considerations:** Critical timing. Easy dismissal. Positive tone. Use platform-idiomatic prompts.
* **Navigation / User Flow Links:** Triggered programmatically. Leads to App/Play Store or dismisses.
* **Requirements/Specifications:** Platform-idiomatic. Non-intrusive.

### 19. "Content Update Available" Notification/Popup (In Story Library context)
* **Purpose:** Inform users a new version of a downloaded story is available.
* **Key UI Elements:** Badge/icon (🔃) on story cover (Screen 2). Optional modal prompt on tap: Message, "Update Now" / "Play Current Version" / "Later".
* **Key Interactions/Functionality:** Badge informational. Prompt actions: Update, Play old, Dismiss.
* **Narrator Synergy:** If prompt: Narrator: "Oh look! [Character]'s story has an update..."
* **Design/UX Considerations:** Badge noticeable, not distracting. Smooth update. Allow use of old.
* **Navigation / User Flow Links:** Badge on Screen 2. Popup after tap on Screen 2. Leads to download (then Screen 3 new), Screen 3 old, or dismisses to Screen 2.
* **Requirements/Specifications:** Detect updates. Indicate clearly. Simple update mechanism.

### 20. Calm Exit Screen (If "All Done for Tonight?" is tapped from Story End Screen)
* **Purpose:** Final, extremely calm screen reinforcing bedtime transition.
* **Key UI Elements:** Full-screen, soft, dreamy illustration. Minimal text: "Sweet Dreams...". No interactive buttons.
* **Key Interactions/Functionality:** Passive viewing. App might auto-close or parent uses OS.
* **Narrator Synergy:** Final, soft goodnight whisper: "The stars are twinkling... Goodnight...".
* **Design/UX Considerations:** Calmest screen. No stimulating elements. Gentle "tuck-in."
* **Navigation / User Flow Links:** Arrives from Screen 8. Leads to app close (auto/manual).
* **Requirements/Specifications:** Visually/audibly extremely calming. Sufficient time if auto-closing.

