import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:choice_once_upon_a_time/core/services/new_story_service.dart';
import 'package:choice_once_upon_a_time/features/story_library/data/story_repository.dart';
import 'package:choice_once_upon_a_time/core/services/story_download_manager.dart';
import 'package:choice_once_upon_a_time/models/story_metadata_model.dart';
import 'package:choice_once_upon_a_time/models/firebase_story_metadata.dart';
import 'package:choice_once_upon_a_time/core/utils/app_logger.dart';

/// Provider for the new story service
final newStoryServiceProvider = Provider<NewStoryService>((ref) {
  return NewStoryService();
});

/// Provider for the enhanced story repository with Firebase support
final storyRepositoryProvider = Provider<StoryRepository>((ref) {
  return StoryRepository();
});

/// Provider for the story download manager
final storyDownloadManagerProvider = Provider<StoryDownloadManager>((ref) {
  return StoryDownloadManager();
});

/// State class for new story library
class NewStoryLibraryState {
  final List<StoryMetadataModel> stories;
  final bool isLoading;
  final String? error;
  final String searchQuery;

  const NewStoryLibraryState({
    this.stories = const [],
    this.isLoading = false,
    this.error,
    this.searchQuery = '',
  });

  NewStoryLibraryState copyWith({
    List<StoryMetadataModel>? stories,
    bool? isLoading,
    String? error,
    String? searchQuery,
  }) {
    return NewStoryLibraryState(
      stories: stories ?? this.stories,
      isLoading: isLoading ?? this.isLoading,
      error: error,
      searchQuery: searchQuery ?? this.searchQuery,
    );
  }
}

/// Notifier for managing new story library state with Firebase support
class NewStoryLibraryNotifier extends StateNotifier<NewStoryLibraryState> {
  final NewStoryService _storyService;
  final StoryRepository _storyRepository;
  final StoryDownloadManager _downloadManager;

  NewStoryLibraryNotifier(
    this._storyService,
    this._storyRepository,
    this._downloadManager,
  ) : super(const NewStoryLibraryState()) {
    _loadStories();
    _listenToDownloadProgress();
  }

  /// Load all stories from both asset and Firebase sources
  Future<void> _loadStories() async {
    AppLogger.debug('[NEW_STORY_LIBRARY] Loading stories from all sources (assets + Firebase)');

    state = state.copyWith(isLoading: true, error: null);

    try {
      // Get stories from the enhanced repository (includes Firebase stories)
      final stories = await _storyRepository.fetchStoryMetadataList();
      AppLogger.debug('[NEW_STORY_LIBRARY] Loaded ${stories.length} stories from all sources');

      state = state.copyWith(
        stories: stories,
        isLoading: false,
        error: null,
      );
    } catch (e) {
      AppLogger.debug('[NEW_STORY_LIBRARY] Error loading stories: $e');
      state = state.copyWith(
        isLoading: false,
        error: 'Failed to load stories: $e',
      );
    }
  }

  /// Listen to download progress updates
  void _listenToDownloadProgress() {
    _downloadManager.downloadProgress.listen((progress) {
      AppLogger.debug('[NEW_STORY_LIBRARY] Download progress for ${progress.storyId}: ${progress.progress}');
      // Trigger a rebuild to update UI
      if (mounted) {
        // Force a state update to trigger UI rebuild
        state = state.copyWith();
      }
    });
  }

  /// Refresh the story list
  Future<void> refresh() async {
    AppLogger.debug('[NEW_STORY_LIBRARY] Refreshing story list');
    _storyService.clearCache();
    _storyRepository.clearCache();
    await _loadStories();
  }

  /// Search stories by title
  void search(String query) {
    AppLogger.debug('[NEW_STORY_LIBRARY] Searching stories with query: $query');
    state = state.copyWith(searchQuery: query);
  }

  /// Get story status (play/download) using enhanced repository
  Future<String> getStoryStatus(String storyId) async {
    try {
      // Check if currently downloading
      if (_downloadManager.isDownloading(storyId)) {
        return 'downloading';
      }

      // Check if download failed
      if (_downloadManager.isDownloadFailed(storyId)) {
        return 'download_failed';
      }

      // Use repository to get comprehensive status
      return await _storyRepository.getStoryStatus(storyId);
    } catch (e) {
      AppLogger.debug('[NEW_STORY_LIBRARY] Error getting story status for $storyId: $e');
      return 'error';
    }
  }

  /// Download a story from Firebase
  Future<bool> downloadStory(String storyId, {bool checkPermissions = true}) async {
    try {
      AppLogger.debug('[NEW_STORY_LIBRARY] Starting download for story: $storyId');

      final success = await _downloadManager.downloadStory(
        storyId,
        checkPermissions: checkPermissions,
      );

      if (success) {
        AppLogger.debug('[NEW_STORY_LIBRARY] Successfully downloaded story: $storyId');
        // Refresh the story list to update status
        await _loadStories();
      } else {
        AppLogger.debug('[NEW_STORY_LIBRARY] Failed to download story: $storyId');
      }

      return success;
    } catch (e) {
      AppLogger.debug('[NEW_STORY_LIBRARY] Error downloading story $storyId: $e');
      return false;
    }
  }

  /// Get download progress for a story
  double getDownloadProgress(String storyId) {
    return _downloadManager.getDownloadProgressPercentage(storyId);
  }

  /// Cancel download for a story
  Future<void> cancelDownload(String storyId) async {
    await _downloadManager.cancelDownload(storyId);
  }
}

/// Provider for new story library state
final newStoryLibraryProvider = StateNotifierProvider<NewStoryLibraryNotifier, NewStoryLibraryState>((ref) {
  final storyService = ref.watch(newStoryServiceProvider);
  final storyRepository = ref.watch(storyRepositoryProvider);
  final downloadManager = ref.watch(storyDownloadManagerProvider);
  return NewStoryLibraryNotifier(storyService, storyRepository, downloadManager);
});

/// Provider for filtered stories based on search query
final newFilteredStoriesProvider = Provider<List<StoryMetadataModel>>((ref) {
  final state = ref.watch(newStoryLibraryProvider);
  final searchQuery = state.searchQuery.toLowerCase().trim();

  if (searchQuery.isEmpty) {
    return state.stories;
  }

  return state.stories.where((story) {
    final title = story.getLocalizedTitle('en-US').toLowerCase();
    final moral = story.targetMoralValue.toLowerCase();
    return title.contains(searchQuery) || moral.contains(searchQuery);
  }).toList();
});
