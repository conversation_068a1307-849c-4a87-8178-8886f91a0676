import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:intl/intl.dart' as intl;

import 'app_localizations_en.dart';

// ignore_for_file: type=lint

/// Callers can lookup localized strings with an instance of AppLocalizations
/// returned by `AppLocalizations.of(context)`.
///
/// Applications need to include `AppLocalizations.delegate()` in their app's
/// `localizationDelegates` list, and the locales they support in the app's
/// `supportedLocales` list. For example:
///
/// ```dart
/// import 'l10n/app_localizations.dart';
///
/// return MaterialApp(
///   localizationsDelegates: AppLocalizations.localizationsDelegates,
///   supportedLocales: AppLocalizations.supportedLocales,
///   home: MyApplicationHome(),
/// );
/// ```
///
/// ## Update pubspec.yaml
///
/// Please make sure to update your pubspec.yaml to include the following
/// packages:
///
/// ```yaml
/// dependencies:
///   # Internationalization support.
///   flutter_localizations:
///     sdk: flutter
///   intl: any # Use the pinned version from flutter_localizations
///
///   # Rest of dependencies
/// ```
///
/// ## iOS Applications
///
/// iOS applications define key application metadata, including supported
/// locales, in an Info.plist file that is built into the application bundle.
/// To configure the locales supported by your app, you’ll need to edit this
/// file.
///
/// First, open your project’s ios/Runner.xcworkspace Xcode workspace file.
/// Then, in the Project Navigator, open the Info.plist file under the Runner
/// project’s Runner folder.
///
/// Next, select the Information Property List item, select Add Item from the
/// Editor menu, then select Localizations from the pop-up menu.
///
/// Select and expand the newly-created Localizations item then, for each
/// locale your application supports, add a new item and select the locale
/// you wish to add from the pop-up menu in the Value field. This list should
/// be consistent with the languages listed in the AppLocalizations.supportedLocales
/// property.
abstract class AppLocalizations {
  AppLocalizations(String locale) : localeName = intl.Intl.canonicalizedLocale(locale.toString());

  final String localeName;

  static AppLocalizations? of(BuildContext context) {
    return Localizations.of<AppLocalizations>(context, AppLocalizations);
  }

  static const LocalizationsDelegate<AppLocalizations> delegate = _AppLocalizationsDelegate();

  /// A list of this localizations delegate along with the default localizations
  /// delegates.
  ///
  /// Returns a list of localizations delegates containing this delegate along with
  /// GlobalMaterialLocalizations.delegate, GlobalCupertinoLocalizations.delegate,
  /// and GlobalWidgetsLocalizations.delegate.
  ///
  /// Additional delegates can be added by appending to this list in
  /// MaterialApp. This list does not have to be used at all if a custom list
  /// of delegates is preferred or required.
  static const List<LocalizationsDelegate<dynamic>> localizationsDelegates = <LocalizationsDelegate<dynamic>>[
    delegate,
    GlobalMaterialLocalizations.delegate,
    GlobalCupertinoLocalizations.delegate,
    GlobalWidgetsLocalizations.delegate,
  ];

  /// A list of this localizations delegate's supported locales.
  static const List<Locale> supportedLocales = <Locale>[
    Locale('en')
  ];

  /// No description provided for @appTitle.
  ///
  /// In en, this message translates to:
  /// **'Choice: Once Upon A Time'**
  String get appTitle;

  /// No description provided for @ftueScreenWelcome.
  ///
  /// In en, this message translates to:
  /// **'Welcome to Choice! Let me show you how to start your interactive story adventure.'**
  String get ftueScreenWelcome;

  /// No description provided for @ftueFeatureInteractiveStories.
  ///
  /// In en, this message translates to:
  /// **'Our interactive stories let you choose your own path in every tale.'**
  String get ftueFeatureInteractiveStories;

  /// No description provided for @ftueFeatureLifeValues.
  ///
  /// In en, this message translates to:
  /// **'These stories teach and inspire, reinforcing positive life values.'**
  String get ftueFeatureLifeValues;

  /// No description provided for @ftueFeatureNarration.
  ///
  /// In en, this message translates to:
  /// **'Listen to your stories come alive with our empathetic narrator.'**
  String get ftueFeatureNarration;

  /// No description provided for @ftueCompletePrompt.
  ///
  /// In en, this message translates to:
  /// **'Are you ready? Tap \'Start Reading\' to begin!'**
  String get ftueCompletePrompt;

  /// No description provided for @ftueSkipPrompt.
  ///
  /// In en, this message translates to:
  /// **'Or, if you\'re ready, you can skip the tutorial.'**
  String get ftueSkipPrompt;

  /// No description provided for @storyIntroGenericPrompt.
  ///
  /// In en, this message translates to:
  /// **'Welcome to the story introduction. Please review the details and decide to start or download the story.'**
  String get storyIntroGenericPrompt;

  /// No description provided for @errorStoryNotFound.
  ///
  /// In en, this message translates to:
  /// **'Sorry, we could not find the details for this story.'**
  String get errorStoryNotFound;

  /// No description provided for @downloadInProgressPleaseWait.
  ///
  /// In en, this message translates to:
  /// **'Download is in progress. Please wait.'**
  String get downloadInProgressPleaseWait;

  /// No description provided for @errorDownloadFailed.
  ///
  /// In en, this message translates to:
  /// **'Download failed. Please try again.'**
  String get errorDownloadFailed;

  /// No description provided for @downloadCompletePrompt.
  ///
  /// In en, this message translates to:
  /// **'Story downloaded successfully and is now available offline!'**
  String get downloadCompletePrompt;

  /// No description provided for @storyLockedPrompt.
  ///
  /// In en, this message translates to:
  /// **'This story is locked. You can unlock it with a premium subscription.'**
  String get storyLockedPrompt;

  /// No description provided for @storyIntroActionStartOfflinePrompt.
  ///
  /// In en, this message translates to:
  /// **'The story is available offline. You can start reading now or manage your downloads.'**
  String get storyIntroActionStartOfflinePrompt;

  /// No description provided for @storyIntroActionStartDownloadPrompt.
  ///
  /// In en, this message translates to:
  /// **'You can start the story now, or download it to make it available offline.'**
  String get storyIntroActionStartDownloadPrompt;

  /// No description provided for @storyIntroDownloadConfirmationTitle.
  ///
  /// In en, this message translates to:
  /// **'Download Story?'**
  String get storyIntroDownloadConfirmationTitle;

  /// No description provided for @storyIntroDownloadConfirmationMessage.
  ///
  /// In en, this message translates to:
  /// **'Would you like to download this story for offline reading?'**
  String get storyIntroDownloadConfirmationMessage;

  /// No description provided for @storyIntroNotNowButton.
  ///
  /// In en, this message translates to:
  /// **'Not Now'**
  String get storyIntroNotNowButton;

  /// No description provided for @storyIntroDownloadButton.
  ///
  /// In en, this message translates to:
  /// **'Download'**
  String get storyIntroDownloadButton;

  /// No description provided for @storyIntroPremiumRequiredTitle.
  ///
  /// In en, this message translates to:
  /// **'Premium Required'**
  String get storyIntroPremiumRequiredTitle;

  /// No description provided for @storyIntroPremiumRequiredMessage.
  ///
  /// In en, this message translates to:
  /// **'This story is part of our premium collection. Subscribe to unlock all stories and features.'**
  String get storyIntroPremiumRequiredMessage;

  /// No description provided for @storyIntroCancelButton.
  ///
  /// In en, this message translates to:
  /// **'Cancel'**
  String get storyIntroCancelButton;

  /// No description provided for @storyIntroSubscribeButton.
  ///
  /// In en, this message translates to:
  /// **'Subscribe'**
  String get storyIntroSubscribeButton;

  /// No description provided for @homeScreenIntro.
  ///
  /// In en, this message translates to:
  /// **'Welcome to your Story Library. Browse the stories below and tap on one to begin your adventure!'**
  String get homeScreenIntro;

  /// No description provided for @refreshingStories.
  ///
  /// In en, this message translates to:
  /// **'Refreshing your stories...'**
  String get refreshingStories;

  /// No description provided for @loadingStories.
  ///
  /// In en, this message translates to:
  /// **'Loading your stories, please wait.'**
  String get loadingStories;

  /// No description provided for @errorLoadingStories.
  ///
  /// In en, this message translates to:
  /// **'Oops, there was an error loading your stories: {errorMessage}'**
  String errorLoadingStories(Object errorMessage);

  /// No description provided for @noStoriesAvailable.
  ///
  /// In en, this message translates to:
  /// **'There are currently no stories available. Please check back later or try refreshing.'**
  String get noStoriesAvailable;

  /// No description provided for @parentalGateScreenIntro.
  ///
  /// In en, this message translates to:
  /// **'Welcome to the Parent Zone access screen. This area is for parents and guardians only.'**
  String get parentalGateScreenIntro;

  /// No description provided for @parentalGateHoldButtonPrompt.
  ///
  /// In en, this message translates to:
  /// **'To continue, please press and hold the button below for 3 seconds.'**
  String get parentalGateHoldButtonPrompt;

  /// No description provided for @parentalGateKeepHoldingPrompt.
  ///
  /// In en, this message translates to:
  /// **'Keep holding... Almost there!'**
  String get parentalGateKeepHoldingPrompt;

  /// No description provided for @parentalGatePassedPrompt.
  ///
  /// In en, this message translates to:
  /// **'Access granted. Loading Parent Zone.'**
  String get parentalGatePassedPrompt;

  /// No description provided for @aiStoryGenerationScreenIntro.
  ///
  /// In en, this message translates to:
  /// **'Welcome to the AI Story Creator! Here you can create personalized stories just for your child.'**
  String get aiStoryGenerationScreenIntro;

  /// No description provided for @aiStoryGeneratingPrompt.
  ///
  /// In en, this message translates to:
  /// **'Creating your personalized story now. This may take a moment, so please be patient.'**
  String get aiStoryGeneratingPrompt;

  /// No description provided for @aiStoryGeneratedPrompt.
  ///
  /// In en, this message translates to:
  /// **'Your story is ready! Let\'s start reading your personalized adventure.'**
  String get aiStoryGeneratedPrompt;

  /// No description provided for @aiStoryGenerationErrorPrompt.
  ///
  /// In en, this message translates to:
  /// **'Sorry, there was an issue creating your story. Please check your settings and try again.'**
  String get aiStoryGenerationErrorPrompt;

  /// Personalized welcome message for returning users
  ///
  /// In en, this message translates to:
  /// **'Welcome back, {userName}!'**
  String welcomeBackUser(String userName);

  /// Personalized welcome message for child profiles
  ///
  /// In en, this message translates to:
  /// **'Hello, {childName}! Ready for an adventure?'**
  String welcomeChild(String childName);

  /// Personalized welcome message for parent zone
  ///
  /// In en, this message translates to:
  /// **'Welcome to the Parent Zone, {parentName}!'**
  String parentZoneWelcome(String parentName);

  /// Greeting message when child profile is selected
  ///
  /// In en, this message translates to:
  /// **'Hi {childName}! Let\'s continue your reading journey.'**
  String childProfileGreeting(String childName);

  /// Congratulations message when child completes a story
  ///
  /// In en, this message translates to:
  /// **'Congratulations, {childName}! You completed the story!'**
  String storyCompletionCongrats(String childName);

  /// Voice guide welcome message for users
  ///
  /// In en, this message translates to:
  /// **'Welcome back, {userName}. I\'m here to guide you through your storytelling adventure.'**
  String voiceGuideWelcomeUser(String userName);

  /// Voice guide welcome message for children
  ///
  /// In en, this message translates to:
  /// **'Hello {childName}! I\'m your story guide. Let\'s explore amazing tales together!'**
  String voiceGuideWelcomeChild(String childName);

  /// Voice guide message for story selection
  ///
  /// In en, this message translates to:
  /// **'{childName}, choose a story that interests you. I\'ll be here to help you along the way.'**
  String voiceGuideStorySelection(String childName);

  /// Voice guide message for parent zone
  ///
  /// In en, this message translates to:
  /// **'{parentName}, this is your parent zone where you can manage profiles and track progress.'**
  String voiceGuideParentZone(String parentName);

  /// Message when a child profile is activated
  ///
  /// In en, this message translates to:
  /// **'{childName}\'s profile is now active. Happy reading!'**
  String childProfileActivated(String childName);

  /// Reading progress update message
  ///
  /// In en, this message translates to:
  /// **'Great job, {childName}! You\'ve read for {minutes} minutes today.'**
  String readingProgressUpdate(String childName, int minutes);
}

class _AppLocalizationsDelegate extends LocalizationsDelegate<AppLocalizations> {
  const _AppLocalizationsDelegate();

  @override
  Future<AppLocalizations> load(Locale locale) {
    return SynchronousFuture<AppLocalizations>(lookupAppLocalizations(locale));
  }

  @override
  bool isSupported(Locale locale) => <String>['en'].contains(locale.languageCode);

  @override
  bool shouldReload(_AppLocalizationsDelegate old) => false;
}

AppLocalizations lookupAppLocalizations(Locale locale) {


  // Lookup logic when only language code is specified.
  switch (locale.languageCode) {
    case 'en': return AppLocalizationsEn();
  }

  throw FlutterError(
    'AppLocalizations.delegate failed to load unsupported locale "$locale". This is likely '
    'an issue with the localizations generation tool. Please file an issue '
    'on GitHub with a reproducible sample app and the gen-l10n configuration '
    'that was used.'
  );
}
