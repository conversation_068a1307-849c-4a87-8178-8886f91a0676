import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:choice_once_upon_a_time/features/parent_zone/data/user_profile_service.dart';
import 'package:choice_once_upon_a_time/core/utils/app_logger.dart';

/// Service for synchronizing user profiles between online and offline storage
class ProfileSyncService {
  static const String _logPrefix = 'ProfileSyncService';
  static const String _lastSyncKey = 'last_profile_sync';
  static const String _pendingSyncKey = 'pending_profile_sync';
  
  final UserProfileService _userProfileService;
  
  ProfileSyncService(this._userProfileService);

  /// Sync profiles between online and offline storage
  Future<ProfileSyncResult> syncProfiles() async {
    try {
      AppLogger.debug('$_logPrefix: Starting profile synchronization');
      
      // Load local profiles
      final localProfiles = await _userProfileService.loadProfiles();
      AppLogger.debug('$_logPrefix: Found ${localProfiles.length} local profiles');
      
      // Check if we have internet connectivity
      final hasConnectivity = await _checkConnectivity();
      
      if (!hasConnectivity) {
        AppLogger.debug('$_logPrefix: No internet connectivity, using offline mode');
        return ProfileSyncResult(
          success: true,
          profiles: localProfiles,
          syncMode: SyncMode.offline,
          message: 'Using offline profiles',
        );
      }
      
      // Try to sync with online storage
      try {
        final onlineProfiles = await _fetchOnlineProfiles();
        final mergedProfiles = await _mergeProfiles(localProfiles, onlineProfiles);
        
        // Save merged profiles locally
        await _userProfileService.saveProfiles(mergedProfiles);
        
        // Upload any local changes to online storage
        await _uploadProfiles(mergedProfiles);
        
        // Update last sync timestamp
        await _updateLastSyncTime();
        
        AppLogger.info('$_logPrefix: Successfully synced ${mergedProfiles.length} profiles');
        
        return ProfileSyncResult(
          success: true,
          profiles: mergedProfiles,
          syncMode: SyncMode.online,
          message: 'Profiles synced successfully',
        );
        
      } catch (e) {
        AppLogger.warning('$_logPrefix: Online sync failed, falling back to offline: $e');
        
        // Mark profiles for pending sync
        await _markPendingSync(localProfiles);
        
        return ProfileSyncResult(
          success: true,
          profiles: localProfiles,
          syncMode: SyncMode.offline,
          message: 'Online sync failed, using local profiles',
        );
      }
      
    } catch (e, stackTrace) {
      AppLogger.error('$_logPrefix: Profile sync failed', e, stackTrace);
      
      // Try to load local profiles as fallback
      try {
        final localProfiles = await _userProfileService.loadProfiles();
        return ProfileSyncResult(
          success: false,
          profiles: localProfiles,
          syncMode: SyncMode.offline,
          message: 'Sync failed, using cached profiles',
        );
      } catch (localError) {
        return ProfileSyncResult(
          success: false,
          profiles: [],
          syncMode: SyncMode.offline,
          message: 'Failed to load profiles: $localError',
        );
      }
    }
  }

  /// Check if we have internet connectivity
  Future<bool> _checkConnectivity() async {
    // In a real implementation, you would use connectivity_plus package
    // For now, we'll simulate connectivity check
    try {
      // Simulate network check delay
      await Future.delayed(const Duration(milliseconds: 100));
      
      // For development, assume we have connectivity
      // In production, implement actual connectivity check
      return true;
    } catch (e) {
      return false;
    }
  }

  /// Fetch profiles from online storage (Firebase/API)
  Future<List<UserProfile>> _fetchOnlineProfiles() async {
    // TODO: Implement actual online profile fetching
    // This would typically involve Firebase Firestore or REST API calls
    
    AppLogger.debug('$_logPrefix: Fetching profiles from online storage');
    
    // Simulate API call delay
    await Future.delayed(const Duration(milliseconds: 500));
    
    // For now, return empty list (no online profiles)
    // In production, implement actual online fetching
    return [];
  }

  /// Upload profiles to online storage
  Future<void> _uploadProfiles(List<UserProfile> profiles) async {
    // TODO: Implement actual online profile uploading
    // This would typically involve Firebase Firestore or REST API calls
    
    AppLogger.debug('$_logPrefix: Uploading ${profiles.length} profiles to online storage');
    
    // Simulate API call delay
    await Future.delayed(const Duration(milliseconds: 300));
    
    // In production, implement actual online uploading
    AppLogger.debug('$_logPrefix: Profiles uploaded successfully (simulated)');
  }

  /// Merge local and online profiles, resolving conflicts
  Future<List<UserProfile>> _mergeProfiles(
    List<UserProfile> localProfiles,
    List<UserProfile> onlineProfiles,
  ) async {
    AppLogger.debug('$_logPrefix: Merging ${localProfiles.length} local and ${onlineProfiles.length} online profiles');
    
    final Map<String, UserProfile> mergedMap = {};
    
    // Add all local profiles first
    for (final profile in localProfiles) {
      mergedMap[profile.id] = profile;
    }
    
    // Merge online profiles, resolving conflicts by last modified time
    for (final onlineProfile in onlineProfiles) {
      final localProfile = mergedMap[onlineProfile.id];
      
      if (localProfile == null) {
        // New online profile, add it
        mergedMap[onlineProfile.id] = onlineProfile;
        AppLogger.debug('$_logPrefix: Added new online profile: ${onlineProfile.name}');
      } else {
        // Conflict resolution: use the most recently updated profile
        if (onlineProfile.lastActiveAt.isAfter(localProfile.lastActiveAt)) {
          mergedMap[onlineProfile.id] = onlineProfile;
          AppLogger.debug('$_logPrefix: Updated profile from online: ${onlineProfile.name}');
        } else {
          AppLogger.debug('$_logPrefix: Kept local profile: ${localProfile.name}');
        }
      }
    }
    
    final mergedProfiles = mergedMap.values.toList();
    AppLogger.debug('$_logPrefix: Merged to ${mergedProfiles.length} total profiles');
    
    return mergedProfiles;
  }

  /// Mark profiles for pending sync when online sync fails
  Future<void> _markPendingSync(List<UserProfile> profiles) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final profilesJson = profiles.map((p) => p.toJson()).toList();
      await prefs.setString(_pendingSyncKey, jsonEncode(profilesJson));
      AppLogger.debug('$_logPrefix: Marked ${profiles.length} profiles for pending sync');
    } catch (e) {
      AppLogger.error('$_logPrefix: Failed to mark pending sync', e);
    }
  }

  /// Update last sync timestamp
  Future<void> _updateLastSyncTime() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_lastSyncKey, DateTime.now().toIso8601String());
    } catch (e) {
      AppLogger.error('$_logPrefix: Failed to update last sync time', e);
    }
  }

  /// Get last sync time
  Future<DateTime?> getLastSyncTime() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final lastSyncString = prefs.getString(_lastSyncKey);
      if (lastSyncString != null) {
        return DateTime.parse(lastSyncString);
      }
    } catch (e) {
      AppLogger.error('$_logPrefix: Failed to get last sync time', e);
    }
    return null;
  }

  /// Check if profiles need sync (haven't been synced in 24 hours)
  Future<bool> needsSync() async {
    final lastSync = await getLastSyncTime();
    if (lastSync == null) return true;
    
    final hoursSinceSync = DateTime.now().difference(lastSync).inHours;
    return hoursSinceSync >= 24;
  }

  /// Retry pending sync operations
  Future<void> retryPendingSync() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final pendingJson = prefs.getString(_pendingSyncKey);
      
      if (pendingJson != null) {
        final profilesData = jsonDecode(pendingJson) as List;
        final profiles = profilesData.map((json) => UserProfile.fromJson(json)).toList();
        
        AppLogger.debug('$_logPrefix: Retrying sync for ${profiles.length} pending profiles');
        
        await _uploadProfiles(profiles);
        await prefs.remove(_pendingSyncKey);
        await _updateLastSyncTime();
        
        AppLogger.info('$_logPrefix: Successfully synced pending profiles');
      }
    } catch (e) {
      AppLogger.error('$_logPrefix: Failed to retry pending sync', e);
    }
  }
}

/// Result of profile synchronization operation
class ProfileSyncResult {
  final bool success;
  final List<UserProfile> profiles;
  final SyncMode syncMode;
  final String message;

  ProfileSyncResult({
    required this.success,
    required this.profiles,
    required this.syncMode,
    required this.message,
  });

  @override
  String toString() {
    return 'ProfileSyncResult(success: $success, profiles: ${profiles.length}, mode: $syncMode, message: $message)';
  }
}

/// Synchronization mode
enum SyncMode {
  online,   // Successfully synced with online storage
  offline,  // Using local storage only
}

/// Extension to add sync capabilities to UserProfile
extension UserProfileSync on UserProfile {
  /// Check if this profile has been modified since last sync
  bool isModifiedSince(DateTime? lastSync) {
    if (lastSync == null) return true;
    return lastActiveAt.isAfter(lastSync);
  }
  
  /// Create a sync-ready version of this profile
  Map<String, dynamic> toSyncJson() {
    final json = toJson();
    json['syncTimestamp'] = DateTime.now().toIso8601String();
    return json;
  }
}
