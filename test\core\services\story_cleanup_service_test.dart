import 'package:flutter_test/flutter_test.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:choice_once_upon_a_time/core/services/story_cleanup_service.dart';

void main() {
  group('StoryCleanupService Tests', () {
    late StoryCleanupService cleanupService;

    setUp(() {
      cleanupService = StoryCleanupService();
      // Clear SharedPreferences before each test
      SharedPreferences.setMockInitialValues({});
    });

    group('Download Tracking', () {
      test('should record story download correctly', () async {
        // Arrange
        const storyId = 'test_story';
        const localPath = '/path/to/story';

        // Act
        await cleanupService.recordStoryDownload(storyId, localPath);

        // Assert
        final storiesInfo = await cleanupService.getDownloadedStoriesInfo();
        expect(storiesInfo.length, equals(1));
        expect(storiesInfo.first.storyId, equals(storyId));
        expect(storiesInfo.first.localPath, equals(localPath));
        expect(storiesInfo.first.ageInDays, equals(0)); // Downloaded today
      });

      test('should update story access correctly', () async {
        // Arrange
        const storyId = 'test_story';
        const localPath = '/path/to/story';

        await cleanupService.recordStoryDownload(storyId, localPath);

        // Act
        await cleanupService.updateStoryAccess(storyId);

        // Assert
        final storiesInfo = await cleanupService.getDownloadedStoriesInfo();
        expect(storiesInfo.length, equals(1));
        expect(storiesInfo.first.accessCount, equals(2)); // Initial + 1 update
      });

      test('should remove story from tracking correctly', () async {
        // Arrange
        const storyId = 'test_story';
        const localPath = '/path/to/story';

        await cleanupService.recordStoryDownload(storyId, localPath);

        // Act
        await cleanupService.removeStoryFromTracking(storyId);

        // Assert
        final storiesInfo = await cleanupService.getDownloadedStoriesInfo();
        expect(storiesInfo.length, equals(0));
      });
    });

    group('Cleanup Settings', () {
      test('should default to automatic cleanup enabled', () async {
        // Act
        final isEnabled = await cleanupService.isAutomaticCleanupEnabled();

        // Assert
        expect(isEnabled, isTrue);
      });

      test('should set and get automatic cleanup preference', () async {
        // Act
        await cleanupService.setAutomaticCleanupEnabled(false);
        final isEnabled = await cleanupService.isAutomaticCleanupEnabled();

        // Assert
        expect(isEnabled, isFalse);

        // Act again
        await cleanupService.setAutomaticCleanupEnabled(true);
        final isEnabledAgain = await cleanupService.isAutomaticCleanupEnabled();

        // Assert
        expect(isEnabledAgain, isTrue);
      });
    });

    group('Story Age Calculation', () {
      test('should calculate story age correctly', () async {
        // Arrange
        const storyId = 'test_story';
        const localPath = '/path/to/story';

        // Mock a story downloaded 15 days ago
        final prefs = await SharedPreferences.getInstance();
        final oldDate = DateTime.now().subtract(const Duration(days: 15));
        final trackingData = {
          storyId: {
            'downloadedAt': oldDate.toIso8601String(),
            'localPath': localPath,
            'lastAccessedAt': oldDate.toIso8601String(),
            'accessCount': 1,
          }
        };
        await prefs.setString('story_download_tracking', 
            '${trackingData.toString().replaceAll("'", '"')}');

        // Act
        final storiesInfo = await cleanupService.getDownloadedStoriesInfo();

        // Assert
        expect(storiesInfo.length, equals(1));
        expect(storiesInfo.first.ageInDays, equals(15));
        expect(storiesInfo.first.willBeDeletedSoon, isTrue); // 15 days > (30-7) days
        expect(storiesInfo.first.isExpired, isFalse); // 15 days < 30 days
      });

      test('should identify expired stories correctly', () async {
        // Arrange
        const storyId = 'old_story';
        const localPath = '/path/to/story';

        // Mock a story downloaded 35 days ago (expired)
        final prefs = await SharedPreferences.getInstance();
        final oldDate = DateTime.now().subtract(const Duration(days: 35));
        final trackingData = {
          storyId: {
            'downloadedAt': oldDate.toIso8601String(),
            'localPath': localPath,
            'lastAccessedAt': oldDate.toIso8601String(),
            'accessCount': 1,
          }
        };
        await prefs.setString('story_download_tracking', 
            '${trackingData.toString().replaceAll("'", '"')}');

        // Act
        final storiesInfo = await cleanupService.getDownloadedStoriesInfo();

        // Assert
        expect(storiesInfo.length, equals(1));
        expect(storiesInfo.first.ageInDays, equals(35));
        expect(storiesInfo.first.isExpired, isTrue);
        expect(storiesInfo.first.willBeDeletedSoon, isTrue);
      });
    });

    group('Cleanup Operations', () {
      test('should not perform cleanup if done recently', () async {
        // Arrange
        final prefs = await SharedPreferences.getInstance();
        await prefs.setString('last_cleanup_date', DateTime.now().toIso8601String());

        // Act
        final result = await cleanupService.performAutomaticCleanup();

        // Assert
        expect(result.storiesChecked, equals(0));
        expect(result.storiesDeleted, equals(0));
        expect(result.hasDeletedStories, isFalse);
      });

      test('should perform cleanup when forced', () async {
        // Arrange
        final prefs = await SharedPreferences.getInstance();
        await prefs.setString('last_cleanup_date', DateTime.now().toIso8601String());

        // Add an old story
        const storyId = 'old_story';
        const localPath = '/path/to/story';
        final oldDate = DateTime.now().subtract(const Duration(days: 35));
        final trackingData = {
          storyId: {
            'downloadedAt': oldDate.toIso8601String(),
            'localPath': localPath,
            'lastAccessedAt': oldDate.toIso8601String(),
            'accessCount': 1,
          }
        };
        await prefs.setString('story_download_tracking', 
            '${trackingData.toString().replaceAll("'", '"')}');

        // Act
        final result = await cleanupService.performAutomaticCleanup(force: true);

        // Assert
        expect(result.storiesChecked, equals(1));
        // Note: storiesDeleted might be 0 because the directory doesn't actually exist
        // but the story should be marked for deletion
      });

      test('should handle invalid date formats gracefully', () async {
        // Arrange
        const storyId = 'invalid_story';
        const localPath = '/path/to/story';

        final prefs = await SharedPreferences.getInstance();
        final trackingData = {
          storyId: {
            'downloadedAt': 'invalid-date',
            'localPath': localPath,
            'lastAccessedAt': 'invalid-date',
            'accessCount': 1,
          }
        };
        await prefs.setString('story_download_tracking', 
            '${trackingData.toString().replaceAll("'", '"')}');

        // Act
        final result = await cleanupService.performAutomaticCleanup(force: true);

        // Assert
        expect(result.storiesChecked, equals(1));
        expect(result.hasErrors, isTrue);
        expect(result.errors.first, contains('Invalid date format'));
      });
    });

    group('CleanupResult', () {
      test('should format space correctly', () {
        // Test bytes
        final result1 = CleanupResult(
          storiesChecked: 1,
          storiesDeleted: 1,
          spaceFreed: 512,
          errors: [],
        );
        expect(result1.spaceFreedFormatted, equals('512B'));

        // Test KB
        final result2 = CleanupResult(
          storiesChecked: 1,
          storiesDeleted: 1,
          spaceFreed: 1536, // 1.5 KB
          errors: [],
        );
        expect(result2.spaceFreedFormatted, equals('1.5KB'));

        // Test MB
        final result3 = CleanupResult(
          storiesChecked: 1,
          storiesDeleted: 1,
          spaceFreed: 1572864, // 1.5 MB
          errors: [],
        );
        expect(result3.spaceFreedFormatted, equals('1.5MB'));

        // Test GB
        final result4 = CleanupResult(
          storiesChecked: 1,
          storiesDeleted: 1,
          spaceFreed: 1610612736, // 1.5 GB
          errors: [],
        );
        expect(result4.spaceFreedFormatted, equals('1.5GB'));
      });

      test('should report status correctly', () {
        final resultWithDeleted = CleanupResult(
          storiesChecked: 5,
          storiesDeleted: 2,
          spaceFreed: 1024,
          errors: [],
        );
        expect(resultWithDeleted.hasDeletedStories, isTrue);
        expect(resultWithDeleted.hasErrors, isFalse);

        final resultWithErrors = CleanupResult(
          storiesChecked: 3,
          storiesDeleted: 0,
          spaceFreed: 0,
          errors: ['Error 1', 'Error 2'],
        );
        expect(resultWithErrors.hasDeletedStories, isFalse);
        expect(resultWithErrors.hasErrors, isTrue);
      });
    });

    group('DownloadedStoryInfo', () {
      test('should format size correctly', () {
        final storyInfo = DownloadedStoryInfo(
          storyId: 'test',
          downloadedAt: DateTime.now(),
          lastAccessedAt: DateTime.now(),
          localPath: '/path',
          ageInDays: 1,
          daysSinceLastAccess: 1,
          accessCount: 1,
          sizeInBytes: 1572864, // 1.5 MB
          willBeDeletedSoon: false,
          isExpired: false,
        );

        expect(storyInfo.sizeFormatted, equals('1.5MB'));
      });
    });

    test('should return correct max age in days', () {
      expect(cleanupService.maxAgeInDays, equals(30));
    });
  });
}
