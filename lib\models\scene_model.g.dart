// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'scene_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

SceneModel _$SceneModelFromJson(Map<String, dynamic> json) => SceneModel(
      sceneId: json['sceneId'] as String,
      order: (json['order'] as num?)?.toInt(),
      sceneType: json['sceneType'] as String? ?? 'narration_illustration',
      settingDescription: json['settingDescription'] as String?,
      atmosphereDescription: json['atmosphereDescription'] as String?,
      imageDescriptionForGeneration:
          json['imageDescriptionForGeneration'] as String?,
      narratorSegments: (json['narratorSegments'] as List<dynamic>)
          .map((e) => TextSegmentModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      characterDialogues: (json['characterDialogues'] as List<dynamic>?)
          ?.map((e) => e as Map<String, dynamic>)
          .toList(),
      isChoicePoint: json['isChoicePoint'] as bool? ?? false,
      nextSceneId: json['nextSceneId'] as String?,
      choicePointData: json['choicePointData'] as Map<String, dynamic>?,
      isEndOfStory: json['isEndOfStory'] as bool?,
    );

Map<String, dynamic> _$SceneModelToJson(SceneModel instance) =>
    <String, dynamic>{
      'sceneId': instance.sceneId,
      'order': instance.order,
      'sceneType': instance.sceneType,
      'settingDescription': instance.settingDescription,
      'atmosphereDescription': instance.atmosphereDescription,
      'imageDescriptionForGeneration': instance.imageDescriptionForGeneration,
      'narratorSegments': instance.narratorSegments,
      'characterDialogues': instance.characterDialogues,
      'isChoicePoint': instance.isChoicePoint,
      'nextSceneId': instance.nextSceneId,
      'choicePointData': instance.choicePointData,
      'isEndOfStory': instance.isEndOfStory,
    };
