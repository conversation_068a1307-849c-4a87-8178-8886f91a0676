# Guidelines for AI Agent: Flutter Code Generation - Modularity & Security

**Preamble:**
These guidelines are essential for ensuring the generated Flutter codebase for "Choice: Once Upon A Time" is well-structured, maintainable, scalable, and secure. Please adhere to these principles throughout the code generation process. Your primary reference for specific structures and components remains the **Detailed Technical Design Document (TDD)**.

**I. Guidelines for Modular Design Patterns:**

1.  **Adhere to TDD Structure:**
    * Implement the feature-first folder structure (`app/`, `core/`, `features/`, `shared_widgets/`, `models/`, `l10n/`) as specified in the TDD (Section 3.1) [cite: 53-56].
    * Organize files within each feature module into `presentation` (screens, widgets, providers), `domain` (entities, use cases - if applicable for more complex logic later), and `data` (repositories, data sources) subdirectories as conceptually outlined for features like `story_library` in the TDD.

2.  **Reusable Widgets (`shared_widgets/`):**
    * Identify UI elements that are used across multiple screens or features (e.g., `PrimaryButtonWidget`, `ConfirmationDialogWidget`, `LoadingIndicatorWidget` as per TDD Section 3.2).
    * Implement these as separate, configurable, and reusable widgets located in the `shared_widgets/` directory.
    * Ensure these shared widgets are styled according to the global theme (`theme.dart`) and Art Style Definition (Task 2.3) [cite: 3-88].

3.  **Feature-Specific Widgets:**
    * Widgets that are unique to a specific feature (e.g., `StoryCoverCardWidget` for the Story Library, `ChoiceButtonWidget` for the Story Player) should reside within their respective feature's `presentation/widgets/` directory (TDD Section 3.2).

4.  **Separation of Concerns:**
    * **UI (Presentation):** Widgets should primarily be responsible for rendering the UI and delegating user interactions to providers or controllers. Avoid embedding complex business logic directly within UI widgets.
    * **State Management (Riverpod):** Utilize Riverpod providers (`StateNotifierProvider`, `FutureProvider`, `StreamProvider`, `Provider`) for managing state and exposing business logic to the UI, as outlined in TDD Section 3.3 [cite: 74-83]. UI widgets should consume data from these providers.
    * **Business Logic (Services/Repositories):** Encapsulate business logic, data fetching/manipulation, and interactions with external systems (Firebase, TTS, Offline Storage) into dedicated service classes (e.g., `TTSService`, `OfflineStorageService`, `StoryRepository`) located in `core/` or feature-specific `data/` or `domain/` layers as per TDD. These should be accessed via Riverpod.
    * **Data Models (`models/`):** Use the clearly defined Dart model classes (from TDD Section 3.5 [cite: 94-120]) for representing and parsing data (e.g., from Story JSONs, Firestore). These models should be plain Dart objects with `fromJson` methods where applicable.

5.  **Single Responsibility Principle (SRP):**
    * Strive to create classes and widgets that have one primary responsibility. Avoid creating overly large or complex "god" classes/widgets that do too many things.

6.  **Clear Interfaces (Conceptual):**
    * When defining service classes or repositories, ensure their public methods form a clear contract for how they should be used by other parts of the application.

7.  **Minimize Direct Dependencies:**
    * Use Riverpod for dependency injection to provide instances of services, repositories, and providers rather than instantiating them directly within dependent classes or widgets.

**II. Guidelines for Keeping Secret Information Hidden (Security):**

1.  **NO Hardcoding of Secrets:**
    * **Absolutely DO NOT** hardcode sensitive information such as API keys (for Firebase or any third-party services like a potential premium TTS), database credentials, secret salts, encryption keys, or any other confidential strings directly into the Dart source code files.

2.  **Use Environment Variables for Secrets:**
    * Implement a mechanism to load secrets from environment variables.
    * **Create a `.env` file (this file itself will NOT be committed to version control):** This file will store the actual secret values locally during development. Example content:
        ```
        FIREBASE_API_KEY=your_actual_firebase_api_key_here
        TTS_SERVICE_API_KEY=your_actual_tts_api_key_here
        ```
    * **Utilize the `flutter_dotenv` package:**
        * Add `flutter_dotenv` to `pubspec.yaml`.
        * Load the `.env` file in your `main.dart` before `runApp()`:
            ```dart
            // main.dart
            import 'package:flutter_dotenv/flutter_dotenv.dart';
            import 'package:flutter_riverpod/flutter_riverpod.dart'; // Assuming Riverpod
            import 'package:flutter/widgets.dart'; // For WidgetsFlutterBinding
            // Import your AppWidget

            Future<void> main() async {
              WidgetsFlutterBinding.ensureInitialized(); // Ensure bindings are initialized
              await dotenv.load(fileName: ".env"); // Load .env file
              runApp(ProviderScope(child: AppWidget())); // Replace AppWidget with your main app widget
            }
            ```
    * **Access Secrets in Code:** Access secrets via `dotenv.env['SECRET_NAME']`. Provide clear placeholder names.
        ```dart
        // Example service using a secret
        // final String apiKey = dotenv.env['FIREBASE_API_KEY'] ?? 'MISSING_FIREBASE_KEY';
        // if (apiKey == 'MISSING_FIREBASE_KEY') {
        //   // Handle missing key error appropriately, do not proceed with a default key
        //   print('ERROR: FIREBASE_API_KEY is not set in .env file');
        // }
        ```
    * **Crucial:** If a key is absolutely essential for functionality and is missing, the app should handle this gracefully (e.g., show an error, disable the feature) rather than attempting to use a default or invalid key.

3.  **`.gitignore` for Secret Files:**
    * Ensure that the `.env` file (or any similar local configuration file containing actual secrets) is included in the project's `.gitignore` file to prevent it from being committed to version control. Generate or update `.gitignore` to include:
        ```
        # Secret files
        .env
        *.env
        ```

4.  **Firebase Configuration Files:**
    * The `google-services.json` (Android) and `GoogleService-Info.plist` (iOS) files required for Firebase setup should be correctly placed in the project as per Firebase documentation. These files contain configuration details but typically not direct user-managed API *keys* that need the `.env` treatment (they are more like project identifiers). However, if any *additional custom keys* related to Firebase services are ever used (e.g., for specific Cloud Functions with restricted API keys), those custom keys *must* follow the environment variable pattern.

5.  **Build-time Configuration (Dart-Define - Alternative for some secrets):**
    * For some configurations that might vary by build environment (dev, staging, prod) but are not ultra-sensitive API keys, consider using `--dart-define` during the Flutter build process.
    * Code can access these via `String.fromEnvironment('YOUR_DEFINED_VARIABLE')`.
        ```dart
        // const String backendUrl = String.fromEnvironment('BACKEND_URL', defaultValue: '[https://default.api.com](https://default.api.com)');
        ```
    * This is more for environment-specific URLs or flags rather than top-secret API keys which are better in `.env`.

6.  **Backend Responsibility for Sensitive Operations:**
    * Whenever possible, operations involving highly sensitive secrets (e.g., payment processing private keys, third-party service admin keys) should be handled on a secure backend (like Firebase Cloud Functions), not directly in the client app. The client app should make authenticated requests to your backend, which then performs the sensitive operation.

7.  **Placeholder Naming Convention:**
    * If a placeholder value for a secret must be present in any example or template code the AI generates, use a clear and explicit naming convention like `YOUR_SERVICE
    _API_KEY_HERE` or `REPLACE_WITH_YOUR_ACTUAL_KEY`. Accompany this with a comment indicating it needs secure replacement.

**Adherence to these guidelines is critical for producing a high-quality, secure, and maintainable prototype.**