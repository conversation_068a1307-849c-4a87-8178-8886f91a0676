import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

/// A widget representing the Story Library section on the Home Screen grid.
///
/// This widget acts as a gateway to the full story library, displaying
/// a title, an icon, and some preview content. It is designed to be
/// tapped to navigate to the detailed story library screen.
class StoryLibraryGridSection extends StatelessWidget {
  const StoryLibraryGridSection({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return GestureDetector(
      onTap: () {
        // Navigate to the full story library screen.
        // The actual route path might need adjustment based on your GoRouter setup.
        context.go('/story_library');
      },
      child: Card(
        elevation: 4.0,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16.0),
        ),
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(16.0),
            gradient: LinearGradient(
              colors: [
                theme.colorScheme.primary.withValues(alpha: 0.7),
                theme.colorScheme.primary,
              ],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.menu_book,
                size: 48.0,
                color: theme.colorScheme.onPrimary,
              ),
              const SizedBox(height: 8.0),
              Text(
                'Story Library',
                style: theme.textTheme.titleMedium?.copyWith(
                  color: theme.colorScheme.onPrimary,
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
