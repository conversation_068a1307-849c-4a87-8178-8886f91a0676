import 'package:json_annotation/json_annotation.dart';

part 'text_segment_model.g.dart';

/// Model for individual text segments used in TTS narration
@JsonSerializable()
class TextSegmentModel {
  /// Unique identifier for this text segment
  @JsonKey(name: 'segmentId')
  final String id;

  /// Localized text content for TTS (e.g., {"en-US": "Hello there..."})
  final Map<String, String> text;

  /// Emotion cue for TTS delivery (e.g., "warm, gentle", "sad, whisper")
  final String emotionCue;

  /// Optional pre-crafted SSML for specific languages
  final Map<String, String>? ssml;

  /// Optional estimated duration for this segment in milliseconds
  @JsonKey(name: 'durationEstimateMs')
  final int? durationEstimateMs;

  const TextSegmentModel({
    required this.id,
    required this.text,
    required this.emotionCue,
    this.ssml,
    this.durationEstimateMs,
  });

  /// Creates a TextSegmentModel from JSON
  factory TextSegmentModel.fromJson(Map<String, dynamic> json) =>
      _$TextSegmentModelFromJson(json);

  /// Converts the TextSegmentModel to JSON
  Map<String, dynamic> toJson() => _$TextSegmentModelToJson(this);

  /// Gets the localized text for the given language code
  /// Falls back to the first available language if not found
  String getLocalizedText(String languageCode) {
    return text[languageCode] ?? text.values.first;
  }

  /// Gets the SSML for the given language code if available
  String? getLocalizedSSML(String languageCode) {
    return ssml?[languageCode];
  }

  /// Creates a copy of this model with updated fields
  TextSegmentModel copyWith({
    String? id,
    Map<String, String>? text,
    String? emotionCue,
    Map<String, String>? ssml,
    int? durationEstimateMs,
  }) {
    return TextSegmentModel(
      id: id ?? this.id,
      text: text ?? this.text,
      emotionCue: emotionCue ?? this.emotionCue,
      ssml: ssml ?? this.ssml,
      durationEstimateMs: durationEstimateMs ?? this.durationEstimateMs,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is TextSegmentModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'TextSegmentModel(id: $id, emotionCue: $emotionCue)';
  }
}
