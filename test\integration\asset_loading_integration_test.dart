import 'package:flutter_test/flutter_test.dart';
import 'package:flutter/services.dart';
import 'package:choice_once_upon_a_time/core/services/asset_story_service.dart';
import 'package:choice_once_upon_a_time/core/services/asset_fallback_service.dart';
import 'package:choice_once_upon_a_time/features/story_library/data/story_repository.dart';

void main() {
  group('Asset Loading Integration Tests', () {
    late AssetStoryService assetStoryService;
    late AssetFallbackService assetFallbackService;
    late StoryRepository storyRepository;

    setUpAll(() {
      // Set up mock asset bundle for integration testing
      const mockManifest = '''
      {
        "assets/stories/the_courageous_quest/story.json": ["assets/stories/the_courageous_quest/story.json"],
        "assets/stories/the_courageous_quest/images/scene1.jpg": ["assets/stories/the_courageous_quest/images/scene1.jpg"],
        "assets/stories/the_courageous_quest/images/scene2.jpg": ["assets/stories/the_courageous_quest/images/scene2.jpg"],
        "assets/stories/the_courageous_quest/audio/scene1.mp3": ["assets/stories/the_courageous_quest/audio/scene1.mp3"],
        "assets/stories/the_lantern_of_unity/story.json": ["assets/stories/the_lantern_of_unity/story.json"],
        "assets/stories/the_lantern_of_unity/story_with_rewards.json": ["assets/stories/the_lantern_of_unity/story_with_rewards.json"],
        "assets/images/placeholder.jpg": ["assets/images/placeholder.jpg"],
        "assets/images/story_covers/placeholder_cover.jpg": ["assets/images/story_covers/placeholder_cover.jpg"],
        "assets/images/placeholder_scene.jpg": ["assets/images/placeholder_scene.jpg"]
      }
      ''';

      const mockCourageStory = '''
      {
        "id": "courage_city_adventure",
        "title": "The Lost Melody of Mumbai-New York",
        "moral_values": ["Courage", "Helping Others"],
        "scenes": [
          {
            "id": "scene1",
            "text": "The city buzzed like a happy bee! Maya and Jake were best friends exploring the festival.",
            "image": "scene1.jpg",
            "sound": "scene1.mp3",
            "emotional_cue": "[Happy]",
            "choices": [
              {
                "text": "Go to the chai stall",
                "next_scene": "scene2"
              }
            ]
          },
          {
            "id": "scene2",
            "text": "They found the flute and returned it to Old Man Tiber.",
            "image": "scene2.jpg",
            "sound": "scene2.mp3",
            "emotional_cue": "[Joyful]"
          }
        ]
      }
      ''';

      const mockUnityStory = '''
      {
        "id": "lantern_festival_adventure",
        "title": {
          "en-US": "The Lantern of Unity"
        },
        "targetMoralValue": "Sharing and Community",
        "rewards": {
          "completion": "Unity Badge",
          "moralChoices": ["Sharing Star", "Community Medal"]
        },
        "scenes": [
          {
            "id": "scene1",
            "text": "Aria and Sam arrived at the magical festival.",
            "image": "scene1.jpg",
            "sound": "scene1.mp3",
            "emotional_cue": "[Cheerful]"
          }
        ]
      }
      ''';

      TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger
          .setMockMethodCallHandler(
        const MethodChannel('flutter/assets'),
        (MethodCall methodCall) async {
          if (methodCall.method == 'loadString') {
            final String key = methodCall.arguments as String;
            switch (key) {
              case 'AssetManifest.json':
                return mockManifest;
              case 'assets/stories/the_courageous_quest/story.json':
                return mockCourageStory;
              case 'assets/stories/the_lantern_of_unity/story.json':
                return mockUnityStory;
              case 'assets/images/placeholder.jpg':
              case 'assets/images/story_covers/placeholder_cover.jpg':
              case 'assets/images/placeholder_scene.jpg':
                return 'mock_image_data';
              default:
                throw PlatformException(code: 'NOT_FOUND', message: 'Asset not found: $key');
            }
          }
          throw PlatformException(code: 'UNKNOWN_METHOD', message: 'Unknown method: ${methodCall.method}');
        },
      );
    });

    setUp(() {
      assetStoryService = AssetStoryService();
      assetFallbackService = AssetFallbackService();
      storyRepository = StoryRepository(
        assetStoryService: assetStoryService,
      );
    });

    tearDown(() {
      assetStoryService.clearCache();
    });

    tearDownAll(() {
      TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger
          .setMockMethodCallHandler(
        const MethodChannel('flutter/assets'),
        null,
      );
    });

    group('Asset Discovery and Loading', () {
      test('should discover all available stories from assets', () async {
        // Act
        final storyIds = await assetStoryService.getAvailableStoryIds();

        // Assert
        expect(storyIds, contains('the_courageous_quest'));
        expect(storyIds, contains('the_lantern_of_unity'));
        expect(storyIds.length, 2);
      });

      test('should load story with legacy format correctly', () async {
        // Act
        final story = await assetStoryService.loadStoryFromAssets('the_courageous_quest');

        // Assert
        expect(story, isNotNull);
        expect(story!.id, 'courage_city_adventure');
        expect(story.title, 'The Lost Melody of Mumbai-New York');
        expect(story.targetMoralValue, 'Courage');
        expect(story.scenes, hasLength(2));

        // Check scene transformation
        final firstScene = story.scenes.first;
        expect(firstScene.sceneId, 'scene1');
        expect(firstScene.sceneType, 'choice_point');
        expect(firstScene.isChoicePoint, true);
        expect(firstScene.narratorSegments, hasLength(1));

        // Check narrator segment
        final segment = firstScene.narratorSegments.first;
        expect(segment.text['en-US'], contains('The city buzzed like a happy bee!'));
        expect(segment.emotionCue, 'happy');
      });

      test('should load story with rewards format correctly', () async {
        // Act
        final story = await assetStoryService.loadStoryFromAssets('the_lantern_of_unity');

        // Assert
        expect(story, isNotNull);
        expect(story!.id, 'lantern_festival_adventure');
        expect(story.title, 'The Lantern of Unity');
        expect(story.targetMoralValue, 'Sharing and Community');
        expect(story.rewards, isNotNull);
        expect(story.rewards!.completion, 'Unity Badge');
        expect(story.rewards!.moralChoices, contains('Sharing Star'));
        expect(story.rewards!.moralChoices, contains('Community Medal'));
      });
    });

    group('Asset Validation', () {
      test('should validate story assets correctly', () async {
        // Act
        final validation = await assetStoryService.validateStoryAssets('the_courageous_quest');

        // Assert
        expect(validation['storyId'], 'the_courageous_quest');
        expect(validation['hasStoryJson'], true);
        expect(validation['hasImagesFolder'], true);
        expect(validation['hasAudioFolder'], true);
        expect(validation['isValid'], true);
        expect(validation['missingAssets'], isEmpty);
      });

      test('should handle missing assets gracefully', () async {
        // Act
        final validation = await assetStoryService.validateStoryAssets('non_existent_story');

        // Assert
        expect(validation['storyId'], 'non_existent_story');
        expect(validation['hasStoryJson'], false);
        expect(validation['isValid'], false);
        expect(validation['missingAssets'], contains('story.json'));
      });
    });

    group('Asset Fallback Integration', () {
      test('should provide fallback for missing images', () async {
        // Act
        final fallbackPath = await assetFallbackService.getImageOrPlaceholder('non_existent_image.jpg');

        // Assert
        expect(fallbackPath, 'assets/images/placeholder.jpg');
      });

      test('should provide fallback for missing cover images', () async {
        // Act
        final fallbackPath = await assetFallbackService.getCoverImageOrPlaceholder('non_existent_cover.jpg');

        // Assert
        expect(fallbackPath, 'assets/images/story_covers/placeholder_cover.jpg');
      });

      test('should validate placeholder assets exist', () async {
        // Act
        final isValid = await assetFallbackService.validatePlaceholderAssets();

        // Assert
        expect(isValid, true);
      });
    });

    group('Repository Integration', () {
      test('should include asset stories in metadata list', () async {
        // Act
        final metadataList = await storyRepository.fetchStoryMetadataList();

        // Assert
        expect(metadataList, isNotEmpty);
        
        // Should include asset-based stories
        final assetStories = metadataList.where((story) => story.dataSource == 'asset').toList();
        expect(assetStories, isNotEmpty);
        
        // Check for specific stories
        final courageStory = metadataList.firstWhere(
          (story) => story.id == 'courage_city_adventure',
          orElse: () => throw StateError('Courage story not found'),
        );
        expect(courageStory.title['en-US'], 'The Lost Melody of Mumbai-New York');
        expect(courageStory.targetMoralValue, 'Courage');

        final unityStory = metadataList.firstWhere(
          (story) => story.id == 'lantern_festival_adventure',
          orElse: () => throw StateError('Unity story not found'),
        );
        expect(unityStory.title['en-US'], 'The Lantern of Unity');
        expect(unityStory.rewards, isNotNull);
      });

      test('should load story through repository with priority loading', () async {
        // Act
        final story = await storyRepository.fetchStoryById('courage_city_adventure');

        // Assert
        expect(story, isNotNull);
        expect(story.id, 'courage_city_adventure');
        expect(story.title, 'The Lost Melody of Mumbai-New York');
        expect(story.scenes, hasLength(2));
      });

      test('should handle story not found gracefully', () async {
        // Act & Assert
        expect(
          () => storyRepository.fetchStoryById('non_existent_story'),
          throwsA(isA<StoryLoadException>()),
        );
      });
    });

    group('Caching Behavior', () {
      test('should cache stories for improved performance', () async {
        // Act
        final story1 = await assetStoryService.loadStoryFromAssets('the_courageous_quest');
        final story2 = await assetStoryService.loadStoryFromAssets('the_courageous_quest');

        // Assert
        expect(story1, isNotNull);
        expect(story2, isNotNull);
        expect(identical(story1, story2), true); // Should be same cached instance

        final cacheStats = assetStoryService.getCacheStats();
        expect(cacheStats['cachedStories'], 1);
        expect(cacheStats['storyIds'], contains('the_courageous_quest'));
      });

      test('should clear cache correctly', () async {
        // Arrange
        await assetStoryService.loadStoryFromAssets('the_courageous_quest');
        var cacheStats = assetStoryService.getCacheStats();
        expect(cacheStats['cachedStories'], 1);

        // Act
        assetStoryService.clearCache();

        // Assert
        cacheStats = assetStoryService.getCacheStats();
        expect(cacheStats['cachedStories'], 0);
      });
    });

    group('Error Handling', () {
      test('should handle malformed story JSON gracefully', () async {
        // Setup mock for malformed JSON
        TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger
            .setMockMethodCallHandler(
          const MethodChannel('flutter/assets'),
          (MethodCall methodCall) async {
            if (methodCall.method == 'loadString') {
              final String key = methodCall.arguments as String;
              if (key == 'assets/stories/malformed_story/story.json') {
                return '{ invalid json }';
              }
            }
            throw PlatformException(code: 'NOT_FOUND', message: 'Asset not found');
          },
        );

        // Act
        final story = await assetStoryService.loadStoryFromAssets('malformed_story');

        // Assert
        expect(story, isNull);
      });

      test('should handle missing story files gracefully', () async {
        // Act
        final story = await assetStoryService.loadStoryFromAssets('completely_missing_story');

        // Assert
        expect(story, isNull);
      });
    });
  });
}
