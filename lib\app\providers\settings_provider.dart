import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:choice_once_upon_a_time/core/utils/app_logger.dart';

/// App settings state
class AppSettingsState {
  final bool ftueCompleted;
  final double masterVolume;
  final bool musicEnabled;
  final bool sfxEnabled;
  final String narrationLanguage;
  final bool isLoading;
  final bool isVoiceGuideEnabled;

  // TTS-specific settings
  final String ttsVoiceId;
  final Map<String, String>? ttsVoiceMap;
  final double ttsPitch;
  final double ttsRate;
  final double ttsVolume;

  const AppSettingsState({
    this.ftueCompleted = false,
    this.masterVolume = 0.8,
    this.musicEnabled = true,
    this.sfxEnabled = true,
    this.narrationLanguage = 'en-US',
    this.isLoading = false,
    this.isVoiceGuideEnabled = true,
    this.ttsVoiceId = '',
    this.ttsVoiceMap,
    this.ttsPitch = 1.0,
    this.ttsRate = 0.5,
    this.ttsVolume = 0.8,
  });

  AppSettingsState copyWith({
    bool? ftueCompleted,
    double? masterVolume,
    bool? musicEnabled,
    bool? sfxEnabled,
    String? narrationLanguage,
    bool? isLoading,
    bool? isVoiceGuideEnabled,
    String? ttsVoiceId,
    Map<String, String>? ttsVoiceMap,
    double? ttsPitch,
    double? ttsRate,
    double? ttsVolume,
  }) {
    return AppSettingsState(
      ftueCompleted: ftueCompleted ?? this.ftueCompleted,
      masterVolume: masterVolume ?? this.masterVolume,
      musicEnabled: musicEnabled ?? this.musicEnabled,
      sfxEnabled: sfxEnabled ?? this.sfxEnabled,
      narrationLanguage: narrationLanguage ?? this.narrationLanguage,
      isLoading: isLoading ?? this.isLoading,
      isVoiceGuideEnabled: isVoiceGuideEnabled ?? this.isVoiceGuideEnabled,
      ttsVoiceId: ttsVoiceId ?? this.ttsVoiceId,
      ttsVoiceMap: ttsVoiceMap ?? this.ttsVoiceMap,
      ttsPitch: ttsPitch ?? this.ttsPitch,
      ttsRate: ttsRate ?? this.ttsRate,
      ttsVolume: ttsVolume ?? this.ttsVolume,
    );
  }
}

/// Settings provider for managing app preferences
class SettingsNotifier extends StateNotifier<AppSettingsState> {
  static const String _ftueKey = 'ftue_completed';
  static const String _masterVolumeKey = 'master_volume';
  static const String _musicEnabledKey = 'music_enabled';
  static const String _sfxEnabledKey = 'sfx_enabled';
  static const String _narrationLanguageKey = 'narration_language';
  static const String _isVoiceGuideEnabledKey = 'is_voice_guide_enabled';

  // TTS settings keys
  static const String _ttsVoiceIdKey = 'tts_voice_id';
  static const String _ttsVoiceMapKey = 'tts_voice_map';
  static const String _ttsPitchKey = 'tts_pitch';
  static const String _ttsRateKey = 'tts_rate';
  static const String _ttsVolumeKey = 'tts_volume';

  SettingsNotifier() : super(const AppSettingsState()) {
    _loadSettings();
  }

  /// Load settings from SharedPreferences
  Future<void> _loadSettings() async {
    state = state.copyWith(isLoading: true);

    try {
      final prefs = await SharedPreferences.getInstance();

      // Parse TTS voice map from JSON string if it exists
      Map<String, String>? ttsVoiceMap;
      final voiceMapString = prefs.getString(_ttsVoiceMapKey);
      if (voiceMapString != null && voiceMapString.isNotEmpty) {
        try {
          // Simple JSON parsing for voice map
          final cleanString = voiceMapString.replaceAll('{', '').replaceAll('}', '');
          final pairs = cleanString.split(',');
          ttsVoiceMap = {};
          for (final pair in pairs) {
            final keyValue = pair.split(':');
            if (keyValue.length == 2) {
              final key = keyValue[0].replaceAll('"', '').trim();
              final value = keyValue[1].replaceAll('"', '').trim();
              ttsVoiceMap[key] = value;
            }
          }
        } catch (e) {
          AppLogger.error('Error parsing TTS voice map', e);
          ttsVoiceMap = null;
        }
      }

      state = state.copyWith(
        ftueCompleted: prefs.getBool(_ftueKey) ?? false,
        masterVolume: prefs.getDouble(_masterVolumeKey) ?? 0.8,
        musicEnabled: prefs.getBool(_musicEnabledKey) ?? true,
        sfxEnabled: prefs.getBool(_sfxEnabledKey) ?? true,
        narrationLanguage: prefs.getString(_narrationLanguageKey) ?? 'en-US',
        isVoiceGuideEnabled: prefs.getBool(_isVoiceGuideEnabledKey) ?? true,
        ttsVoiceId: prefs.getString(_ttsVoiceIdKey) ?? '',
        ttsVoiceMap: ttsVoiceMap,
        ttsPitch: prefs.getDouble(_ttsPitchKey) ?? 1.0,
        ttsRate: prefs.getDouble(_ttsRateKey) ?? 0.5,
        ttsVolume: prefs.getDouble(_ttsVolumeKey) ?? 0.8,
        isLoading: false,
      );
    } catch (e) {
      AppLogger.error('Error loading settings', e);
      state = state.copyWith(isLoading: false);
    }
  }

  /// Mark FTUE as completed
  Future<void> completeFTUE() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool(_ftueKey, true);
      state = state.copyWith(ftueCompleted: true);
    } catch (e) {
      AppLogger.error('Error saving FTUE completion', e);
    }
  }

  /// Set master volume
  Future<void> setMasterVolume(double volume) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final clampedVolume = volume.clamp(0.0, 1.0);
      await prefs.setDouble(_masterVolumeKey, clampedVolume);
      state = state.copyWith(masterVolume: clampedVolume);
    } catch (e) {
      AppLogger.error('Error saving master volume', e);
    }
  }

  /// Toggle music enabled/disabled
  Future<void> toggleMusic() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final newValue = !state.musicEnabled;
      await prefs.setBool(_musicEnabledKey, newValue);
      state = state.copyWith(musicEnabled: newValue);
    } catch (e) {
      AppLogger.error('Error toggling music', e);
    }
  }

  /// Toggle sound effects enabled/disabled
  Future<void> toggleSFX() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final newValue = !state.sfxEnabled;
      await prefs.setBool(_sfxEnabledKey, newValue);
      state = state.copyWith(sfxEnabled: newValue);
    } catch (e) {
      AppLogger.error('Error toggling SFX', e);
    }
  }

  /// Toggle voice guide enabled/disabled
  Future<void> toggleVoiceGuide() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final newValue = !state.isVoiceGuideEnabled;
      await prefs.setBool(_isVoiceGuideEnabledKey, newValue);
      state = state.copyWith(isVoiceGuideEnabled: newValue);
    } catch (e) {
      AppLogger.error('Error toggling voice guide', e);
    }
  }

  /// Set narration language
  Future<void> setNarrationLanguage(String languageCode) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_narrationLanguageKey, languageCode);
      state = state.copyWith(narrationLanguage: languageCode);
    } catch (e) {
      AppLogger.error('Error saving narration language', e);
    }
  }

  /// Set TTS voice ID
  Future<void> setTTSVoiceId(String voiceId) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_ttsVoiceIdKey, voiceId);
      state = state.copyWith(ttsVoiceId: voiceId);
    } catch (e) {
      AppLogger.error('Error saving TTS voice ID', e);
    }
  }

  /// Set TTS voice map (for flutter_tts compatibility)
  Future<void> setTTSVoiceMap(Map<String, String> voiceMap) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      // Store as JSON string
      final jsonString = voiceMap.entries.map((e) => '"${e.key}":"${e.value}"').join(',');
      await prefs.setString(_ttsVoiceMapKey, '{$jsonString}');
      state = state.copyWith(ttsVoiceMap: voiceMap);
    } catch (e) {
      AppLogger.error('Error saving TTS voice map', e);
    }
  }

  /// Set TTS pitch
  Future<void> setTTSPitch(double pitch) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final clampedPitch = pitch.clamp(0.5, 2.0);
      await prefs.setDouble(_ttsPitchKey, clampedPitch);
      state = state.copyWith(ttsPitch: clampedPitch);
    } catch (e) {
      AppLogger.error('Error saving TTS pitch', e);
    }
  }

  /// Set TTS rate
  Future<void> setTTSRate(double rate) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final clampedRate = rate.clamp(0.1, 2.0);
      await prefs.setDouble(_ttsRateKey, clampedRate);
      state = state.copyWith(ttsRate: clampedRate);
    } catch (e) {
      AppLogger.error('Error saving TTS rate', e);
    }
  }

  /// Set TTS volume
  Future<void> setTTSVolume(double volume) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final clampedVolume = volume.clamp(0.0, 1.0);
      await prefs.setDouble(_ttsVolumeKey, clampedVolume);
      state = state.copyWith(ttsVolume: clampedVolume);
    } catch (e) {
      AppLogger.error('Error saving TTS volume', e);
    }
  }

  /// Reset all settings to defaults
  Future<void> resetToDefaults() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_masterVolumeKey);
      await prefs.remove(_musicEnabledKey);
      await prefs.remove(_sfxEnabledKey);
      await prefs.remove(_narrationLanguageKey);
      await prefs.remove(_isVoiceGuideEnabledKey);
      await prefs.remove(_ttsVoiceIdKey);
      await prefs.remove(_ttsVoiceMapKey);
      await prefs.remove(_ttsPitchKey);
      await prefs.remove(_ttsRateKey);
      await prefs.remove(_ttsVolumeKey);
      // Note: We don't reset FTUE completion

      state = state.copyWith(
        masterVolume: 0.8,
        musicEnabled: true,
        sfxEnabled: true,
        narrationLanguage: 'en-US',
        isVoiceGuideEnabled: true,
        ttsVoiceId: '',
        ttsVoiceMap: null,
        ttsPitch: 1.0,
        ttsRate: 0.5,
        ttsVolume: 0.8,
      );
    } catch (e) {
      debugPrint('Error resetting settings: $e');
    }
  }
}

/// Provider for app settings
final settingsProvider = StateNotifierProvider<SettingsNotifier, AppSettingsState>((ref) {
  return SettingsNotifier();
});

/// Provider for checking if FTUE is completed
final ftueCompletedProvider = Provider<bool>((ref) {
  return ref.watch(settingsProvider).ftueCompleted;
});

/// Provider for master volume
final masterVolumeProvider = Provider<double>((ref) {
  return ref.watch(settingsProvider).masterVolume;
});

/// Provider for music enabled state
final musicEnabledProvider = Provider<bool>((ref) {
  return ref.watch(settingsProvider).musicEnabled;
});

/// Provider for SFX enabled state
final sfxEnabledProvider = Provider<bool>((ref) {
  return ref.watch(settingsProvider).sfxEnabled;
});

/// Provider for narration language
final narrationLanguageProvider = Provider<String>((ref) {
  return ref.watch(settingsProvider).narrationLanguage;
});
