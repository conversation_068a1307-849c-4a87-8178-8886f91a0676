import 'package:flutter/material.dart';
import 'package:choice_once_upon_a_time/core/services/story_rewards_service.dart';

/// Animated card widget for displaying individual rewards
class RewardCardWidget extends StatefulWidget {
  final RewardEarned reward;
  final Duration animationDelay;
  final AnimationController animationController;

  const RewardCardWidget({
    super.key,
    required this.reward,
    required this.animationDelay,
    required this.animationController,
  });

  @override
  State<RewardCardWidget> createState() => _RewardCardWidgetState();
}

class _RewardCardWidgetState extends State<RewardCardWidget>
    with SingleTickerProviderStateMixin {
  late final AnimationController _cardController;
  late final Animation<double> _scaleAnimation;
  late final Animation<double> _fadeAnimation;
  late final Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _startAnimation();
  }

  void _initializeAnimations() {
    _cardController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );

    _scaleAnimation = Tween<double>(
      begin: 0.8,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _cardController,
      curve: Curves.easeOutBack,
    ));

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _cardController,
      curve: Curves.easeInOut,
    ));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0.3, 0.0),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _cardController,
      curve: Curves.easeOutCubic,
    ));
  }

  Future<void> _startAnimation() async {
    await Future.delayed(widget.animationDelay);
    if (mounted) {
      _cardController.forward();
    }
  }

  Color _getRewardColor() {
    switch (widget.reward.type) {
      case 'completion':
        return Colors.green;
      case 'moral':
        return Colors.blue;
      case 'choice':
        return Colors.purple;
      case 'exploration':
        return Colors.orange;
      default:
        return Colors.grey;
    }
  }

  IconData _getRewardIcon() {
    switch (widget.reward.type) {
      case 'completion':
        return Icons.check_circle;
      case 'moral':
        return Icons.lightbulb;
      case 'choice':
        return Icons.psychology;
      case 'exploration':
        return Icons.explore;
      default:
        return Icons.star;
    }
  }

  @override
  void dispose() {
    _cardController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final rewardColor = _getRewardColor();

    return SlideTransition(
      position: _slideAnimation,
      child: FadeTransition(
        opacity: _fadeAnimation,
        child: ScaleTransition(
          scale: _scaleAnimation,
          child: Card(
            elevation: 4,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            child: Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(12),
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    rewardColor.withOpacity(0.1),
                    theme.colorScheme.surface,
                  ],
                ),
                border: Border.all(
                  color: rewardColor.withOpacity(0.3),
                  width: 1,
                ),
              ),
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Row(
                  children: [
                    // Reward icon
                    Container(
                      width: 48,
                      height: 48,
                      decoration: BoxDecoration(
                        color: rewardColor,
                        shape: BoxShape.circle,
                        boxShadow: [
                          BoxShadow(
                            color: rewardColor.withOpacity(0.3),
                            blurRadius: 8,
                            offset: const Offset(0, 2),
                          ),
                        ],
                      ),
                      child: Icon(
                        _getRewardIcon(),
                        color: Colors.white,
                        size: 24,
                      ),
                    ),

                    const SizedBox(width: 16),

                    // Reward details
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            widget.reward.title,
                            style: theme.textTheme.titleMedium?.copyWith(
                              fontWeight: FontWeight.bold,
                              color: theme.colorScheme.onSurface,
                            ),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            widget.reward.description,
                            style: theme.textTheme.bodyMedium?.copyWith(
                              color: theme.colorScheme.onSurfaceVariant,
                            ),
                          ),
                          const SizedBox(height: 8),
                          Text(
                            _formatDate(widget.reward.earnedAt),
                            style: theme.textTheme.bodySmall?.copyWith(
                              color: theme.colorScheme.onSurfaceVariant,
                              fontStyle: FontStyle.italic,
                            ),
                          ),
                        ],
                      ),
                    ),

                    // Celebration effect
                    AnimatedBuilder(
                      animation: _cardController,
                      builder: (context, child) {
                        return Transform.rotate(
                          angle: _cardController.value * 0.1,
                          child: Icon(
                            Icons.celebration,
                            color: rewardColor.withOpacity(0.6),
                            size: 20,
                          ),
                        );
                      },
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays == 0) {
      return 'Today';
    } else if (difference.inDays == 1) {
      return 'Yesterday';
    } else if (difference.inDays < 7) {
      return '${difference.inDays} days ago';
    } else {
      return '${date.day}/${date.month}/${date.year}';
    }
  }
}
