import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:choice_once_upon_a_time/core/storage/offline_storage_service_mobile.dart';
import 'package:choice_once_upon_a_time/models/story_metadata_model.dart';

/// State class for managing story downloads
class DownloadManagerState {
  final Map<String, bool> activeDownloads;
  final Map<String, double> downloadProgress;
  final Map<String, String> downloadStatus;

  const DownloadManagerState({
    this.activeDownloads = const {},
    this.downloadProgress = const {},
    this.downloadStatus = const {},
  });

  /// Check if a story is currently being downloaded
  bool isDownloading(String storyId) => activeDownloads[storyId] == true;

  /// Get the current download progress for a story
  double getProgress(String storyId) => downloadProgress[storyId] ?? 0.0;

  /// Get the current download status for a story
  String getStatus(String storyId) => downloadStatus[storyId] ?? '';

  DownloadManagerState copyWith({
    Map<String, bool>? activeDownloads,
    Map<String, double>? downloadProgress,
    Map<String, String>? downloadStatus,
  }) {
    return DownloadManagerState(
      activeDownloads: activeDownloads ?? this.activeDownloads,
      downloadProgress: downloadProgress ?? this.downloadProgress,
      downloadStatus: downloadStatus ?? this.downloadStatus,
    );
  }
}

/// Notifier class for managing story downloads
class DownloadManagerNotifier extends StateNotifier<DownloadManagerState> {
  final OfflineStorageService _offlineStorage;
  bool _cancelRequested = false;

  DownloadManagerNotifier(this._offlineStorage) : super(const DownloadManagerState());

  /// Start downloading a story
  Future<bool> startDownload(StoryMetadataModel story) async {
    if (state.activeDownloads[story.id] == true) {
      return false; // Already downloading
    }

    _cancelRequested = false;

    state = state.copyWith(
      activeDownloads: {...state.activeDownloads, story.id: true},
      downloadProgress: {...state.downloadProgress, story.id: 0.0},
      downloadStatus: {...state.downloadStatus, story.id: 'Starting download...'},
    );

    try {
      final success = await _offlineStorage.downloadStory(
        story,
        onProgress: (progress) {
          if (_cancelRequested) {
            throw Exception('Download cancelled by user');
          }
          state = state.copyWith(
            downloadProgress: {...state.downloadProgress, story.id: progress},
          );
        },
        onStatusUpdate: (status) {
          state = state.copyWith(
            downloadStatus: {...state.downloadStatus, story.id: status},
          );
        },
      );

      // Clear the download state
      state = state.copyWith(
        activeDownloads: {...state.activeDownloads}..remove(story.id),
        downloadProgress: {...state.downloadProgress}..remove(story.id),
        downloadStatus: {...state.downloadStatus}..remove(story.id),
      );

      return success;
    } catch (e) {
      // Clear the download state
      state = state.copyWith(
        activeDownloads: {...state.activeDownloads}..remove(story.id),
        downloadProgress: {...state.downloadProgress}..remove(story.id),
        downloadStatus: {...state.downloadStatus}..remove(story.id),
      );
      return false;
    }
  }

  /// Cancel an active download
  void cancelDownload(String storyId) {
    if (state.activeDownloads[storyId] == true) {
      _cancelRequested = true;
      _offlineStorage.cancelDownload();
      state = state.copyWith(
        downloadStatus: {...state.downloadStatus, storyId: 'Cancelling download...'},
      );
    }
  }
}

/// Provider for managing story downloads
final downloadManagerProvider = StateNotifierProvider<DownloadManagerNotifier, DownloadManagerState>((ref) {
  return DownloadManagerNotifier(OfflineStorageService());
});
