// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'child_progress_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ChildProgressModel _$ChildProgressModelFromJson(Map<String, dynamic> json) =>
    ChildProgressModel(
      profileId: json['profileId'] as String,
      storyId: json['storyId'] as String,
      lastPlayedAt: DateTime.parse(json['lastPlayedAt'] as String),
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
      isCompleted: json['isCompleted'] as bool? ?? false,
      completedOutcome: json['completedOutcome'] as String?,
      totalPlayTime: (json['totalPlayTime'] as num?)?.toInt() ?? 0,
      completionCount: (json['completionCount'] as num?)?.toInt() ?? 0,
      visitedScenes: (json['visitedScenes'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
      choicesMade: (json['choicesMade'] as Map<String, dynamic>?)?.map(
            (k, e) => MapEntry(k, e as String),
          ) ??
          const {},
      scenePlayTime: (json['scenePlayTime'] as Map<String, dynamic>?)?.map(
            (k, e) => MapEntry(k, (e as num).toInt()),
          ) ??
          const {},
      moralValues: (json['moralValues'] as List<dynamic>?)
              ?.map(
                  (e) => MoralValueProgress.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      vocabularyLearned: (json['vocabularyLearned'] as List<dynamic>?)
              ?.map(
                  (e) => VocabularyProgress.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      readingSkills: ReadingSkillsProgress.fromJson(
          json['readingSkills'] as Map<String, dynamic>),
      engagement: EngagementMetrics.fromJson(
          json['engagement'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$ChildProgressModelToJson(ChildProgressModel instance) =>
    <String, dynamic>{
      'profileId': instance.profileId,
      'storyId': instance.storyId,
      'lastPlayedAt': instance.lastPlayedAt.toIso8601String(),
      'createdAt': instance.createdAt.toIso8601String(),
      'updatedAt': instance.updatedAt.toIso8601String(),
      'isCompleted': instance.isCompleted,
      'completedOutcome': instance.completedOutcome,
      'totalPlayTime': instance.totalPlayTime,
      'completionCount': instance.completionCount,
      'visitedScenes': instance.visitedScenes,
      'choicesMade': instance.choicesMade,
      'scenePlayTime': instance.scenePlayTime,
      'moralValues': instance.moralValues,
      'vocabularyLearned': instance.vocabularyLearned,
      'readingSkills': instance.readingSkills,
      'engagement': instance.engagement,
    };

MoralValueProgress _$MoralValueProgressFromJson(Map<String, dynamic> json) =>
    MoralValueProgress(
      moralValue: json['moralValue'] as String,
      storyContext: json['storyContext'] as String,
      score: (json['score'] as num).toDouble(),
      learnedAt: DateTime.parse(json['learnedAt'] as String),
      relatedChoices: (json['relatedChoices'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
      reflection: json['reflection'] as String?,
    );

Map<String, dynamic> _$MoralValueProgressToJson(MoralValueProgress instance) =>
    <String, dynamic>{
      'moralValue': instance.moralValue,
      'storyContext': instance.storyContext,
      'score': instance.score,
      'learnedAt': instance.learnedAt.toIso8601String(),
      'relatedChoices': instance.relatedChoices,
      'reflection': instance.reflection,
    };

VocabularyProgress _$VocabularyProgressFromJson(Map<String, dynamic> json) =>
    VocabularyProgress(
      word: json['word'] as String,
      definition: json['definition'] as String,
      context: json['context'] as String,
      imageUrl: json['imageUrl'] as String?,
      learnedAt: DateTime.parse(json['learnedAt'] as String),
      exposureCount: (json['exposureCount'] as num?)?.toInt() ?? 1,
      isUnderstood: json['isUnderstood'] as bool? ?? false,
      relatedWords: (json['relatedWords'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
    );

Map<String, dynamic> _$VocabularyProgressToJson(VocabularyProgress instance) =>
    <String, dynamic>{
      'word': instance.word,
      'definition': instance.definition,
      'context': instance.context,
      'imageUrl': instance.imageUrl,
      'learnedAt': instance.learnedAt.toIso8601String(),
      'exposureCount': instance.exposureCount,
      'isUnderstood': instance.isUnderstood,
      'relatedWords': instance.relatedWords,
    };

ReadingSkillsProgress _$ReadingSkillsProgressFromJson(
        Map<String, dynamic> json) =>
    ReadingSkillsProgress(
      wordsPerMinute: (json['wordsPerMinute'] as num?)?.toInt() ?? 0,
      comprehensionScore:
          (json['comprehensionScore'] as num?)?.toDouble() ?? 0.0,
      totalWordsRead: (json['totalWordsRead'] as num?)?.toInt() ?? 0,
      totalSentencesRead: (json['totalSentencesRead'] as num?)?.toInt() ?? 0,
      averageReadingTime:
          (json['averageReadingTime'] as num?)?.toDouble() ?? 0.0,
      strugglingWords: (json['strugglingWords'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
      masteredWords: (json['masteredWords'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
      lastAssessment: DateTime.parse(json['lastAssessment'] as String),
    );

Map<String, dynamic> _$ReadingSkillsProgressToJson(
        ReadingSkillsProgress instance) =>
    <String, dynamic>{
      'wordsPerMinute': instance.wordsPerMinute,
      'comprehensionScore': instance.comprehensionScore,
      'totalWordsRead': instance.totalWordsRead,
      'totalSentencesRead': instance.totalSentencesRead,
      'averageReadingTime': instance.averageReadingTime,
      'strugglingWords': instance.strugglingWords,
      'masteredWords': instance.masteredWords,
      'lastAssessment': instance.lastAssessment.toIso8601String(),
    };

EngagementMetrics _$EngagementMetricsFromJson(Map<String, dynamic> json) =>
    EngagementMetrics(
      totalInteractions: (json['totalInteractions'] as num?)?.toInt() ?? 0,
      choicesChanged: (json['choicesChanged'] as num?)?.toInt() ?? 0,
      pauseCount: (json['pauseCount'] as num?)?.toInt() ?? 0,
      replayCount: (json['replayCount'] as num?)?.toInt() ?? 0,
      attentionSpan: (json['attentionSpan'] as num?)?.toDouble() ?? 0.0,
      favoriteScenes: (json['favoriteScenes'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
      emotionalResponses:
          (json['emotionalResponses'] as Map<String, dynamic>?)?.map(
                (k, e) => MapEntry(k, (e as num).toInt()),
              ) ??
              const {},
      overallEngagement: (json['overallEngagement'] as num?)?.toDouble() ?? 0.0,
    );

Map<String, dynamic> _$EngagementMetricsToJson(EngagementMetrics instance) =>
    <String, dynamic>{
      'totalInteractions': instance.totalInteractions,
      'choicesChanged': instance.choicesChanged,
      'pauseCount': instance.pauseCount,
      'replayCount': instance.replayCount,
      'attentionSpan': instance.attentionSpan,
      'favoriteScenes': instance.favoriteScenes,
      'emotionalResponses': instance.emotionalResponses,
      'overallEngagement': instance.overallEngagement,
    };
