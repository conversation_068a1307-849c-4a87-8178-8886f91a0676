# AI Agent Instructions: Complete Sprint 1 - Data Foundation & Story Library Shell
## Project: "Choice: Once Upon A Time" Flutter App

**Objective:**
Autonomously generate the Flutter code and configurations for Sprint 1 tasks. This sprint focuses on implementing all Dart data models, setting up Firebase configurations (for manual user setup), fleshing out core services, and developing the UI shells for the App Launch, FTUE, Story Library, Parental Gate, and Parent Zone Dashboard. The code must build upon the Sprint 0 output, be web-compatible for testing in Chrome, and adhere to the project's TDD, Art Style, and Screen Specifications.

**I. Prerequisites & Assumed Inputs:**
* The complete Flutter project structure and files generated from **Sprint 0** are the starting point.
* **Detailed Technical Design Document (TDD - Task 2.7):** This is your primary reference for all implementation details.
* **Art Style Definition & Asset Creation Plan (Task 2.3):** For styling any new UI elements.
* **Screen Specifications Document ("Task 2.2 update"):** For UI details of screens developed in this sprint.
* **Story JSON files (`story_01.json`, `story_02.json`, `story_03.json`):** Primarily for defining the structure of Dart Data Models.

**II. General Instructions for Code Generation:**
1.  **Continue from Sprint 0:** Modify and add to the existing codebase from Sprint 0.
2.  **Flutter Version:** Continue using the latest stable Flutter version.
3.  **Modularity & Security:** Adhere strictly to the "Guidelines for AI Agent: Flutter Code Generation - Modularity & Security" provided previously (regarding separation of concerns, reusable widgets, Riverpod, environment variables for secrets, no hardcoding secrets, etc.).
4.  **Web Compatibility:** Ensure generated Flutter code is web-compatible. Include instructions in the `README.md` to run the app on the web (`flutter run -d chrome`).
5.  **Error Handling (Basic):** Implement basic error handling (e.g., for failed data fetches, display a simple error message or use placeholder data).
6.  **Comments:** Add comments to complex sections of code or where TDD decisions are being implemented.

**III. Specific Task Instructions for Sprint 1:**

**1. Implement Dart Data Models (`lib/models/`):**
    * Create/update all Dart classes for:
        * `StoryMetadataModel`
        * `StoryModel`
        * `SceneModel`
        * `TextSegmentModel`
        * `ChoiceModel`
        * `BackgroundMusicConfig`
        * `SoundEffectConfig`
        * `UserModel` (for Firebase Auth and Firestore `users` collection - see TDD Section 5.1)
        * `AppSettingsModel` (if you create a model for settings managed by `SettingsProvider`).
        * `DownloadedStoryEntry` (for Isar, defined in TDD Section 3.7).
    * All models must include `fromJson` factory constructors to parse data from Firestore/JSON, and `toJson` methods if they will be written back (though mostly read for stories).
    * **Testing:** Generate unit test files in `test/models/` for each model, focusing on testing the `fromJson` and `toJson` logic with sample data.

**2. Firebase Setup Configuration (AI to provide content for manual setup by user):**
    * **Firebase Project:** (AI Instruction: Remind the user they need to have created a Firebase project and configured it for Flutter: `flutterfire configure`).
    * **Authentication (TDD Section 4.3):**
        * Provide instructions or a note in `README.md` for the user to enable "Anonymous" and "Email/Password" sign-in methods in the Firebase console.
    * **Cloud Firestore (TDD Sections 4.3, 5.1):**
        * **Security Rules (`firestore.rules`):** Generate the complete content for `firestore.rules` as specified in TDD Section 4.3.
        * **Initial Data (for manual input by user or via a script if AI can generate one):** Provide example JSON structures for:
            * At least one placeholder document in the `stories` collection for each of the 3 stories (`pip_pantry_puzzle`, `lila_moonpetal_wish`, `finley_flying_machine`). Include minimal fields like `storyId`, `title` (e.g., `{"en-US": "Pip..."}`), `coverImageUrl` (use placeholder paths from Task 2.3 asset list), `isFree: true`, `version: "1.0.0"`, `initialSceneId: "scene_..._01_intro"`.
            * A placeholder document in `app_config/global_settings` (e.g., `minRequiredAppVersion: "1.0.0"`, `isMaintenanceMode: false`).
    * **Cloud Storage (TDD Sections 4.3, 6.1):**
        * **Security Rules (`storage.rules`):** Generate the complete content for `storage.rules` as specified in TDD Section 4.3.
        * (AI Instruction: Remind user to create the bucket structure manually if needed, as defined in TDD 6.1).
    * **Firebase Cloud Functions (TDD Section 4.2):**
        * For this sprint, only set up the `functions` directory if it's standard for Firebase projects. The actual function code (`validateReceipt`, `onNewUserCreate`) will be implemented later. If AI can generate placeholder function files, do so.

**3. Implement/Flesh Out Core Services & Riverpod Providers (`lib/core/`, `lib/features/.../data|domain|providers`):**
    * **Riverpod Setup (TDD Section 3.3):** Ensure `ProviderScope` is in `main.dart`. Create basic Riverpod providers for the services below.
    * **`StoryRepository` (in `features/story_library/data/` - TDD Section 3.5):**
        * Implement method `Future<List<StoryMetadataModel>> fetchStoryMetadataList()`.
        * For this sprint, this method should fetch the minimal story metadata from the `stories` collection in Firestore.
        * Handle basic loading/error states to be consumed by the `StoryLibraryProvider`.
    * **`OfflineStorageService` (`core/storage/` - TDD Section 3.7):**
        * Ensure Isar is initialized.
        * Implement methods: `Future<void> initDatabase()`, `Future<bool> isStoryDownloaded(String storyId, String version)`, `Future<StoryModel?> getLocalStory(String storyId)`. For this sprint, these can return default/empty values or check for basic schema presence. Full download logic is later.
    * **`TTSService` (`core/audio/` - TDD Section 3.6):**
        * Flesh out placeholder methods `initialize()`, `setLanguage(String langCode)`, `speakSegment(TextSegmentModel segment)`, `pause()`, `resume()`, `stop()`. Basic integration with `flutter_tts`. Actual emotional modulation logic comes in a later sprint.
    * **`SoundEffectPlayerService` (`core/audio/`):**
        * Flesh out placeholder methods `playSound(String assetPath)`, `playUISound(UISoundType type)`.
    * **Riverpod Providers:** Create `StateNotifierProvider` or `FutureProvider` for `StoryLibraryProvider` to expose the list of stories. Create simple `Provider`s for each service.

**4. Develop/Finalize UI Shells & Basic Functionality (from "Task 2.2 update" & TDD Section 3.2):**
    * **Screen 1: `AppLaunchScreen`:** Ensure it correctly navigates to FTUE or Home after a delay. Use `CalmAnimatedBackgroundWidget` shell if available from Sprint 0.
    * **Screen 4.1: `FTUEScreen` shell:** Implement basic logic to show only on first launch (use a `SettingsProvider` that reads/writes a flag to `shared_preferences` or Isar for `ftueCompleted`). Implement skippable functionality.
    * **Screen 2: `StoryLibraryScreen`:**
        * Display `StoryCoverCardWidget`s in a scrollable grid, populated from `StoryLibraryProvider`.
        * `StoryCoverCardWidget` should display title and cover image placeholder (white container + asset path text, or a generic placeholder image). Implement badges for "NEW" / "Locked" based on `StoryMetadataModel` fields.
        * Navigation: Tapping a story cover should navigate to `StoryIntroScreen` (Screen 3) route (passing `storyId`).
    * **Screen 9: `ParentalGateScreen`:** Implement the "press and hold for 3 seconds" functionality. On success, navigate to the `ParentZoneDashboardScreen` route.
    * **Screen 10: `ParentZoneDashboardScreen` shell:** Display list of navigable `SettingsListItemWidget`s. Tapping items should navigate to placeholder screens (created in Sprint 0 or new simple placeholders for Sound Settings, Subscription, etc.).

**5. Testing:**
    * **Unit Tests:** Ensure all new/updated Dart data models have parsing tests. Add basic unit tests for any new logic in services or providers (e.g., `StoryRepository` mock fetching).
    * **Widget Tests:** Write basic widget tests for `StoryCoverCardWidget` (verify it displays data from `StoryMetadataModel`) and any new significant UI components developed in this sprint.
    * **`README.md` Update:** Add a section to `README.md` with:
        * Clear, step-by-step instructions for the **human user to manually set up the Firebase project** (creating project, enabling Auth types, creating Firestore database in native mode, creating Storage bucket, applying generated security rules).
        * Instructions on how to populate the initial Firestore data (for stories and app_config) using the JSON structures you provide.
        * Instructions on how to run the app on Flutter Web (`flutter run -d chrome`).

**IV. Deliverables:**
1.  **Updated Flutter Project Source Code:** All Dart files for models, services, providers, screens, and widgets developed or updated in this sprint.
2.  **Firebase Configuration Files Content:**
    * Content for `firestore.rules`.
    * Content for `storage.rules`.
    * Example JSON for initial Firestore data (`stories` collection placeholders, `app_config/global_settings`).
3.  **Updated `README.md`:** With Firebase setup instructions for the user and web run instructions.
4.  **Unit and Widget Test Files:** For models and key new widgets.

**V. Placeholder Strategy Reminder:**
* For images not yet available: Use `Image.asset('assets/images/EXPECTED_ASSET_NAME.png')` pointing to a non-existent file for now, or a generic placeholder image if preferred. Story scene backgrounds should be white with the `sceneId` as text if the image is missing.
* Narrator audio: UI shows text, buttons for play/pause are UI only, actual TTS playback is minimal in this sprint.
* Backend data: Assume AI can use mocked data locally for UI building if Firebase fetching is complex to simulate fully, but it should generate the Firebase setup instructions.

**Final Instruction to AI Agent:** Generate the code and configuration content according to these specifications for Sprint 1. Ensure all file paths and names are consistent with the TDD and previous Sprint 0 structure. The output should allow a developer to set up Firebase as instructed and then run the Flutter app to see the Sprint 1 features.