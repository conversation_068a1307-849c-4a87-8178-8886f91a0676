import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:choice_once_upon_a_time/models/narrator_profile_model.dart';
import 'package:choice_once_upon_a_time/features/parent_zone/presentation/providers/narrator_creation_provider.dart';
import 'package:choice_once_upon_a_time/features/parent_zone/presentation/widgets/voice_preview_widget.dart';
import 'package:choice_once_upon_a_time/shared_widgets/loading_indicator_widget.dart';
import 'package:choice_once_upon_a_time/core/utils/app_logger.dart';

/// Screen for selecting and managing narrator voices
class NarratorSelectionScreen extends ConsumerStatefulWidget {
  const NarratorSelectionScreen({super.key});

  @override
  ConsumerState<NarratorSelectionScreen> createState() => _NarratorSelectionScreenState();
}

class _NarratorSelectionScreenState extends ConsumerState<NarratorSelectionScreen> {
  @override
  void initState() {
    super.initState();
    AppLogger.debug('[COMPONENT_LOAD] Full path: lib/features/parent_zone/presentation/screens/narrator_selection_screen.dart - NarratorSelectionScreen');
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final narratorState = ref.watch(narratorCreationProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Narrator Voices'),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => context.pop(),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: () => _createNewNarrator(),
            tooltip: 'Create New Narrator',
          ),
        ],
      ),
      body: narratorState.isLoading
          ? const Center(child: LoadingIndicatorWidget())
          : _buildContent(theme, narratorState),
    );
  }

  Widget _buildContent(ThemeData theme, NarratorCreationState state) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header section
          _buildHeaderSection(theme),
          
          const SizedBox(height: 24),
          
          // Current selection
          if (state.selectedNarrator != null)
            _buildCurrentSelection(theme, state.selectedNarrator!),
          
          const SizedBox(height: 24),
          
          // Available narrators
          _buildAvailableNarrators(theme, state),
          
          const SizedBox(height: 24),
          
          // Create new narrator button
          _buildCreateNewButton(theme),
        ],
      ),
    );
  }

  Widget _buildHeaderSection(ThemeData theme) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: theme.colorScheme.primary.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        children: [
          Icon(
            Icons.record_voice_over,
            size: 48,
            color: theme.colorScheme.primary,
          ),
          const SizedBox(height: 12),
          Text(
            'Choose Your Narrator',
            style: theme.textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
              color: theme.colorScheme.primary,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 8),
          Text(
            'Select or create a custom narrator voice for your child\'s stories',
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildCurrentSelection(ThemeData theme, NarratorProfileModel narrator) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.check_circle,
                  color: theme.colorScheme.primary,
                ),
                const SizedBox(width: 8),
                Text(
                  'Currently Selected',
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: theme.colorScheme.primary,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            _buildNarratorCard(theme, narrator, isSelected: true),
          ],
        ),
      ),
    );
  }

  Widget _buildAvailableNarrators(ThemeData theme, NarratorCreationState state) {
    final availableNarrators = state.narrators.where((n) => n.id != state.selectedNarrator?.id).toList();
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Available Narrators',
          style: theme.textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        
        if (availableNarrators.isEmpty)
          _buildEmptyState(theme)
        else
          ...availableNarrators.map((narrator) => Padding(
            padding: const EdgeInsets.only(bottom: 12),
            child: _buildNarratorCard(theme, narrator),
          )),
      ],
    );
  }

  Widget _buildNarratorCard(ThemeData theme, NarratorProfileModel narrator, {bool isSelected = false}) {
    return Card(
      elevation: isSelected ? 4 : 1,
      color: isSelected ? theme.colorScheme.primary.withValues(alpha: 0.1) : null,
      child: InkWell(
        onTap: isSelected ? null : () => _selectNarrator(narrator),
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          narrator.displayName,
                          style: theme.textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                            color: isSelected ? theme.colorScheme.primary : null,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          narrator.description,
                          style: theme.textTheme.bodyMedium?.copyWith(
                            color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ],
                    ),
                  ),
                  if (!narrator.isDefault) ...[
                    IconButton(
                      icon: const Icon(Icons.edit),
                      onPressed: () => _editNarrator(narrator),
                      tooltip: 'Edit Narrator',
                    ),
                    IconButton(
                      icon: const Icon(Icons.delete),
                      onPressed: () => _deleteNarrator(narrator),
                      tooltip: 'Delete Narrator',
                    ),
                  ],
                ],
              ),
              const SizedBox(height: 12),
              
              // Narrator details
              Wrap(
                spacing: 8,
                runSpacing: 4,
                children: [
                  _buildDetailChip(theme, narrator.category.displayName, Icons.category),
                  _buildDetailChip(theme, narrator.voiceDescription, Icons.person),
                  if (narrator.personalities.isNotEmpty)
                    _buildDetailChip(theme, narrator.personalityDescription, Icons.psychology),
                ],
              ),
              
              const SizedBox(height: 12),
              
              // Preview button
              Align(
                alignment: Alignment.centerRight,
                child: TextButton.icon(
                  onPressed: () => _previewNarrator(narrator),
                  icon: const Icon(Icons.play_arrow),
                  label: const Text('Preview Voice'),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildDetailChip(ThemeData theme, String label, IconData icon) {
    return Chip(
      avatar: Icon(icon, size: 16),
      label: Text(
        label,
        style: theme.textTheme.bodySmall,
      ),
      materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
    );
  }

  Widget _buildEmptyState(ThemeData theme) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(32),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: theme.colorScheme.outline.withValues(alpha: 0.3)),
      ),
      child: Column(
        children: [
          Icon(
            Icons.voice_over_off,
            size: 64,
            color: theme.colorScheme.onSurface.withValues(alpha: 0.3),
          ),
          const SizedBox(height: 16),
          Text(
            'No Custom Narrators',
            style: theme.textTheme.titleMedium?.copyWith(
              color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Create your first custom narrator to get started',
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.onSurface.withValues(alpha: 0.5),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildCreateNewButton(ThemeData theme) {
    return SizedBox(
      width: double.infinity,
      child: ElevatedButton.icon(
        onPressed: _createNewNarrator,
        icon: const Icon(Icons.add),
        label: const Text('Create New Narrator'),
        style: ElevatedButton.styleFrom(
          backgroundColor: theme.colorScheme.primary,
          foregroundColor: theme.colorScheme.onPrimary,
          padding: const EdgeInsets.symmetric(vertical: 16),
        ),
      ),
    );
  }

  void _selectNarrator(NarratorProfileModel narrator) {
    ref.read(narratorCreationProvider.notifier).setSelectedNarrator(narrator.id);
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Selected narrator: ${narrator.displayName}'),
        backgroundColor: Colors.green,
      ),
    );
  }

  void _createNewNarrator() {
    context.go('/parent_zone/narrator_creation');
  }

  void _editNarrator(NarratorProfileModel narrator) {
    context.go('/parent_zone/narrator_creation?narratorId=${narrator.id}');
  }

  void _deleteNarrator(NarratorProfileModel narrator) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Narrator'),
        content: Text('Are you sure you want to delete "${narrator.displayName}"?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              ref.read(narratorCreationProvider.notifier).deleteNarrator(narrator.id);
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('Deleted narrator: ${narrator.displayName}'),
                  backgroundColor: Colors.orange,
                ),
              );
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }

  void _previewNarrator(NarratorProfileModel narrator) {
    showDialog(
      context: context,
      builder: (context) => Dialog(
        child: Container(
          width: 400,
          padding: const EdgeInsets.all(16),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                'Preview: ${narrator.displayName}',
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 16),
              VoicePreviewWidget(
                pitch: narrator.voice.pitch,
                rate: narrator.voice.rate,
                volume: narrator.voice.volume,
                selectedVoiceName: narrator.voice.name,
              ),
              const SizedBox(height: 16),
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('Close'),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
