import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:choice_once_upon_a_time/models/story_model.dart';
import 'package:choice_once_upon_a_time/features/ai_stories/data/ai_story_service.dart';

/// Provider for the AI story service
final aiStoryServiceProvider = Provider<AIStoryService>((ref) {
  return AIStoryService();
});

/// State for managing AI generated stories
class AIStoryState {
  final StoryModel? currentAIStory;
  final bool isGenerating;
  final String? error;

  const AIStoryState({
    this.currentAIStory,
    this.isGenerating = false,
    this.error,
  });

  AIStoryState copyWith({
    StoryModel? currentAIStory,
    bool? isGenerating,
    String? error,
  }) {
    return AIStoryState(
      currentAIStory: currentAIStory ?? this.currentAIStory,
      isGenerating: isGenerating ?? this.isGenerating,
      error: error,
    );
  }
}

/// Notifier for managing AI story state
class AIStoryNotifier extends StateNotifier<AIStoryState> {
  final AIStoryService _aiStoryService;

  AIStoryNotifier(this._aiStoryService) : super(const AIStoryState());

  /// Set the current AI generated story
  void setCurrentAIStory(StoryModel story) {
    state = state.copyWith(currentAIStory: story, error: null);
  }

  /// Clear the current AI story
  void clearCurrentAIStory() {
    state = state.copyWith(currentAIStory: null, error: null);
  }

  /// Set generating state
  void setGenerating(bool isGenerating) {
    state = state.copyWith(isGenerating: isGenerating);
  }

  /// Set error state
  void setError(String error) {
    state = state.copyWith(error: error, isGenerating: false);
  }

  /// Clear error
  void clearError() {
    state = state.copyWith(error: null);
  }
}

/// Provider for AI story state management
final aiStoryProvider = StateNotifierProvider<AIStoryNotifier, AIStoryState>((ref) {
  final aiStoryService = ref.watch(aiStoryServiceProvider);
  return AIStoryNotifier(aiStoryService);
});