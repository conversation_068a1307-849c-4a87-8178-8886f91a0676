import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:choice_once_upon_a_time/core/services/story_download_manager.dart';
import 'package:choice_once_upon_a_time/core/services/story_cleanup_service.dart';
import 'package:choice_once_upon_a_time/core/utils/app_logger.dart';

/// Screen for managing downloaded stories and cleanup settings
class DownloadedStoriesScreen extends ConsumerStatefulWidget {
  const DownloadedStoriesScreen({super.key});

  @override
  ConsumerState<DownloadedStoriesScreen> createState() => _DownloadedStoriesScreenState();
}

class _DownloadedStoriesScreenState extends ConsumerState<DownloadedStoriesScreen> {
  final StoryDownloadManager _downloadManager = StoryDownloadManager();
  final StoryCleanupService _cleanupService = StoryCleanupService();
  
  List<DownloadedStoryInfo> _downloadedStories = [];
  bool _isLoading = true;
  bool _isAutomaticCleanupEnabled = true;

  @override
  void initState() {
    super.initState();
    AppLogger.debug('[COMPONENT_LOAD] Full path: lib/features/settings/presentation/screens/downloaded_stories_screen.dart - DownloadedStoriesScreen');
    _loadDownloadedStories();
    _loadCleanupSettings();
  }

  Future<void> _loadDownloadedStories() async {
    try {
      final stories = await _downloadManager.getDownloadedStoriesInfo();
      if (mounted) {
        setState(() {
          _downloadedStories = stories;
          _isLoading = false;
        });
      }
    } catch (e) {
      AppLogger.debug('[DOWNLOADED_STORIES] Error loading downloaded stories: $e');
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _loadCleanupSettings() async {
    try {
      final isEnabled = await _cleanupService.isAutomaticCleanupEnabled();
      if (mounted) {
        setState(() {
          _isAutomaticCleanupEnabled = isEnabled;
        });
      }
    } catch (e) {
      AppLogger.debug('[DOWNLOADED_STORIES] Error loading cleanup settings: $e');
    }
  }

  Future<void> _toggleAutomaticCleanup(bool enabled) async {
    try {
      await _cleanupService.setAutomaticCleanupEnabled(enabled);
      setState(() {
        _isAutomaticCleanupEnabled = enabled;
      });
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              enabled 
                  ? 'Automatic cleanup enabled'
                  : 'Automatic cleanup disabled',
            ),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      AppLogger.debug('[DOWNLOADED_STORIES] Error toggling automatic cleanup: $e');
    }
  }

  Future<void> _performManualCleanup() async {
    try {
      final result = await _downloadManager.performManualCleanup();
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              result.hasDeletedStories
                  ? 'Cleaned up ${result.storiesDeleted} stories, freed ${result.spaceFreedFormatted}'
                  : 'No stories needed cleanup',
            ),
            backgroundColor: result.hasDeletedStories ? Colors.green : Colors.blue,
          ),
        );
        
        // Reload the list
        await _loadDownloadedStories();
      }
    } catch (e) {
      AppLogger.debug('[DOWNLOADED_STORIES] Error performing manual cleanup: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Failed to perform cleanup'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _deleteStory(DownloadedStoryInfo storyInfo) async {
    try {
      final success = await _downloadManager.deleteDownloadedStory(storyInfo.storyId);
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              success
                  ? 'Story deleted successfully'
                  : 'Failed to delete story',
            ),
            backgroundColor: success ? Colors.green : Colors.red,
          ),
        );
        
        if (success) {
          // Reload the list
          await _loadDownloadedStories();
        }
      }
    } catch (e) {
      AppLogger.debug('[DOWNLOADED_STORIES] Error deleting story: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Scaffold(
      appBar: AppBar(
        title: const Text('Downloaded Stories'),
        backgroundColor: theme.colorScheme.surface,
        foregroundColor: theme.colorScheme.onSurface,
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.cleaning_services),
            onPressed: _performManualCleanup,
            tooltip: 'Clean up old stories',
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : Column(
              children: [
                // Settings section
                Container(
                  margin: const EdgeInsets.all(16),
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: theme.colorScheme.surfaceContainerHighest,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Cleanup Settings',
                        style: theme.textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 8),
                      SwitchListTile(
                        title: const Text('Automatic Cleanup'),
                        subtitle: Text(
                          'Automatically delete stories older than ${_cleanupService.maxAgeInDays} days',
                        ),
                        value: _isAutomaticCleanupEnabled,
                        onChanged: _toggleAutomaticCleanup,
                        contentPadding: EdgeInsets.zero,
                      ),
                    ],
                  ),
                ),
                
                // Stories list
                Expanded(
                  child: _downloadedStories.isEmpty
                      ? Center(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Icon(
                                Icons.download_outlined,
                                size: 64,
                                color: theme.colorScheme.onSurfaceVariant,
                              ),
                              const SizedBox(height: 16),
                              Text(
                                'No downloaded stories',
                                style: theme.textTheme.titleMedium?.copyWith(
                                  color: theme.colorScheme.onSurfaceVariant,
                                ),
                              ),
                              const SizedBox(height: 8),
                              Text(
                                'Stories you download will appear here',
                                style: theme.textTheme.bodyMedium?.copyWith(
                                  color: theme.colorScheme.onSurfaceVariant,
                                ),
                              ),
                            ],
                          ),
                        )
                      : ListView.builder(
                          padding: const EdgeInsets.symmetric(horizontal: 16),
                          itemCount: _downloadedStories.length,
                          itemBuilder: (context, index) {
                            final story = _downloadedStories[index];
                            return _buildStoryCard(story, theme);
                          },
                        ),
                ),
              ],
            ),
    );
  }

  Widget _buildStoryCard(DownloadedStoryInfo story, ThemeData theme) {
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        story.storyId,
                        style: theme.textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        'Downloaded ${story.ageInDays} days ago',
                        style: theme.textTheme.bodySmall?.copyWith(
                          color: theme.colorScheme.onSurfaceVariant,
                        ),
                      ),
                    ],
                  ),
                ),
                if (story.isExpired) ...[
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: theme.colorScheme.errorContainer,
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      'EXPIRED',
                      style: theme.textTheme.labelSmall?.copyWith(
                        color: theme.colorScheme.onErrorContainer,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ] else if (story.willBeDeletedSoon) ...[
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: theme.colorScheme.tertiaryContainer,
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      'EXPIRES SOON',
                      style: theme.textTheme.labelSmall?.copyWith(
                        color: theme.colorScheme.onTertiaryContainer,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ],
                const SizedBox(width: 8),
                IconButton(
                  icon: const Icon(Icons.delete_outline),
                  onPressed: () => _deleteStory(story),
                  tooltip: 'Delete story',
                ),
              ],
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                Icon(
                  Icons.storage,
                  size: 16,
                  color: theme.colorScheme.onSurfaceVariant,
                ),
                const SizedBox(width: 4),
                Text(
                  story.sizeFormatted,
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: theme.colorScheme.onSurfaceVariant,
                  ),
                ),
                const SizedBox(width: 16),
                Icon(
                  Icons.access_time,
                  size: 16,
                  color: theme.colorScheme.onSurfaceVariant,
                ),
                const SizedBox(width: 4),
                Text(
                  'Last accessed ${story.daysSinceLastAccess} days ago',
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: theme.colorScheme.onSurfaceVariant,
                  ),
                ),
                const SizedBox(width: 16),
                Icon(
                  Icons.play_circle_outline,
                  size: 16,
                  color: theme.colorScheme.onSurfaceVariant,
                ),
                const SizedBox(width: 4),
                Text(
                  '${story.accessCount} plays',
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: theme.colorScheme.onSurfaceVariant,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
