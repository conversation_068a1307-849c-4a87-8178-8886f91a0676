import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:choice_once_upon_a_time/features/story_player/presentation/widgets/word_highlight_widget.dart';
import 'package:choice_once_upon_a_time/core/services/story_narration_service_interface.dart';
import 'package:choice_once_upon_a_time/models/narration_models.dart';
import 'dart:async';

// Mock implementation for testing
class MockStoryNarrationService implements IStoryNarrationService {
  final StreamController<NarrationState> _stateController = StreamController<NarrationState>.broadcast();
  final StreamController<NarrationProgress> _progressController = StreamController<NarrationProgress>.broadcast();
  final StreamController<WordHighlight> _wordHighlightController = StreamController<WordHighlight>.broadcast();

  NarrationState _currentState = NarrationState.idle;
  final NarrationConfig _currentConfig = NarrationConfig.defaultConfig;

  @override
  Stream<NarrationState> get stateStream => _stateController.stream;

  @override
  Stream<NarrationProgress> get progressStream => _progressController.stream;

  @override
  Stream<WordHighlight> get wordHighlightStream => _wordHighlightController.stream;

  @override
  NarrationState get currentState => _currentState;

  @override
  NarrationConfig get currentConfig => _currentConfig;

  @override
  bool get isInitialized => true;

  @override
  bool get isNarrating => _currentState.status == NarrationStatus.playing;

  @override
  bool get isPaused => _currentState.status == NarrationStatus.paused;

  // Method to simulate word highlighting for testing
  void simulateWordHighlight(String word, int startIndex, int endIndex) {
    _wordHighlightController.add(WordHighlight(
      startIndex: startIndex,
      endIndex: endIndex,
      word: word,
      startTimeMs: 0,
      endTimeMs: 500,
      isActive: true,
      sentenceIndex: 0,
    ));
  }

  void updateNarrationState(NarrationStatus status) {
    _currentState = _currentState.copyWith(status: status);
    _stateController.add(_currentState);
  }

  // Implement required methods with basic functionality
  @override
  Future<void> initialize({NarrationConfig? config}) async {}

  @override
  Future<void> configure(NarrationConfig config) async {}

  @override
  Future<void> narrateScene(scene, {String? storyId}) async {}

  @override
  Future<void> narrateText(String text, {String? emotionCue, String? storyId, String? sceneId}) async {}

  @override
  Future<void> play() async {}

  @override
  Future<void> pause() async {}

  @override
  Future<void> stop() async {}

  @override
  Future<void> skipToNextSentence() async {}

  @override
  Future<void> skipToPreviousSentence() async {}

  @override
  Future<void> seekToWord(int wordIndex) async {}

  @override
  Future<void> seekToSentence(int sentenceIndex) async {}

  @override
  Future<void> replayCurrentSentence() async {}

  @override
  Future<void> replayScene() async {}

  @override
  Future<void> setSpeechRate(double rate) async {}

  @override
  Future<void> setSpeechPitch(double pitch) async {}

  @override
  Future<void> setSpeechVolume(double volume) async {}

  @override
  Future<void> setAutoProgression(bool enabled) async {}

  @override
  Future<void> setWordHighlighting(bool enabled) async {}

  @override
  NarrationProgress? getCurrentProgress() => null;

  @override
  Future<void> saveProgress() async {}

  @override
  Future<void> loadProgress(String storyId, String sceneId) async {}

  @override
  Future<void> clearProgress(String storyId, String sceneId) async {}

  @override
  Future<void> dispose() async {
    await _stateController.close();
    await _progressController.close();
    await _wordHighlightController.close();
  }
}

void main() {
  group('WordHighlightWidget', () {
    late MockStoryNarrationService mockNarrationService;

    setUp(() {
      mockNarrationService = MockStoryNarrationService();
    });

    tearDown(() async {
      await mockNarrationService.dispose();
    });

    testWidgets('should display text correctly', (WidgetTester tester) async {
      // Arrange
      const testText = 'The quick brown fox jumps over the lazy dog.';
      
      // Act
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: WordHighlightWidget(
              narrationService: mockNarrationService,
              text: testText,
            ),
          ),
        ),
      );

      // Assert
      expect(find.text(testText), findsOneWidget);
    });

    testWidgets('should apply custom text styles', (WidgetTester tester) async {
      // Arrange
      const testText = 'Custom style test text.';
      const customFontSize = 24.0;
      const customLineHeight = 2.0;
      
      // Act
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: WordHighlightWidget(
              narrationService: mockNarrationService,
              text: testText,
              fontSize: customFontSize,
              lineHeight: customLineHeight,
            ),
          ),
        ),
      );

      // Assert
      final richTextWidget = tester.widget<RichText>(find.byType(RichText));
      final textStyle = richTextWidget.text.style;
      expect(textStyle?.fontSize, customFontSize);
      expect(textStyle?.height, customLineHeight);
    });

    testWidgets('should apply custom padding', (WidgetTester tester) async {
      // Arrange
      const testText = 'Padding test text.';
      const customPadding = EdgeInsets.all(32.0);
      
      // Act
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: WordHighlightWidget(
              narrationService: mockNarrationService,
              text: testText,
              padding: customPadding,
            ),
          ),
        ),
      );

      // Assert
      final containerWidget = tester.widget<Container>(find.byType(Container));
      expect(containerWidget.padding, customPadding);
    });

    testWidgets('should highlight words during narration', (WidgetTester tester) async {
      // Arrange
      const testText = 'Highlight test text.';
      
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: WordHighlightWidget(
              narrationService: mockNarrationService,
              text: testText,
              enableAnimations: false, // Disable animations for testing
            ),
          ),
        ),
      );

      // Act - Start narration and simulate word highlighting
      mockNarrationService.updateNarrationState(NarrationStatus.playing);
      await tester.pump();

      // Simulate highlighting the first word "Highlight"
      mockNarrationService.simulateWordHighlight('Highlight', 0, 9);
      await tester.pump();

      // Assert - The widget should update to show highlighting
      // Note: Exact assertion depends on how highlighting is implemented
      expect(find.byType(RichText), findsOneWidget);
    });

    testWidgets('should handle text alignment correctly', (WidgetTester tester) async {
      // Arrange
      const testText = 'Text alignment test.';
      const customAlignment = TextAlign.center;
      
      // Act
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: WordHighlightWidget(
              narrationService: mockNarrationService,
              text: testText,
              textAlign: customAlignment,
            ),
          ),
        ),
      );

      // Assert
      final richTextWidget = tester.widget<RichText>(find.byType(RichText));
      expect(richTextWidget.textAlign, customAlignment);
    });

    testWidgets('should disable animations when specified', (WidgetTester tester) async {
      // Arrange
      const testText = 'Animation disabled test.';
      
      // Act
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: WordHighlightWidget(
              narrationService: mockNarrationService,
              text: testText,
              enableAnimations: false,
            ),
          ),
        ),
      );

      // Assert - Widget should render without errors
      expect(find.text(testText), findsOneWidget);
    });

    testWidgets('should disable glow effect when specified', (WidgetTester tester) async {
      // Arrange
      const testText = 'Glow disabled test.';
      
      // Act
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: WordHighlightWidget(
              narrationService: mockNarrationService,
              text: testText,
              enableGlow: false,
            ),
          ),
        ),
      );

      // Assert - Widget should render without errors
      expect(find.text(testText), findsOneWidget);
    });

    testWidgets('should apply custom highlight color', (WidgetTester tester) async {
      // Arrange
      const testText = 'Custom highlight color test.';
      const customHighlightColor = Colors.red;
      
      // Act
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: WordHighlightWidget(
              narrationService: mockNarrationService,
              text: testText,
              highlightColor: customHighlightColor,
            ),
          ),
        ),
      );

      // Assert - Widget should render without errors
      expect(find.text(testText), findsOneWidget);
    });

    testWidgets('should handle empty text gracefully', (WidgetTester tester) async {
      // Arrange
      const testText = '';
      
      // Act
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: WordHighlightWidget(
              narrationService: mockNarrationService,
              text: testText,
            ),
          ),
        ),
      );

      // Assert - Widget should render without errors
      expect(find.byType(WordHighlightWidget), findsOneWidget);
    });

    testWidgets('should handle text with special characters', (WidgetTester tester) async {
      // Arrange
      const testText = 'Hello, world! How are you? I\'m fine.';
      
      // Act
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: WordHighlightWidget(
              narrationService: mockNarrationService,
              text: testText,
            ),
          ),
        ),
      );

      // Assert
      expect(find.text(testText), findsOneWidget);
    });

    testWidgets('should update when text changes', (WidgetTester tester) async {
      // Arrange
      const initialText = 'Initial text.';
      const updatedText = 'Updated text content.';
      
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: WordHighlightWidget(
              narrationService: mockNarrationService,
              text: initialText,
            ),
          ),
        ),
      );

      expect(find.text(initialText), findsOneWidget);

      // Act - Update the text
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: WordHighlightWidget(
              narrationService: mockNarrationService,
              text: updatedText,
            ),
          ),
        ),
      );

      // Assert
      expect(find.text(updatedText), findsOneWidget);
      expect(find.text(initialText), findsNothing);
    });

    testWidgets('should handle long text with scrolling', (WidgetTester tester) async {
      // Arrange
      const longText = 'This is a very long text that should require scrolling. ' * 20;
      
      // Act
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: SizedBox(
              height: 200, // Constrain height to force scrolling
              child: WordHighlightWidget(
                narrationService: mockNarrationService,
                text: longText,
              ),
            ),
          ),
        ),
      );

      // Assert - Widget should render without errors
      expect(find.byType(WordHighlightWidget), findsOneWidget);
    });
  });
}
