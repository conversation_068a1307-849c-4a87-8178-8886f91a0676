import 'package:flutter/material.dart';
import 'package:choice_once_upon_a_time/models/enhanced_story_model.dart';
import 'package:choice_once_upon_a_time/core/utils/app_logger.dart';

/// Responsive scene widget with full-width image display and overlay support
class ResponsiveSceneWidget extends StatefulWidget {
  final EnhancedStoryModel story;
  final EnhancedSceneModel scene;
  final VoidCallback? onSceneComplete;
  final Function(ChoiceOptionModel)? onChoiceSelected;
  final bool showControls;
  final bool showChoices;

  const ResponsiveSceneWidget({
    super.key,
    required this.story,
    required this.scene,
    this.onSceneComplete,
    this.onChoiceSelected,
    this.showControls = true,
    this.showChoices = false,
  });

  @override
  State<ResponsiveSceneWidget> createState() => _ResponsiveSceneWidgetState();
}

class _ResponsiveSceneWidgetState extends State<ResponsiveSceneWidget>
    with TickerProviderStateMixin {
  
  late AnimationController _fadeController;
  late Animation<double> _fadeAnimation;
  
  ImageProvider? _imageProvider;
  bool _imageLoaded = false;
  bool _imageError = false;

  @override
  void initState() {
    super.initState();
    AppLogger.debug('[COMPONENT_LOAD] Full path: lib/features/story_player/presentation/widgets/responsive_scene_widget.dart - ResponsiveSceneWidget');
    
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _fadeController, curve: Curves.easeInOut),
    );
    
    _preloadImage();
  }

  @override
  void didUpdateWidget(ResponsiveSceneWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.scene.id != widget.scene.id) {
      _preloadImage();
    }
  }

  Future<void> _preloadImage() async {
    final imagePath = widget.scene.getImagePath(widget.story.storyId);
    AppLogger.debug('[IMAGE_LOAD] Asset: $imagePath | Location: lib/features/story_player/presentation/widgets/responsive_scene_widget.dart:58 | Widget: ResponsiveSceneWidget');
    
    setState(() {
      _imageLoaded = false;
      _imageError = false;
    });

    try {
      _imageProvider = AssetImage(imagePath);
      await precacheImage(_imageProvider!, context);
      
      if (mounted) {
        setState(() {
          _imageLoaded = true;
        });
        _fadeController.forward();
      }
    } catch (e) {
      AppLogger.error('[SCENE_IMAGE] Failed to load image: $imagePath', e);
      if (mounted) {
        setState(() {
          _imageError = true;
        });
      }
    }
  }

  @override
  void dispose() {
    _fadeController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final screenSize = MediaQuery.of(context).size;
    final isLandscape = screenSize.width > screenSize.height;
    final isTablet = screenSize.width > 768;

    return Stack(
      children: [
        // Full-width background image
        _buildFullWidthImage(),
        
        // Content overlays
        if (widget.showControls)
          _buildContentOverlay(isLandscape, isTablet),
        
        // Choice popup overlay
        if (widget.showChoices && widget.scene.hasChoices)
          _buildChoiceOverlay(isLandscape, isTablet),
      ],
    );
  }

  Widget _buildFullWidthImage() {
    return Positioned.fill(
      child: AnimatedBuilder(
        animation: _fadeAnimation,
        builder: (context, child) {
          if (_imageError) {
            return _buildErrorImage();
          }
          
          if (!_imageLoaded || _imageProvider == null) {
            return _buildLoadingImage();
          }

          return FadeTransition(
            opacity: _fadeAnimation,
            child: Image(
              image: _imageProvider!,
              fit: BoxFit.cover,
              width: double.infinity,
              height: double.infinity,
              errorBuilder: (context, error, stackTrace) {
                AppLogger.error('[SCENE_IMAGE] Image render error', error);
                return _buildErrorImage();
              },
            ),
          );
        },
      ),
    );
  }

  Widget _buildLoadingImage() {
    return Container(
      width: double.infinity,
      height: double.infinity,
      color: Colors.black,
      child: const Center(
        child: CircularProgressIndicator(
          valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
        ),
      ),
    );
  }

  Widget _buildErrorImage() {
    final theme = Theme.of(context);
    return Container(
      width: double.infinity,
      height: double.infinity,
      color: theme.colorScheme.errorContainer,
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.broken_image,
              size: 64,
              color: theme.colorScheme.onErrorContainer,
            ),
            const SizedBox(height: 16),
            Text(
              'Failed to load scene image',
              style: theme.textTheme.bodyMedium?.copyWith(
                color: theme.colorScheme.onErrorContainer,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildContentOverlay(bool isLandscape, bool isTablet) {
    if (isLandscape && isTablet) {
      return _buildLandscapeOverlay();
    } else {
      return _buildPortraitOverlay();
    }
  }

  Widget _buildPortraitOverlay() {
    final theme = Theme.of(context);
    
    return Positioned(
      bottom: 0,
      left: 0,
      right: 0,
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Colors.transparent,
              Colors.black.withValues(alpha: 0.8),
            ],
          ),
        ),
        child: SafeArea(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Narration text
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.black.withValues(alpha: 0.7),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  widget.scene.text,
                  style: theme.textTheme.bodyLarge?.copyWith(
                    color: Colors.white,
                    fontSize: 18,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
              
              const SizedBox(height: 16),
              
              // Action button
              if (!widget.scene.hasChoices)
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton(
                    onPressed: widget.onSceneComplete,
                    style: ElevatedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      backgroundColor: theme.colorScheme.primary,
                      foregroundColor: theme.colorScheme.onPrimary,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                    child: const Text(
                      'Continue',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildLandscapeOverlay() {
    final theme = Theme.of(context);
    
    return Positioned(
      right: 0,
      top: 0,
      bottom: 0,
      width: 350,
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.centerLeft,
            end: Alignment.centerRight,
            colors: [
              Colors.transparent,
              Colors.black.withValues(alpha: 0.8),
            ],
          ),
        ),
        child: SafeArea(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // Narration text
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.black.withValues(alpha: 0.7),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  widget.scene.text,
                  style: theme.textTheme.bodyLarge?.copyWith(
                    color: Colors.white,
                    fontSize: 16,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
              
              const SizedBox(height: 24),
              
              // Action button
              if (!widget.scene.hasChoices)
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton(
                    onPressed: widget.onSceneComplete,
                    style: ElevatedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      backgroundColor: theme.colorScheme.primary,
                      foregroundColor: theme.colorScheme.onPrimary,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                    child: const Text(
                      'Continue',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildChoiceOverlay(bool isLandscape, bool isTablet) {
    final theme = Theme.of(context);
    final screenSize = MediaQuery.of(context).size;
    final maxWidth = isLandscape ? screenSize.width * 0.7 : screenSize.width * 0.9;
    
    return Positioned.fill(
      child: Container(
        color: Colors.black.withValues(alpha: 0.8),
        child: Center(
          child: Container(
            constraints: BoxConstraints(
              maxWidth: maxWidth.clamp(300, 800),
              maxHeight: screenSize.height * 0.8,
            ),
            margin: const EdgeInsets.all(16),
            padding: const EdgeInsets.all(24),
            decoration: BoxDecoration(
              color: theme.colorScheme.surface,
              borderRadius: BorderRadius.circular(16),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.3),
                  blurRadius: 20,
                  offset: const Offset(0, 10),
                ),
              ],
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  'Choose your path:',
                  style: theme.textTheme.headlineSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 24),
                ...widget.scene.choices!.map((choice) => Container(
                  width: double.infinity,
                  margin: const EdgeInsets.only(bottom: 12),
                  child: ElevatedButton(
                    onPressed: () => widget.onChoiceSelected?.call(choice),
                    style: ElevatedButton.styleFrom(
                      padding: const EdgeInsets.all(16),
                      backgroundColor: theme.colorScheme.primary,
                      foregroundColor: theme.colorScheme.onPrimary,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                    child: Text(
                      choice.option,
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
                )),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
