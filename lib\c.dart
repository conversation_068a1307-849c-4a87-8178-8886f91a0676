import 'package:flutter/material.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'firebase_options.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await Firebase.initializeApp(
    options: DefaultFirebaseOptions.currentPlatform,
  );
  // Configure Analytics for COPPA compliance (kids' app)
  FirebaseAnalytics.instance.setAnalyticsCollectionEnabled(true);
  FirebaseAnalytics.instance.setUserProperty(
    name: 'restricted_data_processing',
    value: 'true',
  );
  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Kids Storytelling App',
      theme: ThemeData(
        primarySwatch: Colors.blue,
        textTheme: const TextTheme(
          bodyMedium: TextStyle(fontSize: 24), // Large text for kids
        ),
      ),
      home: const StoryCountScreen(),
    );
  }
}

class StoryCountScreen extends StatefulWidget {
  const StoryCountScreen({super.key});

  @override
  _StoryCountScreenState createState() => _StoryCountScreenState();
}

class _StoryCountScreenState extends State<StoryCountScreen> {
  int storyCount = 0;

  @override
  void initState() {
    super.initState();
    _loadStoryCount();
  }

  Future<void> _loadStoryCount() async {
    try {
      // Query Firestore for stories collection
      final querySnapshot = await FirebaseFirestore.instance.collection('stories').get();

      // Debug: Print number of documents
      print('Firestore stories found: ${querySnapshot.docs.length}');

      setState(() {
        storyCount = querySnapshot.docs.length;
      });

      // Log app open event (COPPA-compliant)
      await FirebaseAnalytics.instance.logEvent(
        name: 'app_open',
        parameters: {'story_count': storyCount},
      );
    } catch (e) {
      print('Error loading story count from Firestore: $e');
      setState(() {
        storyCount = -1; // Indicate error
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Story Count'),
      ),
      body: Center(
        child: storyCount == 0
            ? const CircularProgressIndicator()
            : Text(
                storyCount == -1
                    ? 'Error loading stories'
                    : 'Number of Stories: $storyCount',
                style: const TextStyle(fontSize: 30, fontWeight: FontWeight.bold),
              ),
      ),
    );
  }
}