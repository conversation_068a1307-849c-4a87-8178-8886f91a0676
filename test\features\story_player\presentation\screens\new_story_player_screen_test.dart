import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:choice_once_upon_a_time/features/story_player/presentation/screens/new_story_player_screen.dart';


void main() {
  group('NewStoryPlayerScreen', () {


    testWidgets('should display loading state initially', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: const NewStoryPlayerScreen(storyId: 'story013'),
        ),
      );

      // Should show loading indicator
      expect(find.byType(CircularProgressIndicator), findsOneWidget);
      expect(find.text('Loading story...'), findsOneWidget);
    });

    testWidgets('should handle missing story gracefully', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: const NewStoryPlayerScreen(storyId: 'nonexistent_story'),
        ),
      );

      // Wait for loading to complete
      await tester.pumpAndSettle(const Duration(seconds: 3));

      // Should show error state
      expect(find.textContaining('Story not found'), findsOneWidget);
    });

    testWidgets('should display story content when loaded', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: const NewStoryPlayerScreen(storyId: 'story013'),
        ),
      );

      // Wait for story to load
      await tester.pumpAndSettle(const Duration(seconds: 5));

      // Should show story content (if story exists)
      // Note: This test may fail if story013 doesn't exist in test environment
      // In a real test environment, we would mock the story service
    });

    testWidgets('should handle system back button correctly', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: const NewStoryPlayerScreen(storyId: 'story013'),
        ),
      );

      // Wait for initial load
      await tester.pump();

      // Simulate system back button
      await tester.binding.defaultBinaryMessenger.handlePlatformMessage(
        'flutter/navigation',
        const StandardMethodCodec().encodeMethodCall(
          const MethodCall('routePopped', <String, dynamic>{
            'location': '/',
            'state': null,
          }),
        ),
        (data) {},
      );

      await tester.pumpAndSettle();

      // Should handle back button gracefully
      // (Implementation depends on PopScope behavior)
    });

    testWidgets('should maintain landscape orientation', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: const NewStoryPlayerScreen(storyId: 'story013'),
        ),
      );

      await tester.pump();

      // Verify that orientation is set to landscape
      // Note: This is difficult to test directly in widget tests
      // as SystemChrome calls don't affect the test environment
      expect(find.byType(NewStoryPlayerScreen), findsOneWidget);
    });

    testWidgets('should show controls overlay when tapped', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: const NewStoryPlayerScreen(storyId: 'story013'),
        ),
      );

      // Wait for loading
      await tester.pumpAndSettle(const Duration(seconds: 3));

      // Tap on the screen to show controls
      await tester.tap(find.byType(NewStoryPlayerScreen));
      await tester.pumpAndSettle();

      // Should show control buttons (if story is loaded)
      // Note: Actual behavior depends on story loading success
    });

    testWidgets('should handle settings button tap', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: const NewStoryPlayerScreen(storyId: 'story013'),
        ),
      );

      // Wait for loading
      await tester.pumpAndSettle(const Duration(seconds: 3));

      // Look for settings button and tap it
      final settingsButton = find.byIcon(Icons.settings);
      if (settingsButton.evaluate().isNotEmpty) {
        await tester.tap(settingsButton);
        await tester.pumpAndSettle();

        // Should show settings overlay
        expect(find.text('Story Settings'), findsOneWidget);
      }
    });

    testWidgets('should handle narration controls', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: const NewStoryPlayerScreen(storyId: 'story013'),
        ),
      );

      // Wait for loading
      await tester.pumpAndSettle(const Duration(seconds: 3));

      // Look for play/pause button
      final playButton = find.byIcon(Icons.play_arrow);
      if (playButton.evaluate().isNotEmpty) {
        await tester.tap(playButton);
        await tester.pumpAndSettle();

        // Should start narration
        // Note: Actual TTS testing requires mocking
      }
    });

    testWidgets('should handle scene navigation', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: const NewStoryPlayerScreen(storyId: 'story013'),
        ),
      );

      // Wait for loading
      await tester.pumpAndSettle(const Duration(seconds: 3));

      // Look for next button
      final nextButton = find.byIcon(Icons.skip_next);
      if (nextButton.evaluate().isNotEmpty) {
        await tester.tap(nextButton);
        await tester.pumpAndSettle();

        // Should navigate to next scene
      }
    });

    testWidgets('should display choices when available', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: const NewStoryPlayerScreen(storyId: 'story013'),
        ),
      );

      // Wait for loading and potential choice display
      await tester.pumpAndSettle(const Duration(seconds: 5));

      // Check if choices are displayed
      // Note: This depends on the specific story content
      final choiceWidgets = find.textContaining('Choice');
      if (choiceWidgets.evaluate().isNotEmpty) {
        // Should show choice options
        expect(choiceWidgets, findsAtLeastNWidgets(1));
      }
    });

    testWidgets('should handle story completion', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: const NewStoryPlayerScreen(storyId: 'story013'),
        ),
      );

      // Wait for potential story completion
      await tester.pumpAndSettle(const Duration(seconds: 10));

      // Check if completion screen is shown
      final completionText = find.textContaining('Congratulations');
      if (completionText.evaluate().isNotEmpty) {
        expect(completionText, findsOneWidget);
      }
    });

    testWidgets('should handle vocabulary discussion', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: const NewStoryPlayerScreen(storyId: 'story013'),
        ),
      );

      // Wait for potential vocabulary phase
      await tester.pumpAndSettle(const Duration(seconds: 10));

      // Check if vocabulary discussion is shown
      final vocabularyText = find.textContaining('Vocabulary Discussion');
      if (vocabularyText.evaluate().isNotEmpty) {
        expect(vocabularyText, findsOneWidget);
      }
    });

    testWidgets('should persist settings changes', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: const NewStoryPlayerScreen(storyId: 'story013'),
        ),
      );

      // Wait for loading
      await tester.pumpAndSettle(const Duration(seconds: 3));

      // Open settings if available
      final settingsButton = find.byIcon(Icons.settings);
      if (settingsButton.evaluate().isNotEmpty) {
        await tester.tap(settingsButton);
        await tester.pumpAndSettle();

        // Look for font size slider
        final slider = find.byType(Slider);
        if (slider.evaluate().isNotEmpty) {
          // Drag slider to change font size
          await tester.drag(slider.first, const Offset(50, 0));
          await tester.pumpAndSettle();

          // Close settings
          final doneButton = find.text('Done');
          if (doneButton.evaluate().isNotEmpty) {
            await tester.tap(doneButton);
            await tester.pumpAndSettle();
          }

          // Settings should be persisted
          // Note: Testing SharedPreferences requires mocking
        }
      }
    });

    testWidgets('should handle transition animations', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: const NewStoryPlayerScreen(storyId: 'story013'),
        ),
      );

      // Wait for loading
      await tester.pumpAndSettle(const Duration(seconds: 3));

      // Check for fade animations
      expect(find.byType(FadeTransition), findsAtLeastNWidgets(1));

      // Check for scale animations
      expect(find.byType(ScaleTransition), findsWidgets);
    });

    testWidgets('should handle error states gracefully', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: const NewStoryPlayerScreen(storyId: 'invalid_story'),
        ),
      );

      // Wait for error to appear
      await tester.pumpAndSettle(const Duration(seconds: 5));

      // Should show error message
      expect(find.textContaining('Failed to load'), findsOneWidget);
    });

    testWidgets('should maintain immersive UI mode', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: const NewStoryPlayerScreen(storyId: 'story013'),
        ),
      );

      await tester.pump();

      // Verify immersive mode is set
      // Note: SystemChrome calls don't affect test environment
      // but we can verify the widget is created
      expect(find.byType(NewStoryPlayerScreen), findsOneWidget);
    });

    testWidgets('should handle memory cleanup on dispose', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: const NewStoryPlayerScreen(storyId: 'story013'),
        ),
      );

      await tester.pump();

      // Navigate away to trigger dispose
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(body: Text('New Screen')),
        ),
      );

      await tester.pumpAndSettle();

      // Should clean up resources without errors
      expect(find.text('New Screen'), findsOneWidget);
    });
  });
}
