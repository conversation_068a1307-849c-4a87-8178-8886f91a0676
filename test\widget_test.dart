// This is a basic Flutter widget test for Choice: Once Upon A Time app.

import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:choice_once_upon_a_time/app/app_widget.dart';

void main() {
  testWidgets('App launches and shows launch screen', (WidgetTester tester) async {
    // Build our app and trigger a frame.
    await tester.pumpWidget(const ProviderScope(child: AppWidget()));

    // Verify that the launch screen is displayed
    expect(find.text('Choice: Once Upon A Time'), findsOneWidget);
    expect(find.text('Interactive bedtime stories for children'), findsOneWidget);
    expect(find.text('Loading...'), findsOneWidget);

    // Verify the app icon is present
    expect(find.byIcon(Icons.auto_stories), findsOneWidget);
  });

  testWidgets('App navigation works after initialization', (WidgetTester tester) async {
    // Build our app and trigger a frame.
    await tester.pumpWidget(const ProviderScope(child: AppWidget()));

    // Wait for initial frame
    await tester.pump();

    // Check that the app structure is present
    expect(find.byType(MaterialApp), findsOneWidget);
    expect(find.byType(Scaffold), findsWidgets);
  });
}
