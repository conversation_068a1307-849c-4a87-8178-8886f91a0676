// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'user_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

UserModel _$UserModelFromJson(Map<String, dynamic> json) => UserModel(
      userId: json['userId'] as String,
      email: json['email'] as String?,
      displayName: json['displayName'] as String?,
      createdAt: UserModel._timestampFromJson(json['createdAt']),
      lastLoginAt: UserModel._timestampFromJson(json['lastLoginAt']),
      appPreferences: AppPreferences.fromJson(
          json['appPreferences'] as Map<String, dynamic>),
      subscription: SubscriptionInfo.fromJson(
          json['subscription'] as Map<String, dynamic>),
      ftueCompleted: json['ftueCompleted'] as bool? ?? false,
    );

Map<String, dynamic> _$UserModelToJson(UserModel instance) => <String, dynamic>{
      'userId': instance.userId,
      'email': instance.email,
      'displayName': instance.displayName,
      'createdAt': UserModel._timestampToJson(instance.createdAt),
      'lastLoginAt': UserModel._timestampToJson(instance.lastLoginAt),
      'appPreferences': instance.appPreferences,
      'subscription': instance.subscription,
      'ftueCompleted': instance.ftueCompleted,
    };

AppPreferences _$AppPreferencesFromJson(Map<String, dynamic> json) =>
    AppPreferences(
      masterVolume: (json['masterVolume'] as num?)?.toDouble() ?? 0.8,
      musicEnabled: json['musicEnabled'] as bool? ?? true,
      sfxEnabled: json['sfxEnabled'] as bool? ?? true,
      narrationLanguage: json['narrationLanguage'] as String? ?? 'en-US',
    );

Map<String, dynamic> _$AppPreferencesToJson(AppPreferences instance) =>
    <String, dynamic>{
      'masterVolume': instance.masterVolume,
      'musicEnabled': instance.musicEnabled,
      'sfxEnabled': instance.sfxEnabled,
      'narrationLanguage': instance.narrationLanguage,
    };

SubscriptionInfo _$SubscriptionInfoFromJson(Map<String, dynamic> json) =>
    SubscriptionInfo(
      tier: json['tier'] as String? ?? 'free',
      status: json['status'] as String? ?? 'active',
      expiryDate:
          SubscriptionInfo._nullableTimestampFromJson(json['expiryDate']),
      platform: json['platform'] as String? ?? 'unknown',
      originalTransactionId: json['originalTransactionId'] as String?,
    );

Map<String, dynamic> _$SubscriptionInfoToJson(SubscriptionInfo instance) =>
    <String, dynamic>{
      'tier': instance.tier,
      'status': instance.status,
      'expiryDate':
          SubscriptionInfo._nullableTimestampToJson(instance.expiryDate),
      'platform': instance.platform,
      'originalTransactionId': instance.originalTransactionId,
    };
