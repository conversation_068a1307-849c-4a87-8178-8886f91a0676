
---
## File 1: `docs/00_PROJECT_OVERVIEW_AND_ROADMAP_STATUS.md`
---

# Project Context & Full Roadmap Status: "Choice: Once Upon A Time"
**Document Version:** 1.1 (Reflecting new project start)
**Last Updated:** June 4, 2025

**Purpose of this Document:**
To provide a comprehensive overview of the "Choice: Once Upon A Time" project, including its objectives, key decisions, guiding documents, a detailed task-by-task roadmap, and the current development status. This document serves as a central reference for all stakeholders and AI assistants involved in the project.

## I. Project Overview

* **1.1. Project Name:** Choice: Once Upon A Time
* **1.2. Core Objective:** To create an engaging and delightful interactive bedtime story Flutter app for children aged 4-7 years. The app aims to subtly instill and reinforce positive moral values (honesty, empathy, perseverance, etc.), fostering social and emotional development through choice-driven narratives brought to life by a unique, empathetic AI narrator in a safe, ad-free environment.
* **1.3. Core Pillars/Principles:** Empathetic Narrator, Moral Value Integration, Bedtime Context, Intuitive Design, High-Quality Visuals, Parental Trust & Transparency.

## II. Target Audience Summary (Derived from Task 1.3)

* **Children:** Ages 4-7 years. Represented by persona "Lily, 5". Key characteristics include developing cognitive/language skills, 10-20 min attention span, understanding simple narratives and moral concepts via concrete examples, proficient with basic touch interactions. Enjoy animals, fantasy, adventure. Need clear cues, immediate feedback, and a calming experience.
* **Parents/Guardians:** Represented by persona "Sarah". Key motivations include moral reinforcement, educational value, quality entertainment, calming bedtime routine, safe screen time. Concerns include excessive screen time, age-inappropriateness. Value transparency, ethical monetization, offline access, and parental controls.

## III. Key MVP Features & Content

* **Interactive Moral Stories:** Initial set of 3 stories:
    * "Pip and the Pantry Puzzle" (Moral: Honesty)
    * "Lila Fox and the Moonpetal Wish" (Moral: Empathy/Kindness)
    * "Finley's Fantastic Flying Machine" (Moral: Perseverance)
* **Empathetic AI Narrator:** Implemented with on-device TTS (`flutter_tts`), modular service architecture (`TTSServiceInterface`), focus on emotional delivery based on `emotionCue` tags from story JSONs.
* **Branching Narratives:** User choices directly influencing story progression and outcomes.
* **Parent Zone (MVP Basics):** Parental Gate, Sound Settings, informational screens (About Values, Help/FAQ), basic download management UI, Monetization UI shell (fetching product details).
* **Offline Access:** For downloaded stories (story data and asset placeholders initially, full assets later).
* **User Interface:** Based on `docs/02_SCREEN_SPECIFICATIONS.md`.
* **Visuals:** Adhering to "Softly Vibrant Storybook Illustrated" style defined in `docs/03_ART_STYLE_DEFINITION_AND_ASSET_PLAN.md`.

## IV. Technology Stack Summary (Derived from Task 1.6 & TDD)

* **Frontend:** Flutter (latest stable).
* **Backend:** Firebase BaaS (Authentication, Cloud Firestore, Cloud Storage, Cloud Functions for IAP validation).
* **Database (Cloud):** Cloud Firestore (for story data, user preferences, subscription status).
* **Local Storage:** IsarDB (for offline story data, preferences) and file system for assets.
* **Narration Technology:** On-device `flutter_tts` with SSML/parameter adjustments for emotion, architected via `TTSServiceInterface` for future flexibility.
* **State Management:** Riverpod.
* **Navigation:** GoRouter.

## V. Key Guiding Documents (Relative Paths in Project)

* **Market Research:** `docs/Market_Research_Task_1.1.pdf` (User-provided PDF)
* **Screen Specifications:** `docs/02_SCREEN_SPECIFICATIONS.md`
* **Art Style & Asset Plan:** `docs/03_ART_STYLE_DEFINITION_AND_ASSET_PLAN.md`
* **Story Content:**
    * `assets/stories/story_01.json`
    * `assets/stories/story_02.json`
    * `assets/stories/story_03.json`
* **Technical Design Document:** `docs/04_TECHNICAL_DESIGN_DOCUMENT.md`
* **MVP Development Sprints:** `docs/05_MVP_DEVELOPMENT_SPRINTS.md`
* **AI Agent Guidelines:** `docs/06_CURSOR_AI_AGENT_GUIDELINES.md` (or `PROJECT_WORKING_RULES.md` at root)

## VI. Core Development Principles

* Modular Design (as per TDD & AI Guidelines).
* Testability (Unit, Widget, Integration tests).
* Security (No hardcoded secrets, use of `.env`).
* Responsive UI.
* Web Compatibility (for testing).
* Robust Error Handling.
* TDD Adherence: The `docs/04_TECHNICAL_DESIGN_DOCUMENT.md` is the primary blueprint.

## VII. Detailed Project Roadmap & Current Status

| Phase No. | Phase Name                            | Task No. | Task Description                                                                                                                                                                                                                                                                                                                                                        | Status / Current Notes                                                                                                                                                                                                                                                                   |
| :-------- | :------------------------------------ | :------- | :---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | :--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| **1** | **Pre-Production & Conceptualization** | 1.1      | **Conduct Detailed Market Research & Competitor Analysis** | **Completed.** (User-provided document: `Market_Research_Task_1.1.pdf`)                                                                                                                                                                                                |
| 1         | Pre-Production & Conceptualization    | 1.2      | **Define Core Moral Values & Integration Framework:** Develop transparent Moral Framework, Content Generation/Moderation Policy.                                                                                                                                                                                                                                      | **Conceptualized.** User to formalize this document.                                                                                                                                                                                                    |
| 1         | Pre-Production & Conceptualization    | 1.3      | **Define Target Audience & Develop User Personas:** Detailed profiles for child and parent users.                                                                                                                                                                                                                                                                 | **Conceptualized.** User to formalize these personas.                                                                                                                                                                                                   |
| 1         | Pre-Production & Conceptualization    | 1.4      | **Story Concept Development (3-5 flagship stories) with Empathetic Narrator Focus:** Outline stories, narrator's role.                                                                                                                                                                                                                                                   | **Initial 3 Story Concepts Developed** (JSONs: `story_01.json`, `story_02.json`, `story_03.json`).                                                                                                                                                           |
| 1         | Pre-Production & Conceptualization    | 1.5      | **Initial Interaction Design Brainstorming:** Focus on intuitive UX/UI, narrator synergy, choice presentation.                                                                                                                                                                                                                                                            | **Conceptualized.** User to consolidate ideas into UI/UX specs.                                                                                                                                                                                          |
| 1         | Pre-Production & Conceptualization    | 1.6      | **Technology Stack & Platform Identification (Flutter Confirmed):** Deep assessment of AI TTS, NLU. Evaluate build vs. buy for AI.                                                                                                                                                                                                                                           | **Decisions Made:** Flutter, Firebase BaaS, On-device `flutter_tts` (modular).                                                                                                                                                                         |
| 1         | Pre-Production & Conceptualization    | 1.7      | **Team Formation/Skill Assessment:** Assess needs, including specialized AI/ML talent.                                                                                                                                                                                                                                                                                     | **Conceptualized.** User to assess needs.                                                                                                                                                                                                                |
| 1         | Pre-Production & Conceptualization    | 1.8      | **Develop Monetization Strategy:** Freemium, subscription tiers, pricing transparency.                                                                                                                                                                                                                                                                               | **Conceptualized.** User to define strategy.                                                                                                                                                                                                            |
| 1         | Pre-Production & Conceptualization    | 1.9      | **Outline Initial Budget & Timeline:** Factor in AI development, testing, ethical reviews.                                                                                                                                                                                                                                                                                 | **Conceptualized.** User to develop this outline.                                                                                                                                                                                                      |
| 1         | Pre-Production & Conceptualization    | 1.10     | **AI Emotional Narration & Moral Choice Engine - Initial Feasibility Study:** Investigate technical viability of core AI USPs.                                                                                                                                                                                                                                             | **Conceptually Addressed** via choice of `flutter_tts` for MVP and planning for emotional cue mapping.                                                                                                                                               |
| **2** | **Design & Prototyping** | 2.1      | **Story Scripting & Branching Narrative Design:** Detailed scripts for selected concepts.                                                                                                                                                                                                                                                                                               | **Completed** (Represented by the detailed Story JSON files).                                                                                                                                                                                   |
| 2         | Design & Prototyping                  | 2.2      | **UI/UX Design for App:** Intuitive navigation, engaging interactions, clear choice presentation, parental controls.                                                                                                                                                                                                                                                      | **Documentation Provided by User** (`docs/02_SCREEN_SPECIFICATIONS.md` from "Task 2.2 update").                                                                                                                                        |
| 2         | Design & Prototyping                  | 2.3      | **Art Style Definition & Asset Creation Planning:** Define visual style. Plan all visual assets.                                                                                                                                                                                                                                                                            | **Documentation Provided by User** (`docs/03_ART_STYLE_DEFINITION_AND_ASSET_PLAN.md` from "Task 2.3").                                                                                                                                          |
| 2         | Design & Prototyping                  | 2.4      | **Sound Design Concept Development:** Define soundscape (music, SFX) complementing narrator.                                                                                                                                                                                                                                                                            | **Conceptualized.** User to formalize this document.                                                                                                                                                                                                     |
| 2         | Design & Prototyping                  | 2.5      | **Develop Interactive Prototypes (Low to Medium Fidelity):** Focus on core flow, story interaction, choice mechanics, narrator integration.                                                                                                                                                                                                                               | **About to Start / In Progress (via Sprints 0-3).** This is the current main development effort.                                                                                                                                                     |
| 2         | Design & Prototyping                  | 2.6      | **Conduct User Testing (Internal & Limited External for Prototype):** Validate USPs, gather feedback on UI/UX, engagement, moral takeaways.                                                                                                                                                                                                                              | **Upcoming.** Dependent on a stable prototype from Task 2.5 (completion of Sprints 0-3).                                                                                                                                                           |
| 2         | Design & Prototyping                  | 2.7      | **Create Detailed Technical Design Document (TDD):** Finalize architecture, AI design, database schema, API specs, Flutter implementation.                                                                                                                                                                                                                                | **Documentation Provided by User** (`docs/04_TECHNICAL_DESIGN_DOCUMENT.md` from "Task 2.7").                                                                                                                                                           |
| **3** | **Production (MVP Development Sprints from `docs/05_MVP_DEVELOPMENT_SPRINTS.md`)** | Sprint 0 | **Project Setup & Core Architectural Foundations:** Initialize project, folder structure, theme, navigation, shared widgets, basic tests.                                                                                                                                                                                          | **To Be Started.** This is the immediate next step for the new project.                                                                                                                                                                              |
| 3         | Production                            | Sprint 1 | **Data Modeling, Firebase Setup & Core Screen Shells:** Implement Dart models, configure Firebase, core services shells, UI shells for launch, FTUE, library, parental gate, parent dashboard.                                                                                                                                                                     | **Upcoming.** |
| 3         | Production                            | Sprint 2 | **Core Story Player - Narrative Display & Branching Logic:** Dynamic story loading/parsing, story player UI, branching engine, linear progression, placeholder narrator controls.                                                                                                                                                                               | **Upcoming.** |
| 3         | Production                            | Sprint 3 | **Empathetic Narrator (On-Device TTS - MVP) & UI Sounds:** Full `TTSService` (`flutter_tts`) integration with emotional cues, UI sounds, finalize in-story UI screens (Pause, End, Idle).                                                                                                                                                                        | **Upcoming.** |
| 3         | Production                            | Sprint 4 | **Full Story Integration, Offline Functionality & Core Popups:** All 3 stories playable (assets/Firebase), `OfflineStorageService` download/access logic, key popups.                                                                                                                                                                                              | **Upcoming.** |
| 3         | Production                            | Sprint 5 | **Parent Zone Basics & Monetization Shell:** Parent auth, sound settings, info screens, download management UI, monetization UI shell (product fetching), critical popups, calm exit screen.                                                                                                                                                                         | **Upcoming.** |
| 3         | Production                            | Sprint 6 | **MVP Polish, Initial Asset Integration & Thorough Internal QA Focus:** UI polish, integrate available assets, comprehensive internal functional/narrative/usability testing, bug fixing, code review.                                                                                                                                                              | **Upcoming.** |
| **4** | **Testing & Refinement** | 4.1      | **Develop Beta Testing Program:** Plan and recruit beta testers.                                                                                                                                                                                                                                                                                                                      | **Upcoming.** |
| 4         | Testing & Refinement                  | 4.2      | **Collect & Analyze Beta User Feedback.** | **Upcoming.** |
| 4         | Testing & Refinement                  | 4.3      | **Bug Fixing & Performance Optimization.** | **Upcoming.** |
| 4         | Testing & Refinement                  | 4.4      | **Content Review & Polish.** | **Upcoming.** |
| 4         | Testing & Refinement                  | 4.5      | **Accessibility Testing.** | **Upcoming.** |
| 4         | Testing & Refinement                  | 4.6      | **Platform Compliance Testing.** | **Upcoming.** |
| 4         | Testing & Refinement                  | 4.7      | **Finalize App Store Assets & Marketing Materials.** | **Upcoming.** |
| **5** | **Launch & Initial Marketing** | 5.1-5.5  | All launch and initial marketing tasks.                                                                                                                                                                                                                                                                                                                                       | **Upcoming.** |
| **6** | **Post-Launch Operations & Iteration**| 6.1-6.6  | All post-launch operations and iteration tasks.                                                                                                                                                                                                                                                                                                                             | **Future.** |
| **7** | **Ongoing/Setup Tasks** | 7.1-7.5  | All ongoing setup and maintenance tasks (Team Collab, PM Tools, Version Control, Legal/Compliance, AI Governance).                                                                                                                                                                                                                                                                  | **Ongoing/As Needed.** |

## VIII. Immediate Next Steps / Current Focus (New Project Start)

The immediate focus is to **execute Sprint 0: Project Setup & Core Architectural Foundations** for the new Flutter project, as detailed in `docs/05_MVP_DEVELOPMENT_SPRINTS.md`. This involves:
* Initializing the Flutter project.
* Implementing the defined folder structure.
* Setting up the core app widget, global theme, and basic navigation (GoRouter) with placeholder screens.
* Creating initial shared widgets.
* Establishing the basic unit test setup.

This will provide the foundational codebase upon which subsequent sprints will build.
---