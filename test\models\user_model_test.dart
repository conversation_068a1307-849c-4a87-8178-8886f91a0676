import 'package:flutter_test/flutter_test.dart';
import 'package:choice_once_upon_a_time/models/user_model.dart';

void main() {
  group('UserModel', () {
    test('should create from JSON correctly', () {
      // Arrange
      final json = {
        'userId': 'user_123',
        'email': '<EMAIL>',
        'displayName': 'Test User',
        'createdAt': {
          '_seconds': 1640995200, // 2022-01-01 00:00:00 UTC
          '_nanoseconds': 0,
        },
        'lastLoginAt': {
          '_seconds': 1640995200,
          '_nanoseconds': 0,
        },
        'appPreferences': {
          'masterVolume': 0.8,
          'musicEnabled': true,
          'sfxEnabled': true,
          'narrationLanguage': 'en-US',
        },
        'subscription': {
          'tier': 'premium_monthly',
          'status': 'active',
          'expiryDate': {
            '_seconds': 1672531200, // 2023-01-01 00:00:00 UTC
            '_nanoseconds': 0,
          },
          'platform': 'ios',
          'originalTransactionId': 'txn_123',
        },
        'ftueCompleted': true,
      };

      // Act
      final model = UserModel.fromJson(json);

      // Assert
      expect(model.userId, 'user_123');
      expect(model.email, '<EMAIL>');
      expect(model.displayName, 'Test User');
      expect(model.ftueCompleted, true);
      expect(model.appPreferences.masterVolume, 0.8);
      expect(model.appPreferences.musicEnabled, true);
      expect(model.subscription.tier, 'premium_monthly');
      expect(model.subscription.status, 'active');
      expect(model.subscription.platform, 'ios');
    });

    test('should convert to JSON correctly', () {
      // Arrange
      final createdAt = DateTime(2022, 1, 1);
      final lastLoginAt = DateTime(2022, 1, 1);
      
      final model = UserModel(
        userId: 'user_test',
        email: '<EMAIL>',
        displayName: 'Test User',
        createdAt: createdAt,
        lastLoginAt: lastLoginAt,
        appPreferences: const AppPreferences(
          masterVolume: 0.7,
          musicEnabled: false,
          sfxEnabled: true,
          narrationLanguage: 'es-ES',
        ),
        subscription: const SubscriptionInfo(
          tier: 'free',
          status: 'active',
          platform: 'android',
        ),
        ftueCompleted: true,
      );

      // Act
      final json = model.toJson();

      // Assert
      expect(json['userId'], 'user_test');
      expect(json['email'], '<EMAIL>');
      expect(json['displayName'], 'Test User');
      expect(json['ftueCompleted'], true);

      // Check that nested objects are properly serialized
      expect(json.containsKey('appPreferences'), true);
      expect(json.containsKey('subscription'), true);
    });

    test('should handle copyWith correctly', () {
      // Arrange
      final original = UserModel(
        userId: 'user_original',
        createdAt: DateTime(2022, 1, 1),
        lastLoginAt: DateTime(2022, 1, 1),
        appPreferences: const AppPreferences(),
        subscription: const SubscriptionInfo(),
      );

      // Act
      final updated = original.copyWith(
        email: '<EMAIL>',
        ftueCompleted: true,
      );

      // Assert
      expect(updated.userId, 'user_original'); // Unchanged
      expect(updated.email, '<EMAIL>'); // Changed
      expect(updated.ftueCompleted, true); // Changed
      expect(updated.displayName, null); // Unchanged (was null)
    });
  });

  group('AppPreferences', () {
    test('should create from JSON with defaults', () {
      // Arrange
      final json = <String, dynamic>{};

      // Act
      final preferences = AppPreferences.fromJson(json);

      // Assert
      expect(preferences.masterVolume, 0.8);
      expect(preferences.musicEnabled, true);
      expect(preferences.sfxEnabled, true);
      expect(preferences.narrationLanguage, 'en-US');
    });

    test('should create from JSON with custom values', () {
      // Arrange
      final json = {
        'masterVolume': 0.5,
        'musicEnabled': false,
        'sfxEnabled': false,
        'narrationLanguage': 'es-ES',
      };

      // Act
      final preferences = AppPreferences.fromJson(json);

      // Assert
      expect(preferences.masterVolume, 0.5);
      expect(preferences.musicEnabled, false);
      expect(preferences.sfxEnabled, false);
      expect(preferences.narrationLanguage, 'es-ES');
    });

    test('should handle copyWith correctly', () {
      // Arrange
      const original = AppPreferences(
        masterVolume: 0.8,
        musicEnabled: true,
        sfxEnabled: true,
        narrationLanguage: 'en-US',
      );

      // Act
      final updated = original.copyWith(
        masterVolume: 0.5,
        musicEnabled: false,
      );

      // Assert
      expect(updated.masterVolume, 0.5); // Changed
      expect(updated.musicEnabled, false); // Changed
      expect(updated.sfxEnabled, true); // Unchanged
      expect(updated.narrationLanguage, 'en-US'); // Unchanged
    });
  });

  group('SubscriptionInfo', () {
    test('should create from JSON with defaults', () {
      // Arrange
      final json = <String, dynamic>{};

      // Act
      final subscription = SubscriptionInfo.fromJson(json);

      // Assert
      expect(subscription.tier, 'free');
      expect(subscription.status, 'active');
      expect(subscription.expiryDate, null);
      expect(subscription.platform, 'unknown');
      expect(subscription.originalTransactionId, null);
    });

    test('should determine premium status correctly', () {
      // Arrange
      const freeSub = SubscriptionInfo(tier: 'free');
      final activePremium = SubscriptionInfo(
        tier: 'premium_monthly',
        status: 'active',
        expiryDate: DateTime.now().add(const Duration(days: 30)),
      );
      final expiredPremium = SubscriptionInfo(
        tier: 'premium_monthly',
        status: 'active',
        expiryDate: DateTime.now().subtract(const Duration(days: 1)),
      );
      const cancelledPremium = SubscriptionInfo(
        tier: 'premium_monthly',
        status: 'cancelled',
      );

      // Act & Assert
      expect(freeSub.isPremium, false);
      expect(activePremium.isPremium, true);
      expect(expiredPremium.isPremium, false);
      expect(cancelledPremium.isPremium, false);
    });

    test('should handle copyWith correctly', () {
      // Arrange
      const original = SubscriptionInfo(
        tier: 'free',
        status: 'active',
        platform: 'ios',
      );

      // Act
      final updated = original.copyWith(
        tier: 'premium_monthly',
        originalTransactionId: 'txn_123',
      );

      // Assert
      expect(updated.tier, 'premium_monthly'); // Changed
      expect(updated.status, 'active'); // Unchanged
      expect(updated.platform, 'ios'); // Unchanged
      expect(updated.originalTransactionId, 'txn_123'); // Changed
    });
  });
}
