import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:choice_once_upon_a_time/features/story_library/data/story_repository.dart';
import 'package:choice_once_upon_a_time/core/services/asset_story_service.dart';
import 'package:choice_once_upon_a_time/core/services/firebase_storage_service.dart';
import 'package:choice_once_upon_a_time/core/services/zip_extraction_service.dart';
import 'package:choice_once_upon_a_time/core/storage/offline_storage_service_mobile.dart';
import 'package:choice_once_upon_a_time/models/story_model.dart';
import 'package:choice_once_upon_a_time/models/story_metadata_model.dart';
import 'package:choice_once_upon_a_time/models/scene_model.dart';
import 'package:choice_once_upon_a_time/models/text_segment_model.dart';
import 'package:choice_once_upon_a_time/models/rewards_model.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

// Generate mocks
@GenerateMocks([
  AssetStoryService,
  FirebaseStorageService,
  ZipExtractionService,
  OfflineStorageService,
  FirebaseFirestore,
])
import 'enhanced_story_repository_test.mocks.dart';

void main() {
  group('Enhanced StoryRepository', () {
    late StoryRepository repository;
    late MockAssetStoryService mockAssetStoryService;
    late MockFirebaseStorageService mockFirebaseStorageService;
    late MockZipExtractionService mockZipExtractionService;
    late MockOfflineStorageService mockOfflineStorageService;
    late MockFirebaseFirestore mockFirestore;

    setUp(() {
      mockAssetStoryService = MockAssetStoryService();
      mockFirebaseStorageService = MockFirebaseStorageService();
      mockZipExtractionService = MockZipExtractionService();
      mockOfflineStorageService = MockOfflineStorageService();
      mockFirestore = MockFirebaseFirestore();

      repository = StoryRepository(
        firestore: mockFirestore,
        offlineStorage: mockOfflineStorageService,
        firebaseStorage: mockFirebaseStorageService,
        zipExtraction: mockZipExtractionService,
        assetStoryService: mockAssetStoryService,
      );
    });

    group('Priority Loading', () {
      const testStoryId = 'test_story';
      const mockStory = StoryModel(
        id: testStoryId,
        title: 'Test Story',
        targetMoralValue: 'Kindness',
        targetAgeSubSegment: '5-7',
        scenes: [
          SceneModel(
            sceneId: 'scene1',
            narratorSegments: [
              TextSegmentModel(
                id: 'segment1',
                text: {'en-US': 'Test text'},
              ),
            ],
          ),
        ],
        narratorPersonaGuidance: 'warm',
        defaultLanguage: 'en-US',
      );

      test('should load from local storage first (Priority 1)', () async {
        // Arrange
        when(mockZipExtractionService.isStoryDownloaded(testStoryId))
            .thenAnswer((_) async => true);
        when(mockZipExtractionService.getStoryPath(testStoryId))
            .thenAnswer((_) async => '/local/path/test_story');

        // Mock file system would be complex, so we'll test the service call
        // In a real test, you'd mock the File operations

        // Act & Assert - This will fail at file reading, but we can verify the call order
        try {
          await repository.fetchStoryById(testStoryId);
        } catch (e) {
          // Expected to fail at file reading in test environment
        }

        // Verify local storage was checked first
        verify(mockZipExtractionService.isStoryDownloaded(testStoryId)).called(1);
      });

      test('should load from assets second (Priority 3)', () async {
        // Arrange
        when(mockZipExtractionService.isStoryDownloaded(testStoryId))
            .thenAnswer((_) async => false);
        when(mockOfflineStorageService.isStoryDownloaded(testStoryId, any))
            .thenAnswer((_) async => false);
        when(mockAssetStoryService.loadStoryFromAssets(testStoryId))
            .thenAnswer((_) async => mockStory);

        // Act
        final result = await repository.fetchStoryById(testStoryId);

        // Assert
        expect(result, isNotNull);
        expect(result.id, testStoryId);
        verify(mockAssetStoryService.loadStoryFromAssets(testStoryId)).called(1);
      });

      test('should check Firebase Storage when not found locally or in assets', () async {
        // Arrange
        when(mockZipExtractionService.isStoryDownloaded(testStoryId))
            .thenAnswer((_) async => false);
        when(mockOfflineStorageService.isStoryDownloaded(testStoryId, any))
            .thenAnswer((_) async => false);
        when(mockAssetStoryService.loadStoryFromAssets(testStoryId))
            .thenAnswer((_) async => null);
        when(mockFirebaseStorageService.storyExists(testStoryId))
            .thenAnswer((_) async => true);

        // Act & Assert
        expect(
          () => repository.fetchStoryById(testStoryId),
          throwsA(isA<StoryLoadException>()),
        );

        verify(mockFirebaseStorageService.storyExists(testStoryId)).called(1);
      });
    });

    group('Story Metadata Loading', () {
      test('should include asset-based stories in metadata list', () async {
        // Arrange
        final assetStoryIds = ['asset_story_1', 'asset_story_2'];
        const mockAssetStory = StoryModel(
          id: 'asset_story_1',
          title: 'Asset Story 1',
          targetMoralValue: 'Courage',
          targetAgeSubSegment: '6-8',
          scenes: [
            SceneModel(
              sceneId: 'scene1',
              narratorSegments: [
                TextSegmentModel(
                  id: 'segment1',
                  text: {'en-US': 'Asset story text'},
                ),
              ],
            ),
          ],
          narratorPersonaGuidance: 'gentle',
          defaultLanguage: 'en-US',
          rewards: RewardsModel(
            completion: 'Courage Badge',
            moralChoices: ['Brave Choice'],
          ),
        );

        when(mockAssetStoryService.getAvailableStoryIds())
            .thenAnswer((_) async => assetStoryIds);
        when(mockAssetStoryService.loadStoryFromAssets('asset_story_1'))
            .thenAnswer((_) async => mockAssetStory);
        when(mockAssetStoryService.loadStoryFromAssets('asset_story_2'))
            .thenAnswer((_) async => null); // Simulate loading failure

        // Mock Firestore to return empty
        when(mockFirestore.collection('stories')).thenReturn(
          MockCollectionReference(),
        );

        // Act
        final metadataList = await repository.fetchStoryMetadataList();

        // Assert
        expect(metadataList, isNotEmpty);
        
        // Should include the successfully loaded asset story
        final assetStoryMetadata = metadataList.firstWhere(
          (story) => story.id == 'asset_story_1',
          orElse: () => throw StateError('Asset story not found'),
        );
        
        expect(assetStoryMetadata.title['en-US'], 'Asset Story 1');
        expect(assetStoryMetadata.targetMoralValue, 'Courage');
        expect(assetStoryMetadata.dataSource, 'asset');
        expect(assetStoryMetadata.rewards, isNotNull);
        expect(assetStoryMetadata.rewards!.completion, 'Courage Badge');
      });
    });

    group('Download and Extraction', () {
      const testStoryId = 'downloadable_story';

      test('should download and extract story from Firebase Storage', () async {
        // Arrange
        when(mockFirebaseStorageService.storyExists(testStoryId))
            .thenAnswer((_) async => true);
        when(mockZipExtractionService.isStoryDownloaded(testStoryId))
            .thenAnswer((_) async => false);
        when(mockFirebaseStorageService.downloadStoryZip(testStoryId, onProgress: anyNamed('onProgress')))
            .thenAnswer((_) async => '/temp/story.zip');
        when(mockZipExtractionService.extractStoryZip('/temp/story.zip', testStoryId, onProgress: anyNamed('onProgress')))
            .thenAnswer((_) async => '/extracted/story');
        when(mockZipExtractionService.validateStoryStructure('/extracted/story'))
            .thenAnswer((_) async => true);

        // Act
        final result = await repository.downloadStoryFromFirebase(testStoryId);

        // Assert
        expect(result, true);
        verify(mockFirebaseStorageService.downloadStoryZip(testStoryId, onProgress: anyNamed('onProgress'))).called(1);
        verify(mockZipExtractionService.extractStoryZip('/temp/story.zip', testStoryId, onProgress: anyNamed('onProgress'))).called(1);
        verify(mockZipExtractionService.validateStoryStructure('/extracted/story')).called(1);
      });

      test('should handle download failure gracefully', () async {
        // Arrange
        when(mockFirebaseStorageService.storyExists(testStoryId))
            .thenAnswer((_) async => false);

        // Act & Assert
        expect(
          () => repository.downloadStoryFromFirebase(testStoryId),
          throwsA(isA<StoryLoadException>()),
        );
      });

      test('should skip download if story already exists locally', () async {
        // Arrange
        when(mockFirebaseStorageService.storyExists(testStoryId))
            .thenAnswer((_) async => true);
        when(mockZipExtractionService.isStoryDownloaded(testStoryId))
            .thenAnswer((_) async => true);

        // Act
        final result = await repository.downloadStoryFromFirebase(testStoryId);

        // Assert
        expect(result, true);
        verifyNever(mockFirebaseStorageService.downloadStoryZip(any, onProgress: anyNamed('onProgress')));
      });
    });

    group('Storage Information', () {
      const testStoryId = 'info_story';

      test('should provide comprehensive storage information', () async {
        // Arrange
        when(mockZipExtractionService.isStoryDownloaded(testStoryId))
            .thenAnswer((_) async => true);
        when(mockFirebaseStorageService.storyExists(testStoryId))
            .thenAnswer((_) async => true);
        when(mockZipExtractionService.getLocalStorySize(testStoryId))
            .thenAnswer((_) async => 5242880); // 5MB

        // Act
        final info = await repository.getStoryStorageInfo(testStoryId);

        // Assert
        expect(info['isLocallyAvailable'], true);
        expect(info['isAvailableForDownload'], true);
        expect(info['localSizeBytes'], 5242880);
        expect(info['localSizeMB'], 5);
      });
    });

    group('Local Story Management', () {
      const testStoryId = 'local_story';

      test('should delete local story successfully', () async {
        // Arrange
        when(mockZipExtractionService.deleteLocalStory(testStoryId))
            .thenAnswer((_) async => true);

        // Act
        final result = await repository.deleteLocalStory(testStoryId);

        // Assert
        expect(result, true);
        verify(mockZipExtractionService.deleteLocalStory(testStoryId)).called(1);
      });
    });
  });
}

// Mock classes for Firestore
class MockCollectionReference extends Mock implements CollectionReference<Map<String, dynamic>> {
  @override
  Future<QuerySnapshot<Map<String, dynamic>>> get() async {
    return MockQuerySnapshot();
  }
}

class MockQuerySnapshot extends Mock implements QuerySnapshot<Map<String, dynamic>> {
  @override
  List<QueryDocumentSnapshot<Map<String, dynamic>>> get docs => [];
}
