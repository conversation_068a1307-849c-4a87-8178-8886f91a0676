import 'dart:async';
import 'package:flutter_tts/flutter_tts.dart';
import 'package:choice_once_upon_a_time/core/utils/app_logger.dart';
import 'package:choice_once_upon_a_time/core/audio/tts_service_interface.dart';
import 'package:choice_once_upon_a_time/models/text_segment_model.dart';
import 'package:choice_once_upon_a_time/core/audio/emotion_cue_mapper_service.dart';

/// Unified TTS service that prevents audio channel conflicts
/// This service ensures only one FlutterTts instance is active at a time
class UnifiedTTSService implements TTSServiceInterface {
  static final UnifiedTTSService _instance = UnifiedTTSService._internal();
  factory UnifiedTTSService() => _instance;
  static UnifiedTTSService get instance => _instance;
  UnifiedTTSService._internal();

  FlutterTts? _flutterTts;
  bool _isInitialized = false;
  TTSState _currentState = TTSState.stopped;
  String? _currentLanguage;
  TTSSpeechParameters _currentParameters = const TTSSpeechParameters();

  // Stream controllers
  final StreamController<TTSState> _stateController = StreamController<TTSState>.broadcast();
  final StreamController<double> _progressController = StreamController<double>.broadcast();
  final StreamController<String> _wordBoundaryController = StreamController<String>.broadcast();

  // Current usage tracking
  String? _currentUser; // 'story', 'voice_guide', 'screen_narrator'
  
  @override
  Future<bool> initialize() async {
    if (_isInitialized) {
      AppLogger.debug('UnifiedTTS: Service already initialized (Instance: $hashCode)');
      return true;
    }

    try {
      AppLogger.info('UnifiedTTS: Initializing unified TTS service (Instance: $hashCode)');
      _flutterTts = FlutterTts();
      
      // Set up event handlers
      _flutterTts!.setStartHandler(() {
        AppLogger.debug('UnifiedTTS: Speech started (User: $_currentUser, Instance: $hashCode)');
        _updateState(TTSState.playing);
      });

      _flutterTts!.setCompletionHandler(() {
        AppLogger.debug('UnifiedTTS: Speech completed (User: $_currentUser, Instance: $hashCode)');
        _updateState(TTSState.stopped);
        _currentUser = null; // Release user lock
      });

      _flutterTts!.setErrorHandler((msg) {
        AppLogger.error('UnifiedTTS Error (User: $_currentUser, Instance: $hashCode): $msg');
        _updateState(TTSState.error);
        _currentUser = null; // Release user lock
      });

      _flutterTts!.setPauseHandler(() {
        AppLogger.debug('UnifiedTTS: Speech paused (User: $_currentUser, Instance: $hashCode)');
        _updateState(TTSState.paused);
      });

      _flutterTts!.setContinueHandler(() {
        AppLogger.debug('UnifiedTTS: Speech resumed (User: $_currentUser, Instance: $hashCode)');
        _updateState(TTSState.playing);
      });

      // Set up progress handler if available
      _flutterTts!.setProgressHandler((String text, int start, int end, String word) {
        _wordBoundaryController.add(word);
        // Calculate progress based on character position
        if (text.isNotEmpty) {
          final progress = end / text.length;
          _progressController.add(progress.clamp(0.0, 1.0));
        }
      });

      // Set default parameters
      await setLanguage('en-US');
      await setSpeechParameters(const TTSSpeechParameters());

      _isInitialized = true;
      _updateState(TTSState.stopped);
      AppLogger.info('UnifiedTTS: Service initialized successfully (Instance: $hashCode)');
      return true;
    } catch (e) {
      AppLogger.error('UnifiedTTS: Failed to initialize (Instance: $hashCode)', e);
      _isInitialized = false;
      _updateState(TTSState.error);
      return false;
    }
  }

  /// Request exclusive access to TTS for a specific user
  /// Returns true if access granted, false if another user is active
  bool requestAccess(String user) {
    if (_currentUser == null || _currentUser == user) {
      _currentUser = user;
      AppLogger.debug('UnifiedTTS: Access granted to $user');
      return true;
    }
    
    AppLogger.warning('UnifiedTTS: Access denied to $user, currently used by $_currentUser');
    return false;
  }

  /// Release access for a specific user
  void releaseAccess(String user) {
    if (_currentUser == user) {
      _currentUser = null;
      AppLogger.debug('UnifiedTTS: Access released by $user');
    }
  }

  /// Force stop and release access (for emergency situations)
  Future<void> forceStop() async {
    await stop();
    _currentUser = null;
    AppLogger.warning('UnifiedTTS: Force stopped and access released');
  }

  @override
  Future<bool> setLanguage(String languageCode) async {
    if (_flutterTts == null) return false;

    try {
      await _flutterTts!.setLanguage(languageCode);
      _currentLanguage = languageCode;
      AppLogger.debug('UnifiedTTS: Language set to $languageCode');
      return true;
    } catch (e) {
      AppLogger.error('UnifiedTTS: Error setting language to $languageCode', e);
      return false;
    }
  }

  @override
  Future<List<TTSVoice>> getAvailableVoices() async {
    if (_flutterTts == null) return [];

    try {
      final voices = await _flutterTts!.getVoices;
      return voices.map<TTSVoice>((voice) {
        return TTSVoice(
          id: voice['name'] ?? '',
          name: voice['name'] ?? '',
          language: voice['locale'] ?? '',
          gender: voice['gender'],
        );
      }).toList();
    } catch (e) {
      AppLogger.error('UnifiedTTS: Error getting voices', e);
      return [];
    }
  }

  @override
  Future<List<String>> getAvailableLanguages() async {
    if (_flutterTts == null) return [];

    try {
      final languages = await _flutterTts!.getLanguages;
      return languages.cast<String>();
    } catch (e) {
      AppLogger.error('UnifiedTTS: Error getting languages', e);
      return [];
    }
  }

  @override
  Future<bool> setVoice(String voiceId) async {
    if (_flutterTts == null) return false;

    try {
      await _flutterTts!.setVoice({'name': voiceId, 'locale': _currentLanguage ?? 'en-US'});
      AppLogger.debug('UnifiedTTS: Voice set to $voiceId');
      return true;
    } catch (e) {
      AppLogger.error('UnifiedTTS: Error setting voice to $voiceId', e);
      return false;
    }
  }

  @override
  Future<bool> setVoiceFromMap(Map<String, String> voice) async {
    if (_flutterTts == null) return false;

    try {
      await _flutterTts!.setVoice(voice);
      AppLogger.debug('UnifiedTTS: Voice set from map: $voice');
      return true;
    } catch (e) {
      AppLogger.error('UnifiedTTS: Error setting voice from map', e);
      return false;
    }
  }

  @override
  Future<void> setSpeechParameters(TTSSpeechParameters parameters) async {
    if (_flutterTts == null) return;

    try {
      await _flutterTts!.setSpeechRate(parameters.rate);
      await _flutterTts!.setPitch(parameters.pitch);
      await _flutterTts!.setVolume(parameters.volume);
      _currentParameters = parameters;
      AppLogger.debug('UnifiedTTS: Speech parameters updated - Rate: ${parameters.rate}, Pitch: ${parameters.pitch}, Volume: ${parameters.volume}');
    } catch (e) {
      AppLogger.error('UnifiedTTS: Error setting speech parameters', e);
    }
  }

  @override
  Future<bool> speakSegment(TextSegmentModel segment, String languageCode) async {
    if (_flutterTts == null) {
      await initialize();
      if (_flutterTts == null) return false;
    }

    try {
      _updateState(TTSState.loading);

      // Get the text for the specified language
      final text = segment.getLocalizedText(languageCode);
      
      // Apply emotion-based modulation
      final emotionParams = EmotionCueMapperService.getParametersForEmotion(segment.emotionCue);
      await setSpeechParameters(emotionParams);
      
      // Speak the text
      await _flutterTts!.speak(text);
      AppLogger.debug('UnifiedTTS: Speaking segment "${segment.id}" with emotion: ${segment.emotionCue} (User: $_currentUser)');
      return true;
    } catch (e) {
      AppLogger.error('UnifiedTTS: Error speaking segment', e);
      _updateState(TTSState.error);
      return false;
    }
  }

  @override
  Future<bool> speakText(String text, {String? emotionCue}) async {
    if (_flutterTts == null) {
      await initialize();
      if (_flutterTts == null) return false;
    }

    try {
      _updateState(TTSState.loading);

      // Apply emotion-based modulation if provided
      if (emotionCue != null) {
        final emotionParams = EmotionCueMapperService.getParametersForEmotion(emotionCue);
        await setSpeechParameters(emotionParams);
      }
      
      // Speak the text
      await _flutterTts!.speak(text);
      AppLogger.debug('UnifiedTTS: Speaking text with emotion: $emotionCue (User: $_currentUser)');
      return true;
    } catch (e) {
      AppLogger.error('UnifiedTTS: Error speaking text', e);
      _updateState(TTSState.error);
      return false;
    }
  }

  @override
  Future<bool> speakScreenIntroduction({required String text, required String emotionCue}) async {
    return await speakText(text, emotionCue: emotionCue);
  }

  @override
  Future<void> pause() async {
    if (_flutterTts != null && _currentState == TTSState.playing) {
      await _flutterTts!.pause();
      AppLogger.debug('UnifiedTTS: Paused (User: $_currentUser)');
    }
  }

  @override
  Future<void> resume() async {
    if (_flutterTts != null && _currentState == TTSState.paused) {
      await _flutterTts!.speak('');
      AppLogger.debug('UnifiedTTS: Resumed (User: $_currentUser)');
    }
  }

  @override
  Future<void> stop() async {
    if (_flutterTts != null) {
      await _flutterTts!.stop();
      AppLogger.debug('UnifiedTTS: Stopped (User: $_currentUser)');
    }
  }

  @override
  TTSState getCurrentState() => _currentState;

  @override
  bool get isSpeaking => _currentState == TTSState.playing;

  @override
  bool get isPaused => _currentState == TTSState.paused;

  @override
  bool get isAvailable => _isInitialized && _flutterTts != null;

  @override
  double? get speechProgress => null; // Implemented via stream

  @override
  Stream<TTSState> get stateStream => _stateController.stream;

  @override
  Stream<double> get progressStream => _progressController.stream;

  @override
  Stream<String> get wordBoundaryStream => _wordBoundaryController.stream;

  void _updateState(TTSState newState) {
    if (_currentState != newState) {
      _currentState = newState;
      _stateController.add(newState);
    }
  }

  @override
  Future<void> dispose() async {
    await stop();
    await _stateController.close();
    await _progressController.close();
    await _wordBoundaryController.close();
    _flutterTts = null;
    _isInitialized = false;
    _currentUser = null;
    AppLogger.info('UnifiedTTS: Service disposed');
  }
}
