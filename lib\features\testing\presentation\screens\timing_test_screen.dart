import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:choice_once_upon_a_time/core/testing/story_timing_test_runner.dart';
import 'package:choice_once_upon_a_time/core/services/story_timing_analyzer.dart';
import 'package:choice_once_upon_a_time/core/repositories/enhanced_story_repository.dart';
import 'package:choice_once_upon_a_time/core/services/story_download_service.dart';
import 'package:choice_once_upon_a_time/core/utils/app_logger.dart';
import 'package:choice_once_upon_a_time/app/providers/service_providers.dart';

/// Screen for running story timing analysis tests
class TimingTestScreen extends ConsumerStatefulWidget {
  const TimingTestScreen({super.key});

  @override
  ConsumerState<TimingTestScreen> createState() => _TimingTestScreenState();
}

class _TimingTestScreenState extends ConsumerState<TimingTestScreen> {
  bool _isRunningTests = false;
  String _testStatus = 'Ready to run timing tests';
  Map<String, StoryTimingTestResult> _testResults = {};
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Story Timing Analysis'),
        backgroundColor: Colors.blue[800],
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildTestControls(),
            const SizedBox(height: 24),
            _buildTestStatus(),
            const SizedBox(height: 24),
            _buildTestResults(),
          ],
        ),
      ),
    );
  }

  /// Build test control buttons
  Widget _buildTestControls() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Timing Analysis Tests',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 16),
            Text(
              'This will test story playback timing with the following configuration:',
              style: Theme.of(context).textTheme.bodyMedium,
            ),
            const SizedBox(height: 8),
            _buildConfigurationInfo(),
            const SizedBox(height: 16),
            Row(
              children: [
                ElevatedButton.icon(
                  onPressed: _isRunningTests ? null : _runTimingTests,
                  icon: _isRunningTests 
                      ? const SizedBox(
                          width: 16,
                          height: 16,
                          child: CircularProgressIndicator(strokeWidth: 2),
                        )
                      : const Icon(Icons.play_arrow),
                  label: Text(_isRunningTests ? 'Running Tests...' : 'Run Timing Tests'),
                ),
                const SizedBox(width: 16),
                ElevatedButton.icon(
                  onPressed: _testResults.isEmpty ? null : _clearResults,
                  icon: const Icon(Icons.clear),
                  label: const Text('Clear Results'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// Build configuration information
  Widget _buildConfigurationInfo() {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.grey[100],
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildConfigItem('Test Stories', StoryTimingTestRunner.testStoryIds.join(', ')),
          _buildConfigItem('TTS Speed', '${StoryTimingAnalyzer.childFriendlyTTSSpeed} (child-friendly)'),
          _buildConfigItem('Min Scene Display', '${StoryTimingAnalyzer.minimumSceneDisplayMs}ms'),
          _buildConfigItem('Sentence Pause', '${StoryTimingAnalyzer.sentencePauseMs}ms'),
          _buildConfigItem('Autoplay', StoryTimingTestRunner.enableAutoplay ? 'Enabled' : 'Disabled'),
        ],
      ),
    );
  }

  /// Build configuration item
  Widget _buildConfigItem(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        children: [
          SizedBox(
            width: 120,
            child: Text(
              '$label:',
              style: const TextStyle(fontWeight: FontWeight.w500),
            ),
          ),
          Text(value),
        ],
      ),
    );
  }

  /// Build test status display
  Widget _buildTestStatus() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Test Status',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                Icon(
                  _isRunningTests ? Icons.hourglass_empty : Icons.info,
                  color: _isRunningTests ? Colors.orange : Colors.blue,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    _testStatus,
                    style: TextStyle(
                      color: _isRunningTests ? Colors.orange : Colors.black87,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// Build test results display
  Widget _buildTestResults() {
    if (_testResults.isEmpty) {
      return const Card(
        child: Padding(
          padding: EdgeInsets.all(16.0),
          child: Text('No test results yet. Run timing tests to see results.'),
        ),
      );
    }

    return Expanded(
      child: Card(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Test Results',
                style: Theme.of(context).textTheme.titleMedium,
              ),
              const SizedBox(height: 16),
              Expanded(
                child: ListView.builder(
                  itemCount: _testResults.length,
                  itemBuilder: (context, index) {
                    final entry = _testResults.entries.elementAt(index);
                    return _buildTestResultCard(entry.key, entry.value);
                  },
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// Build individual test result card
  Widget _buildTestResultCard(String storyId, StoryTimingTestResult result) {
    final isSuccess = result.success;
    final report = result.timingReport;

    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: Padding(
        padding: const EdgeInsets.all(12.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  isSuccess ? Icons.check_circle : Icons.error,
                  color: isSuccess ? Colors.green : Colors.red,
                ),
                const SizedBox(width: 8),
                Text(
                  storyId,
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                  ),
                ),
                const Spacer(),
                Text(
                  '${result.testDuration}ms',
                  style: TextStyle(
                    color: Colors.grey[600],
                    fontSize: 12,
                  ),
                ),
              ],
            ),
            if (!isSuccess) ...[
              const SizedBox(height: 8),
              Text(
                'Error: ${result.error}',
                style: const TextStyle(color: Colors.red),
              ),
            ] else ...[
              const SizedBox(height: 8),
              _buildTimingMetrics(report),
            ],
          ],
        ),
      ),
    );
  }

  /// Build timing metrics display
  Widget _buildTimingMetrics(TimingReport report) {
    final complianceColor = report.childFriendlyCompliance >= 0.8 
        ? Colors.green 
        : report.childFriendlyCompliance >= 0.6 
            ? Colors.orange 
            : Colors.red;

    return Column(
      children: [
        Row(
          children: [
            Expanded(
              child: _buildMetricItem(
                'Scenes',
                '${report.totalScenes}',
                Icons.movie,
              ),
            ),
            Expanded(
              child: _buildMetricItem(
                'Total Time',
                '${(report.totalStoryTime / 1000).toStringAsFixed(1)}s',
                Icons.timer,
              ),
            ),
            Expanded(
              child: _buildMetricItem(
                'Avg Scene',
                '${(report.averageSceneTime / 1000).toStringAsFixed(1)}s',
                Icons.av_timer,
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        Row(
          children: [
            Expanded(
              child: _buildMetricItem(
                'Load Time',
                '${report.averageLoadTime}ms',
                Icons.download,
              ),
            ),
            Expanded(
              child: _buildMetricItem(
                'Narration',
                '${(report.averageNarrationTime / 1000).toStringAsFixed(1)}s',
                Icons.record_voice_over,
              ),
            ),
            Expanded(
              child: _buildMetricItem(
                'Compliance',
                '${(report.childFriendlyCompliance * 100).toStringAsFixed(0)}%',
                Icons.child_care,
                color: complianceColor,
              ),
            ),
          ],
        ),
      ],
    );
  }

  /// Build individual metric item
  Widget _buildMetricItem(String label, String value, IconData icon, {Color? color}) {
    return Column(
      children: [
        Icon(
          icon,
          size: 16,
          color: color ?? Colors.grey[600],
        ),
        const SizedBox(height: 4),
        Text(
          value,
          style: TextStyle(
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
        Text(
          label,
          style: TextStyle(
            fontSize: 10,
            color: Colors.grey[600],
          ),
        ),
      ],
    );
  }

  /// Run timing tests
  Future<void> _runTimingTests() async {
    setState(() {
      _isRunningTests = true;
      _testStatus = 'Initializing timing analysis...';
      _testResults.clear();
    });

    try {
      // Initialize services
      final timingAnalyzer = StoryTimingAnalyzer();
      final downloadService = StoryDownloadService();
      final storyRepository = EnhancedStoryRepository(downloadService: downloadService);
      
      setState(() {
        _testStatus = 'Running timing tests on ${StoryTimingTestRunner.testStoryIds.length} stories...';
      });

      // Create and run test runner
      final testRunner = StoryTimingTestRunner(
        timingAnalyzer: timingAnalyzer,
        storyRepository: storyRepository,
      );

      final results = await testRunner.runTimingTests();

      setState(() {
        _testResults = results;
        _testStatus = 'Timing tests completed. ${results.length} stories tested.';
        _isRunningTests = false;
      });

      // Show completion message
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Timing analysis completed for ${results.length} stories'),
            backgroundColor: Colors.green,
          ),
        );
      }

    } catch (e, stackTrace) {
      AppLogger.error('[TIMING_TEST_SCREEN] Error running timing tests', e, stackTrace);
      
      setState(() {
        _testStatus = 'Error running tests: $e';
        _isRunningTests = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error running timing tests: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// Clear test results
  void _clearResults() {
    setState(() {
      _testResults.clear();
      _testStatus = 'Ready to run timing tests';
    });
  }
}
