import 'package:choice_once_upon_a_time/models/story_metadata_model.dart';
import 'package:choice_once_upon_a_time/models/enhanced_story_model.dart';
import 'package:choice_once_upon_a_time/core/services/enhanced_story_service.dart';
import 'package:logger/logger.dart';

/// Exception thrown when enhanced story loading fails
class EnhancedStoryLoadException implements Exception {
  final String message;
  final String storyId;
  
  EnhancedStoryLoadException(this.message, this.storyId);
  
  @override
  String toString() => 'EnhancedStoryLoadException: $message (Story ID: $storyId)';
}

/// Repository for managing enhanced stories from assets
class EnhancedStoryRepository {
  static final Logger _logger = Logger();
  final EnhancedStoryService _enhancedStoryService;

  EnhancedStoryRepository({
    EnhancedStoryService? enhancedStoryService,
  }) : _enhancedStoryService = enhancedStoryService ?? EnhancedStoryService();

  /// Fetches all story metadata from assets
  Future<List<StoryMetadataModel>> fetchStoryMetadataList() async {
    try {
      _logger.i('[EnhancedStoryRepository] Fetching story metadata from enhanced assets');
      
      final stories = await _enhancedStoryService.getAllStoryMetadata();
      
      _logger.i('[EnhancedStoryRepository] Total enhanced stories available: ${stories.length}');
      return stories;
      
    } catch (e) {
      _logger.e('[EnhancedStoryRepository] Failed to fetch enhanced story metadata: $e');
      return [];
    }
  }

  /// Fetches a specific enhanced story by ID
  Future<EnhancedStoryModel> fetchEnhancedStoryById(String storyId) async {
    try {
      _logger.i('[EnhancedStoryRepository] Fetching enhanced story: $storyId');
      
      final story = await _enhancedStoryService.loadStory(storyId);
      
      if (story == null) {
        throw EnhancedStoryLoadException('Enhanced story not found in assets', storyId);
      }
      
      _logger.i('[EnhancedStoryRepository] Successfully loaded enhanced story: $storyId');
      return story;
      
    } catch (e) {
      _logger.e('[EnhancedStoryRepository] Failed to load enhanced story $storyId: $e');
      if (e is EnhancedStoryLoadException) {
        rethrow;
      }
      throw EnhancedStoryLoadException('Failed to load enhanced story from assets: $e', storyId);
    }
  }

  /// Fetches character profiles for a story
  Future<List<CharacterModel>> fetchCharacterProfiles(String storyId) async {
    try {
      _logger.i('[EnhancedStoryRepository] Fetching character profiles for story: $storyId');
      
      final characters = await _enhancedStoryService.getCharacterProfiles(storyId);
      
      _logger.i('[EnhancedStoryRepository] Found ${characters.length} characters for story: $storyId');
      return characters;
      
    } catch (e) {
      _logger.e('[EnhancedStoryRepository] Failed to fetch character profiles for $storyId: $e');
      return [];
    }
  }

  /// Fetches story setup information
  Future<StorySetupModel?> fetchStorySetup(String storyId) async {
    try {
      _logger.i('[EnhancedStoryRepository] Fetching story setup for: $storyId');
      
      final setup = await _enhancedStoryService.getStorySetup(storyId);
      
      if (setup != null) {
        _logger.i('[EnhancedStoryRepository] Successfully fetched story setup for: $storyId');
      } else {
        _logger.w('[EnhancedStoryRepository] No story setup found for: $storyId');
      }
      
      return setup;
      
    } catch (e) {
      _logger.e('[EnhancedStoryRepository] Failed to fetch story setup for $storyId: $e');
      return null;
    }
  }

  /// Fetches narrator profile for a story
  Future<NarratorProfileModel?> fetchNarratorProfile(String storyId) async {
    try {
      _logger.i('[EnhancedStoryRepository] Fetching narrator profile for: $storyId');
      
      final profile = await _enhancedStoryService.getNarratorProfile(storyId);
      
      if (profile != null) {
        _logger.i('[EnhancedStoryRepository] Successfully fetched narrator profile for: $storyId');
      } else {
        _logger.w('[EnhancedStoryRepository] No narrator profile found for: $storyId');
      }
      
      return profile;
      
    } catch (e) {
      _logger.e('[EnhancedStoryRepository] Failed to fetch narrator profile for $storyId: $e');
      return null;
    }
  }

  /// Fetches post-story content
  Future<PostStoryModel?> fetchPostStoryContent(String storyId) async {
    try {
      _logger.i('[EnhancedStoryRepository] Fetching post-story content for: $storyId');
      
      final postStory = await _enhancedStoryService.getPostStoryContent(storyId);
      
      if (postStory != null) {
        _logger.i('[EnhancedStoryRepository] Successfully fetched post-story content for: $storyId');
      } else {
        _logger.w('[EnhancedStoryRepository] No post-story content found for: $storyId');
      }
      
      return postStory;
      
    } catch (e) {
      _logger.e('[EnhancedStoryRepository] Failed to fetch post-story content for $storyId: $e');
      return null;
    }
  }

  /// Scans for available stories
  Future<List<String>> scanAvailableStories() async {
    try {
      _logger.i('[EnhancedStoryRepository] Scanning for available enhanced stories');
      
      final storyIds = await _enhancedStoryService.scanAvailableStories();
      
      _logger.i('[EnhancedStoryRepository] Found ${storyIds.length} available enhanced stories');
      return storyIds;
      
    } catch (e) {
      _logger.e('[EnhancedStoryRepository] Failed to scan available stories: $e');
      return [];
    }
  }

  /// Checks if an enhanced story exists
  Future<bool> enhancedStoryExists(String storyId) async {
    try {
      return await _enhancedStoryService.storyExists(storyId);
    } catch (e) {
      return false;
    }
  }

  /// Validates story assets
  Future<bool> validateStoryAssets(String storyId) async {
    try {
      _logger.i('[EnhancedStoryRepository] Validating assets for story: $storyId');
      
      final story = await _enhancedStoryService.loadStory(storyId);
      if (story == null) {
        return false;
      }
      
      // Check if cover image path is valid
      final coverImagePath = story.coverImagePath;
      _logger.d('[EnhancedStoryRepository] Cover image path: $coverImagePath');
      
      // Check if scene images are valid
      for (final scene in story.scenes) {
        final imagePath = scene.getImagePath(storyId);
        _logger.d('[EnhancedStoryRepository] Scene ${scene.id} image path: $imagePath');
      }
      
      // Check character images
      for (final character in story.characters) {
        final imagePath = character.getImagePath(storyId);
        _logger.d('[EnhancedStoryRepository] Character ${character.name} image path: $imagePath');
      }
      
      _logger.i('[EnhancedStoryRepository] Asset validation completed for story: $storyId');
      return true;
      
    } catch (e) {
      _logger.e('[EnhancedStoryRepository] Failed to validate assets for $storyId: $e');
      return false;
    }
  }

  /// Clears all caches
  void clearCache() {
    _enhancedStoryService.clearCache();
    _logger.i('[EnhancedStoryRepository] Cache cleared');
  }

  /// Gets cache statistics
  Map<String, dynamic> getCacheStats() {
    final serviceStats = _enhancedStoryService.getCacheStats();
    return {
      'repository': 'EnhancedStoryRepository',
      'source': 'enhanced_assets',
      ...serviceStats,
    };
  }
}
