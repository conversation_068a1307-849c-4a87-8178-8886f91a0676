import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:choice_once_upon_a_time/features/story_player/presentation/widgets/story_narration_widget.dart';
import 'package:choice_once_upon_a_time/core/services/story_narration_service_interface.dart';
import 'package:choice_once_upon_a_time/models/narration_models.dart';
import 'dart:async';

// Mock implementation for testing
class MockStoryNarrationService implements IStoryNarrationService {
  final StreamController<NarrationState> _stateController = StreamController<NarrationState>.broadcast();
  final StreamController<NarrationProgress> _progressController = StreamController<NarrationProgress>.broadcast();
  final StreamController<WordHighlight> _wordHighlightController = StreamController<WordHighlight>.broadcast();

  NarrationState _currentState = NarrationState.idle;
  NarrationConfig _currentConfig = NarrationConfig.defaultConfig;
  bool _isInitialized = false;

  @override
  Stream<NarrationState> get stateStream => _stateController.stream;

  @override
  Stream<NarrationProgress> get progressStream => _progressController.stream;

  @override
  Stream<WordHighlight> get wordHighlightStream => _wordHighlightController.stream;

  @override
  NarrationState get currentState => _currentState;

  @override
  NarrationConfig get currentConfig => _currentConfig;

  @override
  bool get isInitialized => _isInitialized;

  @override
  bool get isNarrating => _currentState.status == NarrationStatus.playing;

  @override
  bool get isPaused => _currentState.status == NarrationStatus.paused;

  @override
  Future<void> initialize({NarrationConfig? config}) async {
    _isInitialized = true;
    if (config != null) {
      _currentConfig = config;
    }
  }

  @override
  Future<void> configure(NarrationConfig config) async {
    _currentConfig = config;
  }

  @override
  Future<void> narrateText(String text, {String? emotionCue, String? storyId, String? sceneId}) async {
    _updateState(_currentState.copyWith(
      status: NarrationStatus.playing,
      currentText: text,
      progress: 0.0,
    ));
    
    // Simulate word highlighting
    final words = text.split(' ');
    for (int i = 0; i < words.length; i++) {
      await Future.delayed(const Duration(milliseconds: 100));
      _wordHighlightController.add(WordHighlight(
        startIndex: i,
        endIndex: i + words[i].length,
        word: words[i],
        startTimeMs: i * 100,
        endTimeMs: (i + 1) * 100,
        isActive: true,
        sentenceIndex: 0,
      ));
      
      _updateState(_currentState.copyWith(
        currentWordIndex: i,
        progress: (i + 1) / words.length,
      ));
    }
    
    _updateState(_currentState.copyWith(status: NarrationStatus.completed));
  }

  @override
  Future<void> play() async {
    _updateState(_currentState.copyWith(status: NarrationStatus.playing));
  }

  @override
  Future<void> pause() async {
    _updateState(_currentState.copyWith(status: NarrationStatus.paused));
  }

  @override
  Future<void> stop() async {
    _updateState(_currentState.copyWith(
      status: NarrationStatus.idle,
      currentText: null,
      currentWordIndex: 0,
      progress: 0.0,
    ));
  }

  void _updateState(NarrationState newState) {
    _currentState = newState;
    _stateController.add(newState);
  }

  // Implement other required methods with basic functionality
  @override
  Future<void> narrateScene(scene, {String? storyId}) async {}

  @override
  Future<void> skipToNextSentence() async {}

  @override
  Future<void> skipToPreviousSentence() async {}

  @override
  Future<void> seekToWord(int wordIndex) async {}

  @override
  Future<void> seekToSentence(int sentenceIndex) async {}

  @override
  Future<void> replayCurrentSentence() async {}

  @override
  Future<void> replayScene() async {}

  @override
  Future<void> setSpeechRate(double rate) async {}

  @override
  Future<void> setSpeechPitch(double pitch) async {}

  @override
  Future<void> setSpeechVolume(double volume) async {}

  @override
  Future<void> setAutoProgression(bool enabled) async {}

  @override
  Future<void> setWordHighlighting(bool enabled) async {}

  @override
  NarrationProgress? getCurrentProgress() => null;

  @override
  Future<void> saveProgress() async {}

  @override
  Future<void> loadProgress(String storyId, String sceneId) async {}

  @override
  Future<void> clearProgress(String storyId, String sceneId) async {}

  @override
  Future<void> dispose() async {
    await _stateController.close();
    await _progressController.close();
    await _wordHighlightController.close();
  }
}

void main() {
  group('StoryNarrationWidget', () {
    late MockStoryNarrationService mockNarrationService;

    setUp(() {
      mockNarrationService = MockStoryNarrationService();
    });

    tearDown(() async {
      await mockNarrationService.dispose();
    });

    testWidgets('should display text correctly', (WidgetTester tester) async {
      // Arrange
      const testText = 'Once upon a time, there was a brave knight.';
      
      // Act
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: StoryNarrationWidget(
              narrationService: mockNarrationService,
              text: testText,
              showControls: true,
            ),
          ),
        ),
      );

      // Assert
      expect(find.text(testText), findsOneWidget);
    });

    testWidgets('should show narration controls when enabled', (WidgetTester tester) async {
      // Arrange
      const testText = 'Test narration text.';
      
      // Act
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: StoryNarrationWidget(
              narrationService: mockNarrationService,
              text: testText,
              showControls: true,
            ),
          ),
        ),
      );

      // Assert
      expect(find.byIcon(Icons.play_arrow), findsOneWidget);
      expect(find.byIcon(Icons.stop), findsOneWidget);
      expect(find.byIcon(Icons.replay), findsOneWidget);
    });

    testWidgets('should hide narration controls when disabled', (WidgetTester tester) async {
      // Arrange
      const testText = 'Test narration text.';
      
      // Act
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: StoryNarrationWidget(
              narrationService: mockNarrationService,
              text: testText,
              showControls: false,
            ),
          ),
        ),
      );

      // Assert
      expect(find.byIcon(Icons.play_arrow), findsNothing);
      expect(find.byIcon(Icons.stop), findsNothing);
      expect(find.byIcon(Icons.replay), findsNothing);
    });

    testWidgets('should start narration automatically when autoStart is true', (WidgetTester tester) async {
      // Arrange
      const testText = 'Auto start test text.';
      bool narrationStarted = false;
      
      // Act
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: StoryNarrationWidget(
              narrationService: mockNarrationService,
              text: testText,
              autoStart: true,
              onNarrationStart: () {
                narrationStarted = true;
              },
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // Assert
      expect(narrationStarted, true);
    });

    testWidgets('should call onNarrationComplete when narration finishes', (WidgetTester tester) async {
      // Arrange
      const testText = 'Complete test text.';
      bool narrationCompleted = false;
      
      // Act
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: StoryNarrationWidget(
              narrationService: mockNarrationService,
              text: testText,
              autoStart: true,
              onNarrationComplete: () {
                narrationCompleted = true;
              },
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();
      
      // Wait for narration to complete
      await tester.pump(const Duration(seconds: 1));

      // Assert
      expect(narrationCompleted, true);
    });

    testWidgets('should toggle play/pause when button is pressed', (WidgetTester tester) async {
      // Arrange
      const testText = 'Play pause test text.';
      
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: StoryNarrationWidget(
              narrationService: mockNarrationService,
              text: testText,
              showControls: true,
            ),
          ),
        ),
      );

      // Act - Press play button
      await tester.tap(find.byIcon(Icons.play_arrow));
      await tester.pump();

      // Assert - Should show pause icon
      expect(find.byIcon(Icons.pause), findsOneWidget);
      expect(find.byIcon(Icons.play_arrow), findsNothing);

      // Act - Press pause button
      await tester.tap(find.byIcon(Icons.pause));
      await tester.pump();

      // Assert - Should show play icon again
      expect(find.byIcon(Icons.play_arrow), findsOneWidget);
      expect(find.byIcon(Icons.pause), findsNothing);
    });

    testWidgets('should show progress indicator', (WidgetTester tester) async {
      // Arrange
      const testText = 'Progress test text.';
      
      // Act
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: StoryNarrationWidget(
              narrationService: mockNarrationService,
              text: testText,
              showControls: true,
            ),
          ),
        ),
      );

      // Assert
      expect(find.text('Progress'), findsOneWidget);
      expect(find.byType(LinearProgressIndicator), findsOneWidget);
    });

    testWidgets('should update progress during narration', (WidgetTester tester) async {
      // Arrange
      const testText = 'Progress update test.';
      
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: StoryNarrationWidget(
              narrationService: mockNarrationService,
              text: testText,
              autoStart: true,
              showControls: true,
            ),
          ),
        ),
      );

      // Act
      await tester.pumpAndSettle();
      
      // Wait for some progress
      await tester.pump(const Duration(milliseconds: 200));

      // Assert
      expect(find.textContaining('%'), findsOneWidget);
    });

    testWidgets('should handle emotion cue correctly', (WidgetTester tester) async {
      // Arrange
      const testText = 'Emotion test text.';
      const emotionCue = 'happy';
      
      // Act
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: StoryNarrationWidget(
              narrationService: mockNarrationService,
              text: testText,
              emotionCue: emotionCue,
              autoStart: true,
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // Assert - Widget should render without errors
      expect(find.text(testText), findsOneWidget);
    });
  });
}
