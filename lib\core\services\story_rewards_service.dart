import 'dart:async';
import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:logger/logger.dart';

/// Service for managing story rewards and achievements
class StoryRewardsService {
  static final Logger _logger = Logger();
  static StoryRewardsService? _instance;
  
  SharedPreferences? _prefs;
  
  // Storage keys
  static const String _completedStoriesKey = 'completed_stories';
  static const String _earnedRewardsKey = 'earned_rewards';
  static const String _choiceRewardsKey = 'choice_rewards';
  
  // Current state
  Set<String> _completedStories = {};
  Map<String, List<String>> _earnedRewards = {};
  Map<String, int> _choiceRewards = {};
  
  // Stream controllers for rewards updates
  final StreamController<Set<String>> _completedStoriesController = StreamController<Set<String>>.broadcast();
  final StreamController<Map<String, List<String>>> _earnedRewardsController = StreamController<Map<String, List<String>>>.broadcast();
  final StreamController<RewardEarned> _newRewardController = StreamController<RewardEarned>.broadcast();
  
  // Private constructor
  StoryRewardsService._();
  
  /// Get singleton instance
  static StoryRewardsService get instance {
    _instance ??= StoryRewardsService._();
    return _instance!;
  }
  
  // Getters for streams
  Stream<Set<String>> get completedStoriesStream => _completedStoriesController.stream;
  Stream<Map<String, List<String>>> get earnedRewardsStream => _earnedRewardsController.stream;
  Stream<RewardEarned> get newRewardStream => _newRewardController.stream;
  
  // Getters for current state
  Set<String> get completedStories => Set.from(_completedStories);
  Map<String, List<String>> get earnedRewards => Map.from(_earnedRewards);
  Map<String, int> get choiceRewards => Map.from(_choiceRewards);

  /// Initialize the rewards service
  Future<void> initialize() async {
    try {
      _logger.i('[StoryRewardsService] Initializing rewards service');
      
      _prefs = await SharedPreferences.getInstance();
      await _loadRewards();
      
      _logger.i('[StoryRewardsService] Rewards service initialized successfully');
      
    } catch (e) {
      _logger.e('[StoryRewardsService] Failed to initialize rewards service: $e');
    }
  }

  /// Load rewards from SharedPreferences
  Future<void> _loadRewards() async {
    if (_prefs == null) return;
    
    try {
      // Load completed stories
      final completedStoriesJson = _prefs!.getStringList(_completedStoriesKey) ?? [];
      _completedStories = completedStoriesJson.toSet();
      
      // Load earned rewards
      final earnedRewardsJson = _prefs!.getString(_earnedRewardsKey);
      if (earnedRewardsJson != null) {
        final Map<String, dynamic> decoded = jsonDecode(earnedRewardsJson);
        _earnedRewards = decoded.map((key, value) => MapEntry(key, List<String>.from(value)));
      }
      
      // Load choice rewards
      final choiceRewardsJson = _prefs!.getString(_choiceRewardsKey);
      if (choiceRewardsJson != null) {
        final Map<String, dynamic> decoded = jsonDecode(choiceRewardsJson);
        _choiceRewards = decoded.map((key, value) => MapEntry(key, value as int));
      }
      
      _logger.d('[StoryRewardsService] Loaded ${_completedStories.length} completed stories, ${_earnedRewards.length} reward categories');
      
    } catch (e) {
      _logger.e('[StoryRewardsService] Failed to load rewards: $e');
    }
  }

  /// Mark a story as completed and award completion reward
  Future<void> completeStory(String storyId, String storyTitle, String moralValue) async {
    if (_completedStories.contains(storyId)) {
      _logger.d('[StoryRewardsService] Story already completed: $storyId');
      return;
    }
    
    _logger.i('[StoryRewardsService] Completing story: $storyId');
    
    // Mark story as completed
    _completedStories.add(storyId);
    _completedStoriesController.add(Set.from(_completedStories));
    
    // Award completion reward
    await _awardReward(storyId, 'completion', 'Story Completed!', 'You finished "$storyTitle"');
    
    // Award moral value reward
    await _awardReward(storyId, 'moral_$moralValue', '$moralValue Champion!', 'You learned about $moralValue');
    
    // Save to storage
    await _saveCompletedStories();
    
    _logger.i('[StoryRewardsService] Story completed and rewards awarded: $storyId');
  }

  /// Award a reward for making a good choice
  Future<void> awardChoiceReward(String storyId, String choiceId, String choiceText, String moralValue) async {
    _logger.i('[StoryRewardsService] Awarding choice reward for story: $storyId, choice: $choiceId');
    
    // Track choice rewards count
    final key = '${storyId}_choices';
    _choiceRewards[key] = (_choiceRewards[key] ?? 0) + 1;
    
    // Award choice reward
    await _awardReward(storyId, 'choice_$choiceId', 'Great Choice!', 'You chose: "$choiceText"');
    
    // Award moral choice reward if applicable
    if (moralValue.isNotEmpty) {
      await _awardReward(storyId, 'moral_choice_$moralValue', '$moralValue Choice!', 'You made a $moralValue choice');
    }
    
    // Save to storage
    await _saveChoiceRewards();
    
    _logger.i('[StoryRewardsService] Choice reward awarded');
  }

  /// Award a specific reward
  Future<void> _awardReward(String storyId, String rewardType, String title, String description) async {
    // Add to earned rewards
    if (!_earnedRewards.containsKey(storyId)) {
      _earnedRewards[storyId] = [];
    }
    
    final rewardId = '${rewardType}_${DateTime.now().millisecondsSinceEpoch}';
    _earnedRewards[storyId]!.add(rewardId);
    
    // Notify about new reward
    final reward = RewardEarned(
      id: rewardId,
      storyId: storyId,
      type: rewardType,
      title: title,
      description: description,
      earnedAt: DateTime.now(),
    );
    
    _newRewardController.add(reward);
    _earnedRewardsController.add(Map.from(_earnedRewards));
    
    // Save to storage
    await _saveEarnedRewards();
    
    _logger.d('[StoryRewardsService] Reward awarded: $title');
  }

  /// Check if a story is completed
  bool isStoryCompleted(String storyId) {
    return _completedStories.contains(storyId);
  }

  /// Get rewards for a specific story
  List<String> getStoryRewards(String storyId) {
    return _earnedRewards[storyId] ?? [];
  }

  /// Get total number of completed stories
  int getTotalCompletedStories() {
    return _completedStories.length;
  }

  /// Get total number of rewards earned
  int getTotalRewardsEarned() {
    return _earnedRewards.values.fold(0, (sum, rewards) => sum + rewards.length);
  }

  /// Get choice rewards count for a story
  int getChoiceRewardsCount(String storyId) {
    return _choiceRewards['${storyId}_choices'] ?? 0;
  }

  /// Save completed stories to SharedPreferences
  Future<void> _saveCompletedStories() async {
    try {
      await _prefs?.setStringList(_completedStoriesKey, _completedStories.toList());
    } catch (e) {
      _logger.e('[StoryRewardsService] Failed to save completed stories: $e');
    }
  }

  /// Save earned rewards to SharedPreferences
  Future<void> _saveEarnedRewards() async {
    try {
      final encoded = jsonEncode(_earnedRewards);
      await _prefs?.setString(_earnedRewardsKey, encoded);
    } catch (e) {
      _logger.e('[StoryRewardsService] Failed to save earned rewards: $e');
    }
  }

  /// Save choice rewards to SharedPreferences
  Future<void> _saveChoiceRewards() async {
    try {
      final encoded = jsonEncode(_choiceRewards);
      await _prefs?.setString(_choiceRewardsKey, encoded);
    } catch (e) {
      _logger.e('[StoryRewardsService] Failed to save choice rewards: $e');
    }
  }

  /// Reset all rewards (for testing or reset functionality)
  Future<void> resetAllRewards() async {
    _logger.i('[StoryRewardsService] Resetting all rewards');
    
    _completedStories.clear();
    _earnedRewards.clear();
    _choiceRewards.clear();
    
    await _prefs?.remove(_completedStoriesKey);
    await _prefs?.remove(_earnedRewardsKey);
    await _prefs?.remove(_choiceRewardsKey);
    
    _completedStoriesController.add(Set.from(_completedStories));
    _earnedRewardsController.add(Map.from(_earnedRewards));
    
    _logger.i('[StoryRewardsService] All rewards reset');
  }

  /// Get rewards statistics
  Map<String, dynamic> getRewardsStats() {
    return {
      'totalCompletedStories': getTotalCompletedStories(),
      'totalRewardsEarned': getTotalRewardsEarned(),
      'completedStories': _completedStories.toList(),
      'rewardsByStory': _earnedRewards.map((key, value) => MapEntry(key, value.length)),
    };
  }

  /// Dispose of resources
  void dispose() {
    _completedStoriesController.close();
    _earnedRewardsController.close();
    _newRewardController.close();
    _logger.i('[StoryRewardsService] Service disposed');
  }
}

/// Represents a reward that has been earned
class RewardEarned {
  final String id;
  final String storyId;
  final String type;
  final String title;
  final String description;
  final DateTime earnedAt;

  RewardEarned({
    required this.id,
    required this.storyId,
    required this.type,
    required this.title,
    required this.description,
    required this.earnedAt,
  });

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'storyId': storyId,
      'type': type,
      'title': title,
      'description': description,
      'earnedAt': earnedAt.toIso8601String(),
    };
  }

  factory RewardEarned.fromJson(Map<String, dynamic> json) {
    return RewardEarned(
      id: json['id'],
      storyId: json['storyId'],
      type: json['type'],
      title: json['title'],
      description: json['description'],
      earnedAt: DateTime.parse(json['earnedAt']),
    );
  }
}
