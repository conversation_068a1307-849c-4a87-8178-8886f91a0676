import 'package:flutter_test/flutter_test.dart';
import 'package:choice_once_upon_a_time/models/rewards_model.dart';

void main() {
  group('RewardsModel', () {
    test('should create from JSON correctly', () {
      // Arrange
      final json = {
        'completion': 'Unity Badge',
        'moralChoices': ['Sharing Star', 'Community Medal'],
      };

      // Act
      final model = RewardsModel.fromJson(json);

      // Assert
      expect(model.completion, 'Unity Badge');
      expect(model.moralChoices, ['Sharing Star', 'Community Medal']);
    });

    test('should handle null completion', () {
      // Arrange
      final json = {
        'moralChoices': ['Sharing Star'],
      };

      // Act
      final model = RewardsModel.fromJson(json);

      // Assert
      expect(model.completion, null);
      expect(model.moralChoices, ['Sharing Star']);
    });

    test('should handle empty moral choices', () {
      // Arrange
      final json = {
        'completion': 'Unity Badge',
      };

      // Act
      final model = RewardsModel.fromJson(json);

      // Assert
      expect(model.completion, 'Unity Badge');
      expect(model.moralChoices, isEmpty);
    });

    test('should convert to JSON correctly', () {
      // Arrange
      const model = RewardsModel(
        completion: 'Unity Badge',
        moralChoices: ['Sharing Star', 'Community Medal'],
      );

      // Act
      final json = model.toJson();

      // Assert
      expect(json['completion'], 'Unity Badge');
      expect(json['moralChoices'], ['Sharing Star', 'Community Medal']);
    });

    test('should support copyWith', () {
      // Arrange
      const original = RewardsModel(
        completion: 'Unity Badge',
        moralChoices: ['Sharing Star'],
      );

      // Act
      final updated = original.copyWith(
        moralChoices: ['Sharing Star', 'Community Medal'],
      );

      // Assert
      expect(updated.completion, 'Unity Badge');
      expect(updated.moralChoices, ['Sharing Star', 'Community Medal']);
    });

    test('should support equality comparison', () {
      // Arrange
      const model1 = RewardsModel(
        completion: 'Unity Badge',
        moralChoices: ['Sharing Star'],
      );
      const model2 = RewardsModel(
        completion: 'Unity Badge',
        moralChoices: ['Sharing Star'],
      );
      const model3 = RewardsModel(
        completion: 'Different Badge',
        moralChoices: ['Sharing Star'],
      );

      // Assert
      expect(model1, equals(model2));
      expect(model1, isNot(equals(model3)));
    });
  });

  group('EarnedReward', () {
    test('should create from JSON correctly', () {
      // Arrange
      final json = {
        'rewardId': 'Unity Badge',
        'rewardType': 'completion',
        'earnedAt': '2024-01-01T12:00:00.000Z',
        'storyId': 'test_story',
        'sceneId': 'scene_1',
      };

      // Act
      final model = EarnedReward.fromJson(json);

      // Assert
      expect(model.rewardId, 'Unity Badge');
      expect(model.rewardType, 'completion');
      expect(model.earnedAt, DateTime.parse('2024-01-01T12:00:00.000Z'));
      expect(model.storyId, 'test_story');
      expect(model.sceneId, 'scene_1');
    });

    test('should handle null sceneId', () {
      // Arrange
      final json = {
        'rewardId': 'Unity Badge',
        'rewardType': 'completion',
        'earnedAt': '2024-01-01T12:00:00.000Z',
        'storyId': 'test_story',
      };

      // Act
      final model = EarnedReward.fromJson(json);

      // Assert
      expect(model.rewardId, 'Unity Badge');
      expect(model.rewardType, 'completion');
      expect(model.storyId, 'test_story');
      expect(model.sceneId, null);
    });

    test('should convert to JSON correctly', () {
      // Arrange
      final earnedAt = DateTime.parse('2024-01-01T12:00:00.000Z');
      final model = EarnedReward(
        rewardId: 'Unity Badge',
        rewardType: 'completion',
        earnedAt: earnedAt,
        storyId: 'test_story',
        sceneId: 'scene_1',
      );

      // Act
      final json = model.toJson();

      // Assert
      expect(json['rewardId'], 'Unity Badge');
      expect(json['rewardType'], 'completion');
      expect(json['earnedAt'], '2024-01-01T12:00:00.000Z');
      expect(json['storyId'], 'test_story');
      expect(json['sceneId'], 'scene_1');
    });

    test('should support copyWith', () {
      // Arrange
      final earnedAt = DateTime.parse('2024-01-01T12:00:00.000Z');
      final original = EarnedReward(
        rewardId: 'Unity Badge',
        rewardType: 'completion',
        earnedAt: earnedAt,
        storyId: 'test_story',
      );

      // Act
      final updated = original.copyWith(sceneId: 'scene_1');

      // Assert
      expect(updated.rewardId, 'Unity Badge');
      expect(updated.rewardType, 'completion');
      expect(updated.earnedAt, earnedAt);
      expect(updated.storyId, 'test_story');
      expect(updated.sceneId, 'scene_1');
    });

    test('should support equality comparison', () {
      // Arrange
      final earnedAt = DateTime.parse('2024-01-01T12:00:00.000Z');
      final model1 = EarnedReward(
        rewardId: 'Unity Badge',
        rewardType: 'completion',
        earnedAt: earnedAt,
        storyId: 'test_story',
      );
      final model2 = EarnedReward(
        rewardId: 'Unity Badge',
        rewardType: 'completion',
        earnedAt: earnedAt,
        storyId: 'test_story',
      );
      final model3 = EarnedReward(
        rewardId: 'Different Badge',
        rewardType: 'completion',
        earnedAt: earnedAt,
        storyId: 'test_story',
      );

      // Assert
      expect(model1, equals(model2));
      expect(model1, isNot(equals(model3)));
    });
  });
}
