import 'package:flutter/material.dart';

/// A widget to display an animated scene.
///
/// This widget is a placeholder for a more complex animation that will be
/// implemented later. For now, it displays a static representation of the scene.
class AnimatedSceneWidget extends StatelessWidget {
  const AnimatedSceneWidget({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final screenSize = MediaQuery.of(context).size;

    // Responsive icon sizes based on screen width
    final iconSize = screenSize.width < 600 ? 40.0 : 50.0;
    final starSize = screenSize.width < 600 ? 16.0 : 20.0;
    final spacing = screenSize.width < 600 ? 20.0 : 40.0;

    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            theme.colorScheme.primary.withValues(alpha: 0.8),
            theme.colorScheme.secondary.withValues(alpha: 0.6),
          ],
        ),
      ),
      child: Center(
        child: Padding(
          padding: EdgeInsets.symmetric(
            horizontal: screenSize.width * 0.05,
            vertical: 20.0,
          ),
          child: Wrap(
            alignment: WrapAlignment.center,
            crossAxisAlignment: WrapCrossAlignment.center,
            spacing: spacing,
            children: [
              Icon(
                Icons.star,
                color: theme.colorScheme.tertiary,
                size: starSize,
              ),
              Icon(
                Icons.person,
                color: theme.colorScheme.onPrimary,
                size: iconSize,
              ),
              Icon(
                Icons.person_2,
                color: theme.colorScheme.onPrimary,
                size: iconSize,
              ),
              Icon(
                Icons.star,
                color: theme.colorScheme.tertiary,
                size: starSize,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
