# AI Agent Instructions: Debug Story Library, Complete Sprint 2 (Core Story Player), and Autonomously Complete Sprint 3 (Empathetic Narrator & Audio)
## Project: "Choice: Once Upon A Time" Flutter App

**Overall Objective:**
First, debug and ensure the Story Library Screen correctly loads and displays story metadata (using a provided simple story JSON if necessary). Second, complete all tasks for Sprint 2, focusing on implementing the core story player, narrative display, and functional branching logic. Third, autonomously complete all tasks for Sprint 3, focusing on integrating on-device TTS for the empathetic narrator using `flutter_tts` while architecting the TTS service for future flexibility (e.g., easy updates to other TTS engines or server-based TTS). Ensure robust error handling, update the `README.md` after each sprint's conceptual completion, and generate a completion report for each sprint.

**I. Prerequisites & Assumed Inputs:**
* The current Flutter project codebase (after attempts at Sprint 0 and 1).
* **Detailed Technical Design Document (TDD - Task 2.7):** Primary reference.
* **Art Style Definition & Asset Creation Plan (Task 2.3):** For UI styling and asset path conventions.
* **Screen Specifications Document ("Task 2.2 update"):** For UI details.
* **Story JSON Files:**
    * You will be provided with **one simple story JSON file** (e.g., `simple_story_test.json`) that the AI should use to debug the story library loading.
    * The AI should also be able to reference the structure of the more complex story JSONs (`story_01.json`, `story_02.json`, `story_03.json`) for implementing the full data models and story player logic.
* **Modular Design & Security Guidelines:** Previously provided guidelines must be followed.

**II. General Instructions for Code Generation (Applicable to all Sprints):**
1.  **Build Upon Existing Code:** Modify and add to the current Flutter project.
2.  **Web Compatibility:** Ensure generated code is web-compatible for testing in Chrome (`flutter run -d chrome`).
3.  **Error Handling:** Implement robust but user-friendly error handling for all critical operations (data loading, parsing, navigation, TTS). Display clear messages to the user (e.g., "Oops! We couldn't load the stories right now.") and log detailed errors to the console. Avoid app crashes.
4.  **Modularity & Testability:** Adhere to modular design patterns (TDD Section 3.1). Structure code for easy unit and widget testing.
5.  **Comments:** Add comments for complex logic or important decisions.

**III. Part 1: Debug & Complete Story Library Functionality (Finalizing Sprint 1 aspects)**

* **Objective:** Ensure the Story Library Screen (Screen 2) correctly fetches, parses, and displays story metadata from `StoryMetadataModel` (using the provided `simple_story_test.json` placed in `assets/stories/` for initial debugging, then ensure it works for other story JSONs like `story_01.json`).
* **Tasks:**
    1.  **Verify/Implement `StoryMetadataModel` (TDD Section 3.5):** Ensure the Dart model accurately reflects the structure of the story metadata (especially from `simple_story_test.json` and `story_01.json`). Test parsing.
    2.  **Debug/Implement `StoryRepository` (TDD Section 3.5):**
        * Correctly implement `fetchStoryMetadataList()` to:
            * Load story JSON files from the `assets/stories/` folder (for local testing).
            * OR Fetch metadata from the `stories` collection in Firestore (if Firebase setup was part of Sprint 1 and is preferred for this step – clarify if Firestore should be the source, or local assets for now). For debugging, local asset loading is simpler.
        * Ensure it parses the metadata section of each story into `StoryMetadataModel` objects.
        * Implement proper error handling (e.g., if files are not found, or parsing fails).
    3.  **Debug/Implement `StoryLibraryProvider` (TDD Section 3.3):**
        * Ensure it correctly uses `StoryRepository` to get the list of `StoryMetadataModel`s.
        * Manage loading, data, and error states effectively.
    4.  **Debug/Implement `StoryLibraryScreen` UI (Screen 2):**
        * Ensure the `StoryCoverCardWidget` (TDD Section 3.2) correctly receives and displays data from `StoryMetadataModel` (title, cover image placeholder).
        * Ensure the scrollable grid populates correctly and stories are tappable, leading to the Story Intro Screen (Screen 3) with the correct `storyId`.
        * Implement placeholder logic for cover images: white background + `storyId` text if actual image path (from Task 2.3) doesn't resolve.

---

**IV. Part 2: Complete Sprint 2 - Core Story Player, Narrative Display & Branching Logic**

* **Objective:** Implement the core story playing experience, including dynamic display of narrator text and visual placeholders, and functional branching choices based on your story JSON files (once the story library is loading them correctly).
* **Key Tasks & Activities (referencing TDD, Screen Specs "Task 2.2 update", Art Style "Task 2.3"):**
    1.  **Story Loading & Parsing into `StoryPlayerProvider` (TDD Section 3.3, 3.5):**
        * Ensure `StoryRepository` can fetch a full `StoryModel` by `storyId` (from `assets/stories/` or Firestore).
        * `StoryPlayerProvider` loads and parses the full `StoryModel` for the selected story. Implement error handling.
    2.  **Develop `StoryPlayerScreen` UI (Screen 5):**
        * Display current scene background (placeholder logic: white background + `sceneId` text if image not found).
        * Display `narratorSegments[].text` using `NarratorTextDisplayWidget` (TDD Section 3.2). Display `emotionCue` for reference.
        * Implement functional "Home" and "Pause" icons.
    3.  **Implement Branching Narrative Engine (TDD Section 3.5):**
        * For `choice_point` scenes, render choices using `ChoiceButtonWidget`s (TDD Section 3.2).
        * Implement choice selection logic in `StoryPlayerProvider` to update `currentScene` based on `leadsToSceneId`. Handle invalid `leadsToSceneId` errors.
    4.  **Implement Linear Scene Progression (TDD Section 3.5):** For `narration_illustration` scenes, transition to `nextSceneId`. If null, prepare for story end. Handle invalid `nextSceneId`.
    5.  **Implement Narration Controls UI (Placeholder Functionality - `NarrationControlsWidget`, TDD Section 3.2):** UI buttons for Pause/Play, Replay Segment, calling placeholder methods in `TTSService`.
    6.  **Develop Story Intro/Splash Screen (Screen 3) & Loading Screen (Screen 4):** Ensure they correctly trigger `StoryPlayerProvider` to load the story and navigate to `StoryPlayerScreen`. Implement error display if story loading fails.

---

**V. Part 3: Autonomously Complete Sprint 3 - Empathetic Narrator (On-Device TTS - MVP) & Basic Audio**

* **Objective:** Integrate on-device TTS for the empathetic narrator using `flutter_tts`, implement basic emotional modulation, and add UI sounds. **Crucially, architect the `TTSService` in a modular way to facilitate future updates to different TTS engines or server-based TTS.**
* **Key Tasks & Activities (referencing TDD, Screen Specs "Task 2.2 update", Sound Design Concept "Task 2.4"):**
    1.  **Modular `TTSService` Architecture (`core/audio/` - TDD Section 3.6):**
        * Define an abstract `TTSServiceInterface` or base class with methods like `initialize()`, `setLanguage(String langCode)`, `speakSegment(TextSegmentModel segment, String emotionCue)`, `pause()`, `resume()`, `stop()`, `setSpeechParameters(pitch, rate, volume)`, `getAvailableVoices()`, `getCurrentTTSState()`.
        * Implement `FlutterTTSService implements TTSServiceInterface` using the `flutter_tts` package.
        * This structure will allow swapping out `FlutterTTSService` with, for example, `ServerBasedTTSService` or `AdvancedNativeTTSService` in the future with minimal changes to the `StoryPlayerProvider` which will depend on the `TTSServiceInterface`.
    2.  **`FlutterTTSService` Implementation:**
        * Complete `flutter_tts` integration: initialization, voice selection (guide user to Screen 15 if voice data missing).
        * Implement emotional modulation: logic (e.g., in an `EmotionCueMapperService`) to translate `emotionCue` from `TextSegmentModel` into `flutter_tts` parameters (pitch, rate, volume) or basic SSML strings. Document the mapping.
        * Connect functional `NarrationControlsWidget` (Pause/Play, Replay) to actual `FlutterTTSService` methods via `StoryPlayerProvider`.
        * Implement text highlighting synced with TTS progress if feasible with `flutter_tts` callbacks.
        * Robust error handling for TTS failures (e.g., engine error, unsupported language).
    3.  **Integrate `TTSServiceInterface` with `StoryPlayerProvider`:** The provider should use the interface, and Riverpod will provide the concrete `FlutterTTSService` implementation.
    4.  **Implement UI Sounds:**
        * Integrate `SoundEffectPlayerService` (using `just_audio` or similar as per TDD).
        * Add gentle UI sounds for button taps, choice selections, screen transitions (as per Task 2.4 and Task 2.3 asset list for UI SFX).
    5.  **Implement In-Story UI Screens (from "Task 2.2 update"):**
        * Finalize Screen 7: `In-Story Pause Screen/Menu`.
        * Finalize Screen 8: `Story End Screen` (integrating actual `endOfStoryDiscussion` narrator segments from JSON).
        * Finalize Screen 8.1: `"Are You Still There?" Idle Prompt` (with narrator audio).

---

**VI. Testing Requirements (for features implemented in Sprints 1 Debug, Sprint 2, and Sprint 3):**
1.  **Unit Tests:**
    * For all data models, including the simple story JSON and complex ones.
    * For `StoryRepository` (fetching/parsing stories from assets/Firebase).
    * For `StoryPlayerProvider` logic (story loading, scene progression, choice handling, TTS state management).
    * For `TTSServiceInterface` implementation (`FlutterTTSService`) and `EmotionCueMapperService` (mocking `flutter_tts`).
2.  **Widget Tests:**
    * `StoryLibraryScreen`: Test display of stories, navigation.
    * `StoryCoverCardWidget`: Test display of metadata.
    * `StoryPlayerScreen`: Test display of scene elements, narrator text, choice options.
    * `ChoiceButtonWidget`: Test display and tap handling.
    * `NarrationControlsWidget`: Test UI and interaction with provider.
3.  **Integration Tests (Conceptual, if AI can generate skeletons):**
    * Test the flow from Story Library -> Story Intro -> Story Player -> Making a choice -> Next Scene.
    * Test narrator audio playback initiation for a segment.
4.  **Manual Testing (in Chrome using `flutter run -d chrome`):**
    * AI should verify its generated code runs on web. The human user will perform thorough testing.

**VII. Deliverables (AI Agent to provide):**
1.  **Updated Flutter Project Source Code:** All new and modified Dart files for the debugging of Story Library, and completion of Sprint 2 & Sprint 3 tasks.
2.  **Sprint Completion Report (Markdown format - `sprint_01_02_03_completion_report.md`):**
    * **Part 1 (Story Library Debug):**
        * Confirmation of tasks completed to make the Story Library load and display stories (using `simple_story_test.json` initially).
        * Key issues found and how they were resolved.
    * **Part 2 (Sprint 2 Completion):**
        * Overview of Sprint 2 tasks completed.
        * Details on how error handling was implemented for story player logic.
        * List of any significant assumptions made or challenges encountered.
    * **Part 3 (Sprint 3 Completion):**
        * Overview of Sprint 3 tasks completed.
        * Specific details on the modular `TTSServiceInterface` and `FlutterTTSService` implementation.
        * How emotional cues are mapped to `flutter_tts`.
        * Details on error handling for TTS operations.
    * **General:**
        * List of all new key files created and major files modified across these efforts.
        * Confirmation that the prototype is runnable on Flutter Web.
        * Any remaining issues or areas needing human review.
3.  **Updated `README.md`:**
    * Add sections for "Sprint 1 Features (Finalized)", "Sprint 2 Features", and "Sprint 3 Features" detailing what's now functional.
    * Ensure instructions for running on Flutter Web are present.
    * Include any new setup steps required by the user (e.g., for TTS languages if applicable, though `flutter_tts` often uses system capabilities).

**Final Instruction to AI Agent:**
Your primary goal is to ensure the Story Library loads and displays stories correctly using `simple_story_test.json` (and can then handle more complex ones). Then, proceed to fully implement all features outlined for Sprint 2. Following that, autonomously implement all features for Sprint 3, paying close attention to the modular TTS architecture. Document your work thoroughly in the completion report and update the README.md. Implement robust error handling throughout.