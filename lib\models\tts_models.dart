import 'package:json_annotation/json_annotation.dart';
import 'package:equatable/equatable.dart';

part 'tts_models.g.dart';

/// Enumeration for TTS event types
enum TTSEventType {
  @JsonValue('started')
  started,
  @JsonValue('completed')
  completed,
  @JsonValue('paused')
  paused,
  @JsonValue('resumed')
  resumed,
  @JsonValue('stopped')
  stopped,
  @JsonValue('error')
  error,
  @JsonValue('wordBoundary')
  wordBoundary,
  @JsonValue('sentenceBoundary')
  sentenceBoundary,
}

/// TTS event data
@JsonSerializable()
class TTSEvent extends Equatable {
  /// Type of the TTS event
  final TTSEventType type;
  
  /// Text being spoken
  final String? text;
  
  /// Error message if type is error
  final String? error;
  
  /// Word index for word boundary events
  final int? wordIndex;
  
  /// Character position in text
  final int? charIndex;
  
  /// Length of the current word/sentence
  final int? length;
  
  /// Timestamp when event occurred
  final DateTime timestamp;

  const TTSEvent({
    required this.type,
    this.text,
    this.error,
    this.wordIndex,
    this.charIndex,
    this.length,
    required this.timestamp,
  });

  /// Create a started event
  factory TTSEvent.started(String text) => TTSEvent(
        type: TTSEventType.started,
        text: text,
        timestamp: DateTime.now(),
      );

  /// Create a completed event
  factory TTSEvent.completed() => TTSEvent(
        type: TTSEventType.completed,
        timestamp: DateTime.now(),
      );

  /// Create a paused event
  factory TTSEvent.paused() => TTSEvent(
        type: TTSEventType.paused,
        timestamp: DateTime.now(),
      );

  /// Create a resumed event
  factory TTSEvent.resumed() => TTSEvent(
        type: TTSEventType.resumed,
        timestamp: DateTime.now(),
      );

  /// Create a stopped event
  factory TTSEvent.stopped() => TTSEvent(
        type: TTSEventType.stopped,
        timestamp: DateTime.now(),
      );

  /// Create an error event
  factory TTSEvent.error(String errorMessage) => TTSEvent(
        type: TTSEventType.error,
        error: errorMessage,
        timestamp: DateTime.now(),
      );

  /// Create a word boundary event
  factory TTSEvent.wordBoundary({
    required int wordIndex,
    required int charIndex,
    required int length,
  }) =>
      TTSEvent(
        type: TTSEventType.wordBoundary,
        wordIndex: wordIndex,
        charIndex: charIndex,
        length: length,
        timestamp: DateTime.now(),
      );

  /// Create a sentence boundary event
  factory TTSEvent.sentenceBoundary({
    required int charIndex,
    required int length,
  }) =>
      TTSEvent(
        type: TTSEventType.sentenceBoundary,
        charIndex: charIndex,
        length: length,
        timestamp: DateTime.now(),
      );

  /// JSON serialization
  factory TTSEvent.fromJson(Map<String, dynamic> json) => _$TTSEventFromJson(json);
  Map<String, dynamic> toJson() => _$TTSEventToJson(this);

  @override
  List<Object?> get props => [type, text, error, wordIndex, charIndex, length, timestamp];
}

/// TTS voice parameters
@JsonSerializable()
class TTSParameters extends Equatable {
  /// Speech rate (0.1 to 2.0)
  final double rate;
  
  /// Speech pitch (0.5 to 2.0)
  final double pitch;
  
  /// Speech volume (0.0 to 1.0)
  final double volume;
  
  /// Language code
  final String language;
  
  /// Voice identifier
  final String? voiceId;

  const TTSParameters({
    this.rate = 0.35,
    this.pitch = 1.0,
    this.volume = 1.0,
    this.language = 'en-US',
    this.voiceId,
  });

  /// Create a copy with updated values
  TTSParameters copyWith({
    double? rate,
    double? pitch,
    double? volume,
    String? language,
    String? voiceId,
  }) {
    return TTSParameters(
      rate: rate ?? this.rate,
      pitch: pitch ?? this.pitch,
      volume: volume ?? this.volume,
      language: language ?? this.language,
      voiceId: voiceId ?? this.voiceId,
    );
  }

  /// Default parameters
  static const TTSParameters defaultParams = TTSParameters();

  /// JSON serialization
  factory TTSParameters.fromJson(Map<String, dynamic> json) => _$TTSParametersFromJson(json);
  Map<String, dynamic> toJson() => _$TTSParametersToJson(this);

  @override
  List<Object?> get props => [rate, pitch, volume, language, voiceId];
}

/// Available TTS voice
@JsonSerializable()
class TTSVoice extends Equatable {
  /// Unique identifier for the voice
  final String id;
  
  /// Display name of the voice
  final String name;
  
  /// Language code the voice supports
  final String language;
  
  /// Gender of the voice (if available)
  final String? gender;
  
  /// Whether this voice is currently selected
  final bool isSelected;

  const TTSVoice({
    required this.id,
    required this.name,
    required this.language,
    this.gender,
    this.isSelected = false,
  });

  /// Create a copy with updated values
  TTSVoice copyWith({
    String? id,
    String? name,
    String? language,
    String? gender,
    bool? isSelected,
  }) {
    return TTSVoice(
      id: id ?? this.id,
      name: name ?? this.name,
      language: language ?? this.language,
      gender: gender ?? this.gender,
      isSelected: isSelected ?? this.isSelected,
    );
  }

  /// JSON serialization
  factory TTSVoice.fromJson(Map<String, dynamic> json) => _$TTSVoiceFromJson(json);
  Map<String, dynamic> toJson() => _$TTSVoiceToJson(this);

  @override
  List<Object?> get props => [id, name, language, gender, isSelected];
}

/// TTS service status
enum TTSStatus {
  @JsonValue('uninitialized')
  uninitialized,
  @JsonValue('initializing')
  initializing,
  @JsonValue('ready')
  ready,
  @JsonValue('speaking')
  speaking,
  @JsonValue('paused')
  paused,
  @JsonValue('error')
  error,
}

/// TTS service state
@JsonSerializable()
class TTSState extends Equatable {
  /// Current status of the TTS service
  final TTSStatus status;
  
  /// Current parameters being used
  final TTSParameters parameters;
  
  /// Currently selected voice
  final TTSVoice? selectedVoice;
  
  /// Available voices
  final List<TTSVoice> availableVoices;
  
  /// Error message if status is error
  final String? error;
  
  /// Whether the service is available
  final bool isAvailable;

  const TTSState({
    this.status = TTSStatus.uninitialized,
    this.parameters = TTSParameters.defaultParams,
    this.selectedVoice,
    this.availableVoices = const [],
    this.error,
    this.isAvailable = false,
  });

  /// Create a copy with updated values
  TTSState copyWith({
    TTSStatus? status,
    TTSParameters? parameters,
    TTSVoice? selectedVoice,
    List<TTSVoice>? availableVoices,
    String? error,
    bool? isAvailable,
  }) {
    return TTSState(
      status: status ?? this.status,
      parameters: parameters ?? this.parameters,
      selectedVoice: selectedVoice ?? this.selectedVoice,
      availableVoices: availableVoices ?? this.availableVoices,
      error: error ?? this.error,
      isAvailable: isAvailable ?? this.isAvailable,
    );
  }

  /// Initial state
  static const TTSState initial = TTSState();

  /// Ready state
  static const TTSState ready = TTSState(
    status: TTSStatus.ready,
    isAvailable: true,
  );

  /// Error state
  static TTSState createError(String errorMessage) => TTSState(
        status: TTSStatus.error,
        error: errorMessage,
        isAvailable: false,
      );

  /// JSON serialization
  factory TTSState.fromJson(Map<String, dynamic> json) => _$TTSStateFromJson(json);
  Map<String, dynamic> toJson() => _$TTSStateToJson(this);

  @override
  List<Object?> get props => [
        status,
        parameters,
        selectedVoice,
        availableVoices,
        error,
        isAvailable,
      ];
}
