import 'package:json_annotation/json_annotation.dart';
import 'text_segment_model.dart';
import 'choice_model.dart';

part 'scene_model.g.dart';

/// Model for individual story scenes
@JsonSerializable()
class SceneModel {
  /// Unique identifier for this scene
  final String sceneId;

  /// Order/sequence number for this scene (optional)
  final int? order;

  /// Type of scene: "narration_illustration", "choice_point", "conclusion"
  final String sceneType;

  /// Setting description (optional)
  final String? settingDescription;

  /// Atmosphere description (optional)
  final String? atmosphereDescription;

  /// Image description for generation (optional)
  final String? imageDescriptionForGeneration;

  /// List of narrator segments for TTS narration
  final List<TextSegmentModel> narratorSegments;

  /// Character dialogues (optional)
  final List<Map<String, dynamic>>? characterDialogues;

  /// Whether this is a choice point
  final bool isChoicePoint;

  /// ID of the next scene (for linear scenes)
  final String? nextSceneId;

  /// Choice point data (for choice_point scenes)
  final Map<String, dynamic>? choicePointData;

  /// Whether this is the end of the story
  final bool? isEndOfStory;

  const SceneModel({
    required this.sceneId,
    this.order,
    this.sceneType = 'narration_illustration',
    this.settingDescription,
    this.atmosphereDescription,
    this.imageDescriptionForGeneration,
    required this.narratorSegments,
    this.characterDialogues,
    this.isChoicePoint = false,
    this.nextSceneId,
    this.choicePointData,
    this.isEndOfStory,
  });

  /// Creates a SceneModel from JSON
  factory SceneModel.fromJson(Map<String, dynamic> json) =>
      _$SceneModelFromJson(json);

  /// Converts the SceneModel to JSON
  Map<String, dynamic> toJson() => _$SceneModelToJson(this);

  /// Gets the choices from choicePointData if available
  List<ChoiceModel>? get choices {
    if (choicePointData == null || choicePointData!['choices'] == null) return null;
    final choicesData = choicePointData!['choices'] as List<dynamic>;
    return choicesData.map((choice) => ChoiceModel.fromJson(choice as Map<String, dynamic>)).toList();
  }

  /// Gets the prompt text for choice points
  String? getLocalizedPromptText(String languageCode) {
    if (choicePointData == null || choicePointData!['promptTextForTTS'] == null) return null;
    final promptMap = choicePointData!['promptTextForTTS'] as Map<String, dynamic>?;
    return promptMap?[languageCode] as String?;
  }

  /// Whether this scene is a conclusion
  bool get isConclusion => sceneType == 'conclusion';

  /// Whether this scene is a narration illustration
  bool get isNarrationIllustration => sceneType == 'narration_illustration';

  /// Creates a copy of this model with updated fields
  SceneModel copyWith({
    String? sceneId,
    int? order,
    String? sceneType,
    String? settingDescription,
    String? atmosphereDescription,
    String? imageDescriptionForGeneration,
    List<TextSegmentModel>? narratorSegments,
    List<Map<String, dynamic>>? characterDialogues,
    bool? isChoicePoint,
    String? nextSceneId,
    Map<String, dynamic>? choicePointData,
    bool? isEndOfStory,
  }) {
    return SceneModel(
      sceneId: sceneId ?? this.sceneId,
      order: order ?? this.order,
      sceneType: sceneType ?? this.sceneType,
      settingDescription: settingDescription ?? this.settingDescription,
      atmosphereDescription: atmosphereDescription ?? this.atmosphereDescription,
      imageDescriptionForGeneration: imageDescriptionForGeneration ?? this.imageDescriptionForGeneration,
      narratorSegments: narratorSegments ?? this.narratorSegments,
      characterDialogues: characterDialogues ?? this.characterDialogues,
      isChoicePoint: isChoicePoint ?? this.isChoicePoint,
      nextSceneId: nextSceneId ?? this.nextSceneId,
      choicePointData: choicePointData ?? this.choicePointData,
      isEndOfStory: isEndOfStory ?? this.isEndOfStory,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is SceneModel && other.sceneId == sceneId;
  }

  @override
  int get hashCode => sceneId.hashCode;

  @override
  String toString() {
    return 'SceneModel(sceneId: $sceneId, sceneType: $sceneType, order: $order)';
  }
}
