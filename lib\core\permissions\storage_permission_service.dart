import 'dart:async';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:choice_once_upon_a_time/core/utils/app_logger.dart';

/// Service for handling storage permissions
class StoragePermissionService {
  static const String _logPrefix = 'StoragePermission';

  /// Check if storage permission is granted
  Future<bool> hasStoragePermission() async {
    try {
      if (Platform.isAndroid) {
        // For Android 13+ (API 33+), we need different permissions
        final androidInfo = await _getAndroidVersion();
        
        if (androidInfo >= 33) {
          // Android 13+ uses scoped storage, check for media permissions
          final photos = await Permission.photos.status;
          final videos = await Permission.videos.status;
          final audio = await Permission.audio.status;
          
          AppLogger.debug('$_logPrefix: Android 13+ permissions - Photos: $photos, Videos: $videos, Audio: $audio');
          return photos.isGranted && videos.isGranted && audio.isGranted;
        } else if (androidInfo >= 30) {
          // Android 11-12 (API 30-32)
          final manageStorage = await Permission.manageExternalStorage.status;
          AppLogger.debug('$_logPrefix: Android 11-12 manage storage permission: $manageStorage');
          return manageStorage.isGranted;
        } else {
          // Android 10 and below
          final storage = await Permission.storage.status;
          AppLogger.debug('$_logPrefix: Android 10- storage permission: $storage');
          return storage.isGranted;
        }
      } else if (Platform.isIOS) {
        // iOS doesn't require explicit storage permissions for app documents
        AppLogger.debug('$_logPrefix: iOS - no storage permission required');
        return true;
      }
      
      AppLogger.warning('$_logPrefix: Unsupported platform');
      return false;
    } catch (e, stackTrace) {
      AppLogger.error('$_logPrefix: Error checking storage permission', e, stackTrace);
      return false;
    }
  }

  /// Request storage permission
  Future<bool> requestStoragePermission() async {
    try {
      if (Platform.isAndroid) {
        final androidInfo = await _getAndroidVersion();
        
        if (androidInfo >= 33) {
          // Android 13+ - request media permissions
          final Map<Permission, PermissionStatus> statuses = await [
            Permission.photos,
            Permission.videos,
            Permission.audio,
          ].request();
          
          final allGranted = statuses.values.every((status) => status.isGranted);
          AppLogger.info('$_logPrefix: Android 13+ media permissions requested - granted: $allGranted');
          return allGranted;
        } else if (androidInfo >= 30) {
          // Android 11-12 - request manage external storage
          final status = await Permission.manageExternalStorage.request();
          AppLogger.info('$_logPrefix: Android 11-12 manage storage permission requested - status: $status');
          return status.isGranted;
        } else {
          // Android 10 and below - request storage permission
          final status = await Permission.storage.request();
          AppLogger.info('$_logPrefix: Android 10- storage permission requested - status: $status');
          return status.isGranted;
        }
      } else if (Platform.isIOS) {
        // iOS doesn't require explicit storage permissions for app documents
        AppLogger.debug('$_logPrefix: iOS - no storage permission required');
        return true;
      }
      
      AppLogger.warning('$_logPrefix: Unsupported platform for permission request');
      return false;
    } catch (e, stackTrace) {
      AppLogger.error('$_logPrefix: Error requesting storage permission', e, stackTrace);
      return false;
    }
  }

  /// Show permission rationale dialog
  Future<bool> showPermissionRationale(BuildContext context) async {
    final completer = Completer<bool>();
    
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        final theme = Theme.of(context);
        
        return AlertDialog(
          title: Row(
            children: [
              Icon(
                Icons.folder_open,
                color: theme.colorScheme.primary,
              ),
              const SizedBox(width: 8),
              const Text('Storage Permission'),
            ],
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'To download and save stories for offline reading, we need access to your device storage.',
              ),
              const SizedBox(height: 16),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: theme.colorScheme.primaryContainer.withValues(alpha: 0.3),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(
                          Icons.security,
                          size: 16,
                          color: theme.colorScheme.primary,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          'Your privacy is protected:',
                          style: theme.textTheme.bodySmall?.copyWith(
                            fontWeight: FontWeight.bold,
                            color: theme.colorScheme.primary,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    const Text(
                      '• We only access files related to our app\n'
                      '• No personal files or photos are accessed\n'
                      '• Stories are stored securely in app folders',
                      style: TextStyle(fontSize: 12),
                    ),
                  ],
                ),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                completer.complete(false);
              },
              child: const Text('Not Now'),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
                completer.complete(true);
              },
              child: const Text('Grant Permission'),
            ),
          ],
        );
      },
    );
    
    return completer.future;
  }

  /// Show permission denied dialog with settings option
  Future<void> showPermissionDeniedDialog(BuildContext context) async {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        final theme = Theme.of(context);
        
        return AlertDialog(
          title: Row(
            children: [
              Icon(
                Icons.warning,
                color: theme.colorScheme.error,
              ),
              const SizedBox(width: 8),
              const Text('Permission Required'),
            ],
          ),
          content: const Text(
            'Storage permission is required to download stories. '
            'You can enable it in your device settings.',
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Cancel'),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
                openAppSettings();
              },
              child: const Text('Open Settings'),
            ),
          ],
        );
      },
    );
  }

  /// Handle complete permission flow for downloads
  Future<bool> handleDownloadPermission(BuildContext context) async {
    try {
      // First check if we already have permission
      if (await hasStoragePermission()) {
        AppLogger.debug('$_logPrefix: Storage permission already granted');
        return true;
      }

      // Show rationale dialog
      final shouldRequest = await showPermissionRationale(context);
      if (!shouldRequest) {
        AppLogger.info('$_logPrefix: User declined permission rationale');
        return false;
      }

      // Request permission
      final granted = await requestStoragePermission();
      if (granted) {
        AppLogger.info('$_logPrefix: Storage permission granted');
        return true;
      }

      // Permission denied, show settings dialog
      if (context.mounted) {
        await showPermissionDeniedDialog(context);
      }
      
      AppLogger.warning('$_logPrefix: Storage permission denied');
      return false;
    } catch (e, stackTrace) {
      AppLogger.error('$_logPrefix: Error in download permission flow', e, stackTrace);
      return false;
    }
  }

  /// Get Android API level
  Future<int> _getAndroidVersion() async {
    try {
      if (Platform.isAndroid) {
        // For now, we'll use a simplified approach
        // In production, you'd use device_info_plus package
        return 30; // Use API 30 as a safe default that covers most devices
      }
      return 0;
    } catch (e) {
      AppLogger.warning('$_logPrefix: Could not determine Android version, assuming API 30');
      return 30;
    }
  }
}

/// Provider for storage permission service
final storagePermissionServiceProvider = Provider<StoragePermissionService>((ref) {
  return StoragePermissionService();
});
