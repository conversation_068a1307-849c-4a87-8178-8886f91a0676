import 'package:flutter/material.dart';

/// A widget representing the "Featured Stories" section on the Home Screen grid.
///
/// This section highlights specially selected stories to the user. Tapping it
/// could lead to a dedicated page for featured content.
class FeaturedStoriesGridSection extends StatelessWidget {
  const FeaturedStoriesGridSection({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final screenSize = MediaQuery.of(context).size;

    // Responsive icon size
    final iconSize = screenSize.width < 600 ? 40.0 : 48.0;

    return GestureDetector(
      onTap: () {
        // Navigate to a dedicated screen for featured stories, or apply a filter.
        // context.go('/featured_stories');
      },
      child: Card(
        elevation: 4.0,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16.0),
        ),
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(16.0),
            gradient: LinearGradient(
              colors: [
                theme.colorScheme.secondary.withValues(alpha: 0.7),
                theme.colorScheme.secondary,
              ],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
          ),
          child: Padding(
            padding: EdgeInsets.symmetric(
              horizontal: screenSize.width * 0.02,
              vertical: 8.0,
            ),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.star_border,
                  size: iconSize,
                  color: theme.colorScheme.onSecondary,
                ),
                const SizedBox(height: 8.0),
                Text(
                  'Featured Stories',
                  style: theme.textTheme.titleMedium?.copyWith(
                    color: theme.colorScheme.onSecondary,
                    fontWeight: FontWeight.bold,
                  ),
                  textAlign: TextAlign.center,
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
