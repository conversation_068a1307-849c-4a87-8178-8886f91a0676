import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:choice_once_upon_a_time/models/story_metadata_model.dart';
import 'package:choice_once_upon_a_time/features/story_library/data/story_repository.dart';
import 'package:choice_once_upon_a_time/app/providers/service_providers.dart';

/// State for the story library
class StoryLibraryState {
  final List<StoryMetadataModel> stories;
  final bool isLoading;
  final String? error;
  final String searchQuery;

  const StoryLibraryState({
    this.stories = const [],
    this.isLoading = false,
    this.error,
    this.searchQuery = '',
  });

  StoryLibraryState copyWith({
    List<StoryMetadataModel>? stories,
    bool? isLoading,
    String? error,
    String? searchQuery,
  }) {
    return StoryLibraryState(
      stories: stories ?? this.stories,
      isLoading: isLoading ?? this.isLoading,
      error: error,
      searchQuery: searchQuery ?? this.searchQuery,
    );
  }
}

/// Provider for managing story library state
class StoryLibraryNotifier extends StateNotifier<StoryLibraryState> {
  final StoryRepository _storyRepository;

  StoryLibraryNotifier(this._storyRepository) : super(const StoryLibraryState());

  /// Load stories from the repository
  Future<void> loadStories() async {
    // This guard prevents re-fetching if stories are already loaded.
    // We will bypass this with the refresh method.
    if (state.stories.isNotEmpty && !state.isLoading) return;

    state = state.copyWith(isLoading: true, error: null);
    try {
      final fetchedStories = await _storyRepository.fetchStoryMetadataList();
      if (mounted) {
        state = state.copyWith(stories: fetchedStories, isLoading: false);
      }
    } catch (e) {
      if (mounted) {
        state = state.copyWith(error: e.toString(), isLoading: false);
      }
    }
  }
  
  /// Updates the search query.
  void search(String query) {
    state = state.copyWith(searchQuery: query);
  }

  /// Refresh the story list by forcing a re-fetch from the repository.
  Future<void> refresh() async {
    // Set loading state to true and clear any previous errors.
    state = state.copyWith(isLoading: true, error: null);
    try {
      // Force a fetch from the repository, bypassing any cache.
      final fetchedStories = await _storyRepository.fetchStoryMetadataList();
      if (mounted) {
        // Replace the stories list with the new data.
        state = state.copyWith(stories: fetchedStories, isLoading: false);
      }
    } catch (e) {
      if (mounted) {
        state = state.copyWith(error: e.toString(), isLoading: false);
      }
    }
  }

  /// Load stories and check for progress data
  Future<void> loadStoriesWithProgress() async {
    state = state.copyWith(isLoading: true, error: null);
    try {
      final fetchedStories = await _storyRepository.fetchStoryMetadataList();

      // TODO: In a real implementation, you would check story progress from a progress service
      // For now, we'll simulate some stories having progress
      final storiesWithProgress = fetchedStories.map((story) {
        // Simulate progress for demonstration - in real app, check actual progress
        final hasProgress = story.id == 'simple_test_story' || story.id == 'pip_pantry_puzzle_v1';
        return story.copyWith(hasProgress: hasProgress);
      }).toList();

      if (mounted) {
        state = state.copyWith(stories: storiesWithProgress, isLoading: false);
      }
    } catch (e) {
      if (mounted) {
        state = state.copyWith(error: e.toString(), isLoading: false);
      }
    }
  }
}

/// Provider for the story library notifier.
final storyLibraryProvider = StateNotifierProvider<StoryLibraryNotifier, StoryLibraryState>((ref) {
  final repository = ref.watch(storyRepositoryProvider);
  // Create the notifier and immediately trigger the initial data load.
  return StoryLibraryNotifier(repository)..loadStories();
});

/// A provider that returns a filtered list of stories based on the search query.
final filteredStoriesProvider = Provider<List<StoryMetadataModel>>((ref) {
  final storyState = ref.watch(storyLibraryProvider);
  final allStories = storyState.stories;
  final query = storyState.searchQuery.toLowerCase();

  if (query.isEmpty) {
    return allStories;
  }

  // Filter stories where the title contains the search query.
  return allStories.where((story) {
    final title = story.getLocalizedTitle('en-US').toLowerCase();
    return title.contains(query);
  }).toList();
});
