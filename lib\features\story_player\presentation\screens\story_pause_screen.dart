import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:choice_once_upon_a_time/features/story_player/presentation/providers/story_player_provider.dart';
import 'package:choice_once_upon_a_time/shared/widgets/primary_button_widget.dart';

/// Screen displayed when story is paused
class StoryPauseScreen extends ConsumerWidget {
  const StoryPauseScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);
    final storyPlayerState = ref.watch(storyPlayerProvider);
    
    return Scaffold(
      backgroundColor: Colors.black.withOpacity(0.8),
      body: Center(
        child: Container(
          margin: const EdgeInsets.all(24),
          padding: const EdgeInsets.all(24),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.3),
                blurRadius: 20,
                offset: const Offset(0, 10),
              ),
            ],
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Pause icon
              Container(
                width: 80,
                height: 80,
                decoration: BoxDecoration(
                  color: Colors.blue[100],
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  Icons.pause,
                  size: 40,
                  color: Colors.blue[600],
                ),
              ),
              
              const SizedBox(height: 24),
              
              // Title
              Text(
                'Story Paused',
                style: theme.textTheme.headlineMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              
              const SizedBox(height: 8),
              
              // Story info
              if (storyPlayerState.story != null)
                Text(
                  storyPlayerState.story!.title,
                  style: theme.textTheme.bodyLarge?.copyWith(
                    color: Colors.grey[600],
                  ),
                  textAlign: TextAlign.center,
                ),
              
              const SizedBox(height: 32),
              
              // Action buttons
              Column(
                children: [
                  // Resume button
                  SizedBox(
                    width: double.infinity,
                    child: PrimaryButtonWidget(
                      text: 'Resume Story',
                      onPressed: () => _resumeStory(context, ref),
                      icon: const Icon(Icons.play_arrow),
                    ),
                  ),
                  
                  const SizedBox(height: 12),
                  
                  // Restart scene button
                  SizedBox(
                    width: double.infinity,
                    child: OutlinedButton.icon(
                      onPressed: () => _restartScene(context, ref),
                      icon: const Icon(Icons.replay),
                      label: const Text('Restart Scene'),
                    ),
                  ),
                  
                  const SizedBox(height: 12),
                  
                  // Settings button (placeholder)
                  SizedBox(
                    width: double.infinity,
                    child: OutlinedButton.icon(
                      onPressed: () => _showSettings(context),
                      icon: const Icon(Icons.settings),
                      label: const Text('Settings'),
                    ),
                  ),
                  
                  const SizedBox(height: 24),
                  
                  // Exit story button
                  TextButton.icon(
                    onPressed: () => _showExitConfirmation(context, ref),
                    icon: Icon(
                      Icons.exit_to_app,
                      color: Colors.red[600],
                    ),
                    label: Text(
                      'Exit Story',
                      style: TextStyle(
                        color: Colors.red[600],
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _resumeStory(BuildContext context, WidgetRef ref) {
    // Resume the story player
    ref.read(storyPlayerProvider.notifier).togglePlayPause();
    Navigator.of(context).pop();
  }

  void _restartScene(BuildContext context, WidgetRef ref) {
    // Restart the current scene
    ref.read(storyPlayerProvider.notifier).replayCurrentSegment();
    Navigator.of(context).pop();
  }

  void _showSettings(BuildContext context) {
    // Show settings dialog (placeholder)
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Story Settings'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.volume_up),
              title: const Text('Audio Volume'),
              subtitle: const Text('Adjust narration volume'),
              onTap: () {
                Navigator.of(context).pop();
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('Volume settings - Coming Soon')),
                );
              },
            ),
            ListTile(
              leading: const Icon(Icons.speed),
              title: const Text('Reading Speed'),
              subtitle: const Text('Adjust narration speed'),
              onTap: () {
                Navigator.of(context).pop();
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('Speed settings - Coming Soon')),
                );
              },
            ),
            ListTile(
              leading: const Icon(Icons.record_voice_over),
              title: const Text('Voice Selection'),
              subtitle: const Text('Choose narrator voice'),
              onTap: () {
                Navigator.of(context).pop();
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('Voice selection - Coming Soon')),
                );
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  void _showExitConfirmation(BuildContext context, WidgetRef ref) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Exit Story?'),
        content: const Text(
          'Are you sure you want to exit the story? Your progress will be lost.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              // Close dialogs and exit story
              Navigator.of(context).pop(); // Close confirmation dialog
              Navigator.of(context).pop(); // Close pause screen
              ref.read(storyPlayerProvider.notifier).reset();
              context.go('/home');
            },
            style: TextButton.styleFrom(
              foregroundColor: Colors.red,
            ),
            child: const Text('Exit'),
          ),
        ],
      ),
    );
  }
}
