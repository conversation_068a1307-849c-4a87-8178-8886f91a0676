# User Flow Documentation

## Overview
This document provides a comprehensive mapping of all navigation paths through the Flutter storytelling app. It serves as a complete reference for understanding user journeys, testing scenarios, and development planning.

## Navigation Hierarchy

### 1. App Launch Flow

```
Launch Screen (lib/features/app_init/presentation/screens/launch_screen.dart)
├── Condition: First time user → FTUE Screen
├── Condition: Returning user → Home Screen
└── Condition: Error loading → Error state (retry option)

FTUE Screen (lib/features/app_init/presentation/screens/ftue_screen.dart)
├── Action: Complete onboarding → Home Screen
├── Action: Skip → Home Screen
└── Action: Device back button → Exit app confirmation
```

### 2. Main Navigation Flow

```
Home Screen (lib/features/story_library/presentation/screens/home_screen.dart)
├── Action: Tap "Start Reading" → Story Library Screen
├── Action: Tap "Continue Reading" → Continue Story Screen
├── Action: Tap "Parent Zone" → Parent Gate Screen
├── Action: Tap "Rewards" → Rewards Screen
├── Action: Tap "AI Stories" → AI Story Generation Screen
└── Action: Device back button → Exit app confirmation

Story Library Screen (lib/features/story_library/presentation/screens/story_library_screen.dart)
├── Action: Tap story card → Story Intro Screen
├── Action: Back button → Home Screen
├── Action: Filter/Search → Updated library view
└── Action: Device back button → Home Screen

Continue Story Screen (lib/features/story_player/presentation/screens/continue_story_screen.dart)
├── Action: Tap story with progress → Enhanced Story Player Screen
├── Action: Back button → Home Screen
└── Action: Device back button → Home Screen
```

### 3. Authentication Flows

```
Parent Gate Screen (lib/features/auth/presentation/screens/parental_gate_screen.dart)
├── Action: Enter correct PIN → Parent Zone Dashboard
├── Action: Enter wrong PIN → Error state (stay on screen)
├── Action: "Forgot PIN" → Parent Auth Screen (reset mode)
├── Action: Back button → Home Screen
└── Action: Device back button → Home Screen

Parent Auth Screen (lib/features/auth/presentation/screens/parent_auth_screen.dart)
├── Action: Successful sign-in → Parent Zone Dashboard
├── Action: Create account → Account creation flow
├── Action: Forgot password → Password reset dialog
├── Action: Back button → Parent Gate Screen
└── Action: Device back button → Parent Gate Screen

Parent Zone Dashboard (lib/features/parent_zone/presentation/screens/parent_zone_dashboard_screen.dart)
├── Action: Tap "Sound Settings" → Sound Settings Screen
├── Action: Tap "User Profiles" → User Profiles Screen
├── Action: Tap "Progress Tracking" → Progress Tracking Screen
├── Action: Tap "About Stories" → About Stories Screen
├── Action: Tap "Manage Downloads" → Manage Downloads Screen
├── Action: Tap "Help & Support" → Help Support Screen
├── Action: Tap "Subscription" → Subscription Screen
├── Action: Tap "Language Settings" → Language Settings Screen
├── Action: Tap "Sign Out" → Home Screen
├── Action: Back button → Home Screen
└── Action: Device back button → Home Screen
```

### 4. Story Experience Flows

```
Story Intro Screen (lib/features/story_player/presentation/screens/story_intro_screen.dart)
├── Action: Tap "Start Story" → Loading Screen → Enhanced Story Player
├── Action: Tap "Download" → Download progress → Story available offline
├── Action: Back button → Story Library Screen
└── Action: Device back button → Story Library Screen

Loading Screen (lib/features/story_player/presentation/screens/loading_screen.dart)
├── Condition: Story loads successfully → Enhanced Story Player Screen
├── Condition: Loading error → Error state with retry option
└── Condition: User cancels → Story Intro Screen

Enhanced Story Player Screen (lib/features/story_player/presentation/screens/enhanced_story_player_screen.dart)
├── Phase: Welcome → Character Profiles → Story Scenes → Completion
├── Action: Settings button → Settings dialog
├── Action: Back button → Calm Exit Screen
└── Action: Device back button → Calm Exit Screen

Story Player Phases:
├── Welcome Phase
│   ├── Action: Continue → Character Profiles Phase
│   └── Action: Skip → Story Phase
├── Character Profiles Phase
│   ├── Action: Continue → Story Phase
│   └── Action: Previous → Welcome Phase
├── Story Phase (Enhanced Narration System)
│   ├── Narration Controls:
│   │   ├── Action: Play/Pause → Toggle narration state
│   │   ├── Action: Stop → Stop narration and reset progress
│   │   ├── Action: Replay → Restart current scene narration
│   │   ├── Action: Skip to next sentence → Advance sentence-by-sentence
│   │   ├── Action: Skip to previous sentence → Go back sentence-by-sentence
│   │   ├── Action: Adjust speed → Real-time speech rate modification
│   │   ├── Action: Adjust volume → Real-time volume control
│   │   └── Action: Seek progress → Jump to specific word/sentence
│   ├── Word-Level Highlighting:
│   │   ├── Real-time word highlighting synchronized with TTS
│   │   ├── Smooth animation transitions between words
│   │   ├── Customizable highlight colors and effects
│   │   └── Responsive text display with glow effects
│   ├── Character Voice Integration:
│   │   ├── Character-specific voice parameters
│   │   ├── Emotion-based voice modulation (happy, sad, excited, calm, etc.)
│   │   ├── Automatic voice switching based on scene speaker
│   │   └── Voice sample playback during character introductions
│   ├── Progress Tracking:
│   │   ├── Word-level progress indicators
│   │   ├── Sentence-level completion tracking
│   │   ├── Scene progression with auto/manual modes
│   │   ├── Progress persistence across sessions
│   │   └── Visual progress bars and percentage displays
│   ├── Scene Navigation:
│   │   ├── Action: Make choice → Navigate to target scene with voice feedback
│   │   ├── Action: Auto progression → Automatic scene advancement after narration
│   │   ├── Action: Manual progression → User-controlled scene advancement
│   │   └── Action: Scene replay → Restart entire scene with full narration
│   └── Accessibility Features:
│       ├── High contrast mode for text highlighting
│       ├── Large text support with responsive sizing
│       ├── Voice-over compatibility
│       └── Reduced motion options for animations
└── Completion Phase
    ├── Action: Restart → Welcome Phase
    ├── Action: Exit → Home Screen
    └── Action: View Rewards → Rewards Screen

Calm Exit Screen (lib/features/story_player/presentation/screens/calm_exit_screen.dart)
├── Action: "Continue Story" → Return to Enhanced Story Player
├── Action: "Exit to Home" → Home Screen
└── Action: Wait timeout → Auto return to Enhanced Story Player
```

### 5. Settings & Management Flows

```
Sound Settings Screen (lib/features/parent_zone/presentation/screens/sound_settings_screen.dart)
├── Action: Adjust voice settings → Settings updated
├── Action: Test voice → Play sample narration
├── Action: Reset to defaults → Confirmation dialog → Reset settings
├── Action: Back button → Parent Zone Dashboard
└── Action: Device back button → Parent Zone Dashboard

User Profiles Screen (lib/features/parent_zone/presentation/screens/user_profiles_screen.dart)
├── Action: Create profile → Profile creation dialog
├── Action: Edit profile → Profile editing dialog
├── Action: Delete profile → Confirmation dialog → Delete profile
├── Action: Back button → Parent Zone Dashboard
└── Action: Device back button → Parent Zone Dashboard

Subscription Screen (lib/features/parent_zone/presentation/screens/subscription_screen.dart)
├── Action: Select plan → Purchase flow
├── Action: Restore purchases → Restore previous purchases
├── Action: Back button → Parent Zone Dashboard
└── Action: Device back button → Parent Zone Dashboard

Manage Downloads Screen (lib/features/parent_zone/presentation/screens/manage_downloads_screen.dart)
├── Action: Download story → Download progress → Story available offline
├── Action: Delete downloaded story → Confirmation → Remove from device
├── Action: Back button → Parent Zone Dashboard
└── Action: Device back button → Parent Zone Dashboard
```

### 6. Additional Features

```
Rewards Screen (lib/features/rewards/presentation/screens/rewards_screen.dart)
├── Action: View achievement details → Achievement detail view
├── Action: Switch tabs → Different reward categories
├── Action: Back button → Previous screen (Home or Story Player)
└── Action: Device back button → Previous screen

AI Story Generation Screen (lib/features/ai_stories/presentation/screens/ai_story_generation_screen.dart)
├── Action: Fill form → Generate story → Story preview
├── Action: Generate story → Loading → Generated story display
├── Action: Back button → Home Screen
└── Action: Device back button → Home Screen
```

### 7. Error & Edge Case Flows

```
Network Error States:
├── Story loading fails → Error message with retry option
├── Download fails → Error message with retry option
├── Authentication fails → Error message with retry option
└── Purchase fails → Error message with retry option

Permission Denied States:
├── Storage permission denied → Permission request dialog
├── Microphone permission denied → Feature disabled with explanation
└── Network permission denied → Offline mode explanation

Loading States:
├── Story content loading → Loading indicator with cancel option
├── Download progress → Progress bar with cancel option
├── Authentication loading → Loading indicator
└── Purchase processing → Loading indicator with timeout

Empty States:
├── No downloaded stories → Download suggestions
├── No progress data → Start reading suggestions
├── No rewards earned → Achievement suggestions
└── No search results → Alternative suggestions
```

### 8. Enhanced Narration System Flows

```
Narration Service Architecture:
├── IStoryNarrationService (Interface)
│   ├── Enhanced Story Narration Service (Implementation)
│   ├── Scene-by-scene narration management
│   ├── Character voice integration
│   ├── Progress tracking and persistence
│   └── Background music coordination
├── INarrationTTSService (Interface)
│   ├── Enhanced Narration TTS Service (Implementation)
│   ├── Word boundary tracking for highlighting
│   ├── Emotion-based voice modulation
│   ├── Character-specific voice parameters
│   └── SSML support for advanced speech control
└── Narration Widget Components
    ├── StoryNarrationWidget (Main narration display)
    ├── NarrationControlsWidget (Playback controls)
    ├── WordHighlightWidget (Text highlighting)
    └── EnhancedStoryNarrationWidget (Story integration)

Narration Initialization Flow:
├── Story Player Screen loads
├── Initialize Enhanced Story Narration Service
├── Configure TTS service with default parameters
├── Set up character voice profiles from story metadata
├── Initialize word highlighting system
├── Begin scene narration with welcome phase
└── Start real-time progress tracking

Word-Level Highlighting Flow:
├── TTS service begins speaking text
├── Word boundary events generated by TTS engine
├── WordHighlightWidget receives highlight events
├── Smooth animation applied to current word
├── Progress indicators updated in real-time
├── User can seek to specific words via progress slider
└── Highlighting synchronized with pause/resume controls

Character Voice Switching Flow:
├── Scene loads with speaker metadata
├── Check for character-specific voice configuration
├── Apply emotion-based voice parameters
├── Transition smoothly between character voices
├── Maintain voice consistency within scenes
└── Provide voice samples during character introductions

Emotion-Based Voice Modulation:
├── Scene contains emotion cue (happy, sad, excited, etc.)
├── Emotion mapper converts cue to TTS parameters
├── Speech rate, pitch, and volume adjusted accordingly
├── Smooth transitions between emotional states
├── Visual feedback reflects current emotion
└── User can override with manual voice controls
```

## Navigation Patterns

### Back Button Behavior
- **Hardware back button**: Follows the same logic as UI back button
- **UI back button**: Navigates to logical parent screen
- **Story player**: Shows calm exit screen instead of immediate exit
- **Root screens**: Shows exit app confirmation

### Deep Linking Support
- `/story/:storyId` → Story Intro Screen
- `/enhanced_story_player/:storyId` → Enhanced Story Player
- `/parent_zone` → Parent Gate Screen (if not authenticated)
- `/rewards` → Rewards Screen

### State Persistence
- Story progress is saved automatically
- User preferences persist across sessions
- Authentication state maintained for 7 days
- Download queue persists across app restarts

### Error Recovery
- Network errors: Retry with exponential backoff
- Storage errors: Fallback to memory storage
- Authentication errors: Redirect to sign-in
- Parsing errors: Fallback to default content

## Testing Scenarios

### Critical User Journeys
1. **First-time user**: Launch → FTUE → Home → Story selection → Enhanced narration experience → Story completion
2. **Returning user**: Launch → Home → Continue reading → Resume narration from saved progress → Story completion
3. **Parent setup**: Home → Parent Zone → Authentication → Voice settings configuration → Character voice testing
4. **Offline usage**: Download stories → Airplane mode → Story playback with full narration features
5. **Purchase flow**: Parent Zone → Subscription → Purchase → Content unlock with enhanced narration
6. **Narration experience**: Story start → Character introductions with voice samples → Scene narration with word highlighting → Choice interactions with voice feedback → Completion with narrated summary

### Edge Cases
1. **Interrupted story**: Mid-story exit → Resume from same point with narration state preserved
2. **Network loss**: During story → Graceful offline transition with TTS functionality maintained
3. **Low storage**: Download attempt → Storage management suggestions with narration cache cleanup
4. **App backgrounding**: During narration → Pause TTS and resume correctly on return
5. **Device rotation**: All screens → Maintain state and responsive layout with narration controls
6. **TTS service failure**: Fallback to text-only mode with visual progress indicators
7. **Audio interruption**: Phone calls/notifications → Pause narration and resume after interruption
8. **Character voice missing**: Fallback to default narrator voice with smooth transition
9. **Word highlighting sync issues**: Graceful degradation to sentence-level progress tracking
10. **Memory pressure**: Efficient cleanup of narration resources while maintaining user experience

This documentation serves as the definitive guide for understanding all possible user paths through the application and should be updated whenever new features or navigation changes are implemented.
