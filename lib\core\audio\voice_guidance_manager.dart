// lib/core/audio/voice_guidance_manager.dart

import 'package:flutter/widgets.dart'; // For BuildContext
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:choice_once_upon_a_time/l10n/app_localizations.dart'; // For accessing localized strings
import 'package:choice_once_upon_a_time/app/providers/service_providers.dart'; // Assuming ttsServiceProvider is defined here
import 'package:choice_once_upon_a_time/core/audio/tts_service_interface.dart'; // Required for TTSServiceInterface type
import 'package:choice_once_upon_a_time/core/utils/app_logger.dart';

/// Provider for VoiceGuidanceManager
final voiceGuidanceManagerProvider = Provider<VoiceGuidanceManager>((ref) {
  final ttsService = ref.read(ttsServiceProvider); // This will provide TTSServiceInterface
  return VoiceGuidanceManager(ttsService);
});

/// Manages the playback of UI-specific voice guidance.
/// This service provides a centralized way to trigger and control voice prompts
/// for various screens, using localized text or direct text.
class VoiceGuidanceManager {
  final TTSServiceInterface _ttsService;
  
  VoiceGuidanceManager(this._ttsService);

  /// Plays a voice guide using a localized string from AppLocalizations.
  ///
  /// [context]: BuildContext to resolve AppLocalizations.
  /// [getString]: A function that takes AppLocalizations and returns the string to speak.
  /// [emotionCue]: Optional emotion cue for the TTS.
  Future<void> playGuide(BuildContext context, String Function(AppLocalizations) getString, {String emotionCue = 'neutral', Map<String, dynamic>? interpolationArgs}) async {
    await _ttsService.stop(); // Stop any previous speech

    final appLocalizations = AppLocalizations.of(context)!;
    String textToSpeak = getString(appLocalizations);

    // TODO: Add interpolationArgs handling if needed in the future
    // For now, this assumes simple string access.

    if (textToSpeak.isNotEmpty) {
      await _ttsService.speakText(textToSpeak, emotionCue: emotionCue);
      AppLogger.debug('VoiceGuide (Localized): Playing "$textToSpeak" with cue "$emotionCue"');
    }
  }

  /// Plays a direct text string.
  ///
  /// [textToSpeak]: The raw string to be spoken.
  /// [emotionCue]: Optional emotion cue for the TTS.
  Future<void> playText(String textToSpeak, {String emotionCue = 'neutral'}) async {
    await _ttsService.stop(); // Stop any previous speech

    if (textToSpeak.isNotEmpty) {
      await _ttsService.speakText(textToSpeak, emotionCue: emotionCue);
      AppLogger.debug('VoiceGuide (Direct): Playing "$textToSpeak" with cue "$emotionCue"');
    }
  }

  /// Stops any currently playing voice guidance.
  Future<void> stopGuide() async {
    await _ttsService.stop();
    AppLogger.debug('VoiceGuide: Stopped playback.');
  }

  /// Checks if voice guidance is currently speaking.
  bool get isSpeaking => _ttsService.isSpeaking;
}
