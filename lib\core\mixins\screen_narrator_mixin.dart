import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:choice_once_upon_a_time/core/audio/voice_guidance_manager.dart';
import 'package:choice_once_upon_a_time/l10n/app_localizations.dart';

/// Mixin that provides screen narration functionality for widgets
/// This mixin helps screens play introduction narrations and manage voice guidance
mixin ScreenNarratorMixin<T extends ConsumerStatefulWidget> on ConsumerState<T> {
  
  /// Plays the screen introduction narration
  /// Override this method in your screen to provide custom introduction text
  Future<void> playScreenIntroduction() async {
    final introTextProvider = getScreenIntroductionText();
    if (introTextProvider != null) {
      await playNarration(introTextProvider);
    }
  }

  /// Override this method to provide the introduction text for your screen
  /// Return null if no introduction should be played
  String Function(AppLocalizations)? getScreenIntroductionText() {
    return null;
  }

  /// Plays a narration using the voice guidance manager
  Future<void> playNarration(String Function(AppLocalizations) textProvider, {String? emotionCue}) async {
    if (!mounted) return;
    
    try {
      final voiceGuidanceManager = ref.read(voiceGuidanceManagerProvider);
      await voiceGuidanceManager.stopGuide();
      await voiceGuidanceManager.playGuide(context, textProvider, emotionCue: emotionCue ?? 'neutral');
    } catch (e) {
      // Silently handle voice guidance errors to prevent app crashes
      debugPrint('Voice guidance error: $e');
    }
  }

  /// Stops any currently playing narration
  Future<void> stopNarration() async {
    if (!mounted) return;
    
    try {
      final voiceGuidanceManager = ref.read(voiceGuidanceManagerProvider);
      await voiceGuidanceManager.stopGuide();
    } catch (e) {
      // Silently handle voice guidance errors
      debugPrint('Voice guidance stop error: $e');
    }
  }

  /// Call this method in your screen's initState to automatically play introduction
  void initializeScreenNarration() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      playScreenIntroduction();
    });
  }

  /// Call this method in your screen's dispose to stop any playing narration
  void disposeScreenNarration() {
    if (mounted) {
      stopNarration();
    }
  }
}