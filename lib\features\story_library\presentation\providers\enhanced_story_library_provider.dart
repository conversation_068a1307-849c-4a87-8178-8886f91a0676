import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:choice_once_upon_a_time/core/models/story_status.dart';
import 'package:choice_once_upon_a_time/core/repositories/enhanced_story_repository.dart';
import 'package:choice_once_upon_a_time/core/services/story_download_service.dart';
import 'package:choice_once_upon_a_time/core/utils/app_logger.dart';

/// State class for enhanced story library
class EnhancedStoryLibraryState {
  final List<StoryMetadata> stories;
  final bool isLoading;
  final String? error;
  final String searchQuery;
  final Map<String, double> downloadProgress;

  const EnhancedStoryLibraryState({
    this.stories = const [],
    this.isLoading = false,
    this.error,
    this.searchQuery = '',
    this.downloadProgress = const {},
  });

  EnhancedStoryLibraryState copyWith({
    List<StoryMetadata>? stories,
    bool? isLoading,
    String? error,
    String? searchQuery,
    Map<String, double>? downloadProgress,
  }) {
    return EnhancedStoryLibraryState(
      stories: stories ?? this.stories,
      isLoading: isLoading ?? this.isLoading,
      error: error,
      searchQuery: searchQuery ?? this.searchQuery,
      downloadProgress: downloadProgress ?? this.downloadProgress,
    );
  }

  /// Get filtered stories based on search query
  List<StoryMetadata> get filteredStories {
    if (searchQuery.isEmpty) {
      return stories;
    }

    final query = searchQuery.toLowerCase().trim();
    return stories.where((story) {
      final title = story.title.toLowerCase();
      final description = story.description.toLowerCase();
      final tags = story.tags.join(' ').toLowerCase();
      
      return title.contains(query) || 
             description.contains(query) || 
             tags.contains(query);
    }).toList();
  }

  /// Get stories by status
  List<StoryMetadata> getStoriesByStatus(StoryStatus status) {
    return stories.where((story) => story.status == status).toList();
  }

  /// Get story count by source
  Map<String, int> get storyCountBySource {
    final counts = <String, int>{
      'offline': 0,
      'downloaded': 0,
      'downloadable': 0,
      'locked': 0,
    };

    for (final story in stories) {
      switch (story.status) {
        case StoryStatus.offline:
          counts['offline'] = (counts['offline'] ?? 0) + 1;
          break;
        case StoryStatus.downloaded:
          counts['downloaded'] = (counts['downloaded'] ?? 0) + 1;
          break;
        case StoryStatus.downloadable:
          counts['downloadable'] = (counts['downloadable'] ?? 0) + 1;
          break;
        case StoryStatus.locked:
          counts['locked'] = (counts['locked'] ?? 0) + 1;
          break;
        default:
          break;
      }
    }

    return counts;
  }
}

/// Notifier for managing enhanced story library state
class EnhancedStoryLibraryNotifier extends StateNotifier<EnhancedStoryLibraryState> {
  final EnhancedStoryRepository _repository;
  final StoryDownloadService _downloadService;

  EnhancedStoryLibraryNotifier(
    this._repository,
    this._downloadService,
  ) : super(const EnhancedStoryLibraryState()) {
    _loadStories();
  }

  /// Load all stories from multiple sources
  Future<void> _loadStories() async {
    AppLogger.info('[ENHANCED_STORY_LIBRARY] Loading stories from all sources');

    state = state.copyWith(isLoading: true, error: null);

    try {
      final stories = await _repository.getAllStories();
      AppLogger.info('[ENHANCED_STORY_LIBRARY] Loaded ${stories.length} stories');

      state = state.copyWith(
        stories: stories,
        isLoading: false,
        error: null,
      );
    } catch (e, stackTrace) {
      AppLogger.error('[ENHANCED_STORY_LIBRARY] Error loading stories', e, stackTrace);
      state = state.copyWith(
        isLoading: false,
        error: 'Failed to load stories: $e',
      );
    }
  }

  /// Refresh the story list
  Future<void> refresh() async {
    AppLogger.info('[ENHANCED_STORY_LIBRARY] Refreshing story list');
    await _loadStories();
  }

  /// Search stories by query
  void search(String query) {
    AppLogger.debug('[ENHANCED_STORY_LIBRARY] Searching stories with query: $query');
    state = state.copyWith(searchQuery: query);
  }

  /// Download a story
  Future<bool> downloadStory(String storyId) async {
    try {
      AppLogger.info('[ENHANCED_STORY_LIBRARY] Starting download for story: $storyId');

      // Update story status to downloading
      final updatedStories = state.stories.map((story) {
        if (story.id == storyId) {
          return story.copyWith(status: StoryStatus.downloading);
        }
        return story;
      }).toList();

      state = state.copyWith(stories: updatedStories);

      // Start download with progress tracking
      final success = await _repository.downloadStory(
        storyId,
        onProgress: (progress) {
          // Update download progress
          final newProgress = Map<String, double>.from(state.downloadProgress);
          newProgress[storyId] = progress;
          state = state.copyWith(downloadProgress: newProgress);

          // Update story with progress
          final progressStories = state.stories.map((story) {
            if (story.id == storyId) {
              return story.copyWith(downloadProgress: progress);
            }
            return story;
          }).toList();

          state = state.copyWith(stories: progressStories);
        },
      );

      if (success) {
        AppLogger.info('[ENHANCED_STORY_LIBRARY] Successfully downloaded story: $storyId');
        
        // Update story status to downloaded
        final completedStories = state.stories.map((story) {
          if (story.id == storyId) {
            return story.copyWith(
              status: StoryStatus.downloaded,
              downloadProgress: 1.0,
            );
          }
          return story;
        }).toList();

        state = state.copyWith(stories: completedStories);
      } else {
        AppLogger.error('[ENHANCED_STORY_LIBRARY] Failed to download story: $storyId');
        
        // Update story status to download failed
        final failedStories = state.stories.map((story) {
          if (story.id == storyId) {
            return story.copyWith(status: StoryStatus.downloadFailed);
          }
          return story;
        }).toList();

        state = state.copyWith(stories: failedStories);
      }

      return success;
    } catch (e, stackTrace) {
      AppLogger.error('[ENHANCED_STORY_LIBRARY] Error downloading story $storyId', e, stackTrace);
      
      // Update story status to download failed
      final failedStories = state.stories.map((story) {
        if (story.id == storyId) {
          return story.copyWith(status: StoryStatus.downloadFailed);
        }
        return story;
      }).toList();

      state = state.copyWith(stories: failedStories);
      return false;
    }
  }

  /// Delete a downloaded story
  Future<bool> deleteStory(String storyId) async {
    try {
      AppLogger.info('[ENHANCED_STORY_LIBRARY] Deleting story: $storyId');

      final success = await _repository.deleteDownloadedStory(storyId);

      if (success) {
        // Update story status back to downloadable
        final updatedStories = state.stories.map((story) {
          if (story.id == storyId) {
            return story.copyWith(
              status: StoryStatus.downloadable,
              downloadProgress: 0.0,
            );
          }
          return story;
        }).toList();

        state = state.copyWith(stories: updatedStories);
        AppLogger.info('[ENHANCED_STORY_LIBRARY] Successfully deleted story: $storyId');
      }

      return success;
    } catch (e, stackTrace) {
      AppLogger.error('[ENHANCED_STORY_LIBRARY] Error deleting story $storyId', e, stackTrace);
      return false;
    }
  }

  /// Get download progress for a story
  double getDownloadProgress(String storyId) {
    return state.downloadProgress[storyId] ?? 0.0;
  }

  /// Check if story can be played
  bool canPlayStory(String storyId) {
    final story = state.stories.firstWhere(
      (s) => s.id == storyId,
      orElse: () => const StoryMetadata(
        id: '',
        title: '',
        description: '',
        status: StoryStatus.unknown,
      ),
    );
    return story.status.canPlay;
  }

  /// Check if story can be downloaded
  bool canDownloadStory(String storyId) {
    final story = state.stories.firstWhere(
      (s) => s.id == storyId,
      orElse: () => const StoryMetadata(
        id: '',
        title: '',
        description: '',
        status: StoryStatus.unknown,
      ),
    );
    return story.status.canDownload;
  }
}

/// Provider for enhanced story library
final enhancedStoryLibraryProvider = StateNotifierProvider<EnhancedStoryLibraryNotifier, EnhancedStoryLibraryState>((ref) {
  final repository = ref.watch(enhancedStoryRepositoryProvider);
  final downloadService = ref.watch(storyDownloadServiceProvider);
  return EnhancedStoryLibraryNotifier(repository, downloadService);
});

/// Provider for filtered stories
final filteredStoriesProvider = Provider<List<StoryMetadata>>((ref) {
  final state = ref.watch(enhancedStoryLibraryProvider);
  return state.filteredStories;
});

/// Provider for story count by source
final storyCountProvider = Provider<Map<String, int>>((ref) {
  final state = ref.watch(enhancedStoryLibraryProvider);
  return state.storyCountBySource;
});
