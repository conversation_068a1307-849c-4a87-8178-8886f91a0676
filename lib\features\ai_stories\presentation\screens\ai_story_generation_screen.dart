import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:choice_once_upon_a_time/core/mixins/screen_narrator_mixin.dart';
import 'package:choice_once_upon_a_time/features/ai_stories/data/ai_story_service.dart';
import 'package:choice_once_upon_a_time/features/ai_stories/data/ai_story_provider.dart';
import 'package:choice_once_upon_a_time/features/story_player/presentation/providers/story_player_provider.dart';
import 'package:choice_once_upon_a_time/l10n/app_localizations.dart';

// Remove the local provider since we're using the global one
// final aiStoryServiceProvider = Provider<AIStoryService>((ref) {
//   return AIStoryService();
// });

// State provider for form data
final aiStoryFormProvider = StateNotifierProvider<AIStoryFormNotifier, AIStoryFormState>((ref) {
  return AIStoryFormNotifier();
});

class AIStoryFormState {
  final String childName;
  final String theme;
  final String moralValue;
  final String ageGroup;
  final List<String> interests;
  final String setting;
  final bool isGenerating;
  final String? error;

  const AIStoryFormState({
    this.childName = '',
    this.theme = '',
    this.moralValue = '',
    this.ageGroup = '4-6',
    this.interests = const [],
    this.setting = '',
    this.isGenerating = false,
    this.error,
  });

  AIStoryFormState copyWith({
    String? childName,
    String? theme,
    String? moralValue,
    String? ageGroup,
    List<String>? interests,
    String? setting,
    bool? isGenerating,
    String? error,
  }) {
    return AIStoryFormState(
      childName: childName ?? this.childName,
      theme: theme ?? this.theme,
      moralValue: moralValue ?? this.moralValue,
      ageGroup: ageGroup ?? this.ageGroup,
      interests: interests ?? this.interests,
      setting: setting ?? this.setting,
      isGenerating: isGenerating ?? this.isGenerating,
      error: error ?? this.error,
    );
  }

  bool get isValid => 
      childName.isNotEmpty && 
      theme.isNotEmpty && 
      moralValue.isNotEmpty;
}

class AIStoryFormNotifier extends StateNotifier<AIStoryFormState> {
  AIStoryFormNotifier() : super(const AIStoryFormState());

  void updateChildName(String name) {
    state = state.copyWith(childName: name, error: null);
  }

  void updateTheme(String theme) {
    state = state.copyWith(theme: theme, error: null);
  }

  void updateMoralValue(String value) {
    state = state.copyWith(moralValue: value, error: null);
  }

  void updateAgeGroup(String ageGroup) {
    state = state.copyWith(ageGroup: ageGroup, error: null);
  }

  void updateSetting(String setting) {
    state = state.copyWith(setting: setting, error: null);
  }

  void toggleInterest(String interest) {
    final currentInterests = List<String>.from(state.interests);
    if (currentInterests.contains(interest)) {
      currentInterests.remove(interest);
    } else {
      currentInterests.add(interest);
    }
    state = state.copyWith(interests: currentInterests, error: null);
  }

  void setGenerating(bool isGenerating) {
    state = state.copyWith(isGenerating: isGenerating);
  }

  void setError(String error) {
    state = state.copyWith(error: error, isGenerating: false);
  }

  void clearError() {
    state = state.copyWith(error: null);
  }
}

class AIStoryGenerationScreen extends ConsumerStatefulWidget {
  const AIStoryGenerationScreen({super.key});

  @override
  ConsumerState<AIStoryGenerationScreen> createState() => _AIStoryGenerationScreenState();
}

class _AIStoryGenerationScreenState extends ConsumerState<AIStoryGenerationScreen> 
    with ScreenNarratorMixin {

  final _formKey = GlobalKey<FormState>();
  final _childNameController = TextEditingController();
  final _themeController = TextEditingController();
  final _settingController = TextEditingController();

  static const List<String> _moralValues = [
    'Kindness',
    'Honesty', 
    'Courage',
    'Empathy',
    'Perseverance',
    'Responsibility',
    'Friendship',
    'Sharing',
  ];

  static const List<String> _ageGroups = [
    '3-4',
    '4-6', 
    '5-7',
    '6-8',
  ];

  static const List<String> _commonInterests = [
    'Animals',
    'Space',
    'Dinosaurs',
    'Princesses',
    'Superheroes',
    'Cars',
    'Nature',
    'Magic',
    'Sports',
    'Music',
  ];

  @override
  void initState() {
    super.initState();
    initializeScreenNarration();
    
    // Listen to text field changes
    _childNameController.addListener(() {
      ref.read(aiStoryFormProvider.notifier).updateChildName(_childNameController.text);
    });
    _themeController.addListener(() {
      ref.read(aiStoryFormProvider.notifier).updateTheme(_themeController.text);
    });
    _settingController.addListener(() {
      ref.read(aiStoryFormProvider.notifier).updateSetting(_settingController.text);
    });
  }

  @override
  void dispose() {
    _childNameController.dispose();
    _themeController.dispose();
    _settingController.dispose();
    disposeScreenNarration();
    super.dispose();
  }

  @override
  String Function(AppLocalizations)? getScreenIntroductionText() {
    return (l10n) => l10n.aiStoryGenerationScreenIntro;
  }

  Future<void> _generateStory() async {
    if (!_formKey.currentState!.validate()) return;

    final formState = ref.read(aiStoryFormProvider);
    if (!formState.isValid) return;

    final formNotifier = ref.read(aiStoryFormProvider.notifier);
    final aiService = ref.read(aiStoryServiceProvider);
    final aiStoryNotifier = ref.read(aiStoryProvider.notifier);
    final storyPlayerNotifier = ref.read(storyPlayerProvider.notifier);

    formNotifier.setGenerating(true);
    aiStoryNotifier.setGenerating(true);

    try {
      await playNarration((l10n) => l10n.aiStoryGeneratingPrompt);

      final story = await aiService.generateStory(
        childName: formState.childName,
        theme: formState.theme,
        moralValue: formState.moralValue,
        ageGroup: formState.ageGroup,
        interests: formState.interests.isNotEmpty ? formState.interests : null,
        setting: formState.setting.isNotEmpty ? formState.setting : null,
      );

      if (mounted) {
        // Store the AI generated story in the global provider
        aiStoryNotifier.setCurrentAIStory(story);
        
        // Load the story into the story player
        await storyPlayerNotifier.loadAIGeneratedStory(story);
        
        await playNarration((l10n) => l10n.aiStoryGeneratedPrompt);
        
        // Navigate to story player screen
        context.go('/story_player');
      }
    } catch (e) {
      if (mounted) {
        String errorMessage = 'Failed to generate story';
        if (e is AIStoryGenerationException) {
          errorMessage = e.message;
        }
        
        formNotifier.setError(errorMessage);
        aiStoryNotifier.setError(errorMessage);
        await playNarration((l10n) => l10n.aiStoryGenerationErrorPrompt);
      }
    } finally {
      if (mounted) {
        formNotifier.setGenerating(false);
        aiStoryNotifier.setGenerating(false);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final formState = ref.watch(aiStoryFormProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Create Your Story'),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () {
            stopNarration();
            context.go('/home');
          },
        ),
      ),
      body: Form(
        key: _formKey,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    children: [
                      Icon(
                        Icons.auto_stories,
                        size: 48,
                        color: theme.colorScheme.primary,
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'Create a Personalized Story',
                        style: theme.textTheme.headlineSmall?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'Tell us about your child and we\'ll create a unique story just for them!',
                        style: theme.textTheme.bodyMedium?.copyWith(
                          color: Colors.grey[600],
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                ),
              ),

              const SizedBox(height: 24),

              // Child's Name
              _buildSectionTitle('Child\'s Name'),
              TextFormField(
                controller: _childNameController,
                decoration: const InputDecoration(
                  hintText: 'Enter your child\'s name',
                  border: OutlineInputBorder(),
                ),
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'Please enter your child\'s name';
                  }
                  return null;
                },
              ),

              const SizedBox(height: 24),

              // Story Theme
              _buildSectionTitle('Story Theme'),
              TextFormField(
                controller: _themeController,
                decoration: const InputDecoration(
                  hintText: 'e.g., Adventure in the forest, Space exploration',
                  border: OutlineInputBorder(),
                ),
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'Please enter a story theme';
                  }
                  return null;
                },
              ),

              const SizedBox(height: 24),

              // Moral Value
              _buildSectionTitle('What Should Your Child Learn?'),
              DropdownButtonFormField<String>(
                value: formState.moralValue.isNotEmpty ? formState.moralValue : null,
                decoration: const InputDecoration(
                  border: OutlineInputBorder(),
                ),
                hint: const Text('Select a moral value'),
                items: _moralValues.map((value) {
                  return DropdownMenuItem(
                    value: value,
                    child: Text(value),
                  );
                }).toList(),
                onChanged: (value) {
                  if (value != null) {
                    ref.read(aiStoryFormProvider.notifier).updateMoralValue(value);
                  }
                },
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please select a moral value';
                  }
                  return null;
                },
              ),

              const SizedBox(height: 24),

              // Age Group
              _buildSectionTitle('Age Group'),
              DropdownButtonFormField<String>(
                value: formState.ageGroup,
                decoration: const InputDecoration(
                  border: OutlineInputBorder(),
                ),
                items: _ageGroups.map((age) {
                  return DropdownMenuItem(
                    value: age,
                    child: Text('$age years'),
                  );
                }).toList(),
                onChanged: (value) {
                  if (value != null) {
                    ref.read(aiStoryFormProvider.notifier).updateAgeGroup(value);
                  }
                },
              ),

              const SizedBox(height: 24),

              // Interests (Optional)
              _buildSectionTitle('Interests (Optional)'),
              Text(
                'Select things your child likes:',
                style: theme.textTheme.bodySmall?.copyWith(
                  color: Colors.grey[600],
                ),
              ),
              const SizedBox(height: 8),
              Wrap(
                spacing: 8,
                runSpacing: 8,
                children: _commonInterests.map((interest) {
                  final isSelected = formState.interests.contains(interest);
                  return FilterChip(
                    label: Text(interest),
                    selected: isSelected,
                    onSelected: (_) {
                      ref.read(aiStoryFormProvider.notifier).toggleInterest(interest);
                    },
                  );
                }).toList(),
              ),

              const SizedBox(height: 24),

              // Setting (Optional)
              _buildSectionTitle('Setting (Optional)'),
              TextFormField(
                controller: _settingController,
                decoration: const InputDecoration(
                  hintText: 'e.g., Magical kingdom, Underwater world',
                  border: OutlineInputBorder(),
                ),
              ),

              const SizedBox(height: 32),

              // Error Display
              if (formState.error != null)
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(12),
                  margin: const EdgeInsets.only(bottom: 16),
                  decoration: BoxDecoration(
                    color: Colors.red.shade50,
                    border: Border.all(color: Colors.red.shade200),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Row(
                    children: [
                      Icon(Icons.error_outline, color: Colors.red.shade600),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          formState.error!,
                          style: TextStyle(color: Colors.red.shade700),
                        ),
                      ),
                    ],
                  ),
                ),

              // Generate Button
              SizedBox(
                width: double.infinity,
                height: 56,
                child: ElevatedButton(
                  onPressed: formState.isGenerating ? null : _generateStory,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: theme.colorScheme.primary,
                    foregroundColor: Colors.white,
                  ),
                  child: formState.isGenerating
                      ? const Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            SizedBox(
                              width: 20,
                              height: 20,
                              child: CircularProgressIndicator(
                                strokeWidth: 2,
                                valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                              ),
                            ),
                            SizedBox(width: 12),
                            Text('Creating Your Story...'),
                          ],
                        )
                      : const Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(Icons.auto_awesome),
                            SizedBox(width: 8),
                            Text('Generate Story'),
                          ],
                        ),
                ),
              ),

              const SizedBox(height: 16),

              // Info text
              Text(
                'Story generation may take 30-60 seconds. Please be patient while we create your personalized story!',
                style: theme.textTheme.bodySmall?.copyWith(
                  color: Colors.grey[600],
                  fontStyle: FontStyle.italic,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8.0),
      child: Text(
        title,
        style: Theme.of(context).textTheme.titleMedium?.copyWith(
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }
}