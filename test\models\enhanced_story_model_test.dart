import 'package:flutter_test/flutter_test.dart';
import 'package:choice_once_upon_a_time/models/enhanced_story_model.dart';

void main() {
  group('EnhancedStoryModel Tests', () {
    late Map<String, dynamic> validStoryJson;

    setUp(() {
      validStoryJson = {
        'story_id': 'test_story',
        'age_group': '3-5',
        'difficulty': 'easy',
        'title': 'Test Story',
        'moral': 'kindness',
        'cover_image': 'cover.jpg',
        'setup': {
          'setting': 'A magical forest',
          'tone': 'cheerful',
          'context': 'A story about friendship',
          'brief_intro': 'Welcome to our story!',
        },
        'background_music': 'forest_sounds.mp3',
        'narrator_profile': {
          'name': 'Friendly Narrator',
          'voice': {
            'name': 'en-US-Standard-A',
            'pitch': 1.0,
            'rate': 0.5,
            'volume': 1.0,
          },
        },
        'characters': [
          {
            'name': 'Alice',
            'description': 'A kind girl',
            'role': 'protagonist',
            'voice': {
              'pitch': 1.2,
              'rate': 0.6,
              'volume': 0.9,
            },
          },
        ],
        'scenes': [
          {
            'id': 'scene1',
            'text': 'Once upon a time...',
            'speaker': 'Narrator',
            'emotion': 'calm',
            'image': 'scene1.jpg',
            'pause_duration': 1000,
            'next': 'scene2',
          },
        ],
        'post_story': {
          'discussion': {
            'text': 'What did you learn?',
            'emotion': 'curious',
          },
        },
      };
    });

    test('should create EnhancedStoryModel from valid JSON', () {
      final story = EnhancedStoryModel.fromJson(validStoryJson);

      expect(story.storyId, equals('test_story'));
      expect(story.ageGroup, equals('3-5'));
      expect(story.difficulty, equals('easy'));
      expect(story.title, equals('Test Story'));
      expect(story.moral, equals('kindness'));
      expect(story.coverImage, equals('cover.jpg'));
      expect(story.backgroundMusic, equals('forest_sounds.mp3'));
    });

    test('should serialize EnhancedStoryModel to JSON', () {
      final story = EnhancedStoryModel.fromJson(validStoryJson);
      final json = story.toJson();

      expect(json['story_id'], equals('test_story'));
      expect(json['title'], equals('Test Story'));
      expect(json['moral'], equals('kindness'));
    });

    test('should get initial scene correctly', () {
      final story = EnhancedStoryModel.fromJson(validStoryJson);
      final initialScene = story.initialScene;

      expect(initialScene.id, equals('scene1'));
      expect(initialScene.text, equals('Once upon a time...'));
    });

    test('should get scene by ID', () {
      final story = EnhancedStoryModel.fromJson(validStoryJson);
      final scene = story.getSceneById('scene1');

      expect(scene, isNotNull);
      expect(scene!.id, equals('scene1'));
    });

    test('should return null for non-existent scene ID', () {
      final story = EnhancedStoryModel.fromJson(validStoryJson);
      final scene = story.getSceneById('non_existent');

      expect(scene, isNull);
    });

    test('should get character by name', () {
      final story = EnhancedStoryModel.fromJson(validStoryJson);
      final character = story.getCharacterByName('Alice');

      expect(character, isNotNull);
      expect(character!.name, equals('Alice'));
      expect(character.role, equals('protagonist'));
    });

    test('should return null for non-existent character name', () {
      final story = EnhancedStoryModel.fromJson(validStoryJson);
      final character = story.getCharacterByName('Bob');

      expect(character, isNull);
    });

    test('should generate correct cover image path', () {
      final story = EnhancedStoryModel.fromJson(validStoryJson);
      final coverPath = story.coverImagePath;

      expect(coverPath, equals('assets/stories/test_story/images/cover.jpg'));
    });

    test('should generate correct background music path', () {
      final story = EnhancedStoryModel.fromJson(validStoryJson);
      final musicPath = story.backgroundMusicPath;

      expect(musicPath, equals('assets/stories/test_story/assets/forest_sounds.mp3'));
    });

    test('should handle null background music', () {
      validStoryJson.remove('background_music');
      final story = EnhancedStoryModel.fromJson(validStoryJson);
      final musicPath = story.backgroundMusicPath;

      expect(musicPath, isNull);
    });
  });

  group('StorySetupModel Tests', () {
    test('should create StorySetupModel from JSON', () {
      final json = {
        'setting': 'A magical forest',
        'tone': 'cheerful',
        'context': 'A story about friendship',
        'brief_intro': 'Welcome to our story!',
      };

      final setup = StorySetupModel.fromJson(json);

      expect(setup.setting, equals('A magical forest'));
      expect(setup.tone, equals('cheerful'));
      expect(setup.context, equals('A story about friendship'));
      expect(setup.briefIntro, equals('Welcome to our story!'));
    });

    test('should handle null brief_intro', () {
      final json = {
        'setting': 'A magical forest',
        'tone': 'cheerful',
        'context': 'A story about friendship',
      };

      final setup = StorySetupModel.fromJson(json);

      expect(setup.briefIntro, isNull);
    });
  });

  group('CharacterModel Tests', () {
    test('should create CharacterModel from JSON', () {
      final json = {
        'name': 'Alice',
        'description': 'A kind girl',
        'role': 'protagonist',
        'voice': {
          'pitch': 1.2,
          'rate': 0.6,
          'volume': 0.9,
        },
      };

      final character = CharacterModel.fromJson(json);

      expect(character.name, equals('Alice'));
      expect(character.description, equals('A kind girl'));
      expect(character.role, equals('protagonist'));
      expect(character.voice.pitch, equals(1.2));
    });

    test('should generate correct image path', () {
      final json = {
        'name': 'Alice',
        'description': 'A kind girl',
        'role': 'protagonist',
        'voice': {
          'pitch': 1.2,
          'rate': 0.6,
          'volume': 0.9,
        },
      };

      final character = CharacterModel.fromJson(json);
      final imagePath = character.getImagePath('test_story');

      expect(imagePath, equals('assets/stories/test_story/images/alice.jpg'));
    });
  });

  group('EnhancedSceneModel Tests', () {
    test('should create EnhancedSceneModel from JSON', () {
      final json = {
        'id': 'scene1',
        'text': 'Once upon a time...',
        'speaker': 'Narrator',
        'emotion': 'calm',
        'image': 'scene1.jpg',
        'pause_duration': 1000,
        'next': 'scene2',
      };

      final scene = EnhancedSceneModel.fromJson(json);

      expect(scene.id, equals('scene1'));
      expect(scene.text, equals('Once upon a time...'));
      expect(scene.speaker, equals('Narrator'));
      expect(scene.emotion, equals('calm'));
      expect(scene.pauseDuration, equals(1000));
      expect(scene.next, equals('scene2'));
    });

    test('should detect scenes with choices', () {
      final jsonWithChoices = {
        'id': 'scene1',
        'text': 'What do you choose?',
        'speaker': 'Narrator',
        'emotion': 'curious',
        'image': 'scene1.jpg',
        'pause_duration': 1000,
        'choices': [
          {
            'option': 'Go left',
            'visual': 'left_arrow.png',
            'next': 'scene2',
          },
        ],
      };

      final scene = EnhancedSceneModel.fromJson(jsonWithChoices);

      expect(scene.hasChoices, isTrue);
      expect(scene.choices!.length, equals(1));
      expect(scene.choices!.first.option, equals('Go left'));
    });

    test('should detect ending scenes', () {
      final jsonEnding = {
        'id': 'ending',
        'text': 'The end.',
        'speaker': 'Narrator',
        'emotion': 'satisfied',
        'image': 'ending.jpg',
        'pause_duration': 2000,
      };

      final scene = EnhancedSceneModel.fromJson(jsonEnding);

      expect(scene.isEnding, isTrue);
      expect(scene.hasChoices, isFalse);
    });

    test('should generate correct image path', () {
      final json = {
        'id': 'scene1',
        'text': 'Once upon a time...',
        'speaker': 'Narrator',
        'emotion': 'calm',
        'image': 'scene1.jpg',
        'pause_duration': 1000,
      };

      final scene = EnhancedSceneModel.fromJson(json);
      final imagePath = scene.getImagePath('test_story');

      expect(imagePath, equals('assets/stories/test_story/images/scene1.jpg'));
    });
  });

  group('ChoiceOptionModel Tests', () {
    test('should create ChoiceOptionModel from JSON', () {
      final json = {
        'option': 'Go left',
        'visual': 'left_arrow.png',
        'next': 'scene2',
      };

      final choice = ChoiceOptionModel.fromJson(json);

      expect(choice.option, equals('Go left'));
      expect(choice.visual, equals('left_arrow.png'));
      expect(choice.next, equals('scene2'));
    });

    test('should generate correct visual path', () {
      final json = {
        'option': 'Go left',
        'visual': 'left_arrow.png',
        'next': 'scene2',
      };

      final choice = ChoiceOptionModel.fromJson(json);
      final visualPath = choice.getVisualPath('test_story');

      expect(visualPath, equals('assets/stories/test_story/images/left_arrow.png'));
    });
  });

  group('VoiceModel Tests', () {
    test('should create VoiceModel from JSON', () {
      final json = {
        'name': 'en-US-Standard-A',
        'pitch': 1.0,
        'rate': 0.5,
        'volume': 1.0,
      };

      final voice = VoiceModel.fromJson(json);

      expect(voice.name, equals('en-US-Standard-A'));
      expect(voice.pitch, equals(1.0));
      expect(voice.rate, equals(0.5));
      expect(voice.volume, equals(1.0));
    });

    test('should handle null voice name', () {
      final json = {
        'pitch': 1.0,
        'rate': 0.5,
        'volume': 1.0,
      };

      final voice = VoiceModel.fromJson(json);

      expect(voice.name, isNull);
      expect(voice.pitch, equals(1.0));
    });
  });
}
