// File generated by FlutterFire CLI.
// ignore_for_file: type=lint
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        return macos;
      case TargetPlatform.windows:
        return windows;
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyAjJhXGEUI74on-7xJVfzVNVijct4FNTtE',
    appId: '1:64639788428:web:452f632085a6518aa5e2a9',
    messagingSenderId: '64639788428',
    projectId: 'interactive-tales-de6d4',
    authDomain: 'interactive-tales-de6d4.firebaseapp.com',
    databaseURL: 'https://interactive-tales-de6d4-default-rtdb.firebaseio.com',
    storageBucket: 'interactive-tales-de6d4.firebasestorage.app',
    measurementId: 'G-GGWTBTERXZ',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyCPCgvdVD35YbqYJzbrrD8RO0XCWU2VZNw',
    appId: '1:64639788428:android:fdc18f6a241c4af5a5e2a9',
    messagingSenderId: '64639788428',
    projectId: 'interactive-tales-de6d4',
    databaseURL: 'https://interactive-tales-de6d4-default-rtdb.firebaseio.com',
    storageBucket: 'interactive-tales-de6d4.firebasestorage.app',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyC4mXEYQwa8GIA6aknK6UlSRKPpc1JHGCo',
    appId: '1:64639788428:ios:eb0af7ad1bd56150a5e2a9',
    messagingSenderId: '64639788428',
    projectId: 'interactive-tales-de6d4',
    databaseURL: 'https://interactive-tales-de6d4-default-rtdb.firebaseio.com',
    storageBucket: 'interactive-tales-de6d4.firebasestorage.app',
    iosClientId: '64639788428-f1ncitddu0rsate0gofphpcbt69sl9ld.apps.googleusercontent.com',
    iosBundleId: 'com.example.choiceOnceUponATime',
  );

  static const FirebaseOptions macos = FirebaseOptions(
    apiKey: 'AIzaSyC4mXEYQwa8GIA6aknK6UlSRKPpc1JHGCo',
    appId: '1:64639788428:ios:eb0af7ad1bd56150a5e2a9',
    messagingSenderId: '64639788428',
    projectId: 'interactive-tales-de6d4',
    databaseURL: 'https://interactive-tales-de6d4-default-rtdb.firebaseio.com',
    storageBucket: 'interactive-tales-de6d4.firebasestorage.app',
    iosClientId: '64639788428-f1ncitddu0rsate0gofphpcbt69sl9ld.apps.googleusercontent.com',
    iosBundleId: 'com.example.choiceOnceUponATime',
  );

  static const FirebaseOptions windows = FirebaseOptions(
    apiKey: 'AIzaSyAjJhXGEUI74on-7xJVfzVNVijct4FNTtE',
    appId: '1:64639788428:web:c67df68dd077fa67a5e2a9',
    messagingSenderId: '64639788428',
    projectId: 'interactive-tales-de6d4',
    authDomain: 'interactive-tales-de6d4.firebaseapp.com',
    databaseURL: 'https://interactive-tales-de6d4-default-rtdb.firebaseio.com',
    storageBucket: 'interactive-tales-de6d4.firebasestorage.app',
    measurementId: 'G-GDN9SQBM0N',
  );
}
