import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter/foundation.dart';
import 'package:choice_once_upon_a_time/models/story_model.dart';
import 'package:choice_once_upon_a_time/models/scene_model.dart';
import 'package:choice_once_upon_a_time/features/story_library/data/story_repository.dart';
import 'package:choice_once_upon_a_time/core/audio/tts_service_interface.dart';
import 'package:choice_once_upon_a_time/app/providers/service_providers.dart';

/// State for the story player
class StoryPlayerState {
  final StoryModel? story;
  final SceneModel? currentScene;
  final bool isLoading;
  final String? error;
  final bool isPlaying;
  final int currentSegmentIndex;

  const StoryPlayerState({
    this.story,
    this.currentScene,
    this.isLoading = false,
    this.error,
    this.isPlaying = false,
    this.currentSegmentIndex = 0,
  });

  StoryPlayerState copyWith({
    StoryModel? story,
    SceneModel? currentScene,
    bool? isLoading,
    String? error,
    bool? isPlaying,
    int? currentSegmentIndex,
  }) {
    return StoryPlayerState(
      story: story ?? this.story,
      currentScene: currentScene ?? this.currentScene,
      isLoading: isLoading ?? this.isLoading,
      error: error ?? this.error,
      isPlaying: isPlaying ?? this.isPlaying,
      currentSegmentIndex: currentSegmentIndex ?? this.currentSegmentIndex,
    );
  }
}

/// Provider for managing story player state and logic
class StoryPlayerNotifier extends StateNotifier<StoryPlayerState> {
  final StoryRepository _storyRepository;
  final TTSServiceInterface _ttsService;

  StoryPlayerNotifier(this._storyRepository, this._ttsService) : super(const StoryPlayerState());

  /// Load a story by ID and initialize the player
  Future<void> loadStory(String storyId, {String dataSource = 'asset'}) async {
    debugPrint('[StoryPlayerNotifier.loadStory] Loading story with ID: $storyId from $dataSource');
    state = state.copyWith(isLoading: true, error: null);

    try {
      final story = await _storyRepository.fetchStoryById(storyId, dataSource: dataSource);

      await _initializeStoryPlayer(story);
    } catch (e) {
      debugPrint('[StoryPlayerNotifier.loadStory] Error loading story $storyId: $e');
      state = state.copyWith(
        isLoading: false,
        error: 'Could not load the story. Please try again.',
        story: null,
        currentScene: null,
      );
    }
  }

  /// Load an AI generated story directly
  Future<void> loadAIGeneratedStory(StoryModel story) async {
    debugPrint('[StoryPlayerNotifier.loadAIGeneratedStory] Loading AI generated story: ${story.title}');
    state = state.copyWith(isLoading: true, error: null);

    try {
      await _initializeStoryPlayer(story);
    } catch (e) {
      debugPrint('[StoryPlayerNotifier.loadAIGeneratedStory] Error loading AI story: $e');
      state = state.copyWith(
        isLoading: false,
        error: 'Could not load the AI generated story. Please try again.',
        story: null,
        currentScene: null,
      );
    }
  }

  /// Common initialization logic for both regular and AI generated stories
  Future<void> _initializeStoryPlayer(StoryModel story) async {
    debugPrint('[StoryPlayerNotifier._initializeStoryPlayer] Initializing story: ${story.title} (ID: ${story.id})');

    // Ensure the story has scenes
    if (story.scenes.isEmpty) {
      debugPrint('[StoryPlayerNotifier._initializeStoryPlayer] Story has no scenes');
      state = state.copyWith(
        isLoading: false,
        error: 'This story appears to be empty. Please try another one.',
        story: null,
        currentScene: null,
      );
      return;
    }

    // Find the initial scene
    final initialScene = story.scenes.firstWhere(
      (scene) => scene.sceneId == story.initialSceneId,
      orElse: () => story.scenes.first,
    );

    debugPrint('[StoryPlayerNotifier._initializeStoryPlayer] Starting with scene: ${initialScene.sceneId}');

    // Initialize TTS service
    await _ttsService.setLanguage(story.defaultLanguage);

    state = state.copyWith(
      story: story,
      currentScene: initialScene,
      isLoading: false,
      error: null,
      currentSegmentIndex: 0,
      isPlaying: false,
    );

    // Auto-start narration for the first scene
    if (initialScene.narratorSegments.isNotEmpty) {
      debugPrint('[StoryPlayerNotifier._initializeStoryPlayer] Auto-starting narration for first scene');
      // Start playing automatically
      state = state.copyWith(isPlaying: true);
      await _speakCurrentSegment();
    }
  }

  /// Select a choice and navigate to the next scene
  void selectChoice(String choiceId) {
    final currentScene = state.currentScene;
    final story = state.story;
    
    if (currentScene == null || story == null) {
      debugPrint('[StoryPlayerNotifier.selectChoice] Cannot select choice: story or scene is null');
      return;
    }

    debugPrint('[StoryPlayerNotifier.selectChoice] Selecting choice $choiceId for story ${story.storyId}');

    try {
      // Find the selected choice
      final selectedChoice = currentScene.choices?.firstWhere(
        (choice) => choice.choiceId == choiceId,
      );

      if (selectedChoice == null) {
        debugPrint('[StoryPlayerNotifier.selectChoice] Invalid choice $choiceId for scene ${currentScene.sceneId}');
        state = state.copyWith(
          error: 'Invalid choice selected. Please try again.',
        );
        return;
      }

      debugPrint('[StoryPlayerNotifier.selectChoice] Navigating to scene ${selectedChoice.leadsToSceneId}');
      // Navigate to the next scene
      _navigateToScene(selectedChoice.leadsToSceneId);
    } catch (e) {
      debugPrint('[StoryPlayerNotifier.selectChoice] Error selecting choice: $e');
      state = state.copyWith(
        error: 'Oops! Something went wrong with your choice. Please try again.',
      );
    }
  }

  /// Navigate to a specific scene by ID
  void _navigateToScene(String sceneId) {
    final story = state.story;
    if (story == null) {
      debugPrint('[StoryPlayerNotifier._navigateToScene] Cannot navigate: story is null');
      return;
    }

    debugPrint('[StoryPlayerNotifier._navigateToScene] Navigating to scene $sceneId in story ${story.storyId}');

    try {
      final nextScene = story.scenes.firstWhere(
        (scene) => scene.sceneId == sceneId,
      );

      debugPrint('[StoryPlayerNotifier._navigateToScene] Successfully loaded scene $sceneId');

      state = state.copyWith(
        currentScene: nextScene,
        currentSegmentIndex: 0,
        error: null,
      );
    } catch (e) {
      debugPrint('[StoryPlayerNotifier._navigateToScene] Error navigating to scene $sceneId: $e');
      state = state.copyWith(
        error: 'Scene not found. The story may be incomplete.',
      );
    }
  }

  /// Progress to the next scene (for linear progression)
  void progressToNextScene() {
    final currentScene = state.currentScene;
    if (currentScene?.nextSceneId != null) {
      _navigateToScene(currentScene!.nextSceneId!);
    } else {
      // End of story reached
      state = state.copyWith(
        error: null, // Clear any previous errors
      );
      // TODO: Navigate to story completion screen
    }
  }

  /// Toggle play/pause state and control TTS
  void togglePlayPause() {
    final newPlayingState = !state.isPlaying;
    state = state.copyWith(isPlaying: newPlayingState);

    if (newPlayingState) {
      _speakCurrentSegment();
    } else {
      _ttsService.pause();
    }
  }

  /// Speak the current narrator segment
  Future<void> _speakCurrentSegment() async {
    final currentScene = state.currentScene;
    final story = state.story;

    if (currentScene == null || story == null) {
      debugPrint('[StoryPlayerNotifier._speakCurrentSegment] Cannot speak: story or scene is null');
      return;
    }
    if (currentScene.narratorSegments.isEmpty) {
      debugPrint('[StoryPlayerNotifier._speakCurrentSegment] No narrator segments available for scene ${currentScene.sceneId}');
      return;
    }

    final segmentIndex = state.currentSegmentIndex;
    if (segmentIndex >= currentScene.narratorSegments.length) {
      debugPrint('[StoryPlayerNotifier._speakCurrentSegment] Invalid segment index $segmentIndex');
      return;
    }

    debugPrint('[StoryPlayerNotifier._speakCurrentSegment] Speaking segment $segmentIndex in scene ${currentScene.sceneId} (Story: ${story.storyId})');

    final segment = currentScene.narratorSegments[segmentIndex];

    try {
      final success = await _ttsService.speakSegment(segment, story.defaultLanguage);
      if (success) {
        // TTS completed successfully, auto-advance to next segment if available
        _onSegmentCompleted();
      }
    } catch (e) {
      debugPrint('[StoryPlayerNotifier._speakCurrentSegment] Error speaking segment: $e');
      state = state.copyWith(isPlaying: false);
    }
  }

  /// Handle completion of a narrator segment
  void _onSegmentCompleted() {
    final currentScene = state.currentScene;
    if (currentScene == null) return;

    final maxSegments = currentScene.narratorSegments.length;
    if (state.currentSegmentIndex < maxSegments - 1) {
      // Move to next segment
      debugPrint('[StoryPlayerNotifier._onSegmentCompleted] Moving to next segment');
      state = state.copyWith(
        currentSegmentIndex: state.currentSegmentIndex + 1,
      );
      // Continue speaking the next segment
      _speakCurrentSegment();
    } else {
      // All segments completed
      debugPrint('[StoryPlayerNotifier._onSegmentCompleted] All segments completed');
      state = state.copyWith(isPlaying: false);

      // Check if we should auto-progress to next scene
      if (currentScene.sceneType == 'narration_illustration' &&
          currentScene.nextSceneId != null) {
        debugPrint('[StoryPlayerNotifier._onSegmentCompleted] Auto-progressing to next scene');
        progressToNextScene();
      }
    }
  }

  /// Stop TTS playback
  void stopTTS() {
    _ttsService.stop();
    state = state.copyWith(isPlaying: false);
  }

  /// Replay the current segment
  void replayCurrentSegment() {
    _ttsService.stop();
    state = state.copyWith(isPlaying: true);
    _speakCurrentSegment();
  }

  /// Move to next narrator segment
  void nextSegment() {
    final currentScene = state.currentScene;
    if (currentScene == null) return;

    final maxSegments = currentScene.narratorSegments.length;
    if (state.currentSegmentIndex < maxSegments - 1) {
      state = state.copyWith(
        currentSegmentIndex: state.currentSegmentIndex + 1,
      );
    } else {
      // All segments completed, check if we should auto-progress
      if (currentScene.sceneType == 'narration_illustration' && 
          currentScene.nextSceneId != null) {
        progressToNextScene();
      }
    }
  }

  /// Move to previous narrator segment
  void previousSegment() {
    if (state.currentSegmentIndex > 0) {
      state = state.copyWith(
        currentSegmentIndex: state.currentSegmentIndex - 1,
      );
    }
  }



  /// Clear any error messages
  void clearError() {
    state = state.copyWith(error: null);
  }

  /// Reset the story player state
  void reset() {
    state = const StoryPlayerState();
  }
}

/// Provider for the story player
final storyPlayerProvider = StateNotifierProvider<StoryPlayerNotifier, StoryPlayerState>((ref) {
  final storyRepository = ref.watch(storyRepositoryProvider);
  final ttsService = ref.watch(ttsServiceProvider);
  return StoryPlayerNotifier(storyRepository, ttsService);
});

/// Provider for the story repository
final storyRepositoryProvider = Provider<StoryRepository>((ref) {
  return StoryRepository();
});
