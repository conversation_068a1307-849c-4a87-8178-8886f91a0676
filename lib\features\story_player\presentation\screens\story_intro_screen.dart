import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:choice_once_upon_a_time/features/story_library/presentation/providers/story_library_provider.dart';
import 'package:choice_once_upon_a_time/features/story_library/presentation/providers/download_manager_provider.dart';
import 'package:choice_once_upon_a_time/shared_widgets/primary_button_widget.dart';
import 'package:choice_once_upon_a_time/shared/widgets/popups/download_progress_dialog.dart';
import 'package:choice_once_upon_a_time/core/storage/offline_storage_service_mobile.dart';
import 'package:choice_once_upon_a_time/models/story_metadata_model.dart';
import 'package:choice_once_upon_a_time/core/audio/voice_guidance_manager.dart'; // Added import
import 'package:choice_once_upon_a_time/core/utils/app_logger.dart';

/// Story introduction screen
class StoryIntroScreen extends ConsumerStatefulWidget {
  final String storyId;
  final String dataSource;

  const StoryIntroScreen({
    super.key,
    required this.storyId,
    this.dataSource = 'asset',
  });

  @override
  ConsumerState<StoryIntroScreen> createState() => _StoryIntroScreenState();
}

class _StoryIntroScreenState extends ConsumerState<StoryIntroScreen> {
  final _offlineStorage = OfflineStorageService();
  bool? _isOfflineAvailable;
  bool _isShowingProgress = false;
  StoryMetadataModel? _storyMetadata; // To store fetched story metadata

  @override
  void initState() {
    super.initState();
    _checkOfflineAvailability();
    // Fetch metadata and play intro after the first frame
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _fetchStoryMetadataAndPlayIntro();
    });
  }

  Future<void> _fetchStoryMetadataAndPlayIntro() async {
    // Ensure the widget is still mounted
    if (!mounted) return;

    final story = ref.read(storyLibraryProvider)
        .stories
        .where((s) => s.id == widget.storyId)
        .firstOrNull;

    if (story != null) {
      setState(() {
        _storyMetadata = story;
      });
      // Use current locale, or fallback to 'en-US'
      final currentLocale = Localizations.localeOf(context).toString().replaceAll('_', '-');
      // Use the existing logline as the description for narration
      final description = story.getLocalizedLogline(currentLocale);
      
      if (description.isNotEmpty) {
        final voiceGuidanceManager = ref.read(voiceGuidanceManagerProvider);
        // It's good practice to stop any ongoing speech before starting a new one.
        await voiceGuidanceManager.stopGuide(); 
        await voiceGuidanceManager.playText(description, emotionCue: 'neutral');
      } else {
        // Optionally play a generic intro if description is missing
        final voiceGuidanceManager = ref.read(voiceGuidanceManagerProvider);
        await voiceGuidanceManager.playGuide(context, (l10n) => l10n.storyIntroGenericPrompt, emotionCue: 'neutral');
      }
    } else {
       // Optionally play a "story not found" or similar guide
        final voiceGuidanceManager = ref.read(voiceGuidanceManagerProvider);
        await voiceGuidanceManager.playGuide(context, (l10n) => l10n.errorStoryNotFound, emotionCue: 'neutral');
    }
  }

  Future<void> _checkOfflineAvailability() async {
    final isAvailable = await _offlineStorage.isStoryDownloaded(widget.storyId, '1.0.0'); // Assuming a version, adjust if necessary
    if (mounted) {
      setState(() {
        _isOfflineAvailable = isAvailable;
      });
    }
  }

  @override
  void dispose() {
    // Stop voice guidance when the screen is disposed
    if (mounted && ProviderScope.containerOf(context, listen: false).exists(voiceGuidanceManagerProvider)) {
      ref.read(voiceGuidanceManagerProvider).stopGuide();
    }
    super.dispose();
  }

  /// Show a confirmation dialog before starting a download
  Future<bool> _showDownloadConfirmation(BuildContext context) async {
    // Stop guidance before showing dialog
    ref.read(voiceGuidanceManagerProvider).stopGuide();
    final bool result = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Download Story?'),
        content: const Text('Would you like to download this story for offline reading?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Not Now'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text('Download'),
          ),
        ],
      ),
    ) ?? false;
    // Potentially resume or play new guidance after dialog closes if needed
    // For now, we'll let the next screen handle its own intro.
    return result;
  }

  void _showDownloadProgress(BuildContext context, String storyId) {
    if (!_isShowingProgress) {
      _isShowingProgress = true;
      // Stop guidance before showing dialog
      ref.read(voiceGuidanceManagerProvider).stopGuide();
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => Consumer(
          builder: (context, ref, _) {
            final downloadState = ref.watch(downloadManagerProvider);
            final progress = downloadState.getProgress(storyId);
            final status = downloadState.getStatus(storyId);
            final isDownloading = downloadState.isDownloading(storyId);

            if (!isDownloading) {
              WidgetsBinding.instance.addPostFrameCallback((_) {
                if (Navigator.of(context).canPop()) { // Check if dialog is still shown
                    Navigator.of(context).pop();
                }
                _isShowingProgress = false;
              });
            }

            return DownloadProgressDialog(
              progress: progress,
              status: status,
              onCancel: () {
                ref.read(downloadManagerProvider.notifier).cancelDownload(storyId);
                 if (Navigator.of(context).canPop()) {
                    Navigator.of(context).pop();
                }
                _isShowingProgress = false;
              },
            );
          },
        ),
      ).then((_) {
        // After dialog closes, decide if new guidance is needed.
        // For example, if download was cancelled, or if it completed.
        // For now, we defer to the main screen intro logic.
        _fetchStoryMetadataAndPlayIntro(); // Re-play intro or relevant guide
      });
    }
  }

  Future<void> _startStory(BuildContext context, String storyId, String dataSource) async {
    // Stop any current narration before navigating
    await ref.read(voiceGuidanceManagerProvider).stopGuide();

    final downloadManager = ref.read(downloadManagerProvider);
    final isDownloading = downloadManager.isDownloading(storyId);

    if (isDownloading) {
      // Maybe play a "download in progress" guide
      await ref.read(voiceGuidanceManagerProvider).playGuide(context, (l10n) => l10n.downloadInProgressPleaseWait);
      return;
    }

    if (_isOfflineAvailable == true) {
      context.go('/loading/$storyId/$dataSource');
      return;
    }

    final shouldDownload = await _showDownloadConfirmation(context);
    if (shouldDownload) {
      final notifier = ref.read(downloadManagerProvider.notifier);
      final story = _storyMetadata ?? ref.read(storyLibraryProvider).stories.firstWhere((s) => s.id == storyId);
      
      _showDownloadProgress(context, storyId);
      
      final success = await notifier.startDownload(story);

      if (success && mounted) {
        setState(() {
          _isOfflineAvailable = true;
        });
        // Stop narration before navigating
        await ref.read(voiceGuidanceManagerProvider).stopGuide();
        context.go('/loading/$storyId/$dataSource');
      } else if (!success && mounted) {
        // Handle download failure, maybe play a guide
         await ref.read(voiceGuidanceManagerProvider).playGuide(context, (l10n) => l10n.errorDownloadFailed);
         _fetchStoryMetadataAndPlayIntro(); // Re-play intro if download failed and dialog closed
      }
    } else {
      // Stop narration before navigating
      await ref.read(voiceGuidanceManagerProvider).stopGuide();
      context.go('/loading/$storyId/$dataSource');
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final storyLibraryState = ref.watch(storyLibraryProvider);
    final downloadState = ref.watch(downloadManagerProvider);
    final screenSize = MediaQuery.of(context).size;
    
    final screenMode = _determineScreenMode(screenSize);
    
    // Use the stored _storyMetadata if available, otherwise fetch
    final story = _storyMetadata ?? storyLibraryState.stories
        .where((s) => s.id == widget.storyId)
        .firstOrNull;

    if (story == null && _storyMetadata == null) { // Check both as metadata might be fetching
      // If still loading metadata, show a generic loading or wait.
      // If fetch already attempted and failed (e.g. from initState), show not found.
      // For now, assuming initState's fetch is in progress or build is called before it completes.
      return Scaffold(
          appBar: AppBar(title: const Text("Loading...")), 
          body: const Center(child: CircularProgressIndicator()));
    }
    
    if (story == null){ // This means _storyMetadata is also null and fetch failed
        return _buildNotFoundScreen();
    }


    final isDownloading = downloadState.isDownloading(story.id);
    if (isDownloading && !_isShowingProgress) {
      // Potentially stop voice guide before showing progress
      // ref.read(voiceGuidanceManagerProvider).stopGuide(); // Already handled in _showDownloadProgress
      _showDownloadProgress(context, story.id);
    }

    return Scaffold(
      appBar: AppBar(
        title: Text(story.getLocalizedTitle(Localizations.localeOf(context).toString().replaceAll('_', '-'))),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () {
            ref.read(voiceGuidanceManagerProvider).stopGuide();
            context.go('/home');
          }
        ),
        centerTitle: screenMode == ScreenMode.tv,
      ),
      body: SafeArea(
        child: LayoutBuilder(
          builder: (context, constraints) {
            switch (screenMode) {
              case ScreenMode.portrait:
                return _buildPortraitLayout(context, story, theme, constraints, isDownloading);
              case ScreenMode.landscape:
                return _buildLandscapeLayout(context, story, theme, constraints, isDownloading);
              case ScreenMode.tv:
                return _buildTVLayout(context, story, theme, constraints, isDownloading);
            }
          },
        ),
      ),
    );
  }

  ScreenMode _determineScreenMode(Size screenSize) {
    final width = screenSize.width;
    final isPortrait = screenSize.height > screenSize.width;

    if (width >= 1200) {
      return ScreenMode.tv;
    } else if (isPortrait) {
      return ScreenMode.portrait;
    } else {
      return ScreenMode.landscape;
    }
  }

  Widget _buildNotFoundScreen() {
    // Optionally play a "story not found" guide when this screen is built
    // WidgetsBinding.instance.addPostFrameCallback((_) {
    //   if(mounted) ref.read(voiceGuidanceManagerProvider).playGuide(context, (l10n) => l10n.errorStoryNotFound);
    // });
    return Scaffold(
      appBar: AppBar(
        title: const Text('Story Not Found'),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () {
             ref.read(voiceGuidanceManagerProvider).stopGuide();
             context.go('/home');
          }
        ),
      ),
      body: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.grey,
            ),
            SizedBox(height: 16),
            Text(
              'Story not found',
              style: TextStyle(fontSize: 18),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPortraitLayout(BuildContext context, StoryMetadataModel story, ThemeData theme, BoxConstraints constraints, bool isDownloading) {
    final smallScreen = constraints.maxHeight < 600;
    final spacing = smallScreen ? 16.0 : 24.0;
    
    return SingleChildScrollView(
      padding: EdgeInsets.all(smallScreen ? 16.0 : 24.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          _buildCoverSection(constraints, theme, ScreenMode.portrait, story),
          SizedBox(height: spacing * 0.75),
          _buildTitleSection(story, theme, ScreenMode.portrait, context),
          const SizedBox(height: 12),
          _buildDetailsSection(story, context, ScreenMode.portrait),
          SizedBox(height: spacing * 0.75),
          _buildActionButtons(story, isDownloading, context, ScreenMode.portrait),
        ],
      ),
    );
  }

  Widget _buildLandscapeLayout(BuildContext context, StoryMetadataModel story, ThemeData theme, BoxConstraints constraints, bool isDownloading) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(24.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Expanded(
            flex: 3,
            child: _buildCoverSection(constraints, theme, ScreenMode.landscape, story),
          ),
          const SizedBox(width: 24),
          Expanded(
            flex: 4,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildTitleSection(story, theme, ScreenMode.landscape, context),
                const SizedBox(height: 12),
                _buildDetailsSection(story, context, ScreenMode.landscape),
                const SizedBox(height: 24),
                _buildActionButtons(story, isDownloading, context, ScreenMode.landscape),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTVLayout(BuildContext context, StoryMetadataModel story, ThemeData theme, BoxConstraints constraints, bool isDownloading) {
    final maxWidth = constraints.maxWidth * 0.8; 
    
    return Center(
      child: SizedBox(
        width: maxWidth,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(32.0),
          child: Column(
            children: [
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  SizedBox(
                    width: maxWidth * 0.4,
                    child: _buildCoverSection(constraints, theme, ScreenMode.tv, story),
                  ),
                  const SizedBox(width: 32),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        _buildTitleSection(story, theme, ScreenMode.tv, context),
                        const SizedBox(height: 24),
                        _buildDetailsSection(story, context, ScreenMode.tv),
                      ],
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 32),
              SizedBox(
                width: maxWidth * 0.6, 
                child: _buildActionButtons(story, isDownloading, context, ScreenMode.tv),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildCoverSection(BoxConstraints constraints, ThemeData theme, ScreenMode mode, StoryMetadataModel story) {
    final aspectRatio = mode == ScreenMode.tv 
        ? 16/9 
        : mode == ScreenMode.landscape 
            ? 3/4 
            : 4/3;

    final iconSize = mode == ScreenMode.tv 
        ? 160.0 
        : constraints.maxWidth > 600 
            ? 120.0 
            : 80.0;
            
    final coverImageUrl = story.coverImageUrl;

    return AspectRatio(
      aspectRatio: aspectRatio,
      child: Stack(
        children: [
          Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(mode == ScreenMode.tv ? 24 : 16),
              image: coverImageUrl.isNotEmpty
                  ? DecorationImage(
                      image: () {
                        AppLogger.debug('[IMAGE_LOAD] Asset: $coverImageUrl | Location: lib/features/story_player/presentation/screens/story_intro_screen.dart:444 | Widget: DecorationImage');
                        return coverImageUrl.startsWith('assets/')
                            ? AssetImage(coverImageUrl) as ImageProvider
                            : NetworkImage(coverImageUrl);
                      }(),
                      fit: BoxFit.cover,
                      onError: (exception, stackTrace) {
                        // Handle image loading errors gracefully
                        AppLogger.debug('Error loading cover image: $exception');
                      },
                    )
                  : null,
              gradient: coverImageUrl.isEmpty ? LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  Colors.blue[100]!,
                  Colors.purple[100]!,
                ],
              ) : null,
            ),
            child: (coverImageUrl.isEmpty) ? Column( // Only show icon if no image
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.book,
                  size: iconSize,
                  color: Colors.grey[600],
                ),
              ],
            ) : null,
          ),
          if (_isOfflineAvailable == true)
            Positioned(
              top: 16,
              right: 16,
              child: _buildOfflineIndicator(theme, mode),
            ),
        ],
      ),
    );
  }

  Widget _buildOfflineIndicator(ThemeData theme, ScreenMode mode) {
    final scale = mode == ScreenMode.tv ? 1.5 : 1.0;
    
    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: 12 * scale,
        vertical: 6 * scale,
      ),
      decoration: BoxDecoration(
        color: Colors.green.withValues(alpha: 0.9),
        borderRadius: BorderRadius.circular(16 * scale),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            Icons.offline_pin,
            size: 16 * scale,
            color: Colors.white,
          ),
          SizedBox(width: 4 * scale),
          Text(
            'Available Offline',
            style: theme.textTheme.bodySmall?.copyWith(
              color: Colors.white,
              fontWeight: FontWeight.w500,
              fontSize: (theme.textTheme.bodySmall?.fontSize ?? 12) * scale,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTitleSection(StoryMetadataModel story, ThemeData theme, ScreenMode mode, BuildContext context) {
    final titleStyle = mode == ScreenMode.tv
        ? theme.textTheme.displaySmall?.copyWith(fontWeight: FontWeight.bold)
        : theme.textTheme.headlineMedium?.copyWith(fontWeight: FontWeight.bold);

    final subtitleStyle = mode == ScreenMode.tv
        ? theme.textTheme.headlineSmall?.copyWith(color: Colors.grey[600])
        : theme.textTheme.bodyLarge?.copyWith(color: Colors.grey[600]);

    final currentLocale = Localizations.localeOf(context).toString().replaceAll('_', '-');
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          story.getLocalizedTitle(currentLocale),
          style: titleStyle,
        ),
        SizedBox(height: mode == ScreenMode.tv ? 12 : 8),
        Text(
          story.getLocalizedLogline(currentLocale), // Logline is usually shorter than description
          style: subtitleStyle,
        ),
      ],
    );
  }

  Widget _buildDetailsSection(StoryMetadataModel story, BuildContext context, ScreenMode mode) {
    final theme = Theme.of(context);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Wrap(
          spacing: mode == ScreenMode.tv ? 16 : 8,
          runSpacing: mode == ScreenMode.tv ? 16 : 8,
          children: [
            _buildDetailChip(
              context,
              Icons.favorite,
              story.targetMoralValue,
              mode,
            ),
            _buildDetailChip(
              context,
              Icons.access_time,
              '${story.estimatedDurationMinutes} min',
              mode,
            ),
            _buildDetailChip(
              context,
              Icons.child_care,
              'Ages ${story.targetAgeSubSegment}', // Assuming this is a direct string
              mode,
            ),
          ],
        ),
        SizedBox(height: mode == ScreenMode.tv ? 16 : 12),
        Container(
          padding: EdgeInsets.all(mode == ScreenMode.tv ? 16 : 12),
          decoration: BoxDecoration(
            color: theme.colorScheme.surfaceContainerHighest.withValues(alpha: 0.5),
            borderRadius: BorderRadius.circular(mode == ScreenMode.tv ? 12 : 8),
            border: Border.all(
              color: theme.colorScheme.outline.withValues(alpha: 0.2),
            ),
          ),
          child: Row(
            children: [
              Icon(
                Icons.library_books,
                size: mode == ScreenMode.tv ? 20 : 16,
                color: theme.colorScheme.primary,
              ),
              SizedBox(width: mode == ScreenMode.tv ? 12 : 8),
              Expanded(
                child: Text(
                  'Story sourced from our curated online library',
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: theme.colorScheme.onSurfaceVariant,
                    fontSize: mode == ScreenMode.tv ? 14 : 12,
                    fontStyle: FontStyle.italic,
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildDetailChip(BuildContext context, IconData icon, String label, ScreenMode mode) {
    final scale = mode == ScreenMode.tv ? 1.5 : 1.0;
    
    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: 12 * scale,
        vertical: 6 * scale,
      ),
      decoration: BoxDecoration(
        color: Colors.grey[100],
        borderRadius: BorderRadius.circular(16 * scale),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            icon,
            size: 14 * scale,
            color: Colors.grey[600],
          ),
          SizedBox(width: 4 * scale),
          Text(
            label,
            style: TextStyle(
              fontSize: 12 * scale,
              color: Colors.grey[700],
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButtons(StoryMetadataModel story, bool isDownloading, BuildContext context, ScreenMode mode) {
    final buttonScale = mode == ScreenMode.tv ? 1.5 : 1.0;
    final buttonPadding = EdgeInsets.symmetric(
      vertical: 12 * buttonScale,
      horizontal: mode == ScreenMode.tv ? 32 : 16,
    );

    if (story.isLocked) {
      // Play guide for locked story when buttons are built
      // Consider if this should only play once or if a user focuses the section
      // WidgetsBinding.instance.addPostFrameCallback((_) {
      //    if(mounted) ref.read(voiceGuidanceManagerProvider).playGuide(context, (l10n) => l10n.storyLockedPrompt);
      // });
      return Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Container(
            padding: EdgeInsets.all(16 * buttonScale),
            decoration: BoxDecoration(
              color: Colors.orange[50],
              borderRadius: BorderRadius.circular(12 * buttonScale),
              border: Border.all(color: Colors.orange[200]!),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.lock,
                  color: Colors.orange[600],
                  size: 20 * buttonScale,
                ),
                SizedBox(width: 8 * buttonScale),
                Expanded(
                  child: Text(
                    'This story requires a premium subscription', // Consider localizing
                    style: TextStyle(
                      fontSize: 14 * buttonScale,
                      color: Colors.orange[700],
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
          ),
          SizedBox(height: 16 * buttonScale),
          PrimaryButtonWidget(
            text: 'Unlock with Premium', // Consider localizing
            onPressed: () {
              ref.read(voiceGuidanceManagerProvider).stopGuide();
              _showSubscriptionDialog(context);
            },
            icon: Icon(Icons.star, size: 24 * buttonScale),
          ),
        ],
      );
    }

    // Play guide for available actions if story is not locked
    // WidgetsBinding.instance.addPostFrameCallback((_) {
    //    if(mounted) {
    //      if (_isOfflineAvailable == true) {
    //        ref.read(voiceGuidanceManagerProvider).playGuide(context, (l10n) => l10n.storyIntroActionStartOfflinePrompt);
    //      } else {
    //        ref.read(voiceGuidanceManagerProvider).playGuide(context, (l10n) => l10n.storyIntroActionStartDownloadPrompt);
    //      }
    //    }
    // });

    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        PrimaryButtonWidget(
          text: _isOfflineAvailable == true ? 'Start Story' : 'Start Story', // Consider localizing
          onPressed: isDownloading
              ? null
              : () => _startStory(context, story.id, widget.dataSource),
          icon: Icon(
            Icons.play_arrow,
            size: 24 * buttonScale,
          ),
        ),
        if (_isOfflineAvailable != true) ...[
          SizedBox(height: 8 * buttonScale),
          OutlinedButton.icon(
            onPressed: isDownloading
                ? null
                : () => _handleDownload(context, story),
            icon: Icon(
              Icons.download,
              size: 24 * buttonScale,
            ),
            label: Text(
              'Make Available Offline', // Consider localizing
              style: TextStyle(fontSize: 14 * buttonScale),
            ),
            style: OutlinedButton.styleFrom(
              padding: buttonPadding,
            ),
          ),
        ],
      ],
    );
  }

  Future<void> _handleDownload(BuildContext context, StoryMetadataModel story) async {
    final shouldDownload = await _showDownloadConfirmation(context);
    if (shouldDownload) {
      final notifier = ref.read(downloadManagerProvider.notifier);
      _showDownloadProgress(context, story.id);
      final success = await notifier.startDownload(story);
      if (success && mounted) {
        setState(() {
          _isOfflineAvailable = true;
        });
        // Play guide for successful download
        if(mounted) await ref.read(voiceGuidanceManagerProvider).playGuide(context, (l10n) => l10n.downloadCompletePrompt);
        _fetchStoryMetadataAndPlayIntro(); // Re-play intro to guide next action
      } else if (!success && mounted) {
        // Play guide for failed download
        if(mounted) await ref.read(voiceGuidanceManagerProvider).playGuide(context, (l10n) => l10n.errorDownloadFailed);
        _fetchStoryMetadataAndPlayIntro(); // Re-play intro
      }
    } else {
      // If user chose "Not Now" for download, replay the main intro.
      _fetchStoryMetadataAndPlayIntro();
    }
  }

  void _showSubscriptionDialog(BuildContext context) {
    // Stop any current narration before showing dialog
    ref.read(voiceGuidanceManagerProvider).stopGuide();
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Premium Required'), // Consider localizing
        content: const Text(
          'This story is part of our premium collection. Subscribe to unlock all stories and features.', // Consider localizing
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'), // Consider localizing
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              ref.read(voiceGuidanceManagerProvider).stopGuide();
              context.go('/parent_zone/subscription');
            },
            child: const Text('Subscribe'), // Consider localizing
          ),
        ],
      ),
    ).then((_){
        // After dialog is dismissed, replay the main intro for context
        _fetchStoryMetadataAndPlayIntro();
    });
  }
}

enum ScreenMode {
  portrait,
  landscape,
  tv,
}
