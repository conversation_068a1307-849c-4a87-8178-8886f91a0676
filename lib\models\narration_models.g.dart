// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'narration_models.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

NarrationState _$NarrationStateFromJson(Map<String, dynamic> json) =>
    NarrationState(
      status: $enumDecodeNullable(_$NarrationStatusEnumMap, json['status']) ??
          NarrationStatus.idle,
      currentText: json['currentText'] as String?,
      currentWordIndex: (json['currentWordIndex'] as num?)?.toInt() ?? 0,
      progress: (json['progress'] as num?)?.toDouble() ?? 0.0,
      error: json['error'] as String?,
      durationMs: (json['durationMs'] as num?)?.toInt(),
      positionMs: (json['positionMs'] as num?)?.toInt(),
      autoProgress: json['autoProgress'] as bool? ?? true,
    );

Map<String, dynamic> _$NarrationStateToJson(NarrationState instance) =>
    <String, dynamic>{
      'status': _$NarrationStatusEnumMap[instance.status]!,
      'currentText': instance.currentText,
      'currentWordIndex': instance.currentWordIndex,
      'progress': instance.progress,
      'error': instance.error,
      'durationMs': instance.durationMs,
      'positionMs': instance.positionMs,
      'autoProgress': instance.autoProgress,
    };

const _$NarrationStatusEnumMap = {
  NarrationStatus.idle: 'idle',
  NarrationStatus.loading: 'loading',
  NarrationStatus.playing: 'playing',
  NarrationStatus.paused: 'paused',
  NarrationStatus.completed: 'completed',
  NarrationStatus.error: 'error',
};

NarrationConfig _$NarrationConfigFromJson(Map<String, dynamic> json) =>
    NarrationConfig(
      speechRate: (json['speechRate'] as num?)?.toDouble() ?? 0.5,
      speechPitch: (json['speechPitch'] as num?)?.toDouble() ?? 1.0,
      speechVolume: (json['speechVolume'] as num?)?.toDouble() ?? 1.0,
      language: json['language'] as String? ?? 'en-US',
      autoProgress: json['autoProgress'] as bool? ?? true,
      highlightWords: json['highlightWords'] as bool? ?? true,
      emotionCue: json['emotionCue'] as String? ?? 'neutral',
      sentencePauseMs: (json['sentencePauseMs'] as num?)?.toInt() ?? 500,
      wordPauseMs: (json['wordPauseMs'] as num?)?.toInt() ?? 100,
    );

Map<String, dynamic> _$NarrationConfigToJson(NarrationConfig instance) =>
    <String, dynamic>{
      'speechRate': instance.speechRate,
      'speechPitch': instance.speechPitch,
      'speechVolume': instance.speechVolume,
      'language': instance.language,
      'autoProgress': instance.autoProgress,
      'highlightWords': instance.highlightWords,
      'emotionCue': instance.emotionCue,
      'sentencePauseMs': instance.sentencePauseMs,
      'wordPauseMs': instance.wordPauseMs,
    };

WordHighlight _$WordHighlightFromJson(Map<String, dynamic> json) =>
    WordHighlight(
      startIndex: (json['startIndex'] as num).toInt(),
      endIndex: (json['endIndex'] as num).toInt(),
      word: json['word'] as String,
      startTimeMs: (json['startTimeMs'] as num).toInt(),
      endTimeMs: (json['endTimeMs'] as num).toInt(),
      isActive: json['isActive'] as bool? ?? false,
      sentenceIndex: (json['sentenceIndex'] as num?)?.toInt() ?? 0,
    );

Map<String, dynamic> _$WordHighlightToJson(WordHighlight instance) =>
    <String, dynamic>{
      'startIndex': instance.startIndex,
      'endIndex': instance.endIndex,
      'word': instance.word,
      'startTimeMs': instance.startTimeMs,
      'endTimeMs': instance.endTimeMs,
      'isActive': instance.isActive,
      'sentenceIndex': instance.sentenceIndex,
    };

NarrationProgress _$NarrationProgressFromJson(Map<String, dynamic> json) =>
    NarrationProgress(
      storyId: json['storyId'] as String,
      sceneId: json['sceneId'] as String,
      totalWords: (json['totalWords'] as num?)?.toInt() ?? 0,
      completedWords: (json['completedWords'] as num?)?.toInt() ?? 0,
      totalSentences: (json['totalSentences'] as num?)?.toInt() ?? 0,
      completedSentences: (json['completedSentences'] as num?)?.toInt() ?? 0,
      totalDurationMs: (json['totalDurationMs'] as num?)?.toInt() ?? 0,
      currentPositionMs: (json['currentPositionMs'] as num?)?.toInt() ?? 0,
      isCompleted: json['isCompleted'] as bool? ?? false,
      lastUpdated: DateTime.parse(json['lastUpdated'] as String),
    );

Map<String, dynamic> _$NarrationProgressToJson(NarrationProgress instance) =>
    <String, dynamic>{
      'storyId': instance.storyId,
      'sceneId': instance.sceneId,
      'totalWords': instance.totalWords,
      'completedWords': instance.completedWords,
      'totalSentences': instance.totalSentences,
      'completedSentences': instance.completedSentences,
      'totalDurationMs': instance.totalDurationMs,
      'currentPositionMs': instance.currentPositionMs,
      'isCompleted': instance.isCompleted,
      'lastUpdated': instance.lastUpdated.toIso8601String(),
    };
