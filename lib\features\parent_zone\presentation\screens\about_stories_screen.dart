import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

/// About Stories & Moral Values screen (Screen 12.1)
class AboutStoriesScreen extends StatelessWidget {
  const AboutStoriesScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      appBar: AppBar(
        title: const Text('About Stories & Moral Values'),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => context.go('/parent_zone'),
        ),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header section
            Container(
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: theme.colorScheme.primary.withOpacity(0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(
                        Icons.auto_stories,
                        color: theme.colorScheme.primary,
                        size: 28,
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Text(
                          'Our Story Philosophy',
                          style: theme.textTheme.headlineSmall?.copyWith(
                            fontWeight: FontWeight.bold,
                            color: theme.colorScheme.primary,
                          ),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 12),
                  Text(
                    'Every story in "Choice: Once Upon A Time" is carefully crafted to nurture your child\'s moral development through engaging, interactive narratives.',
                    style: theme.textTheme.bodyLarge?.copyWith(
                      color: Colors.grey[700],
                    ),
                  ),
                ],
              ),
            ),

            const SizedBox(height: 24),

            // Core Values Section
            Text(
              'Core Moral Values',
              style: theme.textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),

            // Values grid
            ..._buildMoralValueCards(theme),

            const SizedBox(height: 32),

            // Educational Approach Section
            _buildSectionCard(
              theme: theme,
              title: 'Our Educational Approach',
              icon: Icons.school,
              content: [
                'Interactive storytelling that puts your child in control of the narrative',
                'Age-appropriate moral dilemmas that encourage critical thinking',
                'Gentle guidance through an empathetic narrator',
                'Positive reinforcement of good choices and values',
                'Safe exploration of consequences in a supportive environment',
              ],
            ),

            const SizedBox(height: 24),

            // Story Structure Section
            _buildSectionCard(
              theme: theme,
              title: 'How Our Stories Work',
              icon: Icons.timeline,
              content: [
                'Each story presents meaningful choices for your child',
                'Choices lead to different story branches and outcomes',
                'The narrator provides gentle guidance and emotional support',
                'Stories conclude with reflection on the moral lesson learned',
                'Multiple playthroughs reveal different perspectives and outcomes',
              ],
            ),

            const SizedBox(height: 24),

            // Age Appropriateness Section
            _buildSectionCard(
              theme: theme,
              title: 'Age-Appropriate Content',
              icon: Icons.child_care,
              content: [
                'Designed specifically for children ages 4-7',
                'Simple language and concepts appropriate for early learners',
                'No scary or inappropriate content',
                'Bedtime-friendly themes that promote calm and reflection',
                'Positive, uplifting messages that build confidence',
              ],
            ),

            const SizedBox(height: 32),

            // Contact section
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.blue[50],
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.blue[200]!),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(
                        Icons.contact_support,
                        color: Colors.blue[700],
                      ),
                      const SizedBox(width: 8),
                      Text(
                        'Questions or Feedback?',
                        style: theme.textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: Colors.blue[700],
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'We\'d love to hear about your child\'s experience with our stories. Contact us through the Help & Support section.',
                    style: theme.textTheme.bodyMedium?.copyWith(
                      color: Colors.blue[700],
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  List<Widget> _buildMoralValueCards(ThemeData theme) {
    final values = [
      {
        'title': 'Honesty',
        'description': 'Teaching the importance of truthfulness and integrity',
        'icon': Icons.verified,
        'color': Colors.green,
      },
      {
        'title': 'Kindness',
        'description': 'Encouraging compassion and empathy towards others',
        'icon': Icons.favorite,
        'color': Colors.pink,
      },
      {
        'title': 'Responsibility',
        'description': 'Learning to take ownership of actions and choices',
        'icon': Icons.assignment_turned_in,
        'color': Colors.blue,
      },
      {
        'title': 'Courage',
        'description': 'Building confidence to do the right thing',
        'icon': Icons.shield,
        'color': Colors.orange,
      },
      {
        'title': 'Friendship',
        'description': 'Understanding the value of caring relationships',
        'icon': Icons.people,
        'color': Colors.purple,
      },
      {
        'title': 'Perseverance',
        'description': 'Learning to keep trying when things get difficult',
        'icon': Icons.trending_up,
        'color': Colors.teal,
      },
    ];

    return values.map((value) => Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: Colors.grey[200]!),
          boxShadow: [
            BoxShadow(
              color: Colors.grey.withOpacity(0.1),
              spreadRadius: 1,
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: (value['color'] as Color).withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                value['icon'] as IconData,
                color: value['color'] as Color,
                size: 24,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    value['title'] as String,
                    style: theme.textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    value['description'] as String,
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: Colors.grey[600],
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    )).toList();
  }

  Widget _buildSectionCard({
    required ThemeData theme,
    required String title,
    required IconData icon,
    required List<String> content,
  }) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey[200]!),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            spreadRadius: 1,
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                icon,
                color: theme.colorScheme.primary,
                size: 24,
              ),
              const SizedBox(width: 12),
              Text(
                title,
                style: theme.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          ...content.map((item) => Padding(
            padding: const EdgeInsets.only(bottom: 8),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                  margin: const EdgeInsets.only(top: 6),
                  width: 6,
                  height: 6,
                  decoration: BoxDecoration(
                    color: theme.colorScheme.primary,
                    shape: BoxShape.circle,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    item,
                    style: theme.textTheme.bodyMedium,
                  ),
                ),
              ],
            ),
          )),
        ],
      ),
    );
  }
}
