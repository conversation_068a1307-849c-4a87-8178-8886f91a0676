import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:choice_once_upon_a_time/core/utils/app_logger.dart';

/// Service for managing user profiles with secure storage
class UserProfileService {
  static const String _logPrefix = 'UserProfileService';
  static const String _storageKey = 'user_profiles';
  
  final FlutterSecureStorage _secureStorage = const FlutterSecureStorage(
    aOptions: AndroidOptions(
      encryptedSharedPreferences: true,
    ),
    iOptions: IOSOptions(
      accessibility: KeychainAccessibility.first_unlock_this_device,
    ),
  );

  /// Load all user profiles from secure storage
  Future<List<UserProfile>> loadProfiles() async {
    try {
      AppLogger.debug('$_logPrefix: Loading user profiles');
      
      final profilesJson = await _secureStorage.read(key: _storageKey);
      if (profilesJson == null || profilesJson.isEmpty) {
        AppLogger.debug('$_logPrefix: No profiles found, returning empty list');
        return [];
      }

      final List<dynamic> profilesList = jsonDecode(profilesJson);
      final profiles = profilesList
          .map((json) => UserProfile.fromJson(json))
          .toList();
      
      AppLogger.info('$_logPrefix: Loaded ${profiles.length} profiles');
      return profiles;
    } catch (e, stackTrace) {
      AppLogger.error('$_logPrefix: Error loading profiles', e, stackTrace);
      return [];
    }
  }

  /// Save all user profiles to secure storage
  Future<bool> saveProfiles(List<UserProfile> profiles) async {
    try {
      AppLogger.debug('$_logPrefix: Saving ${profiles.length} profiles');
      
      final profilesJson = jsonEncode(
        profiles.map((profile) => profile.toJson()).toList(),
      );
      
      await _secureStorage.write(key: _storageKey, value: profilesJson);
      AppLogger.info('$_logPrefix: Successfully saved profiles');
      return true;
    } catch (e, stackTrace) {
      AppLogger.error('$_logPrefix: Error saving profiles', e, stackTrace);
      return false;
    }
  }

  /// Create a new user profile
  Future<bool> createProfile(UserProfile profile) async {
    try {
      final profiles = await loadProfiles();
      
      // Check for duplicate names
      if (profiles.any((p) => p.name.toLowerCase() == profile.name.toLowerCase())) {
        AppLogger.warning('$_logPrefix: Profile with name ${profile.name} already exists');
        return false;
      }
      
      profiles.add(profile);
      final success = await saveProfiles(profiles);
      
      if (success) {
        AppLogger.info('$_logPrefix: Created profile for ${profile.name}');
      }
      
      return success;
    } catch (e, stackTrace) {
      AppLogger.error('$_logPrefix: Error creating profile', e, stackTrace);
      return false;
    }
  }

  /// Update an existing user profile
  Future<bool> updateProfile(UserProfile updatedProfile) async {
    try {
      final profiles = await loadProfiles();
      final index = profiles.indexWhere((p) => p.id == updatedProfile.id);
      
      if (index == -1) {
        AppLogger.warning('$_logPrefix: Profile ${updatedProfile.id} not found for update');
        return false;
      }
      
      // Check for duplicate names (excluding current profile)
      if (profiles.any((p) => 
          p.id != updatedProfile.id && 
          p.name.toLowerCase() == updatedProfile.name.toLowerCase())) {
        AppLogger.warning('$_logPrefix: Profile with name ${updatedProfile.name} already exists');
        return false;
      }
      
      profiles[index] = updatedProfile;
      final success = await saveProfiles(profiles);
      
      if (success) {
        AppLogger.info('$_logPrefix: Updated profile for ${updatedProfile.name}');
      }
      
      return success;
    } catch (e, stackTrace) {
      AppLogger.error('$_logPrefix: Error updating profile', e, stackTrace);
      return false;
    }
  }

  /// Delete a user profile
  Future<bool> deleteProfile(String profileId) async {
    try {
      final profiles = await loadProfiles();
      final initialCount = profiles.length;
      
      profiles.removeWhere((p) => p.id == profileId);
      
      if (profiles.length == initialCount) {
        AppLogger.warning('$_logPrefix: Profile $profileId not found for deletion');
        return false;
      }
      
      final success = await saveProfiles(profiles);
      
      if (success) {
        AppLogger.info('$_logPrefix: Deleted profile $profileId');
      }
      
      return success;
    } catch (e, stackTrace) {
      AppLogger.error('$_logPrefix: Error deleting profile', e, stackTrace);
      return false;
    }
  }

  /// Get a specific profile by ID
  Future<UserProfile?> getProfile(String profileId) async {
    try {
      final profiles = await loadProfiles();
      return profiles.firstWhere(
        (p) => p.id == profileId,
        orElse: () => throw StateError('Profile not found'),
      );
    } catch (e) {
      AppLogger.warning('$_logPrefix: Profile $profileId not found');
      return null;
    }
  }

  /// Update reading progress for a profile
  Future<bool> updateReadingProgress(String profileId, String storyId, int readingTimeMinutes) async {
    try {
      final profile = await getProfile(profileId);
      if (profile == null) return false;
      
      final updatedProfile = profile.copyWith(
        totalReadingTime: profile.totalReadingTime + readingTimeMinutes,
        storiesCompleted: profile.favoriteStories.contains(storyId) 
            ? profile.storiesCompleted 
            : profile.storiesCompleted + 1,
        favoriteStories: profile.favoriteStories.contains(storyId)
            ? profile.favoriteStories
            : [...profile.favoriteStories, storyId],
      );
      
      return await updateProfile(updatedProfile);
    } catch (e, stackTrace) {
      AppLogger.error('$_logPrefix: Error updating reading progress', e, stackTrace);
      return false;
    }
  }

  /// Clear all profiles (for testing/reset purposes)
  Future<bool> clearAllProfiles() async {
    try {
      await _secureStorage.delete(key: _storageKey);
      AppLogger.info('$_logPrefix: Cleared all profiles');
      return true;
    } catch (e, stackTrace) {
      AppLogger.error('$_logPrefix: Error clearing profiles', e, stackTrace);
      return false;
    }
  }
}

/// Enhanced user profile model with JSON serialization
class UserProfile {
  final String id;
  final String name;
  final int age;
  final Color avatarColor;
  final List<String> favoriteStories;
  final int totalReadingTime; // in minutes
  final int storiesCompleted;
  final DateTime createdAt;
  final DateTime lastActiveAt;

  UserProfile({
    required this.id,
    required this.name,
    required this.age,
    required this.avatarColor,
    required this.favoriteStories,
    required this.totalReadingTime,
    required this.storiesCompleted,
    required this.createdAt,
    DateTime? lastActiveAt,
  }) : lastActiveAt = lastActiveAt ?? DateTime.now();

  /// Create a copy with updated fields
  UserProfile copyWith({
    String? id,
    String? name,
    int? age,
    Color? avatarColor,
    List<String>? favoriteStories,
    int? totalReadingTime,
    int? storiesCompleted,
    DateTime? createdAt,
    DateTime? lastActiveAt,
  }) {
    return UserProfile(
      id: id ?? this.id,
      name: name ?? this.name,
      age: age ?? this.age,
      avatarColor: avatarColor ?? this.avatarColor,
      favoriteStories: favoriteStories ?? this.favoriteStories,
      totalReadingTime: totalReadingTime ?? this.totalReadingTime,
      storiesCompleted: storiesCompleted ?? this.storiesCompleted,
      createdAt: createdAt ?? this.createdAt,
      lastActiveAt: lastActiveAt ?? this.lastActiveAt,
    );
  }

  /// Convert to JSON for storage
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'age': age,
      'avatarColor': avatarColor.value,
      'favoriteStories': favoriteStories,
      'totalReadingTime': totalReadingTime,
      'storiesCompleted': storiesCompleted,
      'createdAt': createdAt.toIso8601String(),
      'lastActiveAt': lastActiveAt.toIso8601String(),
    };
  }

  /// Create from JSON
  factory UserProfile.fromJson(Map<String, dynamic> json) {
    return UserProfile(
      id: json['id'],
      name: json['name'],
      age: json['age'],
      avatarColor: Color(json['avatarColor']),
      favoriteStories: List<String>.from(json['favoriteStories']),
      totalReadingTime: json['totalReadingTime'],
      storiesCompleted: json['storiesCompleted'],
      createdAt: DateTime.parse(json['createdAt']),
      lastActiveAt: DateTime.parse(json['lastActiveAt']),
    );
  }

  @override
  String toString() {
    return 'UserProfile(id: $id, name: $name, age: $age, stories: $storiesCompleted, time: ${totalReadingTime}min)';
  }
}

/// Provider for user profile service
final userProfileServiceProvider = Provider<UserProfileService>((ref) {
  return UserProfileService();
});

/// Provider for user profiles list
final userProfilesProvider = FutureProvider<List<UserProfile>>((ref) async {
  final service = ref.watch(userProfileServiceProvider);
  return await service.loadProfiles();
});

/// Provider for selected profile
final selectedProfileProvider = StateProvider<UserProfile?>((ref) => null);
