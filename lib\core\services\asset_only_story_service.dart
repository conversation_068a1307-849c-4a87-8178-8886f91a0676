import 'dart:convert';
import 'package:flutter/services.dart';
import 'package:logger/logger.dart';
import 'package:choice_once_upon_a_time/models/story_metadata_model.dart';
import 'package:choice_once_upon_a_time/models/story_model.dart';
import 'package:choice_once_upon_a_time/models/scene_model.dart';
import 'package:choice_once_upon_a_time/models/choice_model.dart';
import 'package:choice_once_upon_a_time/models/text_segment_model.dart';

/// Service for loading stories exclusively from assets folder
class AssetOnlyStoryService {
  static final Logger _logger = Logger();
  
  // Cache for loaded stories and metadata
  final Map<String, StoryModel> _storyCache = {};
  final Map<String, StoryMetadataModel> _metadataCache = {};
  List<StoryMetadataModel>? _allStoriesCache;

  /// Loads all story metadata by discovering stories from asset manifest
  Future<List<StoryMetadataModel>> getAllStoryMetadata() async {
    if (_allStoriesCache != null) {
      return _allStoriesCache!;
    }

    try {
      _logger.i('[AssetOnlyStoryService] Discovering stories from asset manifest');

      // Load asset manifest to discover story folders
      final manifest = await rootBundle.loadString('AssetManifest.json');
      final Map<String, dynamic> manifestMap = jsonDecode(manifest);

      // Filter for story folders (assets/stories/<storyid>/)
      final storyIds = manifestMap.keys
          .where((key) => key.startsWith('assets/stories/'))
          .map((key) {
            final parts = key.split('/');
            if (parts.length > 2) return parts[2]; // Get <storyid>
            return null;
          })
          .whereType<String>()
          .toSet()
          .toList();

      _logger.i('[AssetOnlyStoryService] Found ${storyIds.length} story folders: $storyIds');

      final stories = <StoryMetadataModel>[];

      for (final storyId in storyIds) {
        try {
          final metadata = await _createMetadataFromStoryFolder(storyId);
          if (metadata != null) {
            stories.add(metadata);
            _metadataCache[metadata.id] = metadata;
          }
        } catch (e) {
          _logger.e('[AssetOnlyStoryService] Failed to load metadata for story $storyId: $e');
        }
      }

      _allStoriesCache = stories;
      _logger.i('[AssetOnlyStoryService] Loaded ${stories.length} story metadata entries');
      return stories;

    } catch (e) {
      _logger.e('[AssetOnlyStoryService] Failed to discover stories: $e');
      return [];
    }
  }

  /// Creates StoryMetadataModel from story folder by loading story.json
  Future<StoryMetadataModel?> _createMetadataFromStoryFolder(String storyId) async {
    try {
      // Try to load the story.json file from the story folder
      final storyPath = 'assets/stories/$storyId/story.json';
      final jsonString = await rootBundle.loadString(storyPath);
      final Map<String, dynamic> storyJson = jsonDecode(jsonString);

      // Extract metadata from story.json
      final title = storyJson['title'] as String? ?? storyId;
      final description = storyJson['description'] as String? ?? 'An interactive story';

      // Extract moral values
      String moralValue = 'friendship'; // default
      if (storyJson['moral_values'] != null) {
        final moralValues = storyJson['moral_values'] as List<dynamic>;
        if (moralValues.isNotEmpty) {
          moralValue = moralValues.first.toString();
        }
      }

      return StoryMetadataModel(
        id: storyId,
        title: {'en-US': title},
        coverImageUrl: 'assets/stories/$storyId/images/cover.jpg',
        loglineShort: {'en-US': description},
        targetMoralValue: moralValue,
        targetAgeSubSegment: '5-8',
        estimatedDurationMinutes: 10,
        isFree: true,
        isLocked: false,
        published: true,
        dataSource: 'asset',
        version: '1.0.0',
        supportedLanguages: ['en-US'],
        defaultLanguage: 'en-US',
        initialSceneId: storyJson['start'] as String? ?? 'scene1',
      );
    } catch (e) {
      _logger.w('[AssetOnlyStoryService] Failed to load metadata for story $storyId: $e');
      return null;
    }
  }

  /// Loads a specific story by ID
  Future<StoryModel?> loadStory(String storyId) async {
    if (_storyCache.containsKey(storyId)) {
      return _storyCache[storyId];
    }

    try {
      _logger.i('[AssetOnlyStoryService] Loading story: $storyId');
      
      // Try to load from the story's folder
      final storyPath = 'assets/stories/$storyId/story.json';
      final jsonString = await rootBundle.loadString(storyPath);
      final Map<String, dynamic> storyJson = jsonDecode(jsonString);
      
      final story = _createStoryFromJson(storyJson, storyId);
      _storyCache[storyId] = story;
      
      _logger.i('[AssetOnlyStoryService] Successfully loaded story: $storyId');
      return story;
      
    } catch (e) {
      _logger.e('[AssetOnlyStoryService] Failed to load story $storyId: $e');
      return null;
    }
  }

  /// Creates StoryModel from story.json
  StoryModel _createStoryFromJson(Map<String, dynamic> json, String storyId) {
    final scenes = <SceneModel>[];
    final scenesJson = json['scenes'] as List<dynamic>;

    for (int i = 0; i < scenesJson.length; i++) {
      final sceneJson = scenesJson[i] as Map<String, dynamic>;
      scenes.add(_createSceneFromJson(sceneJson, i));
    }

    return StoryModel(
      id: storyId,
      title: json['title'] as String,
      targetMoralValue: (json['moral_values'] as List<dynamic>).first.toString(),
      targetAgeSubSegment: '5-8',
      version: '1.0.0',
      narratorPersonaGuidance: 'warm, gentle',
      supportedLanguages: ['en-US'],
      defaultLanguage: 'en-US',
      estimatedDurationMinutes: 10,
      isFree: true,
      published: true,
      logline: {'en-US': json['title'] as String},
      scenes: scenes,
      initialSceneId: json['start'] as String,
    );
  }

  /// Creates SceneModel from scene JSON
  SceneModel _createSceneFromJson(Map<String, dynamic> json, int order) {
    final choices = <ChoiceModel>[];
    if (json['choices'] != null) {
      final choicesJson = json['choices'] as List<dynamic>;
      for (int i = 0; i < choicesJson.length; i++) {
        final choiceJson = choicesJson[i] as Map<String, dynamic>;
        choices.add(ChoiceModel(
          choiceId: 'choice_$i',
          displayTextKey: choiceJson['text'] as String,
          leadsToSceneId: choiceJson['next_scene'] as String,
          narratorGuidance: {},
        ));
      }
    }

    // Split text into sentences for narration
    final text = json['text'] as String;
    final sentences = _splitIntoSentences(text);

    final narratorSegments = sentences.map((sentence) => TextSegmentModel(
      id: 'segment_${sentences.indexOf(sentence)}',
      text: {'en-US': sentence},
      emotionCue: _extractEmotionCue(json['emotional_cue'] as String?),
      durationEstimateMs: _estimateDuration(sentence),
    )).toList();

    // Create choice point data if there are choices
    Map<String, dynamic>? choicePointData;
    if (choices.isNotEmpty) {
      choicePointData = {
        'choices': choices.map((choice) => choice.toJson()).toList(),
        'promptTextForTTS': {'en-US': 'What would you like to do?'},
      };
    }

    return SceneModel(
      sceneId: json['id'] as String,
      sceneType: choices.isNotEmpty ? 'choice_point' : 'narration_illustration',
      order: order,
      narratorSegments: narratorSegments,
      isChoicePoint: choices.isNotEmpty,
      choicePointData: choicePointData,
      nextSceneId: choices.isEmpty ? _findNextSceneId(json) : null,
    );
  }

  /// Splits text into sentences for narration
  List<String> _splitIntoSentences(String text) {
    // Remove emotion cues in brackets
    final cleanText = text.replaceAll(RegExp(r'\[.*?\]'), '');
    
    // Split by sentence endings
    final sentences = cleanText
        .split(RegExp(r'[.!?]+'))
        .map((s) => s.trim())
        .where((s) => s.isNotEmpty)
        .toList();
    
    return sentences;
  }

  /// Extracts emotion cue from text
  String _extractEmotionCue(String? emotionalCue) {
    if (emotionalCue == null) return 'neutral';
    return emotionalCue.replaceAll(RegExp(r'[\[\]]'), '').toLowerCase();
  }

  /// Estimates duration for a sentence
  int _estimateDuration(String sentence) {
    // Rough estimate: 150 words per minute, average 5 characters per word
    final words = sentence.split(' ').length;
    return ((words / 150) * 60 * 1000).round();
  }

  /// Finds next scene ID for non-choice scenes
  String? _findNextSceneId(Map<String, dynamic> json) {
    // This would need to be determined based on story structure
    // For now, return null and let the story player handle transitions
    return null;
  }

  /// Clears all caches
  void clearCache() {
    _storyCache.clear();
    _metadataCache.clear();
    _allStoriesCache = null;
    _logger.i('[AssetOnlyStoryService] Cache cleared');
  }
}
