// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'firebase_story_metadata.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

FirebaseStoryMetadata _$FirebaseStoryMetadataFromJson(
        Map<String, dynamic> json) =>
    FirebaseStoryMetadata(
      storyId: json['storyId'] as String,
      title: json['title'] as String,
      description: json['description'] as String,
      coverImageUrl: json['coverImageUrl'] as String,
      categories: (json['categories'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
      ageRangeMin: (json['ageRangeMin'] as num).toInt(),
      ageRangeMax: (json['ageRangeMax'] as num).toInt(),
      estimatedDuration: (json['estimatedDuration'] as num).toDouble(),
      publishedAt: DateTime.parse(json['publishedAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
      version: json['version'] as String,
      downloadSizeMB: (json['downloadSizeMB'] as num).toInt(),
      isPremium: json['isPremium'] as bool,
      languages:
          (json['languages'] as List<dynamic>).map((e) => e as String).toList(),
      assets: json['assets'] as Map<String, dynamic>,
      isDownloadable: json['isDownloadable'] as bool,
      downloadUrl: json['downloadUrl'] as String,
      checksum: json['checksum'] as String,
    );

Map<String, dynamic> _$FirebaseStoryMetadataToJson(
        FirebaseStoryMetadata instance) =>
    <String, dynamic>{
      'storyId': instance.storyId,
      'title': instance.title,
      'description': instance.description,
      'coverImageUrl': instance.coverImageUrl,
      'categories': instance.categories,
      'ageRangeMin': instance.ageRangeMin,
      'ageRangeMax': instance.ageRangeMax,
      'estimatedDuration': instance.estimatedDuration,
      'publishedAt': instance.publishedAt.toIso8601String(),
      'updatedAt': instance.updatedAt.toIso8601String(),
      'version': instance.version,
      'downloadSizeMB': instance.downloadSizeMB,
      'isPremium': instance.isPremium,
      'languages': instance.languages,
      'assets': instance.assets,
      'isDownloadable': instance.isDownloadable,
      'downloadUrl': instance.downloadUrl,
      'checksum': instance.checksum,
    };

DownloadProgress _$DownloadProgressFromJson(Map<String, dynamic> json) =>
    DownloadProgress(
      storyId: json['storyId'] as String,
      progress: (json['progress'] as num).toDouble(),
      status: $enumDecode(_$DownloadStatusEnumMap, json['status']),
      errorMessage: json['errorMessage'] as String?,
      lastUpdated: DateTime.parse(json['lastUpdated'] as String),
    );

Map<String, dynamic> _$DownloadProgressToJson(DownloadProgress instance) =>
    <String, dynamic>{
      'storyId': instance.storyId,
      'progress': instance.progress,
      'status': _$DownloadStatusEnumMap[instance.status]!,
      'errorMessage': instance.errorMessage,
      'lastUpdated': instance.lastUpdated.toIso8601String(),
    };

const _$DownloadStatusEnumMap = {
  DownloadStatus.notDownloaded: 'not_downloaded',
  DownloadStatus.downloading: 'downloading',
  DownloadStatus.downloaded: 'downloaded',
  DownloadStatus.failed: 'failed',
  DownloadStatus.paused: 'paused',
  DownloadStatus.cancelled: 'cancelled',
};

StoryDownloadInfo _$StoryDownloadInfoFromJson(Map<String, dynamic> json) =>
    StoryDownloadInfo(
      storyId: json['storyId'] as String,
      userId: json['userId'] as String,
      downloadedAt: DateTime.parse(json['downloadedAt'] as String),
      localPath: json['localPath'] as String,
      version: json['version'] as String,
      fileSizeMB: (json['fileSizeMB'] as num).toInt(),
      status: $enumDecode(_$DownloadStatusEnumMap, json['status']),
      progress: (json['progress'] as num).toDouble(),
      lastAccessedAt: json['lastAccessedAt'] == null
          ? null
          : DateTime.parse(json['lastAccessedAt'] as String),
    );

Map<String, dynamic> _$StoryDownloadInfoToJson(StoryDownloadInfo instance) =>
    <String, dynamic>{
      'storyId': instance.storyId,
      'userId': instance.userId,
      'downloadedAt': instance.downloadedAt.toIso8601String(),
      'localPath': instance.localPath,
      'version': instance.version,
      'fileSizeMB': instance.fileSizeMB,
      'status': _$DownloadStatusEnumMap[instance.status]!,
      'progress': instance.progress,
      'lastAccessedAt': instance.lastAccessedAt?.toIso8601String(),
    };
