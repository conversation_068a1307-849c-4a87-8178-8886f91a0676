import 'package:flutter_test/flutter_test.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:choice_once_upon_a_time/core/services/story_progress_service.dart';


void main() {
  group('StoryProgressService', () {
    late StoryProgressService service;

    setUp(() async {
      SharedPreferences.setMockInitialValues({});
      service = StoryProgressService();
    });

    group('StoryProgress model', () {
      test('should create progress from JSON', () {
        final json = {
          'storyId': 'story001',
          'currentSceneId': 'scene_1',
          'visitedScenes': ['scene_1', 'scene_2'],
          'choicesMade': {'scene_1_choice': 'option_a'},
          'lastPlayedAt': '2023-01-01T12:00:00.000Z',
          'totalPlayTime': 300,
          'isCompleted': false,
          'progressPercentage': 25.0,
        };

        final progress = StoryProgress.fromJson(json);

        expect(progress.storyId, equals('story001'));
        expect(progress.currentSceneId, equals('scene_1'));
        expect(progress.visitedScenes, equals(['scene_1', 'scene_2']));
        expect(progress.choicesMade, equals({'scene_1_choice': 'option_a'}));
        expect(progress.totalPlayTime, equals(300));
        expect(progress.isCompleted, equals(false));
        expect(progress.progressPercentage, equals(25.0));
      });

      test('should convert progress to JSON', () {
        final progress = StoryProgress(
          storyId: 'story001',
          currentSceneId: 'scene_1',
          visitedScenes: const ['scene_1'],
          choicesMade: const {'choice': 'value'},
          lastPlayedAt: DateTime.parse('2023-01-01T12:00:00.000Z'),
          totalPlayTime: 300,
          isCompleted: false,
          progressPercentage: 25.0,
        );

        final json = progress.toJson();

        expect(json['storyId'], equals('story001'));
        expect(json['currentSceneId'], equals('scene_1'));
        expect(json['visitedScenes'], equals(['scene_1']));
        expect(json['choicesMade'], equals({'choice': 'value'}));
        expect(json['lastPlayedAt'], equals('2023-01-01T12:00:00.000Z'));
        expect(json['totalPlayTime'], equals(300));
        expect(json['isCompleted'], equals(false));
        expect(json['progressPercentage'], equals(25.0));
      });

      test('should create copy with updated values', () {
        final original = StoryProgress(
          storyId: 'story001',
          currentSceneId: 'scene_1',
          visitedScenes: const ['scene_1'],
          choicesMade: const {},
          lastPlayedAt: DateTime.parse('2023-01-01T12:00:00.000Z'),
        );

        final updated = original.copyWith(
          currentSceneId: 'scene_2',
          progressPercentage: 50.0,
        );

        expect(updated.storyId, equals('story001'));
        expect(updated.currentSceneId, equals('scene_2'));
        expect(updated.progressPercentage, equals(50.0));
        expect(updated.visitedScenes, equals(['scene_1'])); // Unchanged
      });
    });

    group('Progress persistence', () {
      test('should save and load progress', () async {
        final progress = StoryProgress(
          storyId: 'story001',
          currentSceneId: 'scene_1',
          visitedScenes: const ['scene_1'],
          choicesMade: const {},
          lastPlayedAt: DateTime.now(),
        );

        await service.saveProgress(progress);
        final loaded = await service.loadProgress('story001');

        expect(loaded, isNotNull);
        expect(loaded!.storyId, equals('story001'));
        expect(loaded.currentSceneId, equals('scene_1'));
      });

      test('should return null for non-existent progress', () async {
        final progress = await service.loadProgress('nonexistent');
        expect(progress, isNull);
      });

      test('should delete progress', () async {
        final progress = StoryProgress(
          storyId: 'story001',
          currentSceneId: 'scene_1',
          visitedScenes: const ['scene_1'],
          choicesMade: const {},
          lastPlayedAt: DateTime.now(),
        );

        await service.saveProgress(progress);
        await service.deleteProgress('story001');
        
        final loaded = await service.loadProgress('story001');
        expect(loaded, isNull);
      });

      test('should clear all progress', () async {
        final progress1 = StoryProgress(
          storyId: 'story001',
          currentSceneId: 'scene_1',
          visitedScenes: const ['scene_1'],
          choicesMade: const {},
          lastPlayedAt: DateTime.now(),
        );

        final progress2 = StoryProgress(
          storyId: 'story002',
          currentSceneId: 'scene_1',
          visitedScenes: const ['scene_1'],
          choicesMade: const {},
          lastPlayedAt: DateTime.now(),
        );

        await service.saveProgress(progress1);
        await service.saveProgress(progress2);
        await service.clearAllProgress();

        final loaded1 = await service.loadProgress('story001');
        final loaded2 = await service.loadProgress('story002');
        
        expect(loaded1, isNull);
        expect(loaded2, isNull);
      });
    });

    group('Progress queries', () {
      test('should check if story has progress', () async {
        expect(await service.hasProgress('story001'), isFalse);

        final progress = StoryProgress(
          storyId: 'story001',
          currentSceneId: 'scene_1',
          visitedScenes: const ['scene_1'],
          choicesMade: const {},
          lastPlayedAt: DateTime.now(),
        );

        await service.saveProgress(progress);
        expect(await service.hasProgress('story001'), isTrue);
      });

      test('should get progress percentage', () async {
        final progress = StoryProgress(
          storyId: 'story001',
          currentSceneId: 'scene_1',
          visitedScenes: const ['scene_1'],
          choicesMade: const {},
          lastPlayedAt: DateTime.now(),
          progressPercentage: 75.0,
        );

        await service.saveProgress(progress);
        final percentage = await service.getProgressPercentage('story001');
        
        expect(percentage, equals(75.0));
      });

      test('should check if story is completed', () async {
        final progress = StoryProgress(
          storyId: 'story001',
          currentSceneId: 'scene_final',
          visitedScenes: const ['scene_1', 'scene_final'],
          choicesMade: const {},
          lastPlayedAt: DateTime.now(),
          isCompleted: true,
        );

        await service.saveProgress(progress);
        final isCompleted = await service.isStoryCompleted('story001');
        
        expect(isCompleted, isTrue);
      });

      test('should get total play time', () async {
        final progress = StoryProgress(
          storyId: 'story001',
          currentSceneId: 'scene_1',
          visitedScenes: const ['scene_1'],
          choicesMade: const {},
          lastPlayedAt: DateTime.now(),
          totalPlayTime: 1800, // 30 minutes
        );

        await service.saveProgress(progress);
        final playTime = await service.getTotalPlayTime('story001');
        
        expect(playTime, equals(1800));
      });
    });

    group('Progress calculations', () {
      test('should calculate progress percentage correctly', () {
        final percentage = StoryProgressService.calculateProgressPercentage(['scene_1', 'scene_2'], 4);
        expect(percentage, equals(50.0));
      });

      test('should handle zero total scenes', () {
        final percentage = StoryProgressService.calculateProgressPercentage(['scene_1'], 0);
        expect(percentage, equals(0.0));
      });

      test('should clamp percentage to 100', () {
        final percentage = StoryProgressService.calculateProgressPercentage(['scene_1', 'scene_2', 'scene_3'], 2);
        expect(percentage, equals(100.0));
      });
    });

    group('Progress creation and updates', () {
      test('should create initial progress', () {
        final progress = StoryProgressService.createInitialProgress('story001', 'scene_1');
        
        expect(progress.storyId, equals('story001'));
        expect(progress.currentSceneId, equals('scene_1'));
        expect(progress.visitedScenes, equals(['scene_1']));
        expect(progress.choicesMade, isEmpty);
        expect(progress.isCompleted, isFalse);
        expect(progress.progressPercentage, equals(0.0));
      });

      test('should update progress with new scene', () {
        final initial = StoryProgressService.createInitialProgress('story001', 'scene_1');
        
        final updated = StoryProgressService.updateProgressWithScene(
          initial,
          'scene_2',
          4,
          newChoices: {'scene_1_choice': 'option_a'},
          additionalPlayTime: 300,
        );

        expect(updated.currentSceneId, equals('scene_2'));
        expect(updated.visitedScenes, equals(['scene_1', 'scene_2']));
        expect(updated.choicesMade, equals({'scene_1_choice': 'option_a'}));
        expect(updated.totalPlayTime, equals(300));
        expect(updated.progressPercentage, equals(50.0)); // 2/4 scenes
      });

      test('should not duplicate visited scenes', () {
        final initial = StoryProgress(
          storyId: 'story001',
          currentSceneId: 'scene_1',
          visitedScenes: const ['scene_1', 'scene_2'],
          choicesMade: const {},
          lastPlayedAt: DateTime.now(),
        );
        
        final updated = StoryProgressService.updateProgressWithScene(
          initial,
          'scene_2', // Revisiting same scene
          4,
        );

        expect(updated.visitedScenes, equals(['scene_1', 'scene_2']));
        expect(updated.progressPercentage, equals(50.0));
      });

      test('should mark as completed when progress reaches 100%', () {
        final initial = StoryProgress(
          storyId: 'story001',
          currentSceneId: 'scene_3',
          visitedScenes: const ['scene_1', 'scene_2', 'scene_3'],
          choicesMade: const {},
          lastPlayedAt: DateTime.now(),
        );
        
        final updated = StoryProgressService.updateProgressWithScene(
          initial,
          'scene_4',
          4,
        );

        expect(updated.progressPercentage, equals(100.0));
        expect(updated.isCompleted, isTrue);
      });
    });

    group('All progress management', () {
      test('should get all progress sorted by date', () async {
        final now = DateTime.now();
        final progress1 = StoryProgress(
          storyId: 'story001',
          currentSceneId: 'scene_1',
          visitedScenes: const ['scene_1'],
          choicesMade: const {},
          lastPlayedAt: now.subtract(const Duration(days: 1)),
        );

        final progress2 = StoryProgress(
          storyId: 'story002',
          currentSceneId: 'scene_1',
          visitedScenes: const ['scene_1'],
          choicesMade: const {},
          lastPlayedAt: now,
        );

        await service.saveProgress(progress1);
        await service.saveProgress(progress2);

        final allProgress = await service.getAllProgress();
        
        expect(allProgress.length, equals(2));
        expect(allProgress.first.storyId, equals('story002')); // Most recent first
        expect(allProgress.last.storyId, equals('story001'));
      });

      test('should handle empty progress list', () async {
        final allProgress = await service.getAllProgress();
        expect(allProgress, isEmpty);
      });
    });
  });
}
