name: choice_once_upon_a_time
description: An interactive bedtime story app for children.
publish_to: 'none' # Remove this line if you plan to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+1

environment:
  sdk: '>=3.0.0 <4.0.0' # Ensure this is compatible with latest stable Flutter

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter
  flutter_localizations: # For l10n later
    sdk: flutter
  # Core Packages
  animated_text_kit: ^4.2.2
  flutter_riverpod: ^2.5.1 # Or latest version
  provider: ^6.1.2
  go_router: ^14.0.0 # Or latest version
  flutter_dotenv: ^5.1.0 # Or latest version for secret management

  # Firebase - Latest unified packages (web support included)
  firebase_core: ^2.32.0
  firebase_analytics: ^10.10.7
  cloud_firestore: ^4.17.5
  firebase_auth: ^4.20.0
  firebase_storage: ^11.7.7  # Storage & Database
  isar: ^3.1.0+1
  isar_flutter_libs: ^3.1.0+1
  path_provider: ^2.1.1
  shared_preferences: ^2.2.2

  # Audio & TTS
  flutter_tts: ^3.8.5
  just_audio: ^0.9.36
  just_audio_web: ^0.4.9

  # JSON & Serialization
  json_annotation: ^4.9.0
  equatable: ^2.0.5

  # In-App Purchase
  in_app_purchase: ^3.1.13

  # URL Launcher
  url_launcher: ^6.2.2

  # Logging
  logger: ^2.0.2+1

  # Permissions
  permission_handler: ^11.3.1

  # Secure Storage
  flutter_secure_storage: ^9.2.2

  # Archive extraction for ZIP files
  archive: ^3.4.10

  # In-App Purchases
  # in_app_purchase: ^3.1.13

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^3.0.1

  # Build runners & code generation
  build_runner: ^2.4.8
  json_serializable: ^6.7.1
  isar_generator: ^3.1.0+1
  analyzer: ^5.13.0  # Compatible with isar_generator

  # Testing
  mockito: ^5.4.4

# The following section was added by dart migrate.
flutter:
  generate: true # <--- Add this line
  uses-material-design: true
  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.  uses-material-design: true

  # Assets
  assets:
    - assets/stories/
    - assets/stories/story013/
    - assets/stories/story013/story.json
    - assets/stories/story013/images/
    - assets/stories/story013/assets/
    - assets/stories/story014/
    - assets/stories/story014/story.json
    - assets/stories/story014/images/
    - assets/stories/story014/assets/
    - assets/images/
    - assets/images/story_covers/
    - assets/audio/ui/
    - assets/localization/

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: NunitoSans
  #     fonts:
  #       - asset: assets/fonts/NunitoSans-Regular.ttf
  #       - asset: assets/fonts/NunitoSans-Bold.ttf
  #         weight: 700
  #       - asset: assets/fonts/NunitoSans-Italic.ttf
  #         style: italic
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #
  # For details regarding fonts from packages,
  # see https://flutter.dev/custom-fonts/#from-packages
