import 'dart:convert';
import 'dart:developer' as developer;
import 'package:flutter/services.dart';

/// Model for screen narration data
class ScreenNarration {
  final String text;
  final String emotionCue;

  const ScreenNarration({
    required this.text,
    required this.emotionCue,
  });

  factory ScreenNarration.fromJson(Map<String, dynamic> json) {
    return ScreenNarration(
      text: json['text'] as String,
      emotionCue: json['emotionCue'] as String,
    );
  }

  @override
  String toString() => 'ScreenNarration(text: "$text", emotionCue: "$emotionCue")';
}

/// Service for managing screen introduction narrations
/// Loads and provides narrator text content for screen introductions
class ScreenNarrationService {
  static final ScreenNarrationService _instance = ScreenNarrationService._internal();
  factory ScreenNarrationService() => _instance;
  ScreenNarrationService._internal();

  Map<String, ScreenNarration>? _narrations;
  Map<String, ScreenNarration>? _parentZoneNarrations;
  bool _isLoaded = false;
  bool _isParentZoneLoaded = false;

  /// Load screen narrations from assets
  Future<void> initialize() async {
    if (_isLoaded && _isParentZoneLoaded) {
      developer.log('[ScreenIntroNarrator] Service already initialized', name: 'ScreenNarrationService');
      return;
    }

    try {
      // Load general screen narrations
      if (!_isLoaded) {
        developer.log('[ScreenIntroNarrator] Loading screen narrations from assets', name: 'ScreenNarrationService');

        final String jsonString = await rootBundle.loadString('assets/localization/screen_narrations_en.json');
        final Map<String, dynamic> jsonData = json.decode(jsonString);

        _narrations = {};
        for (final entry in jsonData.entries) {
          _narrations![entry.key] = ScreenNarration.fromJson(entry.value);
        }

        _isLoaded = true;
        developer.log('[ScreenIntroNarrator] Successfully loaded ${_narrations!.length} screen narrations', name: 'ScreenNarrationService');
      }

      // Load parent zone narrations
      if (!_isParentZoneLoaded) {
        developer.log('[ScreenIntroNarrator] Loading parent zone narrations from assets', name: 'ScreenNarrationService');

        final String parentZoneJsonString = await rootBundle.loadString('assets/localization/parent_zone_narrations_en.json');
        final Map<String, dynamic> parentZoneJsonData = json.decode(parentZoneJsonString);

        _parentZoneNarrations = {};
        for (final entry in parentZoneJsonData.entries) {
          _parentZoneNarrations![entry.key] = ScreenNarration.fromJson(entry.value);
        }

        _isParentZoneLoaded = true;
        developer.log('[ScreenIntroNarrator] Successfully loaded ${_parentZoneNarrations!.length} parent zone narrations', name: 'ScreenNarrationService');
      }
    } catch (e) {
      developer.log('[ScreenIntroNarrator] Error loading narrations: $e', name: 'ScreenNarrationService');
      _narrations ??= {};
      _parentZoneNarrations ??= {};
      _isLoaded = false;
      _isParentZoneLoaded = false;
    }
  }

  /// Get narration for a specific screen
  /// Returns null if screen narration is not found
  ScreenNarration? getNarrationForScreen(String screenKey) {
    if (!_isLoaded && !_isParentZoneLoaded) {
      developer.log('[ScreenIntroNarrator] Service not initialized, cannot get narration for $screenKey', name: 'ScreenNarrationService');
      return null;
    }

    // Check parent zone narrations first if the key starts with 'pz_'
    ScreenNarration? narration;
    if (screenKey.startsWith('pz_') && _parentZoneNarrations != null) {
      narration = _parentZoneNarrations![screenKey];
    }

    // Fall back to general narrations if not found in parent zone
    narration ??= _narrations?[screenKey];

    if (narration != null) {
      developer.log('[ScreenIntroNarrator] Found narration for $screenKey: ${narration.text.substring(0, narration.text.length > 50 ? 50 : narration.text.length)}...', name: 'ScreenNarrationService');
    } else {
      developer.log('[ScreenIntroNarrator] No narration found for screen key: $screenKey', name: 'ScreenNarrationService');
    }

    return narration;
  }

  /// Get all available screen keys
  List<String> getAvailableScreenKeys() {
    final List<String> keys = [];
    if (_isLoaded && _narrations != null) {
      keys.addAll(_narrations!.keys);
    }
    if (_isParentZoneLoaded && _parentZoneNarrations != null) {
      keys.addAll(_parentZoneNarrations!.keys);
    }
    return keys;
  }

  /// Check if service is initialized
  bool get isInitialized => _isLoaded && _isParentZoneLoaded;

  /// Get total number of loaded narrations
  int get narrationCount => (_narrations?.length ?? 0) + (_parentZoneNarrations?.length ?? 0);
}
