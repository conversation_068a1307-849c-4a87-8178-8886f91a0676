import 'dart:convert';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:choice_once_upon_a_time/models/child_progress_model.dart';
import 'package:choice_once_upon_a_time/features/parent_zone/data/user_profile_service.dart';
import 'package:choice_once_upon_a_time/core/utils/app_logger.dart';

/// Provider for child progress service
final childProgressServiceProvider = Provider<ChildProgressService>((ref) {
  return ChildProgressService();
});

/// Provider for child progress data
final childProgressProvider = StateNotifierProvider<ChildProgressNotifier, AsyncValue<List<ChildProgressModel>>>((ref) {
  return ChildProgressNotifier(ref.read(childProgressServiceProvider));
});

/// Service for managing child progress tracking
class ChildProgressService {
  static const String _progressKey = 'child_progress_data';
  static const String _logPrefix = 'ChildProgressService';

  /// Save child progress to local storage
  Future<bool> saveProgress(ChildProgressModel progress) async {
    try {
      AppLogger.debug('$_logPrefix: Saving progress for profile ${progress.profileId}, story ${progress.storyId}');
      
      final prefs = await SharedPreferences.getInstance();
      final existingData = prefs.getStringList(_progressKey) ?? [];
      
      // Find existing progress for this profile-story combination
      final existingIndex = existingData.indexWhere((jsonStr) {
        try {
          final data = jsonDecode(jsonStr);
          return data['profileId'] == progress.profileId && data['storyId'] == progress.storyId;
        } catch (e) {
          return false;
        }
      });
      
      final progressJson = jsonEncode(progress.toJson());
      
      if (existingIndex >= 0) {
        // Update existing progress
        existingData[existingIndex] = progressJson;
        AppLogger.debug('$_logPrefix: Updated existing progress');
      } else {
        // Add new progress
        existingData.add(progressJson);
        AppLogger.debug('$_logPrefix: Added new progress entry');
      }
      
      await prefs.setStringList(_progressKey, existingData);
      AppLogger.info('$_logPrefix: Successfully saved progress for ${progress.profileId}');
      return true;
      
    } catch (e, stackTrace) {
      AppLogger.error('$_logPrefix: Failed to save progress', e, stackTrace);
      return false;
    }
  }

  /// Load all progress data
  Future<List<ChildProgressModel>> loadAllProgress() async {
    try {
      AppLogger.debug('$_logPrefix: Loading all progress data');
      
      final prefs = await SharedPreferences.getInstance();
      final progressData = prefs.getStringList(_progressKey) ?? [];
      
      final progressList = <ChildProgressModel>[];
      
      for (final jsonStr in progressData) {
        try {
          final json = jsonDecode(jsonStr);
          progressList.add(ChildProgressModel.fromJson(json));
        } catch (e) {
          AppLogger.warning('$_logPrefix: Failed to parse progress entry: $e');
        }
      }
      
      AppLogger.debug('$_logPrefix: Loaded ${progressList.length} progress entries');
      return progressList;
      
    } catch (e, stackTrace) {
      AppLogger.error('$_logPrefix: Failed to load progress data', e, stackTrace);
      return [];
    }
  }

  /// Load progress for specific profile
  Future<List<ChildProgressModel>> loadProgressForProfile(String profileId) async {
    try {
      final allProgress = await loadAllProgress();
      final profileProgress = allProgress.where((p) => p.profileId == profileId).toList();
      
      AppLogger.debug('$_logPrefix: Found ${profileProgress.length} progress entries for profile $profileId');
      return profileProgress;
      
    } catch (e, stackTrace) {
      AppLogger.error('$_logPrefix: Failed to load progress for profile $profileId', e, stackTrace);
      return [];
    }
  }

  /// Load progress for specific story and profile
  Future<ChildProgressModel?> loadProgressForStory(String profileId, String storyId) async {
    try {
      final allProgress = await loadAllProgress();
      final storyProgress = allProgress.firstWhere(
        (p) => p.profileId == profileId && p.storyId == storyId,
        orElse: () => _createInitialProgress(profileId, storyId),
      );
      
      AppLogger.debug('$_logPrefix: Loaded progress for profile $profileId, story $storyId');
      return storyProgress;
      
    } catch (e, stackTrace) {
      AppLogger.error('$_logPrefix: Failed to load progress for story', e, stackTrace);
      return null;
    }
  }

  /// Update scene visit
  Future<bool> updateSceneVisit(String profileId, String storyId, String sceneId, int playTime) async {
    try {
      final progress = await loadProgressForStory(profileId, storyId) ?? _createInitialProgress(profileId, storyId);
      
      final updatedVisitedScenes = List<String>.from(progress.visitedScenes);
      if (!updatedVisitedScenes.contains(sceneId)) {
        updatedVisitedScenes.add(sceneId);
      }
      
      final updatedScenePlayTime = Map<String, int>.from(progress.scenePlayTime);
      updatedScenePlayTime[sceneId] = (updatedScenePlayTime[sceneId] ?? 0) + playTime;
      
      final updatedProgress = progress.copyWith(
        visitedScenes: updatedVisitedScenes,
        scenePlayTime: updatedScenePlayTime,
        totalPlayTime: progress.totalPlayTime + playTime,
        lastPlayedAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );
      
      return await saveProgress(updatedProgress);
      
    } catch (e, stackTrace) {
      AppLogger.error('$_logPrefix: Failed to update scene visit', e, stackTrace);
      return false;
    }
  }

  /// Record choice made
  Future<bool> recordChoice(String profileId, String storyId, String sceneId, String choiceId) async {
    try {
      final progress = await loadProgressForStory(profileId, storyId) ?? _createInitialProgress(profileId, storyId);
      
      final updatedChoices = Map<String, String>.from(progress.choicesMade);
      final wasChanged = updatedChoices.containsKey(sceneId) && updatedChoices[sceneId] != choiceId;
      updatedChoices[sceneId] = choiceId;
      
      final updatedEngagement = progress.engagement.copyWith(
        totalInteractions: progress.engagement.totalInteractions + 1,
        choicesChanged: wasChanged ? progress.engagement.choicesChanged + 1 : progress.engagement.choicesChanged,
      );
      
      final updatedProgress = progress.copyWith(
        choicesMade: updatedChoices,
        engagement: updatedEngagement,
        lastPlayedAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );
      
      return await saveProgress(updatedProgress);
      
    } catch (e, stackTrace) {
      AppLogger.error('$_logPrefix: Failed to record choice', e, stackTrace);
      return false;
    }
  }

  /// Add vocabulary learning
  Future<bool> addVocabularyLearning(String profileId, String storyId, VocabularyProgress vocabulary) async {
    try {
      final progress = await loadProgressForStory(profileId, storyId) ?? _createInitialProgress(profileId, storyId);
      
      final updatedVocabulary = List<VocabularyProgress>.from(progress.vocabularyLearned);
      
      // Check if word already exists
      final existingIndex = updatedVocabulary.indexWhere((v) => v.word.toLowerCase() == vocabulary.word.toLowerCase());
      
      if (existingIndex >= 0) {
        // Update existing vocabulary entry
        final existing = updatedVocabulary[existingIndex];
        updatedVocabulary[existingIndex] = existing.copyWith(
          exposureCount: existing.exposureCount + 1,
          isUnderstood: vocabulary.isUnderstood,
        );
      } else {
        // Add new vocabulary entry
        updatedVocabulary.add(vocabulary);
      }
      
      final updatedProgress = progress.copyWith(
        vocabularyLearned: updatedVocabulary,
        lastPlayedAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );
      
      return await saveProgress(updatedProgress);
      
    } catch (e, stackTrace) {
      AppLogger.error('$_logPrefix: Failed to add vocabulary learning', e, stackTrace);
      return false;
    }
  }

  /// Add moral value learning
  Future<bool> addMoralValueLearning(String profileId, String storyId, MoralValueProgress moralValue) async {
    try {
      final progress = await loadProgressForStory(profileId, storyId) ?? _createInitialProgress(profileId, storyId);
      
      final updatedMoralValues = List<MoralValueProgress>.from(progress.moralValues);
      updatedMoralValues.add(moralValue);
      
      final updatedProgress = progress.copyWith(
        moralValues: updatedMoralValues,
        lastPlayedAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );
      
      return await saveProgress(updatedProgress);
      
    } catch (e, stackTrace) {
      AppLogger.error('$_logPrefix: Failed to add moral value learning', e, stackTrace);
      return false;
    }
  }

  /// Mark story as completed
  Future<bool> markStoryCompleted(String profileId, String storyId, String outcome) async {
    try {
      final progress = await loadProgressForStory(profileId, storyId) ?? _createInitialProgress(profileId, storyId);
      
      final updatedProgress = progress.copyWith(
        isCompleted: true,
        completedOutcome: outcome,
        completionCount: progress.completionCount + 1,
        lastPlayedAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );
      
      return await saveProgress(updatedProgress);
      
    } catch (e, stackTrace) {
      AppLogger.error('$_logPrefix: Failed to mark story completed', e, stackTrace);
      return false;
    }
  }

  /// Update reading skills
  Future<bool> updateReadingSkills(String profileId, String storyId, ReadingSkillsProgress readingSkills) async {
    try {
      final progress = await loadProgressForStory(profileId, storyId) ?? _createInitialProgress(profileId, storyId);
      
      final updatedProgress = progress.copyWith(
        readingSkills: readingSkills,
        lastPlayedAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );
      
      return await saveProgress(updatedProgress);
      
    } catch (e, stackTrace) {
      AppLogger.error('$_logPrefix: Failed to update reading skills', e, stackTrace);
      return false;
    }
  }

  /// Get progress summary for profile
  Future<ProgressSummary> getProgressSummary(String profileId) async {
    try {
      final progressList = await loadProgressForProfile(profileId);
      
      final totalStories = progressList.length;
      final completedStories = progressList.where((p) => p.isCompleted).length;
      final totalVocabulary = progressList.fold(0, (sum, p) => sum + p.totalVocabularyLearned);
      final averageMoralScore = progressList.isEmpty ? 0.0 : 
          progressList.fold(0.0, (sum, p) => sum + p.averageMoralScore) / progressList.length;
      final totalReadingTime = progressList.fold(0, (sum, p) => sum + p.totalPlayTime);
      
      return ProgressSummary(
        profileId: profileId,
        totalStories: totalStories,
        completedStories: completedStories,
        totalVocabularyLearned: totalVocabulary,
        averageMoralScore: averageMoralScore,
        totalReadingTimeSeconds: totalReadingTime,
        lastActivity: progressList.isEmpty ? null : 
            progressList.map((p) => p.lastPlayedAt).reduce((a, b) => a.isAfter(b) ? a : b),
      );
      
    } catch (e, stackTrace) {
      AppLogger.error('$_logPrefix: Failed to get progress summary', e, stackTrace);
      return ProgressSummary(profileId: profileId);
    }
  }

  /// Delete progress for profile
  Future<bool> deleteProgressForProfile(String profileId) async {
    try {
      AppLogger.debug('$_logPrefix: Deleting progress for profile $profileId');
      
      final prefs = await SharedPreferences.getInstance();
      final existingData = prefs.getStringList(_progressKey) ?? [];
      
      final filteredData = existingData.where((jsonStr) {
        try {
          final data = jsonDecode(jsonStr);
          return data['profileId'] != profileId;
        } catch (e) {
          return true; // Keep malformed entries
        }
      }).toList();
      
      await prefs.setStringList(_progressKey, filteredData);
      AppLogger.info('$_logPrefix: Deleted progress for profile $profileId');
      return true;
      
    } catch (e, stackTrace) {
      AppLogger.error('$_logPrefix: Failed to delete progress for profile', e, stackTrace);
      return false;
    }
  }

  /// Create initial progress for a story
  ChildProgressModel _createInitialProgress(String profileId, String storyId) {
    final now = DateTime.now();
    return ChildProgressModel(
      profileId: profileId,
      storyId: storyId,
      lastPlayedAt: now,
      createdAt: now,
      updatedAt: now,
      readingSkills: ReadingSkillsProgress(lastAssessment: now),
      engagement: const EngagementMetrics(),
    );
  }
}

/// Notifier for child progress state management
class ChildProgressNotifier extends StateNotifier<AsyncValue<List<ChildProgressModel>>> {
  final ChildProgressService _service;

  ChildProgressNotifier(this._service) : super(const AsyncValue.loading()) {
    loadProgress();
  }

  /// Load all progress data
  Future<void> loadProgress() async {
    try {
      state = const AsyncValue.loading();
      final progress = await _service.loadAllProgress();
      state = AsyncValue.data(progress);
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
    }
  }

  /// Refresh progress data
  Future<void> refresh() async {
    await loadProgress();
  }
}

/// Summary model for progress overview
class ProgressSummary {
  final String profileId;
  final int totalStories;
  final int completedStories;
  final int totalVocabularyLearned;
  final double averageMoralScore;
  final int totalReadingTimeSeconds;
  final DateTime? lastActivity;

  const ProgressSummary({
    required this.profileId,
    this.totalStories = 0,
    this.completedStories = 0,
    this.totalVocabularyLearned = 0,
    this.averageMoralScore = 0.0,
    this.totalReadingTimeSeconds = 0,
    this.lastActivity,
  });

  double get completionRate => totalStories == 0 ? 0.0 : completedStories / totalStories;
  
  String get totalReadingTimeFormatted {
    final hours = totalReadingTimeSeconds ~/ 3600;
    final minutes = (totalReadingTimeSeconds % 3600) ~/ 60;
    if (hours > 0) {
      return '${hours}h ${minutes}m';
    } else {
      return '${minutes}m';
    }
  }
}
