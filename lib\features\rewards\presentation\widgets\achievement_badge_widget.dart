import 'package:flutter/material.dart';

/// Animated achievement badge widget
class AchievementBadgeWidget extends StatefulWidget {
  final String title;
  final String description;
  final IconData icon;
  final bool isEarned;
  final Duration animationDelay;
  final AnimationController animationController;

  const AchievementBadgeWidget({
    super.key,
    required this.title,
    required this.description,
    required this.icon,
    required this.isEarned,
    required this.animationDelay,
    required this.animationController,
  });

  @override
  State<AchievementBadgeWidget> createState() => _AchievementBadgeWidgetState();
}

class _AchievementBadgeWidgetState extends State<AchievementBadgeWidget>
    with TickerProviderStateMixin {
  late final AnimationController _badgeController;
  late final AnimationController _glowController;
  late final Animation<double> _scaleAnimation;
  late final Animation<double> _fadeAnimation;
  late final Animation<double> _glowAnimation;
  late final Animation<double> _rotationAnimation;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _startAnimation();
  }

  void _initializeAnimations() {
    _badgeController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _glowController = AnimationController(
      duration: const Duration(milliseconds: 2000),
      vsync: this,
    );

    _scaleAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _badgeController,
      curve: Curves.elasticOut,
    ));

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _badgeController,
      curve: Curves.easeInOut,
    ));

    _glowAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _glowController,
      curve: Curves.easeInOut,
    ));

    _rotationAnimation = Tween<double>(
      begin: 0.0,
      end: 0.1,
    ).animate(CurvedAnimation(
      parent: _badgeController,
      curve: Curves.easeInOut,
    ));

    // Start glow animation for earned badges
    if (widget.isEarned) {
      _glowController.repeat(reverse: true);
    }
  }

  Future<void> _startAnimation() async {
    await Future.delayed(widget.animationDelay);
    if (mounted) {
      _badgeController.forward();
    }
  }

  Color _getBadgeColor() {
    if (!widget.isEarned) {
      return Colors.grey;
    }

    // Different colors based on achievement type
    switch (widget.title.toLowerCase()) {
      case 'first story':
        return Colors.blue;
      case 'kind heart':
        return Colors.pink;
      case 'brave soul':
        return Colors.orange;
      case 'sharing spirit':
        return Colors.green;
      case 'story explorer':
        return Colors.purple;
      case 'choice master':
        return Colors.amber;
      default:
        return Colors.blue;
    }
  }

  @override
  void dispose() {
    _badgeController.dispose();
    _glowController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final badgeColor = _getBadgeColor();

    return FadeTransition(
      opacity: _fadeAnimation,
      child: ScaleTransition(
        scale: _scaleAnimation,
        child: GestureDetector(
          onTap: widget.isEarned ? _showAchievementDetails : null,
          child: Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(16),
              boxShadow: widget.isEarned
                  ? [
                      BoxShadow(
                        color: badgeColor.withOpacity(0.3),
                        blurRadius: 8,
                        offset: const Offset(0, 4),
                      ),
                    ]
                  : null,
            ),
            child: Card(
              elevation: widget.isEarned ? 6 : 2,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(16),
              ),
              child: Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(16),
                  gradient: widget.isEarned
                      ? LinearGradient(
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                          colors: [
                            badgeColor.withOpacity(0.2),
                            theme.colorScheme.surface,
                          ],
                        )
                      : null,
                  border: Border.all(
                    color: widget.isEarned
                        ? badgeColor.withOpacity(0.5)
                        : Colors.grey.withOpacity(0.3),
                    width: 2,
                  ),
                ),
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      // Badge icon with glow effect
                      AnimatedBuilder(
                        animation: widget.isEarned ? _glowAnimation : _badgeController,
                        builder: (context, child) {
                          return Transform.rotate(
                            angle: _rotationAnimation.value,
                            child: Container(
                              width: 48,
                              height: 48,
                              decoration: BoxDecoration(
                                shape: BoxShape.circle,
                                color: widget.isEarned ? badgeColor : Colors.grey,
                                boxShadow: widget.isEarned
                                    ? [
                                        BoxShadow(
                                          color: badgeColor.withOpacity(
                                            0.5 + (_glowAnimation.value * 0.3),
                                          ),
                                          blurRadius: 12 + (_glowAnimation.value * 8),
                                          spreadRadius: 2,
                                        ),
                                      ]
                                    : null,
                              ),
                              child: Icon(
                                widget.icon,
                                color: Colors.white,
                                size: 24,
                              ),
                            ),
                          );
                        },
                      ),

                      const SizedBox(height: 12),

                      // Badge title
                      Text(
                        widget.title,
                        style: theme.textTheme.titleSmall?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: widget.isEarned
                              ? badgeColor
                              : theme.colorScheme.onSurfaceVariant,
                        ),
                        textAlign: TextAlign.center,
                      ),

                      const SizedBox(height: 4),

                      // Badge description
                      Text(
                        widget.description,
                        style: theme.textTheme.bodySmall?.copyWith(
                          color: widget.isEarned
                              ? theme.colorScheme.onSurface
                              : theme.colorScheme.onSurfaceVariant,
                        ),
                        textAlign: TextAlign.center,
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),

                      const SizedBox(height: 8),

                      // Earned indicator
                      if (widget.isEarned)
                        Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 8,
                            vertical: 4,
                          ),
                          decoration: BoxDecoration(
                            color: badgeColor.withOpacity(0.2),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Text(
                            'Earned!',
                            style: theme.textTheme.labelSmall?.copyWith(
                              color: badgeColor,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        )
                      else
                        Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 8,
                            vertical: 4,
                          ),
                          decoration: BoxDecoration(
                            color: Colors.grey.withOpacity(0.2),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Text(
                            'Locked',
                            style: theme.textTheme.labelSmall?.copyWith(
                              color: Colors.grey,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  void _showAchievementDetails() {
    showDialog(
      context: context,
      builder: (context) => _AchievementDetailsDialog(
        title: widget.title,
        description: widget.description,
        icon: widget.icon,
        color: _getBadgeColor(),
      ),
    );
  }
}

/// Dialog showing achievement details
class _AchievementDetailsDialog extends StatelessWidget {
  final String title;
  final String description;
  final IconData icon;
  final Color color;

  const _AchievementDetailsDialog({
    required this.title,
    required this.description,
    required this.icon,
    required this.color,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return AlertDialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Achievement icon
          Container(
            width: 80,
            height: 80,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: color,
              boxShadow: [
                BoxShadow(
                  color: color.withOpacity(0.3),
                  blurRadius: 12,
                  spreadRadius: 2,
                ),
              ],
            ),
            child: Icon(
              icon,
              color: Colors.white,
              size: 40,
            ),
          ),

          const SizedBox(height: 16),

          // Achievement title
          Text(
            title,
            style: theme.textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
              color: color,
            ),
            textAlign: TextAlign.center,
          ),

          const SizedBox(height: 8),

          // Achievement description
          Text(
            description,
            style: theme.textTheme.bodyLarge,
            textAlign: TextAlign.center,
          ),

          const SizedBox(height: 16),

          // Congratulations message
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: color.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  Icons.celebration,
                  color: color,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  'Achievement Unlocked!',
                  style: theme.textTheme.bodyMedium?.copyWith(
                    color: color,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Close'),
        ),
      ],
    );
  }
}
