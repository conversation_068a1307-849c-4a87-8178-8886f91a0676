import 'package:json_annotation/json_annotation.dart';

part 'app_settings_model.g.dart';

/// Model for global app settings from Firestore app_config/global_settings
@JsonSerializable()
class AppSettingsModel {
  /// Minimum required app version
  final String minRequiredAppVersion;

  /// Whether the app is in maintenance mode
  final bool isMaintenanceMode;

  /// Localized maintenance message
  final Map<String, String> maintenanceMessage;

  /// Localized latest news URL (optional)
  final Map<String, String>? latestNewsUrl;

  /// Default order for moral values in Parent Zone
  final List<String> defaultMoralValuesOrder;

  const AppSettingsModel({
    required this.minRequiredAppVersion,
    this.isMaintenanceMode = false,
    this.maintenanceMessage = const {},
    this.latestNewsUrl,
    this.defaultMoralValuesOrder = const [
      'Honesty',
      'Empathy',
      'Perseverance',
      'Kindness',
      'Courage',
      'Responsibility'
    ],
  });

  /// Creates an AppSettingsModel from JSON
  factory AppSettingsModel.fromJson(Map<String, dynamic> json) =>
      _$AppSettingsModelFromJson(json);

  /// Converts the AppSettingsModel to JSON
  Map<String, dynamic> toJson() => _$AppSettingsModelToJson(this);

  /// Gets the localized maintenance message for the given language code
  String getLocalizedMaintenanceMessage(String languageCode) {
    return maintenanceMessage[languageCode] ?? 
           maintenanceMessage['en-US'] ?? 
           'The app is currently under maintenance. Please try again later.';
  }

  /// Gets the localized news URL for the given language code
  String? getLocalizedNewsUrl(String languageCode) {
    if (latestNewsUrl == null) return null;
    return latestNewsUrl![languageCode] ?? latestNewsUrl!['en-US'];
  }

  /// Creates a copy of this model with updated fields
  AppSettingsModel copyWith({
    String? minRequiredAppVersion,
    bool? isMaintenanceMode,
    Map<String, String>? maintenanceMessage,
    Map<String, String>? latestNewsUrl,
    List<String>? defaultMoralValuesOrder,
  }) {
    return AppSettingsModel(
      minRequiredAppVersion: minRequiredAppVersion ?? this.minRequiredAppVersion,
      isMaintenanceMode: isMaintenanceMode ?? this.isMaintenanceMode,
      maintenanceMessage: maintenanceMessage ?? this.maintenanceMessage,
      latestNewsUrl: latestNewsUrl ?? this.latestNewsUrl,
      defaultMoralValuesOrder: defaultMoralValuesOrder ?? this.defaultMoralValuesOrder,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is AppSettingsModel && 
           other.minRequiredAppVersion == minRequiredAppVersion &&
           other.isMaintenanceMode == isMaintenanceMode;
  }

  @override
  int get hashCode => Object.hash(minRequiredAppVersion, isMaintenanceMode);

  @override
  String toString() {
    return 'AppSettingsModel(minVersion: $minRequiredAppVersion, maintenance: $isMaintenanceMode)';
  }
}
