import 'package:flutter_test/flutter_test.dart';
import 'package:choice_once_upon_a_time/core/localization/screen_narration_service.dart';

void main() {
  group('ScreenNarrationService', () {
    late ScreenNarrationService service;

    setUp(() {
      service = ScreenNarrationService();
    });

    test('should be a singleton', () {
      final service1 = ScreenNarrationService();
      final service2 = ScreenNarrationService();
      expect(service1, same(service2));
    });

    test('should not be initialized initially', () {
      expect(service.isInitialized, false);
      expect(service.narrationCount, 0);
    });

    test('should return null for narration when not initialized', () {
      final narration = service.getNarrationForScreen('screen_home_library_intro');
      expect(narration, isNull);
    });

    test('should return empty list for available screen keys when not initialized', () {
      final keys = service.getAvailableScreenKeys();
      expect(keys, isEmpty);
    });

    group('ScreenNarration model', () {
      test('should create from JSO<PERSON> correctly', () {
        final json = {
          'text': 'Welcome to our app!',
          'emotionCue': 'warmly_welcoming'
        };

        final narration = ScreenNarration.fromJson(json);

        expect(narration.text, 'Welcome to our app!');
        expect(narration.emotionCue, 'warmly_welcoming');
      });

      test('should have correct toString representation', () {
        const narration = ScreenNarration(
          text: 'Hello world',
          emotionCue: 'cheerful'
        );

        expect(narration.toString(), 'ScreenNarration(text: "Hello world", emotionCue: "cheerful")');
      });
    });
  });
}
