import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter/foundation.dart';
import 'package:logger/logger.dart';
import 'package:choice_once_upon_a_time/models/rewards_model.dart';
import 'package:choice_once_upon_a_time/core/services/rewards_service.dart';

/// State for rewards management
@immutable
class RewardsState {
  final List<EarnedReward> earnedRewards;
  final Map<String, int> rewardCounts;
  final bool isLoading;
  final String? error;
  final EarnedReward? lastEarnedReward;

  const RewardsState({
    this.earnedRewards = const [],
    this.rewardCounts = const {},
    this.isLoading = false,
    this.error,
    this.lastEarnedReward,
  });

  RewardsState copyWith({
    List<EarnedReward>? earnedRewards,
    Map<String, int>? rewardCounts,
    bool? isLoading,
    String? error,
    EarnedReward? lastEarnedReward,
  }) {
    return RewardsState(
      earnedRewards: earnedRewards ?? this.earnedRewards,
      rewardCounts: rewardCounts ?? this.rewardCounts,
      isLoading: isLoading ?? this.isLoading,
      error: error,
      lastEarnedReward: lastEarnedReward ?? this.lastEarnedReward,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is RewardsState &&
        listEquals(other.earnedRewards, earnedRewards) &&
        mapEquals(other.rewardCounts, rewardCounts) &&
        other.isLoading == isLoading &&
        other.error == error &&
        other.lastEarnedReward == lastEarnedReward;
  }

  @override
  int get hashCode {
    return Object.hash(
      earnedRewards,
      rewardCounts,
      isLoading,
      error,
      lastEarnedReward,
    );
  }
}

/// Notifier for managing rewards state
class RewardsNotifier extends StateNotifier<RewardsState> {
  final RewardsService _rewardsService;
  final Logger _logger;

  RewardsNotifier({
    RewardsService? rewardsService,
  }) : _rewardsService = rewardsService ?? RewardsService(),
       _logger = Logger(),
       super(const RewardsState());

  /// Loads all earned rewards
  Future<void> loadEarnedRewards() async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      _logger.i('[RewardsNotifier] Loading earned rewards');

      final earnedRewards = await _rewardsService.getAllEarnedRewards();
      final rewardCounts = await _rewardsService.getAllRewardCounts();

      state = state.copyWith(
        earnedRewards: earnedRewards,
        rewardCounts: rewardCounts,
        isLoading: false,
      );

      _logger.i('[RewardsNotifier] Loaded ${earnedRewards.length} earned rewards');

    } catch (e) {
      _logger.e('[RewardsNotifier] Failed to load earned rewards: $e');
      state = state.copyWith(
        isLoading: false,
        error: 'Failed to load rewards',
      );
    }
  }

  /// Awards a completion reward for finishing a story
  Future<void> awardCompletionReward(String storyId, String rewardId) async {
    try {
      _logger.i('[RewardsNotifier] Awarding completion reward: $rewardId for story: $storyId');

      final reward = await _rewardsService.awardCompletionReward(storyId, rewardId);
      
      // Update state with new reward
      final updatedRewards = [...state.earnedRewards, reward];
      final updatedCounts = Map<String, int>.from(state.rewardCounts);
      updatedCounts[rewardId] = (updatedCounts[rewardId] ?? 0) + 1;

      state = state.copyWith(
        earnedRewards: updatedRewards,
        rewardCounts: updatedCounts,
        lastEarnedReward: reward,
      );

      _logger.i('[RewardsNotifier] Successfully awarded completion reward: $rewardId');

    } catch (e) {
      _logger.e('[RewardsNotifier] Failed to award completion reward: $e');
      state = state.copyWith(error: 'Failed to award reward');
    }
  }

  /// Awards a moral choice reward for making a good choice
  Future<void> awardMoralChoiceReward(String storyId, String sceneId, String rewardId) async {
    try {
      _logger.i('[RewardsNotifier] Awarding moral choice reward: $rewardId for story: $storyId, scene: $sceneId');

      final reward = await _rewardsService.awardMoralChoiceReward(storyId, sceneId, rewardId);
      
      // Update state with new reward
      final updatedRewards = [...state.earnedRewards, reward];
      final updatedCounts = Map<String, int>.from(state.rewardCounts);
      updatedCounts[rewardId] = (updatedCounts[rewardId] ?? 0) + 1;

      state = state.copyWith(
        earnedRewards: updatedRewards,
        rewardCounts: updatedCounts,
        lastEarnedReward: reward,
      );

      _logger.i('[RewardsNotifier] Successfully awarded moral choice reward: $rewardId');

    } catch (e) {
      _logger.e('[RewardsNotifier] Failed to award moral choice reward: $e');
      state = state.copyWith(error: 'Failed to award reward');
    }
  }

  /// Checks if a specific reward has been earned for a story
  Future<bool> hasEarnedReward(String storyId, String rewardId) async {
    try {
      return await _rewardsService.hasEarnedReward(storyId, rewardId);
    } catch (e) {
      _logger.e('[RewardsNotifier] Failed to check earned reward: $e');
      return false;
    }
  }

  /// Checks if the completion reward has been earned for a story
  Future<bool> hasCompletedStory(String storyId) async {
    try {
      return await _rewardsService.hasCompletedStory(storyId);
    } catch (e) {
      _logger.e('[RewardsNotifier] Failed to check story completion: $e');
      return false;
    }
  }

  /// Gets earned rewards for a specific story
  List<EarnedReward> getRewardsForStory(String storyId) {
    return state.earnedRewards.where((reward) => reward.storyId == storyId).toList();
  }

  /// Gets the count of how many times a specific reward has been earned
  int getRewardCount(String rewardId) {
    return state.rewardCounts[rewardId] ?? 0;
  }

  /// Gets unique reward types that have been earned
  List<String> getUniqueEarnedRewards() {
    final uniqueRewards = <String>{};
    for (final reward in state.earnedRewards) {
      uniqueRewards.add(reward.rewardId);
    }
    return uniqueRewards.toList();
  }

  /// Clears the last earned reward (after it's been displayed)
  void clearLastEarnedReward() {
    state = state.copyWith(lastEarnedReward: null);
  }

  /// Clears any error state
  void clearError() {
    state = state.copyWith(error: null);
  }

  /// Clears all earned rewards (for testing or reset purposes)
  Future<void> clearAllRewards() async {
    try {
      _logger.i('[RewardsNotifier] Clearing all rewards');
      
      await _rewardsService.clearAllRewards();
      
      state = const RewardsState();
      
      _logger.i('[RewardsNotifier] Successfully cleared all rewards');

    } catch (e) {
      _logger.e('[RewardsNotifier] Failed to clear rewards: $e');
      state = state.copyWith(error: 'Failed to clear rewards');
    }
  }
}

/// Provider for rewards management
final rewardsProvider = StateNotifierProvider<RewardsNotifier, RewardsState>((ref) {
  return RewardsNotifier();
});

/// Provider for checking if a story has been completed
final storyCompletionProvider = FutureProvider.family<bool, String>((ref, storyId) async {
  final rewardsNotifier = ref.read(rewardsProvider.notifier);
  return await rewardsNotifier.hasCompletedStory(storyId);
});

/// Provider for getting rewards for a specific story
final storyRewardsProvider = Provider.family<List<EarnedReward>, String>((ref, storyId) {
  final rewardsState = ref.watch(rewardsProvider);
  return rewardsState.earnedRewards.where((reward) => reward.storyId == storyId).toList();
});

/// Provider for getting the count of a specific reward
final rewardCountProvider = Provider.family<int, String>((ref, rewardId) {
  final rewardsState = ref.watch(rewardsProvider);
  return rewardsState.rewardCounts[rewardId] ?? 0;
});
