import 'dart:convert';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:choice_once_upon_a_time/core/utils/app_logger.dart';

/// Service for managing user authentication sessions with 7-day persistence
class SessionManager {
  static const String _logPrefix = 'SessionManager';
  static const String _sessionKey = 'user_session';
  static const String _lastActivityKey = 'last_activity';
  static const Duration _sessionDuration = Duration(days: 30);
  
  final FlutterSecureStorage _secureStorage = const FlutterSecureStorage(
    aOptions: AndroidOptions(
      encryptedSharedPreferences: true,
    ),
    iOptions: IOSOptions(
      accessibility: KeychainAccessibility.first_unlock_this_device,
    ),
  );

  /// Check if user has a valid session
  Future<bool> hasValidSession() async {
    try {
      AppLogger.debug('$_logPrefix: Checking for valid session');
      
      final sessionData = await _secureStorage.read(key: _session<PERSON>ey);
      final lastActivityData = await _secureStorage.read(key: _lastActivityKey);
      
      if (sessionData == null || lastActivityData == null) {
        AppLogger.debug('$_logPrefix: No session data found');
        return false;
      }

      final lastActivity = DateTime.parse(lastActivityData);
      final now = DateTime.now();
      final timeSinceLastActivity = now.difference(lastActivity);

      if (timeSinceLastActivity > _sessionDuration) {
        AppLogger.info('$_logPrefix: Session expired (${timeSinceLastActivity.inDays} days old)');
        await clearSession();
        return false;
      }

      // Update last activity
      await _updateLastActivity();
      
      AppLogger.info('$_logPrefix: Valid session found (${timeSinceLastActivity.inHours} hours old)');
      return true;
    } catch (e, stackTrace) {
      AppLogger.error('$_logPrefix: Error checking session validity', e, stackTrace);
      return false;
    }
  }

  /// Create a new session for the user
  Future<bool> createSession(UserSession session) async {
    try {
      AppLogger.info('$_logPrefix: Creating new session for user ${session.userId}');
      
      final sessionJson = jsonEncode(session.toJson());
      await _secureStorage.write(key: _sessionKey, value: sessionJson);
      await _updateLastActivity();
      
      AppLogger.info('$_logPrefix: Session created successfully');
      return true;
    } catch (e, stackTrace) {
      AppLogger.error('$_logPrefix: Error creating session', e, stackTrace);
      return false;
    }
  }

  /// Get the current user session
  Future<UserSession?> getCurrentSession() async {
    try {
      if (!await hasValidSession()) {
        return null;
      }

      final sessionData = await _secureStorage.read(key: _sessionKey);
      if (sessionData == null) {
        return null;
      }

      final sessionJson = jsonDecode(sessionData);
      final session = UserSession.fromJson(sessionJson);
      
      AppLogger.debug('$_logPrefix: Retrieved session for user ${session.userId}');
      return session;
    } catch (e, stackTrace) {
      AppLogger.error('$_logPrefix: Error retrieving session', e, stackTrace);
      return null;
    }
  }

  /// Update the session data
  Future<bool> updateSession(UserSession session) async {
    try {
      AppLogger.debug('$_logPrefix: Updating session for user ${session.userId}');
      
      final sessionJson = jsonEncode(session.toJson());
      await _secureStorage.write(key: _sessionKey, value: sessionJson);
      await _updateLastActivity();
      
      return true;
    } catch (e, stackTrace) {
      AppLogger.error('$_logPrefix: Error updating session', e, stackTrace);
      return false;
    }
  }

  /// Clear the current session (logout)
  Future<void> clearSession() async {
    try {
      AppLogger.info('$_logPrefix: Clearing session');
      
      await _secureStorage.delete(key: _sessionKey);
      await _secureStorage.delete(key: _lastActivityKey);
      
      AppLogger.info('$_logPrefix: Session cleared successfully');
    } catch (e, stackTrace) {
      AppLogger.error('$_logPrefix: Error clearing session', e, stackTrace);
    }
  }

  /// Update last activity timestamp
  Future<void> _updateLastActivity() async {
    try {
      final now = DateTime.now().toIso8601String();
      await _secureStorage.write(key: _lastActivityKey, value: now);
    } catch (e, stackTrace) {
      AppLogger.error('$_logPrefix: Error updating last activity', e, stackTrace);
    }
  }

  /// Get session expiry information
  Future<SessionInfo?> getSessionInfo() async {
    try {
      final lastActivityData = await _secureStorage.read(key: _lastActivityKey);
      if (lastActivityData == null) {
        return null;
      }

      final lastActivity = DateTime.parse(lastActivityData);
      final now = DateTime.now();
      final timeSinceLastActivity = now.difference(lastActivity);
      final timeUntilExpiry = _sessionDuration - timeSinceLastActivity;

      return SessionInfo(
        lastActivity: lastActivity,
        timeUntilExpiry: timeUntilExpiry,
        isExpired: timeUntilExpiry.isNegative,
      );
    } catch (e, stackTrace) {
      AppLogger.error('$_logPrefix: Error getting session info', e, stackTrace);
      return null;
    }
  }

  /// Extend session (reset the timer)
  Future<bool> extendSession() async {
    try {
      AppLogger.debug('$_logPrefix: Extending session');
      await _updateLastActivity();
      return true;
    } catch (e, stackTrace) {
      AppLogger.error('$_logPrefix: Error extending session', e, stackTrace);
      return false;
    }
  }
}

/// User session data model
class UserSession {
  final String userId;
  final String email;
  final String displayName;
  final bool isPremium;
  final DateTime createdAt;
  final Map<String, dynamic> preferences;

  UserSession({
    required this.userId,
    required this.email,
    required this.displayName,
    this.isPremium = false,
    DateTime? createdAt,
    this.preferences = const {},
  }) : createdAt = createdAt ?? DateTime.now();

  /// Convert to JSON for storage
  Map<String, dynamic> toJson() {
    return {
      'userId': userId,
      'email': email,
      'displayName': displayName,
      'isPremium': isPremium,
      'createdAt': createdAt.toIso8601String(),
      'preferences': preferences,
    };
  }

  /// Create from JSON
  factory UserSession.fromJson(Map<String, dynamic> json) {
    return UserSession(
      userId: json['userId'],
      email: json['email'],
      displayName: json['displayName'],
      isPremium: json['isPremium'] ?? false,
      createdAt: DateTime.parse(json['createdAt']),
      preferences: Map<String, dynamic>.from(json['preferences'] ?? {}),
    );
  }

  /// Create a copy with updated fields
  UserSession copyWith({
    String? userId,
    String? email,
    String? displayName,
    bool? isPremium,
    DateTime? createdAt,
    Map<String, dynamic>? preferences,
  }) {
    return UserSession(
      userId: userId ?? this.userId,
      email: email ?? this.email,
      displayName: displayName ?? this.displayName,
      isPremium: isPremium ?? this.isPremium,
      createdAt: createdAt ?? this.createdAt,
      preferences: preferences ?? this.preferences,
    );
  }

  @override
  String toString() {
    return 'UserSession(userId: $userId, email: $email, displayName: $displayName, isPremium: $isPremium)';
  }
}

/// Session information for UI display
class SessionInfo {
  final DateTime lastActivity;
  final Duration timeUntilExpiry;
  final bool isExpired;

  SessionInfo({
    required this.lastActivity,
    required this.timeUntilExpiry,
    required this.isExpired,
  });

  /// Get human-readable time until expiry
  String get timeUntilExpiryString {
    if (isExpired) return 'Expired';
    
    final days = timeUntilExpiry.inDays;
    final hours = timeUntilExpiry.inHours % 24;
    final minutes = timeUntilExpiry.inMinutes % 60;

    if (days > 0) {
      return '$days day${days == 1 ? '' : 's'}, $hours hour${hours == 1 ? '' : 's'}';
    } else if (hours > 0) {
      return '$hours hour${hours == 1 ? '' : 's'}, $minutes minute${minutes == 1 ? '' : 's'}';
    } else {
      return '$minutes minute${minutes == 1 ? '' : 's'}';
    }
  }
}

/// Provider for session manager
final sessionManagerProvider = Provider<SessionManager>((ref) {
  return SessionManager();
});

/// Provider for current user session
final currentSessionProvider = FutureProvider<UserSession?>((ref) async {
  final sessionManager = ref.watch(sessionManagerProvider);
  return await sessionManager.getCurrentSession();
});

/// Provider for session info
final sessionInfoProvider = FutureProvider<SessionInfo?>((ref) async {
  final sessionManager = ref.watch(sessionManagerProvider);
  return await sessionManager.getSessionInfo();
});
