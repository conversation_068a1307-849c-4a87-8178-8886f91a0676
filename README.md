# Choice: Once Upon A Time

An interactive bedtime story app for children aged 4-7 years, featuring empathetic narration, moral value integration, and choice-driven storytelling.

## Features

### 🎭 Interactive Storytelling
- Choose-your-own-adventure style narratives
- Meaningful choices that teach moral values
- Branching storylines with multiple outcomes

### 🎙️ Empathetic Narrator
- Warm, gentle narration with emotional modulation
- Age-appropriate speech patterns and pacing
- **NEW: Screen Introduction Narrations** - Welcoming voice guides for each screen

### 📚 Story Library
- Curated collection of high-quality stories
- Offline story downloads for uninterrupted reading
- Regular content updates with new adventures

### 👨‍👩‍👧‍👦 Parent Zone
- Comprehensive parental controls
- Sound and narration settings
- Story progress tracking and insights

### 🌙 Bedtime Optimized
- Calming visual design and gentle transitions
- Gradual wind-down features for bedtime routine
- Sleep-friendly audio settings

## New Feature: Narrator Screen Introductions

The app now includes warm, contextual narrator introductions for each screen:

- **Welcoming Greetings**: Personalized introductions for new and returning users
- **Contextual Guidance**: Screen-specific narrations that orient and guide users
- **Emotional Appropriateness**: Different tones for child-facing vs. parent-facing screens
- **Non-Intrusive**: Brief, skippable introductions that don't block interaction
- **Localized Content**: Stored in `assets/localization/screen_narrations_en.json` for easy translation

### Supported Screens
- App Launch (different for new/returning users)
- Story Library/Home Screen
- First-Time User Experience (FTUE)
- Parent Zone Dashboard
- Sound Settings
- Subscription Screen
- And more...

## Technical Architecture

### Core Technologies
- **Flutter**: Cross-platform mobile development
- **Riverpod**: State management and dependency injection
- **Firebase**: Backend services (Firestore, Auth, Storage)
- **flutter_tts**: Text-to-speech functionality
- **GoRouter**: Navigation and routing

### Key Components
- **TTSServiceInterface**: Abstracted text-to-speech service
- **ScreenNarrationService**: Manages screen introduction content
- **ScreenNarratorMixin**: Reusable mixin for easy screen narrator integration
- **EmotionCueMapperService**: Maps emotion cues to speech parameters

### Project Structure
```
lib/
├── app/                    # App-wide configuration
├── core/                   # Core utilities and services
│   ├── audio/             # TTS and sound services
│   ├── localization/      # Screen narration service
│   └── mixins/            # Reusable mixins
├── features/              # Feature modules
│   ├── app_init/         # App initialization & FTUE
│   ├── story_library/    # Story browsing & selection
│   ├── story_player/     # Story playback & interaction
│   └── parent_zone/      # Parent dashboard & settings
└── shared_widgets/        # Reusable UI components
```

## Getting Started

### Prerequisites
- Flutter SDK (3.0.0 or higher)
- Dart SDK
- Firebase project setup
- Android Studio / VS Code with Flutter extensions

### Installation
1. Clone the repository
2. Install dependencies: `flutter pub get`
3. Set up Firebase configuration
4. Create `.env` file with required environment variables
5. Run the app: `flutter run`

### Environment Variables
Create a `.env` file in the project root with:
```
FIREBASE_API_KEY=your_firebase_api_key_here
# Add other required environment variables
```

## Assets

### Localization
- `assets/localization/screen_narrations_en.json` - Screen introduction narrations
- `assets/localization/` - Future language support files

### Stories
- `assets/stories/` - Story JSON files with narrative content
- Each story includes narrator segments, character dialogues, and choice points

### Audio & Images
- `assets/audio/ui/` - UI sound effects
- `assets/images/` - App icons and illustrations

## Testing

Run tests with:
```bash
flutter test
```

### Test Coverage
- Unit tests for core services and utilities
- Widget tests for key UI components
- Integration tests for critical user flows

## Development Guidelines

- Follow the established feature-first folder structure
- Use Riverpod for state management and dependency injection
- Implement proper error handling and logging
- Ensure web compatibility for all features
- Add comprehensive tests for new functionality

## Contributing

1. Follow the coding standards defined in `analysis_options.yaml`
2. Add tests for new features
3. Update documentation for significant changes
4. Ensure all features work across platforms (mobile, web)

## Documentation

- `docs/` - Comprehensive project documentation
- `screen_narration_feature_report.md` - Detailed implementation report for narrator features
- `FILE_CATALOG.md` - Complete file structure overview

## License

This project is proprietary software. All rights reserved.

---

**Choice: Once Upon A Time** - Where every story is an adventure, and every choice matters. 🌟
