import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:choice_once_upon_a_time/features/story_library/data/story_repository.dart';
import 'package:choice_once_upon_a_time/core/services/firebase_storage_service.dart';
import 'package:choice_once_upon_a_time/core/services/zip_extraction_service.dart';
import 'package:choice_once_upon_a_time/core/services/asset_only_story_service.dart';
import 'package:choice_once_upon_a_time/core/services/enhanced_story_service.dart';
import 'package:choice_once_upon_a_time/models/story_metadata_model.dart';
import 'package:choice_once_upon_a_time/models/story_model.dart';
import 'package:choice_once_upon_a_time/core/exceptions/story_load_exception.dart';

import 'story_repository_firebase_test.mocks.dart';

@GenerateMocks([
  FirebaseStorageService,
  ZipExtractionService,
  AssetOnlyStoryService,
  EnhancedStoryService,
])
void main() {
  group('StoryRepository Firebase Integration Tests', () {
    late StoryRepository repository;
    late MockFirebaseStorageService mockFirebaseStorage;
    late MockZipExtractionService mockZipExtraction;
    late MockAssetOnlyStoryService mockAssetService;
    late MockEnhancedStoryService mockEnhancedService;

    setUp(() {
      mockFirebaseStorage = MockFirebaseStorageService();
      mockZipExtraction = MockZipExtractionService();
      mockAssetService = MockAssetOnlyStoryService();
      mockEnhancedService = MockEnhancedStoryService();

      repository = StoryRepository(
        firebaseStorageService: mockFirebaseStorage,
        zipExtractionService: mockZipExtraction,
        assetStoryService: mockAssetService,
        enhancedStoryService: mockEnhancedService,
      );
    });

    group('downloadStoryFromFirebase', () {
      test('should download and extract story successfully', () async {
        // Arrange
        const storyId = 'test_story';
        const zipPath = '/path/to/story.zip';
        const extractedPath = '/path/to/extracted/story';

        when(mockZipExtraction.isStoryDownloaded(storyId))
            .thenAnswer((_) async => false);
        when(mockFirebaseStorage.storyExists(storyId))
            .thenAnswer((_) async => true);
        when(mockFirebaseStorage.downloadStoryZip(storyId, onProgress: anyNamed('onProgress')))
            .thenAnswer((_) async => zipPath);
        when(mockZipExtraction.extractStoryZip(zipPath, storyId, onProgress: anyNamed('onProgress')))
            .thenAnswer((_) async => extractedPath);
        when(mockZipExtraction.validateStoryStructure(extractedPath))
            .thenAnswer((_) async => true);

        // Act
        final result = await repository.downloadStoryFromFirebase(storyId);

        // Assert
        expect(result, isTrue);
        verify(mockFirebaseStorage.downloadStoryZip(storyId, onProgress: anyNamed('onProgress'))).called(1);
        verify(mockZipExtraction.extractStoryZip(zipPath, storyId, onProgress: anyNamed('onProgress'))).called(1);
        verify(mockZipExtraction.validateStoryStructure(extractedPath)).called(1);
      });

      test('should return true if story already downloaded', () async {
        // Arrange
        const storyId = 'test_story';

        when(mockZipExtraction.isStoryDownloaded(storyId))
            .thenAnswer((_) async => true);

        // Act
        final result = await repository.downloadStoryFromFirebase(storyId);

        // Assert
        expect(result, isTrue);
        verifyNever(mockFirebaseStorage.downloadStoryZip(any, onProgress: anyNamed('onProgress')));
      });

      test('should throw exception if story not found in Firebase', () async {
        // Arrange
        const storyId = 'nonexistent_story';

        when(mockZipExtraction.isStoryDownloaded(storyId))
            .thenAnswer((_) async => false);
        when(mockFirebaseStorage.storyExists(storyId))
            .thenAnswer((_) async => false);

        // Act & Assert
        expect(
          () => repository.downloadStoryFromFirebase(storyId),
          throwsA(isA<StoryLoadException>()),
        );
      });

      test('should throw exception if story structure is invalid', () async {
        // Arrange
        const storyId = 'test_story';
        const zipPath = '/path/to/story.zip';
        const extractedPath = '/path/to/extracted/story';

        when(mockZipExtraction.isStoryDownloaded(storyId))
            .thenAnswer((_) async => false);
        when(mockFirebaseStorage.storyExists(storyId))
            .thenAnswer((_) async => true);
        when(mockFirebaseStorage.downloadStoryZip(storyId, onProgress: anyNamed('onProgress')))
            .thenAnswer((_) async => zipPath);
        when(mockZipExtraction.extractStoryZip(zipPath, storyId, onProgress: anyNamed('onProgress')))
            .thenAnswer((_) async => extractedPath);
        when(mockZipExtraction.validateStoryStructure(extractedPath))
            .thenAnswer((_) async => false);

        // Act & Assert
        expect(
          () => repository.downloadStoryFromFirebase(storyId),
          throwsA(isA<StoryLoadException>()),
        );
      });
    });

    group('getStoryStatus', () {
      test('should return "play" if story is downloaded locally', () async {
        // Arrange
        const storyId = 'test_story';

        when(mockZipExtraction.isStoryDownloaded(storyId))
            .thenAnswer((_) async => true);

        // Act
        final status = await repository.getStoryStatus(storyId);

        // Assert
        expect(status, equals('play'));
      });

      test('should return "play" if story exists in assets', () async {
        // Arrange
        const storyId = 'test_story';

        when(mockZipExtraction.isStoryDownloaded(storyId))
            .thenAnswer((_) async => false);
        when(mockAssetService.storyExists(storyId))
            .thenAnswer((_) async => true);

        // Act
        final status = await repository.getStoryStatus(storyId);

        // Assert
        expect(status, equals('play'));
      });

      test('should return "download" if story is available in Firebase', () async {
        // Arrange
        const storyId = 'test_story';

        when(mockZipExtraction.isStoryDownloaded(storyId))
            .thenAnswer((_) async => false);
        when(mockAssetService.storyExists(storyId))
            .thenAnswer((_) async => false);
        when(mockFirebaseStorage.storyExists(storyId))
            .thenAnswer((_) async => true);

        // Act
        final status = await repository.getStoryStatus(storyId);

        // Assert
        expect(status, equals('download'));
      });

      test('should return "error" if story not available anywhere', () async {
        // Arrange
        const storyId = 'nonexistent_story';

        when(mockZipExtraction.isStoryDownloaded(storyId))
            .thenAnswer((_) async => false);
        when(mockAssetService.storyExists(storyId))
            .thenAnswer((_) async => false);
        when(mockFirebaseStorage.storyExists(storyId))
            .thenAnswer((_) async => false);

        // Act
        final status = await repository.getStoryStatus(storyId);

        // Assert
        expect(status, equals('error'));
      });
    });

    group('fetchStoryMetadataList', () {
      test('should combine stories from all sources', () async {
        // Arrange
        final assetStories = [
          StoryMetadataModel(
            id: 'asset_story',
            title: {'en-US': 'Asset Story'},
            coverImageUrl: 'asset_cover.jpg',
            loglineShort: {'en-US': 'An asset story'},
            targetMoralValue: 'friendship',
            targetAgeSubSegment: '5-7',
            estimatedDurationMinutes: 10,
            isFree: true,
            isLocked: false,
            published: true,
            dataSource: 'assets',
            version: '1.0.0',
            supportedLanguages: ['en-US'],
            defaultLanguage: 'en-US',
            initialSceneId: 'scene_1',
          ),
        ];

        final enhancedStories = [
          StoryMetadataModel(
            id: 'enhanced_story',
            title: {'en-US': 'Enhanced Story'},
            coverImageUrl: 'enhanced_cover.jpg',
            loglineShort: {'en-US': 'An enhanced story'},
            targetMoralValue: 'kindness',
            targetAgeSubSegment: '6-8',
            estimatedDurationMinutes: 15,
            isFree: true,
            isLocked: false,
            published: true,
            dataSource: 'enhanced',
            version: '2.0.0',
            supportedLanguages: ['en-US'],
            defaultLanguage: 'en-US',
            initialSceneId: 'scene_1',
          ),
        ];

        final firebaseStoryIds = ['firebase_story'];

        when(mockAssetService.getAllStoryMetadata())
            .thenAnswer((_) async => assetStories);
        when(mockEnhancedService.getAllStoryMetadata())
            .thenAnswer((_) async => enhancedStories);
        when(mockFirebaseStorage.listAvailableStories())
            .thenAnswer((_) async => firebaseStoryIds);
        when(mockFirebaseStorage.downloadStoryMetadata('firebase_story'))
            .thenAnswer((_) async => {
              'title': 'Firebase Story',
              'description': 'A Firebase story',
              'coverImageUrl': 'firebase_cover.jpg',
            });

        // Act
        final stories = await repository.fetchStoryMetadataList();

        // Assert
        expect(stories.length, equals(3));
        expect(stories.any((s) => s.id == 'asset_story'), isTrue);
        expect(stories.any((s) => s.id == 'enhanced_story'), isTrue);
        expect(stories.any((s) => s.id == 'firebase_story'), isTrue);
      });
    });

    group('getStoryAvailabilityInfo', () {
      test('should return comprehensive availability information', () async {
        // Arrange
        const storyId = 'test_story';

        when(mockAssetService.storyExists(storyId))
            .thenAnswer((_) async => true);
        when(mockFirebaseStorage.storyExists(storyId))
            .thenAnswer((_) async => true);
        when(mockZipExtraction.isStoryDownloaded(storyId))
            .thenAnswer((_) async => false);
        when(mockZipExtraction.getStoryPath(storyId))
            .thenAnswer((_) async => '/path/to/story');

        // Act
        final info = await repository.getStoryAvailabilityInfo(storyId);

        // Assert
        expect(info['storyId'], equals(storyId));
        expect(info['availableInAssets'], isTrue);
        expect(info['availableInFirebase'], isTrue);
        expect(info['downloadedLocally'], isFalse);
        expect(info['recommendedSource'], equals('assets'));
      });
    });
  });
}
