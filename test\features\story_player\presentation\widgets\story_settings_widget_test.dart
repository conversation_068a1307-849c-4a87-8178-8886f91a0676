import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:choice_once_upon_a_time/features/story_player/presentation/widgets/story_settings_widget.dart';


void main() {
  group('StorySettingsWidget', () {
    late StorySettings testSettings;
    late bool settingsChanged;
    late bool closed;



    setUp(() {
      testSettings = const StorySettings();
      settingsChanged = false;
      closed = false;
    });

    Widget createTestWidget() {
      return MaterialApp(
        home: Scaffold(
          body: StorySettingsWidget(
            initialSettings: testSettings,
            onSettingsChanged: (settings) {
              testSettings = settings;
              settingsChanged = true;
            },
            onClose: () {
              closed = true;
            },
          ),
        ),
      );
    }

    testWidgets('should display settings panel with title', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());
      await tester.pumpAndSettle();

      expect(find.text('Story Settings'), findsOneWidget);
      expect(find.byIcon(Icons.close), findsOneWidget);
    });

    testWidgets('should display font size control', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());
      await tester.pumpAndSettle();

      expect(find.text('Font Size'), findsOneWidget);
      expect(find.text('Sample text'), findsOneWidget);
      
      // Should show current font size value
      expect(find.textContaining('18'), findsOneWidget);
    });

    testWidgets('should display transparency control', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());
      await tester.pumpAndSettle();

      expect(find.text('Subtitle Background'), findsOneWidget);
      expect(find.text('Subtitle preview'), findsOneWidget);
      
      // Should show current transparency value
      expect(find.textContaining('50'), findsOneWidget);
    });

    testWidgets('should display narration speed control', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());
      await tester.pumpAndSettle();

      expect(find.text('Narration Speed'), findsOneWidget);
      expect(find.textContaining('1.0x speed'), findsOneWidget);
      expect(find.byIcon(Icons.play_arrow), findsOneWidget);
    });

    testWidgets('should handle font size slider changes', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());
      await tester.pumpAndSettle();

      // Find font size slider
      final sliders = find.byType(Slider);
      expect(sliders, findsAtLeastNWidgets(3)); // Font size, transparency, speed

      // Drag the first slider (font size)
      await tester.drag(sliders.first, const Offset(50, 0));
      await tester.pumpAndSettle();

      expect(settingsChanged, isTrue);
      expect(testSettings.fontSize, greaterThan(18.0));
    });

    testWidgets('should handle transparency slider changes', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());
      await tester.pumpAndSettle();

      // Find transparency slider (second one)
      final sliders = find.byType(Slider);
      await tester.drag(sliders.at(1), const Offset(-50, 0));
      await tester.pumpAndSettle();

      expect(settingsChanged, isTrue);
      expect(testSettings.subtitleTransparency, lessThan(50.0));
    });

    testWidgets('should handle narration speed slider changes', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());
      await tester.pumpAndSettle();

      // Find narration speed slider (third one)
      final sliders = find.byType(Slider);
      await tester.drag(sliders.at(2), const Offset(30, 0));
      await tester.pumpAndSettle();

      expect(settingsChanged, isTrue);
      expect(testSettings.narrationSpeed, greaterThan(1.0));
    });

    testWidgets('should update preview when font size changes', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());
      await tester.pumpAndSettle();

      // Change font size
      final sliders = find.byType(Slider);
      await tester.drag(sliders.first, const Offset(100, 0));
      await tester.pumpAndSettle();

      // Preview text should reflect new font size
      final previewText = tester.widget<Text>(find.text('Sample text'));
      expect(previewText.style?.fontSize, greaterThan(18.0));
    });

    testWidgets('should update preview when transparency changes', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());
      await tester.pumpAndSettle();

      // Change transparency
      final sliders = find.byType(Slider);
      await tester.drag(sliders.at(1), const Offset(-100, 0));
      await tester.pumpAndSettle();

      // Preview container should reflect new transparency
      final previewContainer = tester.widget<Container>(
        find.ancestor(
          of: find.text('Subtitle preview'),
          matching: find.byType(Container),
        ).first,
      );
      
      final decoration = previewContainer.decoration as BoxDecoration;
      final color = decoration.color!;
      expect(color.alpha, lessThan(127)); // Less than 50% transparency
    });

    testWidgets('should update speed icon when narration speed changes', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());
      await tester.pumpAndSettle();

      // Change to slow speed
      final sliders = find.byType(Slider);
      await tester.drag(sliders.at(2), const Offset(-100, 0));
      await tester.pumpAndSettle();

      // Should show slow motion icon
      expect(find.byIcon(Icons.slow_motion_video), findsOneWidget);

      // Change to fast speed
      await tester.drag(sliders.at(2), const Offset(200, 0));
      await tester.pumpAndSettle();

      // Should show fast forward icon
      expect(find.byIcon(Icons.fast_forward), findsOneWidget);
    });

    testWidgets('should handle reset to default button', (WidgetTester tester) async {
      // Start with modified settings
      testSettings = const StorySettings(
        fontSize: 24.0,
        subtitleTransparency: 75.0,
        narrationSpeed: 1.5,
      );

      await tester.pumpWidget(createTestWidget());
      await tester.pumpAndSettle();

      // Tap reset button
      await tester.tap(find.text('Reset to Default'));
      await tester.pumpAndSettle();

      expect(settingsChanged, isTrue);
      expect(testSettings.fontSize, equals(18.0));
      expect(testSettings.subtitleTransparency, equals(50.0));
      expect(testSettings.narrationSpeed, equals(1.0));
    });

    testWidgets('should handle done button', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());
      await tester.pumpAndSettle();

      await tester.tap(find.text('Done'));
      await tester.pumpAndSettle();

      expect(closed, isTrue);
    });

    testWidgets('should handle close button', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());
      await tester.pumpAndSettle();

      await tester.tap(find.byIcon(Icons.close));
      await tester.pumpAndSettle();

      expect(closed, isTrue);
    });

    testWidgets('should animate in and out', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());

      // Should have fade and slide animations
      expect(find.byType(FadeTransition), findsOneWidget);
      expect(find.byType(SlideTransition), findsOneWidget);

      await tester.pumpAndSettle();

      // Animations should complete
      final fadeTransition = tester.widget<FadeTransition>(find.byType(FadeTransition));
      expect(fadeTransition.opacity.value, equals(1.0));
    });

    testWidgets('should be responsive to screen size', (WidgetTester tester) async {
      // Test with small screen
      await tester.binding.setSurfaceSize(const Size(400, 600));
      await tester.pumpWidget(createTestWidget());
      await tester.pumpAndSettle();

      // Should adapt to small screen
      expect(find.byType(StorySettingsWidget), findsOneWidget);

      // Test with large screen
      await tester.binding.setSurfaceSize(const Size(800, 600));
      await tester.pumpWidget(createTestWidget());
      await tester.pumpAndSettle();

      // Should adapt to large screen
      expect(find.byType(StorySettingsWidget), findsOneWidget);

      // Reset surface size
      await tester.binding.setSurfaceSize(null);
    });

    testWidgets('should display correct value labels', (WidgetTester tester) async {
      testSettings = const StorySettings(
        fontSize: 20.0,
        subtitleTransparency: 75.0,
        narrationSpeed: 1.5,
      );

      await tester.pumpWidget(createTestWidget());
      await tester.pumpAndSettle();

      // Should show correct values
      expect(find.text('20px'), findsOneWidget);
      expect(find.text('75%'), findsOneWidget);
      expect(find.text('1.5x'), findsOneWidget);
    });

    testWidgets('should handle edge values correctly', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());
      await tester.pumpAndSettle();

      // Test minimum values
      final sliders = find.byType(Slider);
      
      // Set font size to minimum
      await tester.drag(sliders.first, const Offset(-200, 0));
      await tester.pumpAndSettle();
      expect(testSettings.fontSize, equals(12.0));

      // Set transparency to minimum
      await tester.drag(sliders.at(1), const Offset(-200, 0));
      await tester.pumpAndSettle();
      expect(testSettings.subtitleTransparency, equals(0.0));

      // Set speed to minimum
      await tester.drag(sliders.at(2), const Offset(-200, 0));
      await tester.pumpAndSettle();
      expect(testSettings.narrationSpeed, equals(0.3));
    });
  });
}
