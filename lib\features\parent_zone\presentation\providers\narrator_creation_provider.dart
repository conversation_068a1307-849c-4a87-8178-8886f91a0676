import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:choice_once_upon_a_time/models/narrator_profile_model.dart';
import 'package:choice_once_upon_a_time/models/enhanced_story_model.dart' show VoiceModel;
import 'package:choice_once_upon_a_time/core/utils/app_logger.dart';
import 'dart:convert';

/// Provider for managing narrator creation and editing
final narratorCreationProvider = StateNotifierProvider<NarratorCreationNotifier, NarratorCreationState>((ref) {
  return NarratorCreationNotifier();
});

/// State for narrator creation
class NarratorCreationState {
  final List<NarratorProfileModel> narrators;
  final bool isLoading;
  final String? error;
  final NarratorProfileModel? selectedNarrator;

  const NarratorCreationState({
    this.narrators = const [],
    this.isLoading = false,
    this.error,
    this.selectedNarrator,
  });

  NarratorCreationState copyWith({
    List<NarratorProfileModel>? narrators,
    bool? isLoading,
    String? error,
    NarratorProfileModel? selectedNarrator,
  }) {
    return NarratorCreationState(
      narrators: narrators ?? this.narrators,
      isLoading: isLoading ?? this.isLoading,
      error: error,
      selectedNarrator: selectedNarrator ?? this.selectedNarrator,
    );
  }
}

/// Notifier for narrator creation operations
class NarratorCreationNotifier extends StateNotifier<NarratorCreationState> {
  static const String _narratorsKey = 'custom_narrators';
  static const String _selectedNarratorKey = 'selected_narrator_id';

  NarratorCreationNotifier() : super(const NarratorCreationState()) {
    AppLogger.debug('[COMPONENT_LOAD] Full path: lib/features/parent_zone/presentation/providers/narrator_creation_provider.dart - NarratorCreationNotifier');
    _loadNarrators();
  }

  /// Load all saved narrators
  Future<void> _loadNarrators() async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      final prefs = await SharedPreferences.getInstance();
      final narratorsJson = prefs.getStringList(_narratorsKey) ?? [];

      final narrators = <NarratorProfileModel>[];
      for (final json in narratorsJson) {
        try {
          narrators.add(NarratorProfileModel.fromJson(jsonDecode(json)));
        } catch (e) {
          AppLogger.debug('[NARRATOR_CREATION] Error parsing narrator: $e');
        }
      }

      // Load selected narrator
      final selectedId = prefs.getString(_selectedNarratorKey);
      NarratorProfileModel? selectedNarrator;
      if (selectedId != null) {
        selectedNarrator = narrators.firstWhere(
          (n) => n.id == selectedId,
          orElse: () => _getDefaultNarrator(),
        );
      } else {
        selectedNarrator = _getDefaultNarrator();
      }

      state = state.copyWith(
        narrators: narrators,
        selectedNarrator: selectedNarrator,
        isLoading: false,
      );

      AppLogger.debug('[NARRATOR_CREATION] Loaded ${narrators.length} narrators');
    } catch (e) {
      AppLogger.debug('[NARRATOR_CREATION] Error loading narrators: $e');
      state = state.copyWith(
        isLoading: false,
        error: 'Failed to load narrators: $e',
        selectedNarrator: _getDefaultNarrator(),
      );
    }
  }

  /// Save a narrator (create or update)
  Future<void> saveNarrator(NarratorProfileModel narrator) async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      final updatedNarrators = List<NarratorProfileModel>.from(state.narrators);
      
      // Check if updating existing narrator
      final existingIndex = updatedNarrators.indexWhere((n) => n.id == narrator.id);
      if (existingIndex >= 0) {
        updatedNarrators[existingIndex] = narrator.copyWith(updatedAt: DateTime.now());
      } else {
        updatedNarrators.add(narrator.copyWith(
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        ));
      }

      await _saveNarratorsToStorage(updatedNarrators);

      state = state.copyWith(
        narrators: updatedNarrators,
        isLoading: false,
      );

      AppLogger.debug('[NARRATOR_CREATION] Saved narrator: ${narrator.name}');
    } catch (e) {
      AppLogger.debug('[NARRATOR_CREATION] Error saving narrator: $e');
      state = state.copyWith(
        isLoading: false,
        error: 'Failed to save narrator: $e',
      );
    }
  }

  /// Delete a narrator
  Future<void> deleteNarrator(String narratorId) async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      final updatedNarrators = state.narrators.where((n) => n.id != narratorId).toList();
      await _saveNarratorsToStorage(updatedNarrators);

      // If deleted narrator was selected, switch to default
      NarratorProfileModel? newSelected = state.selectedNarrator;
      if (state.selectedNarrator?.id == narratorId) {
        newSelected = _getDefaultNarrator();
        await setSelectedNarrator(newSelected.id);
      }

      state = state.copyWith(
        narrators: updatedNarrators,
        selectedNarrator: newSelected,
        isLoading: false,
      );

      AppLogger.debug('[NARRATOR_CREATION] Deleted narrator: $narratorId');
    } catch (e) {
      AppLogger.debug('[NARRATOR_CREATION] Error deleting narrator: $e');
      state = state.copyWith(
        isLoading: false,
        error: 'Failed to delete narrator: $e',
      );
    }
  }

  /// Load a specific narrator by ID
  Future<NarratorProfileModel?> loadNarrator(String narratorId) async {
    try {
      return state.narrators.firstWhere((n) => n.id == narratorId);
    } catch (e) {
      AppLogger.debug('[NARRATOR_CREATION] Narrator not found: $narratorId');
      return null;
    }
  }

  /// Set the selected narrator
  Future<void> setSelectedNarrator(String narratorId) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_selectedNarratorKey, narratorId);

      final selectedNarrator = state.narrators.firstWhere(
        (n) => n.id == narratorId,
        orElse: () => _getDefaultNarrator(),
      );

      state = state.copyWith(selectedNarrator: selectedNarrator);
      AppLogger.debug('[NARRATOR_CREATION] Selected narrator: ${selectedNarrator.name}');
    } catch (e) {
      AppLogger.debug('[NARRATOR_CREATION] Error setting selected narrator: $e');
    }
  }

  /// Get the currently selected narrator
  NarratorProfileModel get selectedNarrator => state.selectedNarrator ?? _getDefaultNarrator();

  /// Save narrators to SharedPreferences
  Future<void> _saveNarratorsToStorage(List<NarratorProfileModel> narrators) async {
    final prefs = await SharedPreferences.getInstance();
    final narratorsJson = narrators.map((n) => jsonEncode(n.toJson())).toList();
    await prefs.setStringList(_narratorsKey, narratorsJson);
  }

  /// Get default narrator
  NarratorProfileModel _getDefaultNarrator() {
    return NarratorProfileModel(
      id: 'default',
      name: 'Default Narrator',
      description: 'The standard storytelling voice',
      voice: const VoiceModel(
        name: 'Default',
        pitch: 1.0,
        rate: 1.0,
        volume: 1.0,
      ),
      category: NarratorCategory.custom,
      gender: NarratorGender.neutral,
      ageRange: NarratorAgeRange.adult,
      personalities: [NarratorPersonality.gentle],
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
      isDefault: true,
    );
  }

  /// Create a new narrator with default values
  NarratorProfileModel createNewNarrator() {
    return NarratorProfileModel(
      id: 'narrator_${DateTime.now().millisecondsSinceEpoch}',
      name: '',
      description: '',
      voice: const VoiceModel(
        name: 'Default',
        pitch: 1.0,
        rate: 1.0,
        volume: 1.0,
      ),
      category: NarratorCategory.custom,
      gender: NarratorGender.neutral,
      ageRange: NarratorAgeRange.adult,
      personalities: [],
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );
  }

  /// Get all available narrator categories
  List<NarratorCategory> get availableCategories => NarratorCategory.values;

  /// Get all available narrator genders
  List<NarratorGender> get availableGenders => NarratorGender.values;

  /// Get all available age ranges
  List<NarratorAgeRange> get availableAgeRanges => NarratorAgeRange.values;

  /// Get all available personalities
  List<NarratorPersonality> get availablePersonalities => NarratorPersonality.values;
}
