import 'package:flutter/material.dart';
import 'package:choice_once_upon_a_time/core/services/accessibility_service.dart';
import 'package:choice_once_upon_a_time/core/services/story_settings_service.dart';
import 'package:choice_once_upon_a_time/core/utils/responsive_utils.dart';

/// Accessible subtitle widget with responsive design and high contrast support
class AccessibleSubtitleWidget extends StatefulWidget {
  final String subtitle;
  final String? speaker;
  final String? emotion;
  final bool isVisible;
  final Duration? displayDuration;

  const AccessibleSubtitleWidget({
    super.key,
    required this.subtitle,
    this.speaker,
    this.emotion,
    this.isVisible = true,
    this.displayDuration,
  });

  @override
  State<AccessibleSubtitleWidget> createState() => _AccessibleSubtitleWidgetState();
}

class _AccessibleSubtitleWidgetState extends State<AccessibleSubtitleWidget>
    with SingleTickerProviderStateMixin {
  late final AccessibilityService _accessibilityService;
  late final StorySettingsService _settingsService;
  late final AnimationController _animationController;
  late final Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    _accessibilityService = AccessibilityService.instance;
    _settingsService = StorySettingsService.instance;
    
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    if (widget.isVisible) {
      _animationController.forward();
    }
  }

  @override
  void didUpdateWidget(AccessibleSubtitleWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    
    if (widget.isVisible != oldWidget.isVisible) {
      if (widget.isVisible) {
        _animationController.forward();
      } else {
        _animationController.reverse();
      }
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  Color _getEmotionColor(String? emotion) {
    if (emotion == null) return Colors.white;
    
    switch (emotion.toLowerCase()) {
      case 'happy':
      case 'excited':
      case 'joyful':
        return Colors.yellow.shade100;
      case 'sad':
      case 'disappointed':
        return Colors.blue.shade100;
      case 'angry':
        return Colors.red.shade100;
      case 'surprised':
      case 'amazed':
        return Colors.purple.shade100;
      case 'calm':
      case 'peaceful':
        return Colors.green.shade100;
      default:
        return Colors.white;
    }
  }

  Color _getTextColor(ThemeData theme, bool highContrast) {
    if (highContrast) {
      return Colors.white;
    }
    return Colors.white;
  }

  Color _getBackgroundColor(ThemeData theme, bool highContrast, double contrast) {
    const baseColor = Colors.black;
    final opacity = highContrast ? 0.95 : (0.7 + (contrast * 0.25));
    return baseColor.withOpacity(opacity);
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return StreamBuilder<bool>(
      stream: _settingsService.subtitlesEnabledStream,
      initialData: _settingsService.subtitlesEnabled,
      builder: (context, subtitlesSnapshot) {
        final subtitlesEnabled = subtitlesSnapshot.data ?? true;
        
        if (!subtitlesEnabled || widget.subtitle.isEmpty) {
          return const SizedBox.shrink();
        }
        
        return StreamBuilder<bool>(
          stream: _accessibilityService.highContrastStream,
          initialData: _accessibilityService.highContrastMode,
          builder: (context, contrastSnapshot) {
            final highContrast = contrastSnapshot.data ?? false;
            
            return StreamBuilder<double>(
              stream: _accessibilityService.subtitleContrastStream,
              initialData: _accessibilityService.subtitleContrast,
              builder: (context, contrastLevelSnapshot) {
                final contrastLevel = contrastLevelSnapshot.data ?? 0.8;
                
                return StreamBuilder<double>(
                  stream: _settingsService.subtitleSizeStream,
                  initialData: _settingsService.subtitleSize,
                  builder: (context, sizeSnapshot) {
                    final fontSize = sizeSnapshot.data ?? 18.0;
                    
                    return StreamBuilder<bool>(
                      stream: _accessibilityService.largeTextStream,
                      initialData: _accessibilityService.largeTextMode,
                      builder: (context, largeTextSnapshot) {
                        final largeText = largeTextSnapshot.data ?? false;
                        
                        return _buildSubtitleWidget(
                          theme,
                          highContrast,
                          contrastLevel,
                          fontSize,
                          largeText,
                        );
                      },
                    );
                  },
                );
              },
            );
          },
        );
      },
    );
  }

  Widget _buildSubtitleWidget(
    ThemeData theme,
    bool highContrast,
    double contrastLevel,
    double fontSize,
    bool largeText,
  ) {
    final adjustedFontSize = ResponsiveUtils.getResponsiveFontSize(
      context,
      fontSize * (largeText ? 1.3 : 1.0),
    );
    
    final textColor = _getTextColor(theme, highContrast);
    final backgroundColor = _getBackgroundColor(theme, highContrast, contrastLevel);
    final emotionColor = _getEmotionColor(widget.emotion);
    
    // Create semantic label for screen readers
    final semanticLabel = _accessibilityService.getSemanticLabel(
      widget.subtitle,
      context: widget.speaker != null ? 'Spoken by ${widget.speaker}' : 'Narration',
    );

    return FadeTransition(
      opacity: _fadeAnimation,
      child: Container(
        margin: context.responsiveMargin,
        child: Container(
          padding: ResponsiveUtils.getResponsivePadding(context),
          decoration: BoxDecoration(
            color: backgroundColor,
            borderRadius: ResponsiveUtils.getResponsiveBorderRadius(context, 12),
            border: highContrast
                ? Border.all(
                    color: Colors.white,
                    width: 2,
                  )
                : widget.emotion != null
                    ? Border.all(
                        color: emotionColor,
                        width: 2,
                      )
                    : null,
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.3),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Speaker indicator
              if (widget.speaker != null) ...[
                Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      Icons.person,
                      size: ResponsiveUtils.getResponsiveIconSize(context, 16),
                      color: textColor.withOpacity(0.8),
                    ),
                    const SizedBox(width: 4),
                    Text(
                      widget.speaker!,
                      style: TextStyle(
                        color: textColor.withOpacity(0.8),
                        fontSize: adjustedFontSize * 0.8,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
              ],
              
              // Main subtitle text
              Semantics(
                label: semanticLabel,
                child: Text(
                  widget.subtitle,
                  style: TextStyle(
                    color: textColor,
                    fontSize: adjustedFontSize,
                    height: 1.4,
                    fontWeight: highContrast ? FontWeight.w600 : FontWeight.w500,
                    shadows: highContrast
                        ? [
                            const Shadow(
                              color: Colors.black,
                              blurRadius: 2,
                              offset: Offset(1, 1),
                            ),
                          ]
                        : null,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
              
              // Emotion indicator
              if (widget.emotion != null && !highContrast) ...[
                const SizedBox(height: 8),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: emotionColor.withOpacity(0.3),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Text(
                    widget.emotion!,
                    style: TextStyle(
                      color: textColor,
                      fontSize: adjustedFontSize * 0.7,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }
}
