import 'package:json_annotation/json_annotation.dart';

part 'background_music_config.g.dart';

/// Configuration for background music in a scene
@JsonSerializable()
class BackgroundMusicConfig {
  /// URL to the music track in Firebase Storage
  final String trackUrl;

  /// Volume level (0.0 to 1.0)
  final double volume;

  /// Whether the track should loop
  final bool loop;

  const BackgroundMusicConfig({
    required this.trackUrl,
    this.volume = 0.5,
    this.loop = true,
  });

  /// Creates a BackgroundMusicConfig from JSON
  factory BackgroundMusicConfig.fromJson(Map<String, dynamic> json) =>
      _$BackgroundMusicConfigFromJson(json);

  /// Converts the BackgroundMusicConfig to JSON
  Map<String, dynamic> toJson() => _$BackgroundMusicConfigToJson(this);

  /// Creates a copy of this model with updated fields
  BackgroundMusicConfig copyWith({
    String? trackUrl,
    double? volume,
    bool? loop,
  }) {
    return BackgroundMusicConfig(
      trackUrl: trackUrl ?? this.trackUrl,
      volume: volume ?? this.volume,
      loop: loop ?? this.loop,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is BackgroundMusicConfig && 
           other.trackUrl == trackUrl &&
           other.volume == volume &&
           other.loop == loop;
  }

  @override
  int get hashCode => Object.hash(trackUrl, volume, loop);

  @override
  String toString() {
    return 'BackgroundMusicConfig(trackUrl: $trackUrl, volume: $volume, loop: $loop)';
  }
}
