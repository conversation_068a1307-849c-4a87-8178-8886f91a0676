import 'dart:convert';
import 'dart:io';
import 'package:http/http.dart' as http;
import 'package:choice_once_upon_a_time/models/story_model.dart';
import 'package:choice_once_upon_a_time/models/story_metadata_model.dart';

/// Exception thrown when AI story generation fails
class AIStoryGenerationException implements Exception {
  final String message;
  final String? technicalDetails;

  AIStoryGenerationException(this.message, {this.technicalDetails});

  @override
  String toString() {
    if (technicalDetails != null) {
      return 'AIStoryGenerationException: $message\nDetails: $technicalDetails';
    }
    return 'AIStoryGenerationException: $message';
  }
}

/// Service for generating AI-powered stories
class AIStoryService {
  static const String _baseUrl = 'https://api.openai.com/v1';
  static const int _maxRetries = 3;
  static const Duration _timeout = Duration(seconds: 30);

  final String? _apiKey;
  final http.Client _httpClient;

  AIStoryService({
    String? apiKey,
    http.Client? httpClient,
  }) : _apiKey = apiKey,
       _httpClient = httpClient ?? http.Client();

  /// Generates a story based on user preferences
  Future<StoryModel> generateStory({
    required String childName,
    required String theme,
    required String moralValue,
    required String ageGroup,
    List<String>? interests,
    String? setting,
  }) async {
    if (_apiKey == null || _apiKey!.isEmpty) {
      throw AIStoryGenerationException(
        'AI story generation is not available',
        technicalDetails: 'OpenAI API key not configured',
      );
    }

    try {
      final prompt = _buildStoryPrompt(
        childName: childName,
        theme: theme,
        moralValue: moralValue,
        ageGroup: ageGroup,
        interests: interests,
        setting: setting,
      );

      final response = await _makeOpenAIRequest(prompt);
      final storyData = _parseStoryResponse(response);
      
      return _createStoryModel(storyData, childName, theme, moralValue, ageGroup);
    } catch (e) {
      if (e is AIStoryGenerationException) {
        rethrow;
      }
      throw AIStoryGenerationException(
        'Failed to generate AI story',
        technicalDetails: e.toString(),
      );
    }
  }

  /// Generates story metadata for preview
  Future<StoryMetadataModel> generateStoryMetadata({
    required String theme,
    required String moralValue,
    required String ageGroup,
  }) async {
    final storyId = 'ai_story_${DateTime.now().millisecondsSinceEpoch}';
    
    return StoryMetadataModel(
      id: storyId,
      title: {'en-US': 'Your Personalized Story: $theme'},
      coverImageUrl: 'assets/images/story_covers/ai_generated_cover.png',
      loglineShort: {'en-US': 'A personalized story about $theme, teaching $moralValue'},
      targetMoralValue: moralValue,
      isFree: true,
      targetAgeSubSegment: ageGroup,
      initialSceneId: '${storyId}_scene_01',
      dataSource: 'ai_generated',
    );
  }

  String _buildStoryPrompt({
    required String childName,
    required String theme,
    required String moralValue,
    required String ageGroup,
    List<String>? interests,
    String? setting,
  }) {
    final interestsText = interests?.isNotEmpty == true 
        ? ' The child is interested in: ${interests!.join(', ')}.'
        : '';
    
    final settingText = setting?.isNotEmpty == true 
        ? ' The story should be set in: $setting.'
        : '';

    return '''
Create an interactive children's story with the following specifications:

- Child's name: $childName
- Theme: $theme
- Moral value to teach: $moralValue
- Age group: $ageGroup$interestsText$settingText

The story should be structured as a JSON object with the following format:
{
  "title": "Story Title",
  "description": "Brief story description",
  "scenes": [
    {
      "id": "scene_01",
      "sceneType": "narration_illustration",
      "narratorText": "Story text with $childName as the main character",
      "narratorSegments": ["Segment 1", "Segment 2"],
      "backgroundImage": "description of scene for illustration",
      "nextSceneId": "scene_02"
    },
    {
      "id": "scene_02", 
      "sceneType": "choice_point",
      "narratorText": "Story continues with a choice for $childName",
      "narratorSegments": ["Choice setup text"],
      "backgroundImage": "description of choice scene",
      "choices": [
        {
          "id": "choice_01",
          "text": "First choice option",
          "nextSceneId": "scene_03a"
        },
        {
          "id": "choice_02", 
          "text": "Second choice option",
          "nextSceneId": "scene_03b"
        }
      ]
    }
  ]
}

Requirements:
- Include 5-8 scenes total
- Include 2-3 choice points that teach the moral value
- Keep language appropriate for age group $ageGroup
- Make $childName the protagonist
- Include vivid descriptions for background images
- End with a positive resolution that reinforces $moralValue
- Each scene should have 2-4 narrator segments for natural speech pacing

Return only the JSON object, no additional text.
''';
  }

  Future<String> _makeOpenAIRequest(String prompt) async {
    int retryCount = 0;
    
    while (retryCount < _maxRetries) {
      try {
        final response = await _httpClient.post(
          Uri.parse('$_baseUrl/chat/completions'),
          headers: {
            'Content-Type': 'application/json',
            'Authorization': 'Bearer $_apiKey',
          },
          body: jsonEncode({
            'model': 'gpt-3.5-turbo',
            'messages': [
              {
                'role': 'system',
                'content': 'You are a children\'s story writer who creates engaging, educational stories with moral values. Always respond with valid JSON only.',
              },
              {
                'role': 'user',
                'content': prompt,
              },
            ],
            'max_tokens': 2000,
            'temperature': 0.7,
          }),
        ).timeout(_timeout);

        if (response.statusCode == 200) {
          final data = jsonDecode(response.body);
          final content = data['choices'][0]['message']['content'] as String;
          return content.trim();
        } else if (response.statusCode == 429) {
          // Rate limited, wait and retry
          retryCount++;
          if (retryCount < _maxRetries) {
            await Future.delayed(Duration(seconds: retryCount * 2));
            continue;
          }
          throw AIStoryGenerationException(
            'Rate limit exceeded',
            technicalDetails: 'HTTP ${response.statusCode}: ${response.body}',
          );
        } else {
          throw AIStoryGenerationException(
            'API request failed',
            technicalDetails: 'HTTP ${response.statusCode}: ${response.body}',
          );
        }
      } on SocketException {
        throw AIStoryGenerationException(
          'No internet connection',
          technicalDetails: 'Please check your internet connection and try again',
        );
      } on http.ClientException catch (e) {
        throw AIStoryGenerationException(
          'Network error',
          technicalDetails: e.toString(),
        );
      } catch (e) {
        retryCount++;
        if (retryCount >= _maxRetries) {
          rethrow;
        }
        await Future.delayed(Duration(seconds: retryCount));
      }
    }
    
    throw AIStoryGenerationException('Max retries exceeded');
  }

  Map<String, dynamic> _parseStoryResponse(String response) {
    try {
      // Clean up the response to extract JSON
      String cleanResponse = response.trim();
      
      // Remove any markdown code block markers
      if (cleanResponse.startsWith('```json')) {
        cleanResponse = cleanResponse.substring(7);
      }
      if (cleanResponse.startsWith('```')) {
        cleanResponse = cleanResponse.substring(3);
      }
      if (cleanResponse.endsWith('```')) {
        cleanResponse = cleanResponse.substring(0, cleanResponse.length - 3);
      }
      
      cleanResponse = cleanResponse.trim();
      
      return jsonDecode(cleanResponse) as Map<String, dynamic>;
    } catch (e) {
      throw AIStoryGenerationException(
        'Failed to parse AI response',
        technicalDetails: 'Invalid JSON format: $e\nResponse: $response',
      );
    }
  }

  StoryModel _createStoryModel(
    Map<String, dynamic> storyData,
    String childName,
    String theme,
    String moralValue,
    String ageGroup,
  ) {
    final storyId = 'ai_story_${DateTime.now().millisecondsSinceEpoch}';
    
    // Transform the AI response to match our StoryModel format
    final transformedData = {
      'id': storyId,
      'title': storyData['title'] ?? 'Your Personalized Story',
      'description': storyData['description'] ?? 'A personalized story created just for you!',
      'targetMoralValue': moralValue,
      'targetAgeSubSegment': ageGroup,
      'initialSceneId': storyData['scenes']?[0]?['id'] ?? '${storyId}_scene_01',
      'scenes': _transformScenes(storyData['scenes'] ?? [], storyId),
      'version': '1.0.0',
      'createdAt': DateTime.now().toIso8601String(),
      'isAIGenerated': true,
      'generationParams': {
        'childName': childName,
        'theme': theme,
        'moralValue': moralValue,
        'ageGroup': ageGroup,
      },
    };

    return StoryModel.fromJson(transformedData);
  }

  List<Map<String, dynamic>> _transformScenes(List<dynamic> scenes, String storyId) {
    return scenes.asMap().entries.map((entry) {
      final index = entry.key;
      final scene = entry.value as Map<String, dynamic>;
      
      return {
        'id': scene['id'] ?? '${storyId}_scene_${(index + 1).toString().padLeft(2, '0')}',
        'sceneType': scene['sceneType'] ?? 'narration_illustration',
        'narratorText': scene['narratorText'] ?? '',
        'narratorSegments': scene['narratorSegments'] ?? [scene['narratorText'] ?? ''],
        'backgroundImage': scene['backgroundImage'] ?? 'A beautiful illustrated scene',
        'nextSceneId': scene['nextSceneId'],
        'choices': scene['choices'] ?? [],
        'isChoicePoint': scene['sceneType'] == 'choice_point',
      };
    }).toList();
  }

  /// Disposes of resources
  void dispose() {
    _httpClient.close();
  }
}