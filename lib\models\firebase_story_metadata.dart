import 'package:json_annotation/json_annotation.dart';

part 'firebase_story_metadata.g.dart';

/// Metadata for stories stored in Firebase
@JsonSerializable()
class FirebaseStoryMetadata {
  final String storyId;
  final String title;
  final String description;
  final String coverImageUrl;
  final List<String> categories;
  final int ageRangeMin;
  final int ageRangeMax;
  final double estimatedDuration; // in minutes
  final DateTime publishedAt;
  final DateTime updatedAt;
  final String version;
  final int downloadSizeMB;
  final bool isPremium;
  final List<String> languages;
  final Map<String, dynamic> assets; // Asset URLs and metadata
  
  // Download and caching info
  final bool isDownloadable;
  final String downloadUrl; // ZIP file URL
  final String checksum; // For integrity verification

  const FirebaseStoryMetadata({
    required this.storyId,
    required this.title,
    required this.description,
    required this.coverImageUrl,
    required this.categories,
    required this.ageRangeMin,
    required this.ageRangeMax,
    required this.estimatedDuration,
    required this.publishedAt,
    required this.updatedAt,
    required this.version,
    required this.downloadSizeMB,
    required this.isPremium,
    required this.languages,
    required this.assets,
    required this.isDownloadable,
    required this.downloadUrl,
    required this.checksum,
  });

  factory FirebaseStoryMetadata.fromJson(Map<String, dynamic> json) =>
      _$FirebaseStoryMetadataFromJson(json);

  Map<String, dynamic> toJson() => _$FirebaseStoryMetadataToJson(this);

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is FirebaseStoryMetadata &&
          runtimeType == other.runtimeType &&
          storyId == other.storyId &&
          version == other.version;

  @override
  int get hashCode => storyId.hashCode ^ version.hashCode;

  @override
  String toString() => 'FirebaseStoryMetadata(storyId: $storyId, title: $title, version: $version)';
}

/// Download status for Firebase stories
enum DownloadStatus {
  @JsonValue('not_downloaded')
  notDownloaded,
  
  @JsonValue('downloading')
  downloading,
  
  @JsonValue('downloaded')
  downloaded,
  
  @JsonValue('failed')
  failed,
  
  @JsonValue('paused')
  paused,
  
  @JsonValue('cancelled')
  cancelled,
}

/// Download progress information
@JsonSerializable()
class DownloadProgress {
  final String storyId;
  final double progress; // 0.0 to 1.0
  final DownloadStatus status;
  final String? errorMessage;
  final DateTime lastUpdated;

  const DownloadProgress({
    required this.storyId,
    required this.progress,
    required this.status,
    this.errorMessage,
    required this.lastUpdated,
  });

  factory DownloadProgress.fromJson(Map<String, dynamic> json) =>
      _$DownloadProgressFromJson(json);

  Map<String, dynamic> toJson() => _$DownloadProgressToJson(this);

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is DownloadProgress &&
          runtimeType == other.runtimeType &&
          storyId == other.storyId;

  @override
  int get hashCode => storyId.hashCode;

  @override
  String toString() => 'DownloadProgress(storyId: $storyId, progress: $progress, status: $status)';
}

/// Story download information for tracking
@JsonSerializable()
class StoryDownloadInfo {
  final String storyId;
  final String userId;
  final DateTime downloadedAt;
  final String localPath;
  final String version;
  final int fileSizeMB;
  final DownloadStatus status;
  final double progress; // 0.0 to 1.0
  final DateTime? lastAccessedAt;

  const StoryDownloadInfo({
    required this.storyId,
    required this.userId,
    required this.downloadedAt,
    required this.localPath,
    required this.version,
    required this.fileSizeMB,
    required this.status,
    required this.progress,
    this.lastAccessedAt,
  });

  factory StoryDownloadInfo.fromJson(Map<String, dynamic> json) =>
      _$StoryDownloadInfoFromJson(json);

  Map<String, dynamic> toJson() => _$StoryDownloadInfoToJson(this);

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is StoryDownloadInfo &&
          runtimeType == other.runtimeType &&
          storyId == other.storyId &&
          userId == other.userId;

  @override
  int get hashCode => storyId.hashCode ^ userId.hashCode;

  @override
  String toString() => 'StoryDownloadInfo(storyId: $storyId, status: $status, progress: $progress)';
}
