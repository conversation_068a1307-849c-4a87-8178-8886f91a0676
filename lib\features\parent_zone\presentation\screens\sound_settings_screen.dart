import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:choice_once_upon_a_time/app/providers/settings_provider.dart';

/// Sound Settings screen for Parent Zone (Screen 11)
class SoundSettingsScreen extends ConsumerWidget {
  const SoundSettingsScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);
    final settings = ref.watch(settingsProvider);
    final settingsNotifier = ref.read(settingsProvider.notifier);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Sound Settings'),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => context.pop(),
        ),
      ),
      body: ListView(
        padding: const EdgeInsets.all(16),
        children: [
          // Header section
          Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: theme.colorScheme.primary.withOpacity(0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(
                      Icons.volume_up,
                      color: theme.colorScheme.primary,
                      size: 28,
                    ),
                    const SizedBox(width: 12),
                    Text(
                      'Audio Settings',
                      style: theme.textTheme.headlineSmall?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: theme.colorScheme.primary,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                Text(
                  'Customize the audio experience for your child\'s bedtime stories.',
                  style: theme.textTheme.bodyMedium?.copyWith(
                    color: Colors.grey[700],
                  ),
                ),
              ],
            ),
          ),
          
          const SizedBox(height: 24),

          // Voice Guide Section
          _buildSectionCard(
            theme: theme,
            title: 'Voice Guide',
            subtitle: 'Narrates screen transitions and instructions',
            child: SwitchListTile(
              title: Text(
                settings.isVoiceGuideEnabled ? 'Enabled' : 'Disabled',
                style: theme.textTheme.bodyLarge?.copyWith(
                  color: settings.isVoiceGuideEnabled
                      ? theme.colorScheme.primary
                      : Colors.grey[600],
                  fontWeight: FontWeight.w500,
                ),
              ),
              value: settings.isVoiceGuideEnabled,
              onChanged: (_) => settingsNotifier.toggleVoiceGuide(),
              secondary: Icon(
                Icons.record_voice_over,
                color: settings.isVoiceGuideEnabled
                    ? theme.colorScheme.primary
                    : Colors.grey[400],
              ),
            ),
          ),

          const SizedBox(height: 16),

          // Master Volume Section
          _buildSectionCard(
            theme: theme,
            title: 'Master Volume',
            subtitle: 'Controls overall app volume',
            child: Column(
              children: [
                Row(
                  children: [
                    Icon(
                      Icons.volume_down,
                      color: Colors.grey[600],
                    ),
                    Expanded(
                      child: Slider(
                        value: settings.masterVolume,
                        min: 0.0,
                        max: 1.0,
                        divisions: 10,
                        label: '${(settings.masterVolume * 100).round()}%',
                        onChanged: (value) {
                          settingsNotifier.setMasterVolume(value);
                        },
                      ),
                    ),
                    Icon(
                      Icons.volume_up,
                      color: Colors.grey[600],
                    ),
                  ],
                ),
                Text(
                  '${(settings.masterVolume * 100).round()}%',
                  style: theme.textTheme.bodyLarge?.copyWith(
                    fontWeight: FontWeight.w500,
                    color: theme.colorScheme.primary,
                  ),
                ),
              ],
            ),
          ),

          const SizedBox(height: 16),

          // Background Music Section
          _buildSectionCard(
            theme: theme,
            title: 'Background Music',
            subtitle: 'Gentle music during stories',
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  children: [
                    Icon(
                      Icons.music_note,
                      color: settings.musicEnabled 
                          ? theme.colorScheme.primary 
                          : Colors.grey[400],
                    ),
                    const SizedBox(width: 8),
                    Text(
                      settings.musicEnabled ? 'Enabled' : 'Disabled',
                      style: theme.textTheme.bodyLarge?.copyWith(
                        color: settings.musicEnabled 
                            ? theme.colorScheme.primary 
                            : Colors.grey[600],
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
                Switch(
                  value: settings.musicEnabled,
                  onChanged: (_) => settingsNotifier.toggleMusic(),
                ),
              ],
            ),
          ),

          const SizedBox(height: 16),

          // Sound Effects Section
          _buildSectionCard(
            theme: theme,
            title: 'UI Sound Effects',
            subtitle: 'Button taps and interface sounds',
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  children: [
                    Icon(
                      Icons.touch_app,
                      color: settings.sfxEnabled 
                          ? theme.colorScheme.primary 
                          : Colors.grey[400],
                    ),
                    const SizedBox(width: 8),
                    Text(
                      settings.sfxEnabled ? 'Enabled' : 'Disabled',
                      style: theme.textTheme.bodyLarge?.copyWith(
                        color: settings.sfxEnabled 
                            ? theme.colorScheme.primary 
                            : Colors.grey[600],
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
                Switch(
                  value: settings.sfxEnabled,
                  onChanged: (_) => settingsNotifier.toggleSFX(),
                ),
              ],
            ),
          ),

          const SizedBox(height: 32),

          // Reset to defaults button
          Center(
            child: OutlinedButton.icon(
              onPressed: () => _showResetDialog(context, settingsNotifier),
              icon: const Icon(Icons.refresh),
              label: const Text('Reset to Defaults'),
              style: OutlinedButton.styleFrom(
                padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
              ),
            ),
          ),

          const SizedBox(height: 16),

          // Info note
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.blue[50],
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.blue[200]!),
            ),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Icon(
                  Icons.info_outline,
                  color: Colors.blue[700],
                  size: 20,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    'Settings are automatically saved and will apply to all stories. Changes take effect immediately.',
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: Colors.blue[700],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSectionCard({
    required ThemeData theme,
    required String title,
    required String subtitle,
    required Widget child,
  }) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey[200]!),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            spreadRadius: 1,
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: theme.textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            subtitle,
            style: theme.textTheme.bodySmall?.copyWith(
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 16),
          child,
        ],
      ),
    );
  }

  void _showResetDialog(BuildContext context, SettingsNotifier settingsNotifier) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Reset Settings'),
          content: const Text(
            'Are you sure you want to reset all sound settings to their default values?'
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Cancel'),
            ),
            ElevatedButton(
              onPressed: () {
                settingsNotifier.resetToDefaults();
                Navigator.of(context).pop();
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('Settings reset to defaults'),
                    duration: Duration(seconds: 2),
                  ),
                );
              },
              child: const Text('Reset'),
            ),
          ],
        );
      },
    );
  }
}
