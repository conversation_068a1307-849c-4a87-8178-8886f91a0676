import 'dart:async';
import 'package:choice_once_upon_a_time/models/enhanced_story_model.dart';
import 'package:choice_once_upon_a_time/models/narration_models.dart';

/// Abstract interface for story narration services
/// Provides a clean contract for story-specific narration functionality
abstract class IStoryNarrationService {
  /// Stream of narration state changes
  Stream<NarrationState> get stateStream;
  
  /// Stream of narration progress updates
  Stream<NarrationProgress> get progressStream;
  
  /// Stream of word highlights for real-time text highlighting
  Stream<WordHighlight> get wordHighlightStream;
  
  /// Current narration state
  NarrationState get currentState;
  
  /// Current narration configuration
  NarrationConfig get currentConfig;
  
  /// Whether the service is initialized and ready
  bool get isInitialized;
  
  /// Whether narration is currently active
  bool get isNarrating;
  
  /// Whether narration is paused
  bool get isPaused;

  /// Initialize the narration service
  Future<void> initialize({NarrationConfig? config});

  /// Configure narration settings
  Future<void> configure(NarrationConfig config);

  /// Start narrating a complete scene
  Future<void> narrateScene(EnhancedSceneModel scene, {String? storyId});

  /// Start narrating custom text with emotion
  Future<void> narrateText(String text, {String? emotionCue, String? storyId, String? sceneId});

  /// Play/resume narration
  Future<void> play();

  /// Pause narration
  Future<void> pause();

  /// Stop narration completely
  Future<void> stop();

  /// Skip to next sentence
  Future<void> skipToNextSentence();

  /// Skip to previous sentence
  Future<void> skipToPreviousSentence();

  /// Seek to a specific word index
  Future<void> seekToWord(int wordIndex);

  /// Seek to a specific sentence index
  Future<void> seekToSentence(int sentenceIndex);

  /// Replay current sentence
  Future<void> replayCurrentSentence();

  /// Replay entire scene from beginning
  Future<void> replayScene();

  /// Set speech rate (0.1 to 2.0)
  Future<void> setSpeechRate(double rate);

  /// Set speech pitch (0.5 to 2.0)
  Future<void> setSpeechPitch(double pitch);

  /// Set speech volume (0.0 to 1.0)
  Future<void> setSpeechVolume(double volume);

  /// Enable or disable auto-progression to next scene
  Future<void> setAutoProgression(bool enabled);

  /// Enable or disable word highlighting
  Future<void> setWordHighlighting(bool enabled);

  /// Get current narration progress
  NarrationProgress? getCurrentProgress();

  /// Save narration progress for later resumption
  Future<void> saveProgress();

  /// Load and resume from saved progress
  Future<void> loadProgress(String storyId, String sceneId);

  /// Clear saved progress
  Future<void> clearProgress(String storyId, String sceneId);

  /// Dispose of the service and clean up resources
  Future<void> dispose();
}

/// Enhanced story narration service interface with advanced features
abstract class IEnhancedStoryNarrationService extends IStoryNarrationService {
  /// Stream of sentence boundary events
  Stream<SentenceBoundary> get sentenceBoundaryStream;
  
  /// Stream of emotion change events
  Stream<EmotionChange> get emotionChangeStream;

  /// Narrate scene with character-specific voices
  Future<void> narrateSceneWithCharacterVoices(
    EnhancedSceneModel scene, {
    required String storyId,
    Map<String, NarrationConfig>? characterVoices,
  });

  /// Narrate with background music synchronization
  Future<void> narrateWithBackgroundMusic(
    EnhancedSceneModel scene, {
    required String storyId,
    String? backgroundMusicPath,
    double musicVolume = 0.3,
  });

  /// Narrate with advanced emotion transitions
  Future<void> narrateWithEmotionTransitions(
    EnhancedSceneModel scene, {
    required String storyId,
    Duration transitionDuration = const Duration(milliseconds: 500),
  });

  /// Set character-specific voice configuration
  Future<void> setCharacterVoice(String characterId, NarrationConfig config);

  /// Get character voice configuration
  NarrationConfig? getCharacterVoice(String characterId);

  /// Enable or disable background music
  Future<void> setBackgroundMusicEnabled(bool enabled);

  /// Set background music volume
  Future<void> setBackgroundMusicVolume(double volume);

  /// Enable or disable emotion-based voice modulation
  Future<void> setEmotionModulationEnabled(bool enabled);

  /// Preload scene for faster narration start
  Future<void> preloadScene(EnhancedSceneModel scene, String storyId);

  /// Clear preloaded scenes
  Future<void> clearPreloadedScenes();

  /// Get estimated narration duration for a scene
  Future<Duration?> estimateSceneDuration(EnhancedSceneModel scene);

  /// Get detailed narration analytics
  Future<NarrationAnalytics> getNarrationAnalytics(String storyId);
}

/// Sentence boundary event data
class SentenceBoundary {
  final int sentenceIndex;
  final String sentence;
  final int startCharIndex;
  final int endCharIndex;
  final DateTime timestamp;

  const SentenceBoundary({
    required this.sentenceIndex,
    required this.sentence,
    required this.startCharIndex,
    required this.endCharIndex,
    required this.timestamp,
  });
}

/// Emotion change event data
class EmotionChange {
  final String previousEmotion;
  final String newEmotion;
  final int charIndex;
  final DateTime timestamp;

  const EmotionChange({
    required this.previousEmotion,
    required this.newEmotion,
    required this.charIndex,
    required this.timestamp,
  });
}

/// Narration analytics data
class NarrationAnalytics {
  final String storyId;
  final int totalScenesNarrated;
  final Duration totalNarrationTime;
  final int totalWordsNarrated;
  final Map<String, int> emotionUsageCount;
  final Map<String, Duration> characterSpeakingTime;
  final double averageSpeechRate;
  final DateTime lastNarrationDate;

  const NarrationAnalytics({
    required this.storyId,
    required this.totalScenesNarrated,
    required this.totalNarrationTime,
    required this.totalWordsNarrated,
    required this.emotionUsageCount,
    required this.characterSpeakingTime,
    required this.averageSpeechRate,
    required this.lastNarrationDate,
  });
}

/// Story narration service factory
class StoryNarrationServiceFactory {
  /// Create a story narration service instance
  static IStoryNarrationService createService({
    bool enableAdvancedFeatures = true,
    bool enableCharacterVoices = true,
    bool enableBackgroundMusic = false,
    bool enableAnalytics = true,
  }) {
    if (enableAdvancedFeatures) {
      return _createEnhancedService(
        enableCharacterVoices: enableCharacterVoices,
        enableBackgroundMusic: enableBackgroundMusic,
        enableAnalytics: enableAnalytics,
      );
    } else {
      return _createBasicService();
    }
  }

  static IEnhancedStoryNarrationService _createEnhancedService({
    required bool enableCharacterVoices,
    required bool enableBackgroundMusic,
    required bool enableAnalytics,
  }) {
    // Will be implemented with the concrete enhanced service
    throw UnimplementedError('Enhanced story narration service not yet implemented');
  }

  static IStoryNarrationService _createBasicService() {
    // Will be implemented with the concrete basic service
    throw UnimplementedError('Basic story narration service not yet implemented');
  }
}

/// Exception thrown by story narration services
class StoryNarrationException implements Exception {
  final String message;
  final String? code;
  final dynamic originalError;

  const StoryNarrationException(this.message, {this.code, this.originalError});

  @override
  String toString() {
    if (code != null) {
      return 'StoryNarrationException [$code]: $message';
    }
    return 'StoryNarrationException: $message';
  }
}
