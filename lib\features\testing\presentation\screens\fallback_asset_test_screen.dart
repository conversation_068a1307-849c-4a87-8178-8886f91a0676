import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:choice_once_upon_a_time/core/testing/fallback_asset_test_runner.dart';
import 'package:choice_once_upon_a_time/core/services/fallback_asset_manager.dart';
import 'package:choice_once_upon_a_time/core/widgets/fallback_asset_widgets.dart';
import 'package:choice_once_upon_a_time/core/utils/app_logger.dart';

/// Screen for testing fallback asset system functionality
class FallbackAssetTestScreen extends ConsumerStatefulWidget {
  const FallbackAssetTestScreen({super.key});

  @override
  ConsumerState<FallbackAssetTestScreen> createState() => _FallbackAssetTestScreenState();
}

class _FallbackAssetTestScreenState extends ConsumerState<FallbackAssetTestScreen> {
  bool _isRunningTests = false;
  String _testStatus = 'Ready to test fallback asset system';
  List<FallbackAssetTestResult> _testResults = [];
  String _testReport = '';
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Fallback Asset Testing'),
        backgroundColor: Colors.green[800],
        foregroundColor: Colors.white,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildTestControls(),
            const SizedBox(height: 24),
            _buildTestStatus(),
            const SizedBox(height: 24),
            _buildAssetPreview(),
            const SizedBox(height: 24),
            _buildTestResults(),
          ],
        ),
      ),
    );
  }

  /// Build test control buttons
  Widget _buildTestControls() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Fallback Asset System Tests',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 16),
            Text(
              'This will test the fallback asset system with the following scenarios:',
              style: Theme.of(context).textTheme.bodyMedium,
            ),
            const SizedBox(height: 8),
            _buildTestScenarios(),
            const SizedBox(height: 16),
            Row(
              children: [
                ElevatedButton.icon(
                  onPressed: _isRunningTests ? null : _runFallbackTests,
                  icon: _isRunningTests 
                      ? const SizedBox(
                          width: 16,
                          height: 16,
                          child: CircularProgressIndicator(strokeWidth: 2),
                        )
                      : const Icon(Icons.play_arrow),
                  label: Text(_isRunningTests ? 'Running Tests...' : 'Run Fallback Tests'),
                ),
                const SizedBox(width: 16),
                ElevatedButton.icon(
                  onPressed: _testResults.isEmpty ? null : _clearResults,
                  icon: const Icon(Icons.clear),
                  label: const Text('Clear Results'),
                ),
                const SizedBox(width: 16),
                ElevatedButton.icon(
                  onPressed: _testReport.isEmpty ? null : _showFullReport,
                  icon: const Icon(Icons.description),
                  label: const Text('View Report'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// Build test scenarios list
  Widget _buildTestScenarios() {
    final scenarios = [
      'Default assets validation (default_image.png, default_happy.mp3)',
      'Missing image fallback resolution',
      'Missing audio fallback resolution',
      'Story-specific asset resolution',
      'Batch asset resolution performance',
      'Asset metadata retrieval',
      'Cache performance validation',
      'Edge cases and error handling',
    ];

    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.grey[100],
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: scenarios.map((scenario) => Padding(
          padding: const EdgeInsets.symmetric(vertical: 2),
          child: Row(
            children: [
              const Icon(Icons.check_circle_outline, size: 16, color: Colors.green),
              const SizedBox(width: 8),
              Expanded(child: Text(scenario, style: const TextStyle(fontSize: 13))),
            ],
          ),
        )).toList(),
      ),
    );
  }

  /// Build test status display
  Widget _buildTestStatus() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Test Status',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                Icon(
                  _isRunningTests ? Icons.hourglass_empty : Icons.info,
                  color: _isRunningTests ? Colors.orange : Colors.blue,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    _testStatus,
                    style: TextStyle(
                      color: _isRunningTests ? Colors.orange : Colors.black87,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// Build asset preview section
  Widget _buildAssetPreview() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Asset Preview (with Fallback)',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                // Test existing image
                Expanded(
                  child: Column(
                    children: [
                      const Text('Existing Image', style: TextStyle(fontWeight: FontWeight.bold)),
                      const SizedBox(height: 8),
                      SizedBox(
                        height: 100,
                        child: FallbackImageWidget(
                          imagePath: 'assets/stories/story013/images/scene1.jpg',
                          showFallbackIndicator: true,
                          fit: BoxFit.cover,
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(width: 16),
                // Test missing image (should show fallback)
                Expanded(
                  child: Column(
                    children: [
                      const Text('Missing Image', style: TextStyle(fontWeight: FontWeight.bold)),
                      const SizedBox(height: 8),
                      SizedBox(
                        height: 100,
                        child: FallbackImageWidget(
                          imagePath: 'assets/stories/nonexistent/missing.jpg',
                          showFallbackIndicator: true,
                          fit: BoxFit.cover,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                // Test audio with fallback
                Expanded(
                  child: Column(
                    children: [
                      const Text('Audio with Fallback', style: TextStyle(fontWeight: FontWeight.bold)),
                      const SizedBox(height: 8),
                      FallbackAudioWidget(
                        audioPath: 'assets/stories/story013/audio/missing.mp3',
                        showFallbackIndicator: true,
                        showControls: true,
                      ),
                    ],
                  ),
                ),
                const SizedBox(width: 16),
                // Asset debug info
                Expanded(
                  child: Column(
                    children: [
                      const Text('Asset Debug Info', style: TextStyle(fontWeight: FontWeight.bold)),
                      const SizedBox(height: 8),
                      AssetDebugWidget(
                        assetPath: 'assets/stories/nonexistent/missing.jpg',
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// Build test results display
  Widget _buildTestResults() {
    if (_testResults.isEmpty) {
      return const Card(
        child: Padding(
          padding: EdgeInsets.all(16.0),
          child: Text('No test results yet. Run fallback tests to see results.'),
        ),
      );
    }

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Test Results',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 16),
            _buildTestSummary(),
            const SizedBox(height: 16),
            ...(_testResults.map((result) => _buildTestResultCard(result)).toList()),
          ],
        ),
      ),
    );
  }

  /// Build test summary
  Widget _buildTestSummary() {
    final successCount = _testResults.where((r) => r.success).length;
    final failCount = _testResults.length - successCount;
    final successRate = _testResults.isNotEmpty ? (successCount / _testResults.length * 100) : 0;

    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: successRate >= 80 ? Colors.green[50] : Colors.orange[50],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: successRate >= 80 ? Colors.green : Colors.orange,
          width: 1,
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        children: [
          _buildSummaryItem('Total', '${_testResults.length}', Icons.list),
          _buildSummaryItem('Passed', '$successCount', Icons.check_circle, Colors.green),
          _buildSummaryItem('Failed', '$failCount', Icons.error, Colors.red),
          _buildSummaryItem('Success Rate', '${successRate.toStringAsFixed(1)}%', Icons.percent, 
                           successRate >= 80 ? Colors.green : Colors.orange),
        ],
      ),
    );
  }

  /// Build summary item
  Widget _buildSummaryItem(String label, String value, IconData icon, [Color? color]) {
    return Column(
      children: [
        Icon(icon, color: color ?? Colors.grey[600], size: 20),
        const SizedBox(height: 4),
        Text(
          value,
          style: TextStyle(
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
        Text(
          label,
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey[600],
          ),
        ),
      ],
    );
  }

  /// Build individual test result card
  Widget _buildTestResultCard(FallbackAssetTestResult result) {
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: Padding(
        padding: const EdgeInsets.all(12.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  result.success ? Icons.check_circle : Icons.error,
                  color: result.success ? Colors.green : Colors.red,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    result.testName,
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 14,
                    ),
                  ),
                ),
                Text(
                  result.timestamp.toString().substring(11, 19),
                  style: TextStyle(
                    color: Colors.grey[600],
                    fontSize: 12,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              result.message,
              style: TextStyle(
                color: result.success ? Colors.black87 : Colors.red[700],
              ),
            ),
            if (result.data != null) ...[
              const SizedBox(height: 8),
              ExpansionTile(
                title: const Text('Test Data', style: TextStyle(fontSize: 12)),
                children: [
                  Container(
                    width: double.infinity,
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: Colors.grey[100],
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: Text(
                      result.data.toString(),
                      style: const TextStyle(fontSize: 10, fontFamily: 'monospace'),
                    ),
                  ),
                ],
              ),
            ],
          ],
        ),
      ),
    );
  }

  /// Run fallback asset tests
  Future<void> _runFallbackTests() async {
    setState(() {
      _isRunningTests = true;
      _testStatus = 'Initializing fallback asset tests...';
      _testResults.clear();
      _testReport = '';
    });

    try {
      setState(() {
        _testStatus = 'Running comprehensive fallback asset tests...';
      });

      // Create fallback manager and test runner
      final fallbackManager = FallbackAssetManager();
      final testRunner = FallbackAssetTestRunner(fallbackManager: fallbackManager);

      // Run tests
      final results = await testRunner.runFallbackTests();
      final report = testRunner.generateTestReport();

      setState(() {
        _testResults = results;
        _testReport = report;
        _testStatus = 'Fallback asset tests completed. ${results.length} tests run.';
        _isRunningTests = false;
      });

      // Show completion message
      if (mounted) {
        final successCount = results.where((r) => r.success).length;
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Fallback tests completed: $successCount/${results.length} passed'),
            backgroundColor: successCount == results.length ? Colors.green : Colors.orange,
          ),
        );
      }

    } catch (e, stackTrace) {
      AppLogger.error('[FALLBACK_TEST_SCREEN] Error running fallback tests', e, stackTrace);
      
      setState(() {
        _testStatus = 'Error running tests: $e';
        _isRunningTests = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error running fallback tests: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// Clear test results
  void _clearResults() {
    setState(() {
      _testResults.clear();
      _testReport = '';
      _testStatus = 'Ready to test fallback asset system';
    });
  }

  /// Show full test report
  void _showFullReport() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Fallback Asset Test Report'),
        content: SizedBox(
          width: double.maxFinite,
          height: 400,
          child: SingleChildScrollView(
            child: Text(
              _testReport,
              style: const TextStyle(fontFamily: 'monospace', fontSize: 12),
            ),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }
}
