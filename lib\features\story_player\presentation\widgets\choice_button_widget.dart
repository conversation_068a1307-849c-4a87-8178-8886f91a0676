import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:choice_once_upon_a_time/models/choice_model.dart';
import 'package:choice_once_upon_a_time/core/audio/sound_effect_player_service.dart';
import 'package:choice_once_upon_a_time/app/providers/service_providers.dart';

/// Widget for displaying interactive story choices
class ChoiceButtonWidget extends ConsumerWidget {
  final ChoiceModel choice;
  final VoidCallback onPressed;
  final String language;

  const ChoiceButtonWidget({
    super.key,
    required this.choice,
    required this.onPressed,
    this.language = 'en-US',
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);
    final displayText = choice.getLocalizedDisplayText(language);
    
    return Container(
      width: double.infinity,
      margin: const EdgeInsets.symmetric(vertical: 4),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () {
            // Play choice selection sound
            ref.read(soundEffectPlayerServiceProvider).playUISound(UISoundType.choiceSelect);
            onPressed();
          },
          borderRadius: BorderRadius.circular(16),
          child: Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(16),
              border: Border.all(
                color: theme.colorScheme.primary.withOpacity(0.3),
                width: 2,
              ),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.05),
                  blurRadius: 4,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Row(
              children: [
                // Choice icon placeholder
                Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    color: theme.colorScheme.primary.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: Icon(
                    _getChoiceIcon(),
                    color: theme.colorScheme.primary,
                    size: 20,
                  ),
                ),
                
                const SizedBox(width: 16),
                
                // Choice text
                Expanded(
                  child: Text(
                    displayText,
                    style: theme.textTheme.bodyLarge?.copyWith(
                      fontWeight: FontWeight.w500,
                      color: const Color(0xFF2D3748),
                    ),
                  ),
                ),
                
                // Arrow indicator
                Icon(
                  Icons.arrow_forward_ios,
                  color: theme.colorScheme.primary,
                  size: 16,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  IconData _getChoiceIcon() {
    // For now, use a generic icon. In the future, this could be based on choice type
    // or specified in the choice data
    return Icons.touch_app;
  }
}
