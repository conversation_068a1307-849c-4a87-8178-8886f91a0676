import 'package:json_annotation/json_annotation.dart';
import 'scene_model.dart';
import 'rewards_model.dart';

part 'story_model.g.dart';

/// Model for complete story data including all scenes
@JsonSerializable()
class StoryModel {
  /// Unique identifier for the story
  @<PERSON>son<PERSON>ey(name: 'storyId')
  final String id;

  /// Working title of the story
  @JsonKey(name: 'workingTitle')
  final String title;

  /// URL to the story cover image (optional in JSON)
  final String? coverImageUrl;

  /// The core moral value this story teaches
  @Json<PERSON><PERSON>(name: 'targetCoreMoralValue')
  final String targetMoralValue;

  /// Target age sub-segment (e.g., "4-6")
  final String targetAgeSubSegment;

  /// Story version (optional)
  final String? version;

  /// ID of the initial scene (optional)
  final String? initialSceneId;

  /// List of all scenes in the story
  final List<SceneModel> scenes;

  /// Narrator persona guidance for TTS
  final String narratorPersonaGuidance;

  /// List of supported language codes
  final List<String> supportedLanguages;

  /// Default language for this story
  final String defaultLanguage;

  /// URL to the asset manifest for this story version (optional)
  final String? assetManifestUrl;

  /// Estimated duration in minutes
  final int estimatedDurationMinutes;

  /// Whether this story is free to access
  final bool isFree;

  /// Whether the story is published
  final bool published;

  /// Localized logline/description
  final Map<String, String> logline;

  /// Rewards available for this story
  final RewardsModel? rewards;

  /// Getter for story ID to maintain consistency with property naming
  String get storyId => id;

  const StoryModel({
    required this.id,
    required this.title,
    this.coverImageUrl,
    required this.targetMoralValue,
    required this.targetAgeSubSegment,
    this.version = '1.0.0',
    this.initialSceneId,
    required this.scenes,
    required this.narratorPersonaGuidance,
    this.supportedLanguages = const ['en-US'],
    required this.defaultLanguage,
    this.assetManifestUrl,
    this.estimatedDurationMinutes = 10,
    this.isFree = true,
    this.published = true,
    this.logline = const {},
    this.rewards,
  });

  /// Creates a StoryModel from JSON
  factory StoryModel.fromJson(Map<String, dynamic> json) =>
      _$StoryModelFromJson(json);

  /// Converts the StoryModel to JSON
  Map<String, dynamic> toJson() => _$StoryModelToJson(this);

  /// Gets the localized title for the given language code
  String getLocalizedTitle(String languageCode) {
    return title; // Since title is now a String, return it directly
  }

  /// Gets the localized logline for the given language code
  String getLocalizedLogline(String languageCode) {
    return logline[languageCode] ?? logline[defaultLanguage] ?? logline.values.first;
  }

  /// Gets the initial scene
  SceneModel? get initialScene => scenes.firstWhere(
    (scene) => scene.sceneId == initialSceneId,
    orElse: () => scenes.isNotEmpty ? scenes.first : throw StateError('No scenes available'),
  );

  /// Gets a scene by its ID
  SceneModel? getScene(String sceneId) {
    try {
      return scenes.firstWhere((scene) => scene.sceneId == sceneId);
    } catch (e) {
      return null;
    }
  }

  /// Gets all scenes sorted by order
  List<SceneModel> get sortedScenes {
    final sceneList = List<SceneModel>.from(scenes);
    sceneList.sort((a, b) => (a.order ?? 0).compareTo(b.order ?? 0));
    return sceneList;
  }

  /// Creates a copy of this model with updated fields
  StoryModel copyWith({
    String? id,
    String? title,
    String? coverImageUrl,
    String? targetMoralValue,
    String? targetAgeSubSegment,
    String? version,
    String? initialSceneId,
    List<SceneModel>? scenes,
    String? narratorPersonaGuidance,
    List<String>? supportedLanguages,
    String? defaultLanguage,
    String? assetManifestUrl,
    int? estimatedDurationMinutes,
    bool? isFree,
    bool? published,
    Map<String, String>? logline,
    RewardsModel? rewards,
  }) {
    return StoryModel(
      id: id ?? this.id,
      title: title ?? this.title,
      coverImageUrl: coverImageUrl ?? this.coverImageUrl,
      targetMoralValue: targetMoralValue ?? this.targetMoralValue,
      targetAgeSubSegment: targetAgeSubSegment ?? this.targetAgeSubSegment,
      version: version ?? this.version,
      initialSceneId: initialSceneId ?? this.initialSceneId,
      scenes: scenes ?? this.scenes,
      narratorPersonaGuidance: narratorPersonaGuidance ?? this.narratorPersonaGuidance,
      supportedLanguages: supportedLanguages ?? this.supportedLanguages,
      defaultLanguage: defaultLanguage ?? this.defaultLanguage,
      assetManifestUrl: assetManifestUrl ?? this.assetManifestUrl,
      estimatedDurationMinutes: estimatedDurationMinutes ?? this.estimatedDurationMinutes,
      isFree: isFree ?? this.isFree,
      published: published ?? this.published,
      logline: logline ?? this.logline,
      rewards: rewards ?? this.rewards,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is StoryModel && other.id == id && other.version == version;
  }

  @override
  int get hashCode => Object.hash(id, version);

  @override
  String toString() {
    return 'StoryModel(id: $id, version: $version, scenes: ${scenes.length})';
  }
}
