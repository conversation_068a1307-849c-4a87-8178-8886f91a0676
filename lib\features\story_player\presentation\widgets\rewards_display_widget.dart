import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:choice_once_upon_a_time/models/rewards_model.dart';

/// Widget for displaying earned rewards in a child-friendly manner
class RewardsDisplayWidget extends ConsumerWidget {
  final EarnedReward reward;
  final VoidCallback? onDismiss;

  const RewardsDisplayWidget({
    super.key,
    required this.reward,
    this.onDismiss,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);
    final screenSize = MediaQuery.of(context).size;
    final isSmallScreen = screenSize.width < 600;

    return Dialog(
      backgroundColor: Colors.transparent,
      child: Container(
        constraints: BoxConstraints(
          maxWidth: isSmallScreen ? screenSize.width * 0.9 : 400,
          maxHeight: isSmallScreen ? screenSize.height * 0.6 : 500,
        ),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              theme.colorScheme.primaryContainer,
              theme.colorScheme.secondaryContainer,
            ],
          ),
          borderRadius: BorderRadius.circular(20),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.3),
              blurRadius: 10,
              offset: const Offset(0, 5),
            ),
          ],
        ),
        child: Padding(
          padding: EdgeInsets.all(isSmallScreen ? 16.0 : 24.0),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Celebration header
              _buildCelebrationHeader(theme, isSmallScreen),
              
              SizedBox(height: isSmallScreen ? 16 : 24),
              
              // Reward badge
              _buildRewardBadge(theme, isSmallScreen),
              
              SizedBox(height: isSmallScreen ? 16 : 24),
              
              // Reward text
              _buildRewardText(theme, isSmallScreen),
              
              SizedBox(height: isSmallScreen ? 20 : 32),
              
              // Continue button
              _buildContinueButton(context, theme, isSmallScreen),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildCelebrationHeader(ThemeData theme, bool isSmallScreen) {
    return Column(
      children: [
        // Celebration emoji
        Text(
          '🎉',
          style: TextStyle(
            fontSize: isSmallScreen ? 32 : 48,
          ),
        ),
        SizedBox(height: isSmallScreen ? 8 : 12),
        
        // Congratulations text
        Text(
          'Congratulations!',
          style: theme.textTheme.headlineSmall?.copyWith(
            color: theme.colorScheme.onPrimaryContainer,
            fontWeight: FontWeight.bold,
            fontSize: isSmallScreen ? 20 : 24,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildRewardBadge(ThemeData theme, bool isSmallScreen) {
    final badgeSize = isSmallScreen ? 80.0 : 120.0;
    
    return Container(
      width: badgeSize,
      height: badgeSize,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        gradient: RadialGradient(
          colors: [
            Colors.amber.shade300,
            Colors.amber.shade600,
          ],
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.amber.withValues(alpha: 0.5),
            blurRadius: 15,
            spreadRadius: 2,
          ),
        ],
      ),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // Badge icon based on reward type
            Icon(
              _getRewardIcon(),
              size: isSmallScreen ? 24 : 32,
              color: Colors.white,
            ),
            SizedBox(height: isSmallScreen ? 4 : 8),
            
            // Badge text
            Text(
              _getRewardTypeText(),
              style: TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.bold,
                fontSize: isSmallScreen ? 10 : 12,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRewardText(ThemeData theme, bool isSmallScreen) {
    return Column(
      children: [
        // Reward name
        Text(
          reward.rewardId,
          style: theme.textTheme.titleLarge?.copyWith(
            color: theme.colorScheme.onPrimaryContainer,
            fontWeight: FontWeight.bold,
            fontSize: isSmallScreen ? 18 : 22,
          ),
          textAlign: TextAlign.center,
        ),
        
        SizedBox(height: isSmallScreen ? 8 : 12),
        
        // Reward description
        Text(
          _getRewardDescription(),
          style: theme.textTheme.bodyMedium?.copyWith(
            color: theme.colorScheme.onPrimaryContainer.withValues(alpha: 0.8),
            fontSize: isSmallScreen ? 14 : 16,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildContinueButton(BuildContext context, ThemeData theme, bool isSmallScreen) {
    return SizedBox(
      width: double.infinity,
      child: ElevatedButton(
        onPressed: () {
          Navigator.of(context).pop();
          onDismiss?.call();
        },
        style: ElevatedButton.styleFrom(
          backgroundColor: theme.colorScheme.primary,
          foregroundColor: theme.colorScheme.onPrimary,
          padding: EdgeInsets.symmetric(
            vertical: isSmallScreen ? 12 : 16,
            horizontal: isSmallScreen ? 24 : 32,
          ),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(25),
          ),
          elevation: 3,
        ),
        child: Text(
          'Continue Story',
          style: TextStyle(
            fontSize: isSmallScreen ? 16 : 18,
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
    );
  }

  IconData _getRewardIcon() {
    switch (reward.rewardType) {
      case 'completion':
        return Icons.emoji_events; // Trophy
      case 'moral_choice':
        return Icons.favorite; // Heart
      default:
        return Icons.star; // Star
    }
  }

  String _getRewardTypeText() {
    switch (reward.rewardType) {
      case 'completion':
        return 'COMPLETE';
      case 'moral_choice':
        return 'CHOICE';
      default:
        return 'REWARD';
    }
  }

  String _getRewardDescription() {
    switch (reward.rewardType) {
      case 'completion':
        return 'You completed the entire story! Well done!';
      case 'moral_choice':
        return 'You made a great choice that shows good values!';
      default:
        return 'You earned this special reward!';
    }
  }
}

/// Simple rewards notification widget for quick display
class RewardsNotificationWidget extends StatelessWidget {
  final String rewardName;
  final String rewardType;
  final VoidCallback? onTap;

  const RewardsNotificationWidget({
    super.key,
    required this.rewardName,
    required this.rewardType,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return GestureDetector(
      onTap: onTap,
      child: Container(
        margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: theme.colorScheme.primaryContainer,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.1),
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Row(
          children: [
            // Reward icon
            Container(
              width: 40,
              height: 40,
              decoration: const BoxDecoration(
                color: Colors.amber,
                shape: BoxShape.circle,
              ),
              child: Icon(
                rewardType == 'completion' ? Icons.emoji_events : Icons.favorite,
                color: Colors.white,
                size: 20,
              ),
            ),
            
            const SizedBox(width: 12),
            
            // Reward text
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Reward Earned!',
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: theme.colorScheme.onPrimaryContainer.withValues(alpha: 0.7),
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  Text(
                    rewardName,
                    style: theme.textTheme.bodyMedium?.copyWith(
                      color: theme.colorScheme.onPrimaryContainer,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
            ),
            
            // Tap indicator
            Icon(
              Icons.touch_app,
              color: theme.colorScheme.onPrimaryContainer.withValues(alpha: 0.5),
              size: 16,
            ),
          ],
        ),
      ),
    );
  }
}
